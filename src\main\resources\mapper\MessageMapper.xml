<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.makiyo.aidoctor.mapper.MessageMapper">
  <resultMap id="BaseResultMap" type="com.makiyo.aidoctor.entity.Message">
    <id column="id" jdbcType="INTEGER" property="id"/>
    <result column="session_id" jdbcType="INTEGER" property="sessionId"/>
    <result column="speaker" jdbcType="VARCHAR" property="speaker"/>
    <result column="message" jdbcType="VARCHAR" property="message"/>
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
  </resultMap>

  <sql id="Base_Column_List">
    id, session_id, speaker, message, created_at
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from message
    where id = #{id,jdbcType=INTEGER}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from message
    where id = #{id,jdbcType=INTEGER}
  </delete>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.makiyo.aidoctor.entity.Message" useGeneratedKeys="true">
    insert into message (session_id, speaker, message)
    values (#{sessionId,jdbcType=INTEGER}, #{speaker,jdbcType=VARCHAR}, #{message,jdbcType=VARCHAR})
  </insert>

  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.makiyo.aidoctor.entity.Message" useGeneratedKeys="true">
    insert into message
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sessionId != null">
        session_id,
      </if>
      <if test="speaker != null">
        speaker,
      </if>
      <if test="message != null">
        message,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sessionId != null">
        #{sessionId,jdbcType=INTEGER},
      </if>
      <if test="speaker != null">
        #{speaker,jdbcType=VARCHAR},
      </if>
      <if test="message != null">
        #{message,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.makiyo.aidoctor.entity.Message">
    update message
    <set>
      <if test="sessionId != null">
        session_id = #{sessionId,jdbcType=INTEGER},
      </if>
      <if test="speaker != null">
        speaker = #{speaker,jdbcType=VARCHAR},
      </if>
      <if test="message != null">
        message = #{message,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.makiyo.aidoctor.entity.Message">
    update message
    set session_id = #{sessionId,jdbcType=INTEGER},
      speaker = #{speaker,jdbcType=VARCHAR},
      message = #{message,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectBySessionId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
      id,
      session_id,
      speaker,
      message
    from message
    where session_id = #{sessionId,jdbcType=INTEGER}
    order by id asc
  </select>

  <update id="updateMessageById">
    update message
    set message = #{message,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="getSessionMessages" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM message
    WHERE session_id = #{sessionId,jdbcType=INTEGER}
    ORDER BY created_at ASC
  </select>

  <select id="getLastMessagesForSessions" resultType="map">
    SELECT m1.session_id as sessionId, m1.message as message
    FROM message m1
           INNER JOIN (
      SELECT session_id, MAX(id) AS max_id
      FROM message
      WHERE session_id IN
      <foreach item="item" index="index" collection="sessionIds" open="(" separator="," close=")">
        #{item}
      </foreach>
      GROUP BY session_id
    ) m2 ON m1.session_id = m2.session_id AND m1.id = m2.max_id
  </select>

  <select id="getMessageCounts" resultType="map">
    SELECT session_id as sessionId, COUNT(*) as messageCount
    FROM message
    WHERE session_id IN
    <foreach item="item" index="index" collection="sessionIds" open="(" separator="," close=")">
      #{item}
    </foreach>
    GROUP BY session_id
  </select>
</mapper>