<template>
  <div class="chat-container">
    <div class="main-content">
      <!-- 左侧会话列表 -->
      <div class="session-list" :class="{ collapsed: isCollapsed }">
        <div class="session-header">
          <span class="user-info">
            <a-avatar :src="getAvatarSrc(userInfo?.profilePicture)" />
            <span class="username" v-show="!isCollapsed">{{ userInfo?.username || '用户' }}</span>
          </span>
        </div>
        <div class="new-session-wrapper" v-show="!isCollapsed">
          <a-button type="primary" @click="createNewSession" class="new-session-button">
            <span class="plus-icon">+</span>
            New Chat
          </a-button>
        </div>
        <div class="session-items">
          <a-list :data-source="sessions" :loading="loading">
            <template #renderItem="{ item }">
              <a-list-item 
                :class="['session-item', { active: currentSessionId === item.id }]"
                @click="switchSession(item.id)"
              >
                <div class="session-info" v-show="!isCollapsed">
                  <span class="session-title">会话 {{ item.id }}</span>
                  <span class="session-time">{{ formatTime(item.createdAt) }}</span>
                </div>
                <span class="session-number" v-show="isCollapsed">{{ item.id }}</span>
              </a-list-item>
            </template>
          </a-list>
        </div>
        <div class="collapse-button" @click="toggleCollapse">
          <svg viewBox="0 0 24 24" width="16" height="16">
            <path fill="currentColor" d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
          </svg>
        </div>
      </div>

      <!-- 右侧聊天区域 -->
      <div class="chat-main">
        <div class="chat-title">
          <span>当前会话: {{ currentSessionId ? '会话 ' + currentSessionId : '请选择会话' }}</span>
        </div>
        <div class="chat-content">
          <div class="chat-messages" ref="messageContainer">
            <template v-if="currentSessionId">
              <div 
                v-for="message in currentMessages" 
                :key="message.id"
                :class="['message-item', message.speaker === 'doctor' ? 'doctor' : 'patient']"
              >
                <a-avatar 
                  :src="message.speaker === 'doctor' ? doctorLogo : getAvatarSrc(userInfo?.profilePicture)" 
                  :class="message.speaker"
                />
                <div class="message-content">
                  <div v-html="formatMessage(message.message)"></div>
                </div>
              </div>
            </template>
            <div v-else class="no-session">
              请选择一个会话或创建新会话
            </div>
          </div>
          <div class="loading-container" v-if="loading">
            <div class="loading-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
          <div class="chat-input-container" v-if="currentSessionId">
            <div class="input-wrapper">
              <a-textarea
                v-model:value="inputMessage"
                :rows="2"
                placeholder="请输入消息..."
                :disabled="false"
                @keypress.enter.prevent="handleKeyPress"
                autocomplete="off"
                class="chat-input"
                ref="chatInputRef"
              />
              <div class="button-group">
                <div class="voice-button" :class="{ recording: isRecording }" @click="toggleVoice">
                  <img v-if="!isRecording" src="@/assets/voice-off.svg" alt="开始录音" />
                  <img v-else src="@/assets/voice.svg" alt="停止录音" />
                </div>
                
                <a-button 
                  type="link" 
                  class="send-button"
                  :loading="loading"
                  :disabled="loading || !inputMessage.trim()"
                  @click="sendMessage"
                >
                  <span class="send-icon"></span>
                </a-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, nextTick, reactive, onUnmounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import request from '@/utils/request'
import { useUserStore } from '@/stores/user'
import doctorLogo from '@/assets/doctor.png'

export default {
  name: 'ChatPage',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const loading = ref(false)
    const userStore = useUserStore()
    const userInfo = ref({
      userId: null,
      username: '',
      profilePicture: '',  // 提供默认值
      bio: ''
    })
    const sessions = ref([])
    const currentSessionId = ref(null)
    const inputMessage = ref('')
    const messageContainer = ref(null)
    const isCollapsed = ref(false)
    const chatInputRef = ref(null) // 添加对输入框的引用
    
    // 消息存储
    const sessionMessages = reactive({})

    let messageObserver = null  // 在 setup 顶部声明
    
    // 添加一个标志位，用于标记是否是切换会话导致的消息变化
    const isSwitchingSession = ref(false)

    const isRecording = ref(false)
    const mediaRecorder = ref(null)
    const audioChunks = ref([])
    
    // 跟踪当前的EventSource连接
    let currentEventSource = null

    // 当前会话的消息
    const currentMessages = computed(() => {
      return currentSessionId.value ? (sessionMessages[currentSessionId.value] || []) : []
    })

    // 跟踪当前正在播放的音频元素
    let currentAudio = null;

    // 监听消息变化，自动播放新的音频
    watch(currentMessages, async (newMessages, oldMessages) => {
      // 如果是切换会话导致的消息变化，不播放音频
      if (isSwitchingSession.value) {
        console.log('切换会话导致的消息变化，跳过音频播放');
        isSwitchingSession.value = false;
        return;
      }
      
      if (oldMessages && newMessages.length > oldMessages.length) {
        const latestMessage = newMessages[newMessages.length - 1];
        if (latestMessage.audioUrl) {
          try {
            console.log('准备播放音频，原始URL:', latestMessage.audioUrl);
            
            // 检查URL是否为空
            if (!latestMessage.audioUrl.trim()) {
              console.warn('消息中的音频URL为空，跳过播放');
              return;
            }
            
            // 再次检查是否正在切换会话
            if (isSwitchingSession.value) {
              console.log('检测到会话切换，跳过音频播放');
              return;
            }
            
            const audioUrl = `/audio/stream?audioUrl=${encodeURIComponent(latestMessage.audioUrl)}`;
            console.log('处理后的音频URL:', audioUrl);
            
            // 如果有正在播放的音频，先停止它
            if (currentAudio) {
              currentAudio.pause();
              currentAudio.src = '';
              currentAudio = null;
            }
            
            const audio = new Audio();
            currentAudio = audio;
            
            // 添加错误处理
            const errorHandler = (e) => {
              console.error('音频错误:', e);
              if (audio.error) {
                console.error('错误详情:', audio.error);
                
                // 如果是资源不可用错误，可能是音频还没准备好，不显示错误消息
                if (audio.error.code === 4) {
                  console.warn('音频资源暂时不可用，可能是延迟到达');
                  return;
                }
                
                // 根据错误代码提供更具体的错误信息
                // eslint-disable-next-line no-unused-vars
                let errorMessage = '音频加载失败';
                switch(audio.error.code) {
                  case 1:
                    errorMessage += ': 获取过程被用户终止';
                    break;
                  case 2:
                    errorMessage += ': 网络错误';
                    break;
                  case 3:
                    errorMessage += ': 解码错误';
                    break;
                  case 4:
                    errorMessage += ': 音频资源不可用';
                    break;
                }
                
                message.error(errorMessage);
              }
            };
            
            // 添加所有相关的音频事件监听
            audio.addEventListener('loadstart', () => {
              console.log('开始加载音频');
            });
            
            audio.addEventListener('durationchange', () => {
              console.log('音频时长变化:', audio.duration);
            });
            
            audio.addEventListener('loadedmetadata', () => {
              console.log('音频元数据加载完成');
            });
            
            audio.addEventListener('canplay', () => {
              console.log('音频可以开始播放');
            });
            
            audio.addEventListener('playing', () => {
              console.log('音频开始播放');
              message.success('开始播放语音');
            });
            
            audio.addEventListener('ended', () => {
              console.log('音频播放结束');
              currentAudio = null;
            });
            
            audio.addEventListener('error', errorHandler, true);
            
            // 设置音频属性
            audio.src = audioUrl;
            audio.volume = 1.0;
            audio.preload = 'auto';
            
            // 设置超时处理，延长超时时间以适应音频延迟
            const timeoutPromise = new Promise((_, reject) => {
              setTimeout(() => reject(new Error('音频加载超时')), 30000); // 增加到30秒
            });
            
            // 尝试播放
            try {
              await Promise.race([
                audio.play(),
                timeoutPromise
              ]);
            } catch (playError) {
              console.error('播放失败:', playError);
              
              if (playError.message === '音频加载超时') {
                console.warn('音频加载超时，可能是延迟到达，尝试重新加载');
                
                // 尝试重新加载音频
                setTimeout(async () => {
                  try {
                    if (currentAudio) {
                      currentAudio.load();
                      await currentAudio.play();
                    }
                  } catch (retryError) {
                    console.error('重试播放失败:', retryError);
                  }
                }, 5555); // 5秒后重试
                
                return;
              }
              
              // 尝试静音播放（解决一些浏览器的自动播放限制）
              try {
                audio.muted = true;
                await audio.play();
                audio.muted = false;
              } catch (mutedPlayError) {
                console.error('静音播放也失败:', mutedPlayError);
                // 不显示错误消息，避免用户困扰
                // message.error('浏览器阻止了音频播放');
              }
            }
          } catch (error) {
            console.error('音频处理失败:', error);
            // 不显示错误消息，避免用户困扰
            // message.error('音频处理失败: ' + error.message);
          }
        }
      }
    }, { deep: true })

    // 格式化时间
    const formatTime = (timestamp) => {
      if (!timestamp) return ''
      const date = new Date(timestamp)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    }

    // 格式化消息内容
    const formatMessage = (content) => {
      if (!content) return ''
      return content
        .replace(/<text>\n?/g, '')
        .replace(/<\/text>/g, '')
        .replace(/<observation>\n?/g, '')
        .replace(/<\/observation>/g, '')
        .replace(/^\n+/, '') // 移除开头的所有换行符
        .replace(/\n/g, '<br>')
        .trim()
    }

    // 切换会话
    const switchSession = (sessionId) => {
      // 设置标志位，表示正在切换会话
      isSwitchingSession.value = true
      
      // 停止当前正在播放的音频
      if (currentAudio) {
        console.log('切换会话，停止当前音频播放');
        currentAudio.pause();
        currentAudio.src = '';
        currentAudio = null;
      }
      
      currentSessionId.value = sessionId
      nextTick(() => {
        if (messageContainer.value) {
          messageContainer.value.scrollTop = messageContainer.value.scrollHeight
        }
      })
    }

    // 创建新会话
    const createNewSession = async () => {
      try {
        loading.value = true
        // 设置标志位，表示正在切换会话
        isSwitchingSession.value = true
        const response = await request({
          url: '/session/addSession',
          method: 'post',
          params: {
            userId: route.params.userId
          }
        })
        if (response.code === 200) {
          sessions.value.unshift(response.content)
          sessionMessages[response.content.id] = []
          currentSessionId.value = response.content.id
          
          // 添加医生的欢迎消息到前端显示
          const welcomeMessage = "您好，请问孩子的性别和年龄？"
          const doctorMessageObj = {
            id: Date.now(),
            sessionId: currentSessionId.value,
            speaker: 'doctor',
            message: welcomeMessage,
            createdAt: new Date().toISOString()
          }
          
          // 将欢迎消息添加到会话中
          sessionMessages[currentSessionId.value].push(doctorMessageObj)
          
          // 保存到本地存储
          saveMessagesToLocal()
          
          // 滚动到底部显示新消息
          await nextTick()
          scrollToBottom()
          
          message.success('新会话创建成功')
        } else {
          message.error(response.message || '创建会话失败')
        }
      } catch (error) {
        console.error('创建会话错误:', error)
        message.error('创建会话失败')
      } finally {
        loading.value = false
      }
    }

    // 组件挂载时，如果有会话则选择第一个
    onMounted(async () => {
      const userId = route.params.userId
      if (!userId) {
        message.error('请先登录')
        router.push('/login')
        return
      }

      try {
        loading.value = true
        // 设置标志位，表示正在初始化会话
        isSwitchingSession.value = true
        loadMessagesFromLocal()

        // 先从本地存储获取用户信息
        const storedUserInfo = localStorage.getItem('userInfo')
        if (storedUserInfo) {
          const parsedUserInfo = JSON.parse(storedUserInfo)
          userInfo.value = {
            ...parsedUserInfo,
            userId: parseInt(userId)
          }
          userStore.setUserInfo(userInfo.value)
        }

        // 安全地将userId转换为整数
        let numericUserId;
        try {
          numericUserId = parseInt(userId);
          if (isNaN(numericUserId)) {
            throw new Error('用户ID必须是数字');
          }
        } catch (e) {
          message.error('无效的用户ID: ' + e.message);
          router.push('/');
          return;
        }

        // 从服务器获取最新信息
        const response = await request({
          url: '/user/chat',
          method: 'post',
          params: { userId: numericUserId }
        })

        if (response.code === 200) {
          const data = response.content
          // 更新用户信息
          userInfo.value = {
            ...data.userInfo,
            userId: numericUserId,
            profilePicture: data.userInfo?.profilePicture || userInfo.value.profilePicture
          }
          userStore.setUserInfo(userInfo.value)
          
          // --- V1 Session Filtering Logic Start ---
          const allSessions = data.sessions || [];
          const allSessionMessages = data.sessionMessages || {};
          console.log(`[Chat] All sessions received: ${allSessions.length}`, JSON.parse(JSON.stringify(allSessions)));
          console.log(`[Chat] All session messages received:`, JSON.parse(JSON.stringify(allSessionMessages)));

          const v1Sessions = allSessions.filter(session => {
            const messages = allSessionMessages[session.id];
            const firstMessageContent = messages?.[0]?.message;

            let isV2 = false; // 默认为 false (不是 V2)
            if (typeof firstMessageContent === 'string') {
              const trimmedContent = firstMessageContent.trim();
              if (trimmedContent.startsWith('{') && trimmedContent.endsWith('}')) {
                try {
                  const parsedObject = JSON.parse(trimmedContent);
                  if (parsedObject && typeof parsedObject === 'object' && parsedObject.hasOwnProperty('interaction_history')) {
                    isV2 = true; // 解析成功且包含关键字，判定为 V2
                  }
                } catch (e) {
                  // 解析失败，不是 V2
                  isV2 = false;
                }
              }
            }
            // console.log(`[Chat Debug] Session ${session.id}: isV2 (based on JSON parse)? ${isV2}`);
            return !isV2; // Chat.vue 需要保留非 V2 的会话
          });
          console.log(`[Chat] Filtered V1 sessions (${v1Sessions.length}):`, JSON.parse(JSON.stringify(v1Sessions)));

          sessions.value = v1Sessions; // Assign the filtered V1 sessions
          // Assign all messages to the reactive proxy for potential access (needed for switching)
          Object.assign(sessionMessages, allSessionMessages); 
          console.log(`[Chat] Assigned ${sessions.value.length} V1 sessions to sessions.value.`);
          console.log(`[Chat] sessionMessages object after assign:`, JSON.parse(JSON.stringify(sessionMessages)));
          // --- V1 Session Filtering Logic End ---

          saveMessagesToLocal() // Save the filtered data

          // Set current session ID logic
          let selectedSessionId = null;
          if (sessions.value.length > 0) { // Check the filtered list
              const currentSessionExistsInFiltered = sessions.value.some(s => s.id === currentSessionId.value);
              if (currentSessionId.value && currentSessionExistsInFiltered) {
                  selectedSessionId = currentSessionId.value; // Keep current if valid
                  console.log(`[Chat] Keeping existing currentSessionId: ${selectedSessionId}`);
              } else {
                  selectedSessionId = sessions.value[0].id; // Default to the first V1 session
                  console.log(`[Chat] Setting currentSessionId to the first V1 session: ${selectedSessionId}`);
              }
          } else {
              console.log(`[Chat] No V1 sessions found.`);
              selectedSessionId = null; 
          }
          currentSessionId.value = selectedSessionId; // Update the ref
          console.log(`[Chat] Final currentSessionId.value: ${currentSessionId.value}`);

          // 如果有会话，选择第一个 (This block is replaced by the logic above)
          /*
          if (sessions.value.length > 0) {
            currentSessionId.value = sessions.value[0].id
            await nextTick()
            scrollToBottom()
          }
          */
          // Ensure scroll happens even if no session was selected initially
          await nextTick();
          scrollToBottom();

        } else {
          throw new Error(response.message || '获取用户信息失败')
        }
      } catch (error) {
        console.error('初始化错误:', error)
        message.error(error.message || '初始化失败')
        router.push('/login')
      } finally {
        loading.value = false
      }

      // 在组件挂载完成后设置消息观察器
      observeMessages()
    })

    // 在 setup 函数内添加超时相关的变量
    const connectionTimeout = 360000 // 6分钟超时 (修改此处)
    let messageTimer = null
    let connectionCheckTimer = null

    // 修改 createEventSource 函数
    const createEventSource = (userMessage, doctorMessageObj) => {
      // 创建一个变量跟踪连接是否已关闭，防止重复关闭
      let isConnectionClosed = false;
      let eventSource = null; // 在函数内部声明eventSource变量
      
      // 创建一个函数来安全地关闭连接
      const safeCloseConnection = () => {
        if (!isConnectionClosed && eventSource) {
          try {
            eventSource.close();
            isConnectionClosed = true;
            console.log('EventSource连接已安全关闭');
          } catch (err) {
            console.error('关闭EventSource连接时出错:', err);
          }
        }
      };
      
      // 创建一个函数来清理所有资源
      const cleanupResources = () => {
        clearTimeout(messageTimer);
        if (connectionCheckTimer) {
          clearInterval(connectionCheckTimer);
        }
        loading.value = false;
      };
      
      try {
        // 确保userId是有效的数字
        const userId = userInfo.value.userId;
        if (!userId || isNaN(userId)) {
          throw new Error('用户ID无效');
        }
        
        eventSource = new EventSource(
          `/message/inquiry?userId=${userId}&sessionId=${currentSessionId.value}&message=${encodeURIComponent(userMessage)}`
        );

        console.log('Creating EventSource connection...');
        
        // 移除30秒后显示提示的逻辑
        messageTimer = setTimeout(() => {
          // 不在这里显示诊断结果的提示，而是在检测到solution_text时显示
          console.log('等待响应中...');
        }, 30000);

        // 设置6分钟的单次等待超时 (修改此处)
        messageTimer = setTimeout(() => {
          console.log('请求超过6分钟，关闭连接'); // 修改日志信息
          
          if (doctorMessageObj.message === '正在思考中...' || 
              doctorMessageObj.message === '消息正在生成诊断结果，请稍等...') {
            doctorMessageObj.message = '回复生成中...\n\n可能需要较长时间，请稍后查看或创建新会话。';
          }
          
          // 关闭等待提示
          message.destroy();
          
          safeCloseConnection();
          cleanupResources();
          message.info('回复生成需要较长时间，请稍后查看', 3);
          saveMessagesToLocal();
        }, connectionTimeout);

        // 添加beforeunload事件监听器，确保页面关闭时清理资源
        const handleBeforeUnload = () => {
          safeCloseConnection();
          cleanupResources();
        };
        window.addEventListener('beforeunload', handleBeforeUnload);

        eventSource.onopen = () => {
          console.log('EventSource connection opened');
        };

        eventSource.onmessage = async (event) => {
          try {
            // 收到消息时清除初始等待提示定时器
            clearTimeout(messageTimer);
            // 不在这里直接关闭所有提示消息，因为可能会关闭诊断生成提示
            // message.destroy(); // 这行会关闭所有消息提示
            
            console.log('收到原始消息:', event.data);

            if (event.data === '[DONE]') {
              console.log('对话完成');
              safeCloseConnection();
              cleanupResources();
              window.removeEventListener('beforeunload', handleBeforeUnload);
              
              if (doctorMessageObj.message === '正在思考中...' || 
                  doctorMessageObj.message === '消息正在生成诊断结果，请稍等...') {
                doctorMessageObj.message = '抱歉，我需要更多时间思考，请稍后再试';
              }
              
              // 由于音频文件在播放后会被删除，切换会话时不需要播放音频
              // 只有在当前会话中收到新消息时才播放音频
              if (doctorMessageObj.audioUrl && !isSwitchingSession.value) {
                await playAudio(doctorMessageObj.audioUrl);
              }
              
              saveMessagesToLocal();
              return;
            }

            // 尝试解析消息
            const data = JSON.parse(event.data);
            console.log('解析后的消息数据:', data);

            if (data.content) {
              clearTimeout(messageTimer);
              if (doctorMessageObj.message === '正在思考中...' || 
                  doctorMessageObj.message === '消息正在生成诊断结果，请稍等...') {
                doctorMessageObj.message = '';
              }
              doctorMessageObj.message += data.content;
              if (data.audioUrl) {
                doctorMessageObj.audioUrl = data.audioUrl;
                doctorMessageObj.isPlaying = false;
              }

              // 检查是否包含solution = 'True'
              if (data.content.includes('solution = \'True\'')) {
                console.log('检测到solution = True，准备发送诊断请求');
                
                // 提取solution_text（solution = 'True'之前的内容）
                const solutionMatch = doctorMessageObj.message.split('solution = \'True\'')[0];
                if (solutionMatch) {
                  const solutionText = solutionMatch.trim();
                  console.log('提取的solution_text:', solutionText);
                  
                  // 更新消息内容，删除solution = 'True'部分
                  doctorMessageObj.message = doctorMessageObj.message.replace(/\s*solution = 'True'$/, '');
                  
                  // 立即显示正在生成诊断结果的提示，并确保返回关闭函数
                  const loadingMessage = message.loading('正在生成诊断结果，请稍后...', 0);
                  console.log('显示诊断结果生成提示');
                  
                  // 更新UI后再发送诊断请求
                  nextTick(() => {
                    // 创建新的EventSource连接获取诊断结果
                    const diagnoseEventSource = new EventSource(
                      `/message/diagnose?userId=${userInfo.value.userId}&sessionId=${currentSessionId.value}&solutionText=`
                    );

                    diagnoseEventSource.onmessage = async (diagnoseEvent) => {
                      try {
                        if (diagnoseEvent.data === '[DONE]') {
                          diagnoseEventSource.close();
                          // 确保关闭诊断结果生成提示
                          loadingMessage();
                          return;
                        }

                        // 收到第一个诊断响应时关闭提示
                        loadingMessage();
                        console.log('关闭诊断结果生成提示');

                        const diagnoseData = JSON.parse(diagnoseEvent.data);
                        if (diagnoseData.content) {
                          // 创建新的医生消息对象来显示诊断结果
                          const diagnoseMessageObj = {
                            id: Date.now(),
                            sessionId: currentSessionId.value,
                            speaker: 'doctor',
                            message: diagnoseData.content,
                            createdAt: new Date().toISOString()
                          };

                          if (diagnoseData.audioUrl) {
                            diagnoseMessageObj.audioUrl = diagnoseData.audioUrl;
                            diagnoseMessageObj.isPlaying = false;
                            
                            // 检查是否有正在播放的音频
                            if (currentAudio) {
                              // 给当前音频添加结束事件，以便在其播放完毕后播放诊断音频
                              const playDiagnoseAudio = async () => {
                                // 移除事件监听器，避免重复触发
                                currentAudio.removeEventListener('ended', playDiagnoseAudio);
                                // 等待一小段时间再播放下一个音频
                                setTimeout(async () => {
                                  await playAudio(diagnoseData.audioUrl);
                                }, 1000);
                              };
                              
                              // 监听当前音频的结束事件
                              currentAudio.addEventListener('ended', playDiagnoseAudio);
                              console.log('已设置诊断音频在前一个音频播放完成后播放');
                            } else {
                              // 如果没有正在播放的音频，直接播放诊断音频
                              setTimeout(async () => {
                                await playAudio(diagnoseData.audioUrl);
                              }, 1000);
                            }
                          }

                          // 将诊断消息添加到会话中
                          sessionMessages[currentSessionId.value].push(diagnoseMessageObj);
                          saveMessagesToLocal();
                          nextTick(() => {
                            scrollToBottom();
                          });
                        }
                      } catch (error) {
                        console.error('处理诊断响应时出错:', error);
                        // 出错时也关闭提示
                        loadingMessage();
                      }
                    };

                    diagnoseEventSource.onerror = (error) => {
                      console.error('诊断请求连接错误:', error);
                      // 出错时关闭提示
                      loadingMessage();
                      diagnoseEventSource.close();
                    };
                  });
                }
              }

              saveMessagesToLocal();
              nextTick(() => {
                scrollToBottom();
              });
            }
          } catch (error) {
            console.error('消息处理错误:', error);
          }
        };

        eventSource.onerror = (error) => {
          clearTimeout(messageTimer);
          
          // 关闭等待提示
          message.destroy();
          
          // 连接错误，不再重试
          safeCloseConnection();
          cleanupResources();
          window.removeEventListener('beforeunload', handleBeforeUnload);
          
          if (doctorMessageObj.message === '正在思考中...' || 
              doctorMessageObj.message === '消息正在生成诊断结果，请稍等...') {
            doctorMessageObj.message = '回复生成中...\n\n可能需要较长时间，请稍后查看或创建新会话。';
          }
          
          message.info('连接暂时不可用，您的消息已保存', 3);
          saveMessagesToLocal();
        };

        return {
          eventSource,
          close: () => {
            safeCloseConnection();
            cleanupResources();
            // 确保关闭所有等待提示
            message.destroy();
            window.removeEventListener('beforeunload', handleBeforeUnload);
          }
        };
      } catch (error) {
        console.error('创建EventSource连接失败:', error);
        cleanupResources();
        // 确保关闭所有等待提示
        message.destroy();
        message.info('连接服务器失败，请稍后重试', 3);
        return {
          close: () => {
            console.log('连接已关闭（从未建立）');
          }
        };
      }
    };

    // 修改 sendMessage 函数
    const sendMessage = async () => {
      const trimmedMessage = inputMessage.value.trim();
      if (!trimmedMessage || loading.value) return;
      if (!currentSessionId.value) {
        message.warning('请先选择或创建一个会话');
        return;
      }

      // 关闭所有提示消息
      message.destroy();

      // 如果存在之前的连接，先关闭它
      if (currentEventSource) {
        currentEventSource.close();
        currentEventSource = null;
      }

      try {
        loading.value = true;
        const userMessage = trimmedMessage;
        inputMessage.value = '';

        // 创建用户消息
        const userMessageObj = {
          id: Date.now(),
          sessionId: currentSessionId.value,
          speaker: 'patient',
          message: userMessage,
          createdAt: new Date().toISOString()
        };

        // 确保会话消息数组存在
        if (!sessionMessages[currentSessionId.value]) {
          sessionMessages[currentSessionId.value] = [];
        }

        // 添加用户消息
        sessionMessages[currentSessionId.value].push(userMessageObj);
        console.log('添加用户消息:', userMessageObj);

        // 创建医生消息占位
        const doctorMessageObj = {
          id: Date.now() + 1,
          sessionId: currentSessionId.value,
          speaker: 'doctor',
          message: '正在思考中...',
          createdAt: new Date().toISOString()
        };
        sessionMessages[currentSessionId.value].push(doctorMessageObj);
        console.log('添加医生消息占位:', doctorMessageObj);

        // 保存到本地存储
        saveMessagesToLocal();
        console.log('当前所有会话消息:', sessionMessages);

        // 滚动到底部
        await nextTick();
        scrollToBottom();

        // 创建 EventSource 连接
        currentEventSource = createEventSource(userMessage, doctorMessageObj);

      } catch (error) {
        console.error('Error in sendMessage:', error);
        loading.value = false;
      }
    };

    // 修改 scrollToBottom 函数
    const scrollToBottom = () => {
      if (messageContainer.value) {
        const container = messageContainer.value
        container.scrollTo({
          top: container.scrollHeight,
          behavior: 'smooth'
        })
      }
    }

    // 监听消息容器变化
    const observeMessages = () => {
      if (messageContainer.value) {
        // 如果已存在观察器，先断开连接
        if (messageObserver) {
          messageObserver.disconnect()
        }
        
        // 创建新的观察器
        messageObserver = new MutationObserver(scrollToBottom)
        messageObserver.observe(messageContainer.value, {
          childList: true,
          subtree: true,
          characterData: true
        })
      }
    }

    // 监听回车键
    const handleKeyPress = (e) => {
      if (e.key === 'Enter' && !e.shiftKey && !loading.value) {
        e.preventDefault()
        sendMessage()
      }
    }

    const toggleCollapse = () => {
      isCollapsed.value = !isCollapsed.value
    }

    // 处理头像显示
    const getAvatarSrc = (profilePicture) => {
      // 如果没有头像，返回默认头像
      if (!profilePicture) {
        return 'https://api.dicebear.com/7.x/avataaars/svg?seed=' + (userInfo.value?.username || 'default')
      }
      
      // 检查是否为 base64 数据
      if (profilePicture.startsWith('data:image')) {
        return profilePicture
      }
      
      // 检查是否为 URL
      if (profilePicture.startsWith('http')) {
        return profilePicture
      }
      
      // 如果是纯 base64 数据，添加前缀
      return `data:image/jpeg;base64,${profilePicture}`
    }

    const toggleVoice = async () => {
      if (!isRecording.value) {
        try {
          const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
          
          // 设置录音选项，使用WAV格式并指定比特率
          const options = {
            mimeType: 'audio/webm',
            audioBitsPerSecond: 16000
          }
          
          // 尝试创建MediaRecorder
          try {
            mediaRecorder.value = new MediaRecorder(stream, options)
          } catch (e) {
            console.warn('指定的MIME类型不受支持，使用默认类型', e)
            mediaRecorder.value = new MediaRecorder(stream)
          }
          
          audioChunks.value = []

          mediaRecorder.value.ondataavailable = (event) => {
            audioChunks.value.push(event.data)
          }

          mediaRecorder.value.onstop = async () => {
            // 创建Blob对象
            const audioBlob = new Blob(audioChunks.value, { type: mediaRecorder.value.mimeType })
            console.log('录音完成，MIME类型:', mediaRecorder.value.mimeType)
            
            // 创建FormData对象
            const formData = new FormData()
            let extension = 'webm'
            if (audioBlob.type.includes('wav')) {
              extension = 'wav'
            } else if (audioBlob.type.includes('mp3')) {
              extension = 'mp3'
            } else if (audioBlob.type.includes('ogg')) {
              extension = 'ogg'
            }
            
            formData.append('audio', audioBlob, `recorded_audio.${extension}`)
            
            try {
              // 显示加载提示，但不使用message.loading，避免超时后自动关闭
              const loadingMsg = message.loading('正在识别语音...', 0)
              
              // 上传音频文件
              const response = await fetch('/api/asr/recognize', {
                method: 'POST',
                body: formData,
                // 增加超时时间
                timeout: 60000 // 60秒超时
              })
              
              if (!response.ok) {
                loadingMsg() // 关闭加载提示
                throw new Error('上传失败，服务器返回: ' + response.status)
              }
              
              const audioId = await response.text()
              console.log('获取到音频ID:', audioId)
              
              // 创建SSE连接接收识别结果
              const eventSource = new EventSource(`/api/asr/recognize?audioId=${audioId}`, {
                // 添加withCredentials选项，可能有助于解决某些跨域问题
                withCredentials: true
              })
              
              // 设置超时关闭连接，增加到120秒
              const timeout = setTimeout(() => {
                console.warn('识别超时，关闭连接')
                try {
                  eventSource.close()
                } catch (e) {
                  console.error('关闭EventSource时出错:', e)
                }
                loadingMsg() // 关闭加载提示
                // 不显示任何提示，避免用户困扰
                // message.info('语音识别超时，请重试')
              }, 120000) // 增加到120秒
              
              eventSource.onopen = () => {
                console.log('SSE连接已打开')
              }
              
              eventSource.addEventListener('result', (e) => {
                console.log('收到识别结果:', e.data)
                clearTimeout(timeout)
                loadingMsg() // 关闭加载提示
                inputMessage.value = e.data
              })
              
              eventSource.addEventListener('error', (err) => {
                
                // 清理资源
                clearTimeout(timeout)
                loadingMsg() // 关闭加载提示
                
                // 安全关闭连接
                try {
                  eventSource.close()
                } catch (e) {
                  console.error('关闭EventSource时出错:', e)
                }
                
                // 完全不显示错误消息，即使没有识别结果
                // 如果需要，可以在这里添加静默重试逻辑
              })
              
            } catch (error) {
              console.error('上传失败:', error)
              // 使用info而非error
              message.info('语音识别未成功，请重试')
            }
          }

          mediaRecorder.value.start()
          isRecording.value = true
          message.success('开始录音')
        } catch (error) {
          console.error('录音失败:', error)
          message.error('无法访问麦克风')
        }
      } else {
        mediaRecorder.value.stop()
        isRecording.value = false
        mediaRecorder.value.stream.getTracks().forEach(track => track.stop())
        message.success('录音结束')
      }
    }

    // 保存消息到本地存储
    const saveMessagesToLocal = () => {
      const localData = {
        userInfo: userInfo.value,
        sessions: sessions.value,
        sessionMessages: sessionMessages
      }
      localStorage.setItem('userInfo', JSON.stringify(localData))
    }

    // 加载本地存储的消息
    const loadMessagesFromLocal = () => {
      const storedInfo = localStorage.getItem('userInfo')
      if (storedInfo) {
        const info = JSON.parse(storedInfo)
        userInfo.value = info.userInfo
        userStore.setUserInfo(info.userInfo)
        sessions.value = info.sessions || []
        Object.entries(info.sessionMessages || {}).forEach(([sessionId, messages]) => {
          sessionMessages[sessionId] = messages
        })
      }
    }

    // 在组件卸载时清理
    onUnmounted(() => {
      // 断开观察器连接
      if (messageObserver) {
        messageObserver.disconnect();
      }
      
      // 关闭EventSource连接
      if (currentEventSource) {
        currentEventSource.close();
        currentEventSource = null;
      }
      
      // 停止正在播放的音频
      if (currentAudio) {
        currentAudio.pause();
        currentAudio.src = '';
        currentAudio = null;
      }
      
      // 清理所有定时器
      clearTimeout(messageTimer);
      
      // 保存最后的状态
      saveMessagesToLocal();
    })

    const playAudio = async (audioUrl) => {
      // 如果正在切换会话，不播放音频
      if (isSwitchingSession.value) {
        console.log('正在切换会话，跳过音频播放');
        return;
      }
      
      try {
        if (!audioUrl) {
          console.error('音频URL为空');
          // 不显示错误消息，避免用户困扰
          // message.error('无法播放音频：URL为空');
          return;
        }
        
        console.log('准备播放音频，原始URL:', audioUrl);
        const fullAudioUrl = `/audio/stream?audioUrl=${encodeURIComponent(audioUrl)}`;
        console.log('处理后的音频URL:', fullAudioUrl);
        
        // 如果有正在播放的音频，先停止它
        if (currentAudio) {
          currentAudio.pause();
          currentAudio.src = '';
          currentAudio = null;
        }
        
        const audio = new Audio();
        
        // 添加错误处理
        const errorHandler = (e) => {
          // 如果正在切换会话，不处理错误
          if (isSwitchingSession.value) {
            console.log('正在切换会话，忽略音频错误');
            return;
          }
          
          console.error('音频错误:', e);
          if (audio.error) {
            console.error('错误详情:', audio.error);
            
            // 如果是资源不可用错误，可能是音频还没准备好，不显示错误消息
            if (audio.error.code === 4) {
              console.warn('音频资源暂时不可用，可能是延迟到达');
              
              // 设置重试机制
              setTimeout(() => {
                // 检查是否仍在同一会话中
                if (!isSwitchingSession.value && currentAudio === audio && audio.error && audio.error.code === 4) {
                  console.log('尝试重新加载音频...');
                  audio.load();
                  audio.play().catch(err => console.error('重试播放失败:', err));
                } else {
                  console.log('会话已切换或音频已更改，取消重试');
                }
              }, 5555); // 5秒后重试
              
              return;
            }
            
            // 根据错误代码提供更具体的错误信息
            // eslint-disable-next-line no-unused-vars
            let errorMessage = '音频加载失败';
            switch(audio.error.code) {
              case 1:
                errorMessage += ': 获取过程被用户终止';
                break;
              case 2:
                errorMessage += ': 网络错误';
                break;
              case 3:
                errorMessage += ': 解码错误';
                break;
              case 4:
                errorMessage += ': 音频资源不可用';
                break;
            }
            
            message.error(errorMessage);
          }
        };
        
        // 设置音频属性和事件监听
        audio.addEventListener('loadstart', () => {
          console.log('开始加载音频');
          // 不显示加载消息，避免用户困扰
          // message.loading('正在加载语音...');
        });
        
        audio.addEventListener('canplay', () => {
          console.log('音频可以开始播放');
          message.success('开始播放语音');
        });
        
        audio.addEventListener('ended', () => {
          console.log('音频播放结束');
          // 播放结束后清理资源
          currentAudio = null;
        });
        
        audio.addEventListener('error', errorHandler, true);
        
        // 设置超时处理，延长超时时间以适应音频延迟
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('音频加载超时')), 30000); // 增加到30秒
        });
        
        // 设置音频属性
        audio.src = fullAudioUrl;
        audio.volume = 1.0;
        audio.preload = 'auto';
        currentAudio = audio;
        
        // 尝试播放，并设置超时处理
        try {
          await Promise.race([
            audio.play(),
            timeoutPromise
          ]);
        } catch (playError) {
          // 如果正在切换会话，不处理错误
          if (isSwitchingSession.value) {
            console.log('正在切换会话，忽略播放错误');
            return;
          }
          
          console.error('播放失败:', playError);
          
          if (playError.message === '音频加载超时') {
            console.warn('音频加载超时，可能是延迟到达，尝试重新加载');
            
            // 尝试重新加载音频
            setTimeout(async () => {
              try {
                // 检查是否仍在同一会话中
                if (!isSwitchingSession.value && currentAudio === audio) {
                  currentAudio.load();
                  await currentAudio.play();
                } else {
                  console.log('会话已切换或音频已更改，取消重试');
                }
              } catch (retryError) {
                console.error('重试播放失败:', retryError);
              }
            }, 5555); // 5秒后重试
            
            return;
          }
          
          // 尝试静音播放（解决一些浏览器的自动播放限制）
          try {
            audio.muted = true;
            await audio.play();
            audio.muted = false;
          } catch (mutedPlayError) {
            console.error('静音播放也失败:', mutedPlayError);
            // 不显示错误消息，避免用户困扰
            // message.error('浏览器阻止了音频播放');
          }
        }
      } catch (error) {
        console.error('音频处理失败:', error);
        // 不显示错误消息，避免用户困扰
        // message.error('音频处理失败: ' + error.message);
      }
    };

    // 在setup函数开始处添加
    // 全局错误处理
    window.addEventListener('error', (event) => {
      // 过滤掉SSE相关错误，不显示给用户
      if (event.message && (
        event.message.includes('EventSource') || 
        event.message.includes('SSE') ||
        event.message.includes('ping') ||
        event.message.includes('pong')
      )) {
        console.warn('捕获到SSE相关错误，已忽略:', event.message)
        // 阻止默认错误处理
        event.preventDefault()
        return true
      }
    })

    // 也可以考虑添加unhandledrejection处理
    window.addEventListener('unhandledrejection', (event) => {
      // 过滤掉SSE相关错误
      if (event.reason && (
        (event.reason.message && (
          event.reason.message.includes('EventSource') || 
          event.reason.message.includes('SSE') ||
          event.reason.message.includes('ping') ||
          event.reason.message.includes('pong')
        )) ||
        (typeof event.reason === 'string' && (
          event.reason.includes('EventSource') ||
          event.reason.includes('SSE') ||
          event.reason.includes('ping') ||
          event.reason.includes('pong')
        ))
      )) {
        console.warn('捕获到SSE相关Promise拒绝，已忽略:', event.reason)
        // 阻止默认错误处理
        event.preventDefault()
        return true
      }
    })

    return {
      loading,
      userInfo,
      sessions,
      currentSessionId,
      currentMessages,
      inputMessage,
      messageContainer,
      formatTime,
      formatMessage,
      switchSession,
      createNewSession,
      sendMessage,
      isCollapsed,
      toggleCollapse,
      getAvatarSrc,
      handleKeyPress,
      router,
      doctorLogo,
      observeMessages,
      isRecording,
      toggleVoice,
      chatInputRef,
    }
  }
}
</script>

<style scoped>
.chat-container {
  height: calc(100vh - 80px); /* 减去菜单栏的高度 */
  background-color: #f8fafc;
  overflow: hidden;
  position: fixed;
  top: 100px; /* 菜单栏的高度 */
  left: 0;
  right: 0;
  bottom: 0;
}

.main-content {
  display: flex;
  height: 100%;
  width: 100%;
}

.session-list {
  width: 280px;
  min-width: 280px;
  background-color: #fff;
  border-right: none;
  display: flex;
  flex-direction: column;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 4px 0 12px rgba(0, 0, 0, 0.03);
}

.session-list.collapsed {
  width: 80px;
  min-width: 80px;
}

.session-header {
  padding: 16px;
  border-bottom: none;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 0;
}

.session-list.collapsed .user-info {
  justify-content: center;
}

:deep(.ant-avatar) {
  width: 40px;
  height: 40px;
  border: 2px solid rgba(77, 107, 254, 0.1);
  transition: all 0.3s ease;
  border-radius: 8px;
  margin-top: 8px;
}

:deep(.ant-avatar:hover) {
  transform: scale(1.05);
  border-color: rgba(77, 107, 254, 0.3);
}

.session-list.collapsed .session-item {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px 8px;
}

.session-list.collapsed .session-number {
  font-size: 14px;
  font-weight: 500;
  color: #4D6BFE;
}

.session-items {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

.session-item {
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 12px;
  margin: 4px 8px;
  background: transparent;
  position: relative;
}

.session-item:hover {
  background-color: rgba(77, 107, 254, 0.08);
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(77, 107, 254, 0.05);
}

.session-item.active {
  background-color: rgba(77, 107, 254, 0.12);
  box-shadow: 0 2px 8px rgba(77, 107, 254, 0.08);
}

.session-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0;
  transition: all 0.3s ease;
}

.session-title {
  font-weight: 600;
  letter-spacing: 0.2px;
  color: #333;
  transition: color 0.3s ease;
}

.session-item:hover .session-title {
  color: #4D6BFE;
}

.session-time {
  font-size: 12px;
  color: #999;
  font-weight: 500;
  transition: color 0.3s ease;
}

.session-item:hover .session-time {
  color: rgba(77, 107, 254, 0.7);
}

.session-number {
  font-size: 14px;
  font-weight: 600;
  color: #4D6BFE;
}

.collapse-button {
  position: absolute;
  right: -20px;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #4D6BFE, #3D5BEE);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(77, 107, 254, 0.2);
}

.collapse-button:hover {
  background: linear-gradient(135deg, #3D5BEE, #2D4BDE);
  box-shadow: 0 4px 12px rgba(77, 107, 254, 0.3);
  transform: translateY(-50%) scale(1.05);
}

.collapse-button svg {
  width: 24px;
  height: 24px;
  color: #fff;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: rotate(0deg);
}

.session-list.collapsed .collapse-button svg {
  transform: rotate(180deg);
}

.session-list.collapsed + .chat-main {
  width: calc(100% - 80px);
}

.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  transition: all 0.3s ease;
  height: 100%;
  overflow: hidden;
}

.chat-title {
  padding: 16px;
  background-color: #fff;
  border-bottom: none;
  font-weight: 600;
  letter-spacing: 0.3px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
}

.chat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  margin: 16px;
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  position: relative;
  height: calc(100% - 32px);
}

.chat-messages {
  flex: 1;
  padding: 16px 24px;
  padding-bottom: 160px;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  gap: 16px;
  position: relative;
}

.chat-messages::after {
  display: none;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
  position: absolute;
  bottom: 120px;
  left: 0;
  right: 0;
  background: transparent;
}

.loading-dots {
  display: flex;
  gap: 8px;
}

.loading-dots span {
  width: 8px;
  height: 8px;
  background-color: #4D6BFE;
  border-radius: 50%;
  display: inline-block;
  animation: bounce 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes bounce {
  0%, 80%, 100% { 
    transform: scale(0);
  } 
  40% { 
    transform: scale(1.0);
  }
}

.chat-input-container {
  height: 120px;
  background: #fff;
  border-radius: 0 0 24px 24px;
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.05);
}

.input-wrapper {
  height: 100%;
  padding: 20px;
  position: relative;
  background: #fff;
  border-radius: 0 0 24px 24px;
}

.chat-input {
  width: 100%;
  height: 80px;
  border: 2px solid #4D6BFE;
  resize: none;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  background-color: #fff;
  transition: all 0.3s ease;
  line-height: 1.6;
  border-radius: 16px;
  letter-spacing: 0.2px;
  box-shadow: inset 0 1px 3px rgba(77, 107, 254, 0.1);
}

.chat-input:focus {
  border-color: #4D6BFE;
  box-shadow: 0 0 0 3px rgba(77, 107, 254, 0.2);
  outline: none;
}

.chat-input:hover {
  border-color: #4D6BFE;
  box-shadow: 0 0 0 3px rgba(77, 107, 254, 0.1);
}

.button-group {
  position: absolute;
  right: 36px;
  bottom: 32px;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 0px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.voice-button {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 30px;
  background: linear-gradient(135deg, #f5f5f5, #e8e8e8);
  cursor: pointer;
  transition: all 0.3s ease;
}

.voice-button:hover {
  background: linear-gradient(135deg, #e8e8e8, #ddd);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.voice-button.recording {
  background: linear-gradient(135deg, #4D6BFE, #3D5BEE);
  animation: pulse 2s infinite;
}

.voice-button.recording:hover {
  background: linear-gradient(135deg, #3D5BEE, #2D4BDE);
  box-shadow: 0 4px 12px rgba(77, 107, 254, 0.3);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(77, 107, 254, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(77, 107, 254, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(77, 107, 254, 0);
  }
}

.voice-button img {
  width: 45px;
  height: 45px;
  transition: all 0.3s ease;
  filter: brightness(0.4);
}

.voice-button.recording img {
  filter: brightness(0) invert(1);
}

.voice-button:hover img {
  transform: scale(1.05);
}

.send-button {
  width: 60px;
  height: 60px;
  padding: 0;
  border: none;
  background: linear-gradient(135deg, #4D6BFE, #3D5BEE) !important;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
  border-radius: 30px;
  transition: all 0.3s ease;
}

.send-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #3D5BEE, #2D4BDE) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(77, 107, 254, 0.3);
}

.send-icon {
  width: 24px;
  height: 24px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-icon::before {
  content: '';
  width: 20px;
  height: 3px;
  background: #fff;
  position: absolute;
  transform: rotate(45deg);
  transform-origin: right center;
  right: 0;
  transition: all 0.3s ease;
}

.send-icon::after {
  content: '';
  width: 20px;
  height: 3px;
  background: #fff;
  position: absolute;
  transform: rotate(-45deg);
  transform-origin: right center;
  right: 0;
  transition: all 0.3s ease;
}

.send-button:disabled {
  background: #f0f0f0 !important;
  cursor: not-allowed;
}

.send-button:disabled .send-icon::before,
.send-button:disabled .send-icon::after {
  background: #ccc;
}

.message-item {
  display: flex;
  gap: 16px;
  padding: 12px 20px;
  position: relative;
  margin: 8px 0;
  border-radius: 16px;
  transition: background-color 0.3s ease;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
}

.message-item:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.message-item.patient {
  flex-direction: row-reverse;
  justify-content: flex-start;
}

.message-content {
  padding: 12px 16px;
  max-width: 80%;
  word-break: break-word;
  font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
  font-size: 15px;
  font-weight: normal;
  letter-spacing: 0.2px;
  line-height: 1.8;
  color: #333;
  background: rgba(245, 245, 245, 0.8);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border-radius: 12px;
  text-align: left;
}

.message-item.doctor .message-content {
  background: rgba(245, 245, 245, 0.8);
  color: #333;
  text-align: left;
  margin-right: auto;
}

.message-item.patient .message-content {
  background: rgba(230, 240, 255, 0.8);
  color: #333;
  text-align: left;
  margin-left: auto;
}

:deep(.ant-avatar) {
  width: 40px;
  height: 40px;
  border: 2px solid rgba(77, 107, 254, 0.1);
  transition: all 0.3s ease;
  border-radius: 8px;
  margin-top: 8px;
}

.message-item.patient :deep(.ant-avatar) {
  margin-right: 0;
  margin-left: 8px;
}

.message-item.doctor :deep(.ant-avatar) {
  margin-right: 8px;
  margin-left: 0;
}

.no-session {
  text-align: center;
  color: #999;
  margin-top: 40px;
  font-weight: 500;
  letter-spacing: 0.3px;
}

.message-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.message {
  display: flex;
  gap: 12px;
  max-width: 80%;
}

.message.user {
  flex-direction: row-reverse;
  margin-left: auto;
}

.message .avatar {
  width: 40px;
  height: 40px;
  flex-shrink: 0;
}

.message .avatar img {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

.message-content {
  background: #f0f2f5;
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
}

.message.user .message-content {
  background: #e3f2fd;
}

.message.doctor .message-content {
  background: #f5f5f5;
}

.input-area {
  border-top: 1px solid #e0e0e0;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.input-area textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  resize: none;
  font-size: 14px;
  line-height: 1.5;
}

.button-group {
  display: flex;
  justify-content: flex-end;
}

.button-group button {
  padding: 8px 24px;
  background: #1976d2;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
}

.button-group button:hover {
  background: #1565c0;
}

.button-group button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.new-session-wrapper {
  padding: 16px 0;
  display: flex;
  justify-content: center;
  border-bottom: none;
  margin-bottom: 8px;
}

.new-session-button {
  width: 90%;
  height: 40px;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background: linear-gradient(135deg, #4D6BFE, #3D5BEE);
  border-radius: 20px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
}

.new-session-button:hover {
  background: linear-gradient(135deg, #3D5BEE, #2D4BDE);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(77, 107, 254, 0.3);
}

.plus-icon {
  font-size: 20px;
  font-weight: bold;
  line-height: 1;
}

.username {
  font-weight: 600;
  letter-spacing: 0.3px;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.audio-controls,
.play-button {
  display: none;
}
</style> 