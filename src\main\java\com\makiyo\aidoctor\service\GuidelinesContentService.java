package com.makiyo.aidoctor.service;

import com.makiyo.aidoctor.entity.GuidelinesContent;
import com.makiyo.aidoctor.mapper.GuidelinesContentMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 指南内容服务类
 */
@Service
public class GuidelinesContentService {

    private static final Logger log = LoggerFactory.getLogger(GuidelinesContentService.class);

    @Resource
    private MongoTemplate mongoTemplate;

    /**
     * 保存指南内容
     *
     * @param sessionId 会话ID
     * @param content 指南内容
     * @return 保存后的引用ID
     */
    public String saveContent(Integer sessionId, Object content) {
        try {
            // 创建指南内容对象
            GuidelinesContent guidelines = new GuidelinesContent();
            guidelines.setId(UUID.randomUUID().toString());
            guidelines.setSessionId(sessionId);
            guidelines.setContent(content);
            guidelines.setCreatedAt(LocalDateTime.now());

            // 使用MongoTemplate直接保存到MongoDB
            GuidelinesContent saved = mongoTemplate.save(guidelines, "guidelines_content");

            log.info("成功保存指南内容，会话ID: {}, 引用ID: {}", sessionId, saved.getId());
            return saved.getId();
        } catch (Exception e) {
            log.error("保存指南内容失败，会话ID: {}, 错误: {}", sessionId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据ID获取指南内容
     *
     * @param id 指南内容引用ID
     * @return 指南内容对象
     */
    public Object getContentById(String id) {
        try {
            GuidelinesContent guidelines = mongoTemplate.findById(id, GuidelinesContent.class, "guidelines_content");
            if (guidelines != null) {
                return guidelines.getContent();
            } else {
                log.warn("未找到ID为{}的指南内容", id);
                return null;
            }
        } catch (Exception e) {
            log.error("获取指南内容失败，ID: {}, 错误: {}", id, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据会话ID获取指南内容
     *
     * @param sessionId 会话ID
     * @return 指南内容对象
     */
    public Object getContentBySessionId(Integer sessionId) {
        if (sessionId == null) {
            log.warn("尝试使用null会话ID获取指南内容");
            return null;
        }
        
        try {
            // 创建查询条件
            org.springframework.data.mongodb.core.query.Query query =
                    new org.springframework.data.mongodb.core.query.Query(
                            org.springframework.data.mongodb.core.query.Criteria.where("sessionId").is(sessionId)
                    );
            
            // 按创建时间降序排序，确保获取最新的内容
            query.with(org.springframework.data.domain.Sort.by(
                    org.springframework.data.domain.Sort.Direction.DESC, "createdAt"));

            GuidelinesContent guidelines = mongoTemplate.findOne(
                    query, GuidelinesContent.class, "guidelines_content"
            );

            if (guidelines != null) {
                log.info("成功获取会话ID为{}的指南内容，内容ID: {}", sessionId, guidelines.getId());
                return guidelines.getContent();
            } else {
                log.warn("未找到会话ID为{}的指南内容", sessionId);
                return null;
            }
        } catch (Exception e) {
            log.error("获取指南内容失败，会话ID: {}, 错误类型: {}, 错误信息: {}", 
                    sessionId, e.getClass().getName(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据会话ID更新指南内容
     *
     * @param sessionId 会话ID
     * @param content 新的指南内容
     * @return 是否更新成功
     */
    public boolean updateContentBySessionId(Integer sessionId, Object content) {
        try {
            // 创建查询条件
            org.springframework.data.mongodb.core.query.Query query =
                    new org.springframework.data.mongodb.core.query.Query(
                            org.springframework.data.mongodb.core.query.Criteria.where("sessionId").is(sessionId)
                    );

            GuidelinesContent guidelines = mongoTemplate.findOne(
                    query, GuidelinesContent.class, "guidelines_content"
            );

            if (guidelines != null) {
                guidelines.setContent(content);
                guidelines.setCreatedAt(LocalDateTime.now());
                mongoTemplate.save(guidelines, "guidelines_content");
                log.info("成功更新会话ID为{}的指南内容", sessionId);
                return true;
            } else {
                // 如果不存在，则创建新记录
                saveContent(sessionId, content);
                return true;
            }
        } catch (Exception e) {
            log.error("更新指南内容失败，会话ID: {}, 错误: {}", sessionId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 删除指南内容
     *
     * @param id 指南内容引用ID
     * @return 是否删除成功
     */
    public boolean deleteContent(String id) {
        try {
            org.springframework.data.mongodb.core.query.Query query =
                    new org.springframework.data.mongodb.core.query.Query(
                            org.springframework.data.mongodb.core.query.Criteria.where("_id").is(id)
                    );

            mongoTemplate.remove(query, GuidelinesContent.class, "guidelines_content");
            log.info("成功删除ID为{}的指南内容", id);
            return true;
        } catch (Exception e) {
            log.error("删除指南内容失败，ID: {}, 错误: {}", id, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 根据会话ID删除指南内容
     *
     * @param sessionId 会话ID
     * @return 是否删除成功
     */
    public boolean deleteContentBySessionId(Integer sessionId) {
        try {
            org.springframework.data.mongodb.core.query.Query query =
                    new org.springframework.data.mongodb.core.query.Query(
                            org.springframework.data.mongodb.core.query.Criteria.where("sessionId").is(sessionId)
                    );

            mongoTemplate.remove(query, GuidelinesContent.class, "guidelines_content");
            log.info("成功删除会话ID为{}的指南内容", sessionId);
            return true;
        } catch (Exception e) {
            log.error("删除指南内容失败，会话ID: {}, 错误: {}", sessionId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 根据会话ID获取完整的指南内容对象（包含元数据）
     *
     * @param sessionId 会话ID
     * @return 完整的指南内容对象，包含ID、创建时间等元数据
     */
    public GuidelinesContent getFullContentBySessionId(Integer sessionId) {
        if (sessionId == null) {
            log.warn("尝试使用null会话ID获取完整指南内容对象");
            return null;
        }
        
        try {
            // 创建查询条件
            org.springframework.data.mongodb.core.query.Query query =
                    new org.springframework.data.mongodb.core.query.Query(
                            org.springframework.data.mongodb.core.query.Criteria.where("sessionId").is(sessionId)
                    );
            
            // 按创建时间降序排序，确保获取最新的内容
            query.with(org.springframework.data.domain.Sort.by(
                    org.springframework.data.domain.Sort.Direction.DESC, "createdAt"));

            GuidelinesContent guidelines = mongoTemplate.findOne(
                    query, GuidelinesContent.class, "guidelines_content"
            );

            if (guidelines != null) {
                log.info("成功获取会话ID为{}的完整指南内容对象，内容ID: {}", sessionId, guidelines.getId());
                return guidelines;
            } else {
                log.warn("未找到会话ID为{}的指南内容对象", sessionId);
                return null;
            }
        } catch (Exception e) {
            log.error("获取完整指南内容对象失败，会话ID: {}, 错误类型: {}, 错误信息: {}", 
                    sessionId, e.getClass().getName(), e.getMessage(), e);
            return null;
        }
    }
}