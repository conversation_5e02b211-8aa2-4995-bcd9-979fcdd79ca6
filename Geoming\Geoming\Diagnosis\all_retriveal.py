import json
import torch
from transformers import <PERSON><PERSON>oken<PERSON>, BertModel
from qdrant_client import QdrantClient
from qdrant_client.models import Distance

# 配置参数
model_path = '/root/autodl-tmp/bge-large-zh-v1___5'  # BERT 模型路径
collection_name = "med_all"  # Qdrant 数据库集合名称
dim = 1024  # 向量维度

# Qdrant 连接
qdrant_client = QdrantClient(
    url="https://4c32b9ec-36a6-4290-9dbe-7e455e14dc5b.eu-west-2-0.aws.cloud.qdrant.io:6333",
    api_key="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3MiOiJtIn0.g_G0ko_m5qFdwL6wvxZEcygPftfUak3qZRDEyBGMNbQ",
    timeout=600
)

# 加载 BERT 模型和 tokenizer
tokenizer = BertTokenizer.from_pretrained(model_path)
model = BertModel.from_pretrained(model_path)
model.eval()  # 设为评估模式，优化推理速度

# 文本 embedding 函数
def embed_text(text):
    """将输入文本转换为 BERT 向量"""
    inputs = tokenizer(text, return_tensors='pt', padding=True, truncation=True, max_length=512)
    with torch.no_grad():  # 关闭梯度计算
        outputs = model(**inputs)
    embeddings = outputs.last_hidden_state[:, 0, :].squeeze().numpy()  # 取 [CLS] 作为向量
    return embeddings

# 检索函数
def search_query(query_text, top_k=3):
    """在 Qdrant 数据库中进行向量检索"""
    # print(f"\n🔍 查询: {query_text}  |  结果数: {top_k}")
    query_vector = embed_text(query_text)

    # 进行相似搜索
    search_result = qdrant_client.search(
        collection_name=collection_name,
        query_vector=query_vector.tolist(),
        limit=top_k,  # 返回 top_k 个最相似结果
        with_payload=True  # 返回 payload 数据
    )

    # 处理并打印返回的结果
    if search_result:
        # print(f"\n 找到 {len(search_result)} 个相关结果：")
        for i, result in enumerate(search_result):
     
            print(json.dumps(result.payload, ensure_ascii=False, indent=4))
    else:
        print("\n 未找到相关结果，请尝试其他关键词。")

# 设置查询参数
query_text = "腹泻"  # 你想查询的内容
top_k = 3  # 返回的相似结果数量

# 运行检索
if __name__ == "__main__":
    search_query(query_text, top_k)
