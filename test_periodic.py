import requests
import json
import time

# 设置请求URL
url = 'http://localhost:5555/ai_doctor_v2_inquiry'

# 构造正确的JSON请求体
payload = {
    "interaction_history": {
        "diagnosis": "",
        "cot_entries": [
            {
                "feedback": "",
                "strategy": "询问患者的基本信息（性别、年龄）和主诉症状，了解发病时间、起病情况、症状特点（性质、程度、持续时间、频率）、伴随症状、病情演变以及之前的诊疗经过。",
                "reasoning": "主诉是问诊的切入点，通过了解患者的基本信息和主要症状，可以快速建立初步印象。进一步询问发病时间、起病情况、症状特点、伴随症状、病情演变和诊疗经过，有助于全面了解现病史，为后续诊断提供关键信息。这种系统性的问诊策略能够帮助我们准确把握患者的病情，避免遗漏重要信息。",
                "observation": "",
                "dialogue_history": [
                    {"role": "doctor", "content": "您好，我是本次的医生，请问孩子的年龄，性别，主要的症状是什么？"},
                    {"role": "patient", "content": "男，三岁，有点发烧"},
                    {"role": "doctor", "content": "孩子是从什么时候开始发烧的？"},
                    {"role": "patient", "content": "三天前开始发烧"},
                    {"role": "doctor", "content": "孩子发烧前有没有什么特别的事情，比如着凉、接触生病的人或者剧烈活动？"}
                ]
            }
        ],
        "test_recommendation": [],
        "preliminary_diagnosis": None,
        "treatment_recommendation": [],
        "doctor_supplementary_info": []
    }
}

# 设置请求头
headers = {
    'Content-Type': 'application/json; charset=utf-8'
}

# 定义测试参数
NUM_REQUESTS = 20  # 发送请求的总数
DELAY_SECONDS = 1  # 请求之间的间隔（秒）

print(f"开始周期性测试: 总共 {NUM_REQUESTS} 个请求, 每 {DELAY_SECONDS} 秒发送一次...\n")

success_count = 0
failure_count = 0

# 循环发送请求
for i in range(NUM_REQUESTS):
    print(f"发送第 {i+1}/{NUM_REQUESTS} 个请求...")
    
    try:
        start_time = time.time()
        
        # 使用json参数，让requests库正确处理编码
        response = requests.post(url, json=payload, headers=headers)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 打印响应信息
        print(f"  响应状态码: {response.status_code} (耗时: {duration:.2f}秒)")
        
        if response.status_code == 200:
            success_count += 1
            print("  状态: 成功")
        else:
            failure_count += 1
            print(f"  状态: 失败 - {response.text}")
    
    except Exception as e:
        failure_count += 1
        print(f"  状态: 错误 - {e}")
    
    # 显示进度
    print(f"  进度: {((i+1)/NUM_REQUESTS)*100:.1f}% 完成")
    print("  --------------------------------")
    
    # 等待指定的时间间隔（最后一个请求后不需要等待）
    if i < NUM_REQUESTS - 1:
        time.sleep(DELAY_SECONDS)

# 打印最终结果摘要
print("\n测试完成!")
print(f"成功: {success_count}/{NUM_REQUESTS} ({(success_count/NUM_REQUESTS)*100:.1f}%)")
print(f"失败: {failure_count}/{NUM_REQUESTS} ({(failure_count/NUM_REQUESTS)*100:.1f}%)") 