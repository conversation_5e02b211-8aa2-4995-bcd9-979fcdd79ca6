# Spring Boot参数验证架构说明

## 概述

本文档说明了项目中实现的参数验证架构，该架构基于Spring Boot的验证框架，用于替代手动验证逻辑，使代码更简洁、更统一、更易于维护。

## 架构组件

### 1. 验证注解

在DTO类和控制器方法参数上使用以下注解：

- `@NotNull`: 确保值不为null
- `@NotBlank`: 确保字符串值不为null且至少包含一个非空白字符
- `@NotEmpty`: 确保集合/数组/映射不为null且不为空
- `@Size`: 限制字符串长度或集合大小
- `@Pattern`: 使用正则表达式验证字符串
- `@Min`/`@Max`: 验证数值的最小/最大值

### 2. 关键类和配置

项目中添加了以下类和配置：

- **GlobalExceptionHandler**: 全局异常处理器，统一处理验证错误
- **ValidationConfig**: 参数验证配置，启用方法参数验证
- **@Validated注解**: 应用于控制器类，启用参数验证

## 使用示例

### DTO类验证

```java
public class SupplementaryInfoRequest {
    @NotBlank(message = "ID 不能为空")
    private String id;

    @NotBlank(message = "补充信息不能为空")
    private String supplementaryInfo;
    
    // getters and setters
}
```

### 控制器参数验证

```java
@RestController
@RequestMapping("/api")
@Validated // 必须添加此注解才能验证@RequestParam
public class ExampleController {

    @PostMapping("/endpoint")
    public ResponseEntity<?> endpoint(
        @RequestParam @NotBlank(message = "参数不能为空") String param,
        @Valid @RequestBody SomeRequest request
    ) {
        // 如果验证失败，不会执行到这里
        // 直接由GlobalExceptionHandler处理
        return ResponseEntity.ok("操作成功");
    }
}
```

## 验证错误处理

验证错误由 `GlobalExceptionHandler` 处理，提供统一格式的错误响应：

```json
{
  "success": false,
  "message": "请求参数验证失败",
  "errors": {
    "id": "ID 不能为空",
    "supplementaryInfo": "补充信息不能为空"
  }
}
```

## 验证类型

系统处理以下类型的验证错误：

1. **请求体验证** (`@RequestBody`): 由`MethodArgumentNotValidException`处理
2. **请求参数验证** (`@RequestParam`, `@PathVariable`): 由`ConstraintViolationException`处理
3. **表单数据验证**: 由`BindException`处理
4. **缺少必须参数**: 由`MissingServletRequestParameterException`处理

## 最佳实践

1. **使用明确的错误消息**: 错误消息应当明确指出问题并给出修复建议
2. **在DTO类中集中验证逻辑**: 尽量在DTO类中使用验证注解定义验证规则
3. **避免手动验证**: 不要混合使用框架验证和手动验证
4. **使用嵌套验证**: 对于复杂对象，使用`@Valid`注解验证嵌套对象
5. **考虑使用分组验证**: 对于不同操作场景(新增/更新)使用不同验证组

## 添加自定义验证

如需添加自定义验证规则，可以：

1. 创建自定义验证注解
2. 实现相应的验证器
3. 在需要验证的字段上应用自定义注解

## 参考文档

- [Spring Boot 验证文档](https://docs.spring.io/spring-boot/docs/current/reference/html/features.html#features.validation)
- [Bean Validation文档](https://beanvalidation.org/2.0/spec/)
- [Hibernate Validator文档](https://hibernate.org/validator/documentation/) 