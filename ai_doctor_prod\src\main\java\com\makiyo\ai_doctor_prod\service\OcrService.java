package com.makiyo.ai_doctor_prod.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.makiyo.ai_doctor_prod.entity.CaseItem;
import com.makiyo.ai_doctor_prod.entity.OcrResult;
import com.makiyo.ai_doctor_prod.mapper.CaseItemMapper;
import com.makiyo.ai_doctor_prod.repository.OcrResultRepository;
import com.makiyo.ai_doctor_prod.utils.OcrTextToJson; // 使用 prod 下的工具类
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.core.io.FileSystemResource;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;

@Service
public class OcrService {

    private static final Logger log = LoggerFactory.getLogger(OcrService.class);

    private final RestTemplate restTemplate;
    private final ImageService imageService;
    private final OcrResultRepository ocrResultRepository;

    @Resource private CaseItemMapper caseItemMapper;

    @Value("${ocr.python.prod.service.url:http://localhost:5555/ocr_api}")
    private String pythonProdOcrServiceUrl;

    // Python 基础信息接口 (multipart /ocr)
    @Value("${ocr.python.prod.baseinfo.url:http://localhost:5555/ocr}")
    private String pythonProdBaseinfoUrl;

    @Value("${ocr.image.prod.save.path:${user.dir}/images}")
    private String imageProdSavePath;
    
    // 重试配置
    @Value("${ocr.retry.max-attempts:3}")
    private int maxRetryAttempts;
    
    @Value("${ocr.retry.initial-delay:1000}")
    private int initialRetryDelay;
    
    @Value("${ocr.retry.multiplier:2}")
    private int retryMultiplier;

    // Helper class for OcrService history structures
    private static class OcrInteractionHistoryContainer {
        final Map<String, Object> topLevelHistoryMap;
        final Map<String, Object> innerHistoryMap;

        OcrInteractionHistoryContainer(Map<String, Object> topLevelHistoryMap, Map<String, Object> innerHistoryMap) {
            this.topLevelHistoryMap = topLevelHistoryMap;
            this.innerHistoryMap = innerHistoryMap;
        }
    }

    @Autowired
    public OcrService(RestTemplate restTemplate, ObjectMapper objectMapper, ImageService imageService, 
                     OcrResultRepository ocrResultRepository) {
        this.restTemplate = restTemplate;
        this.imageService = imageService;
        this.ocrResultRepository = ocrResultRepository;
    }

    @SuppressWarnings("unchecked")
    private OcrInteractionHistoryContainer getOrCreateInteractionHistoryMapsForOcr(String caseId, CaseItem caseItem, String contextLogPrefix) {
        // caseItem is passed in and assumed non-null by the caller (processOcrWithDbInteractionProd)
        Object historyDbObject = caseItem.getInteractionHistory();
        Map<String, Object> topLevelHistoryMap;

        if (historyDbObject instanceof Map) {
            try {
                topLevelHistoryMap = (Map<String, Object>) historyDbObject;
                log.debug("[{}] Retrieved existing interaction history object (Map) for case ID: {}.", contextLogPrefix, caseId);
            } catch (ClassCastException e) {
                log.warn("[{}] Interaction history is a Map but with unexpected generic types for case ID: {}. Initializing.", contextLogPrefix, caseId, e);
                topLevelHistoryMap = new HashMap<>();
            }
        } else if (historyDbObject == null) {
            log.warn("[{}] Interaction history is missing for case ID: {}. Initializing.", contextLogPrefix, caseId);
            topLevelHistoryMap = new HashMap<>();
        } else {
            log.warn("[{}] Interaction history is not a Map (Type: {}) for case ID: {}. Initializing.", contextLogPrefix, historyDbObject.getClass().getName(), caseId);
            topLevelHistoryMap = new HashMap<>();
        }

        Object innerHistoryObj = topLevelHistoryMap.computeIfAbsent("interaction_history", k -> new HashMap<>());
        if (!(innerHistoryObj instanceof Map)) {
            log.error("[{}] Inner 'interaction_history' is not a Map after computeIfAbsent for case ID: {}. Type: {}. Forcing new HashMap.", contextLogPrefix, caseId, innerHistoryObj != null ? innerHistoryObj.getClass().getName() : "null");
            innerHistoryObj = new HashMap<String, Object>();
            topLevelHistoryMap.put("interaction_history", innerHistoryObj); 
        }
        Map<String, Object> innerHistoryMap = (Map<String, Object>) innerHistoryObj;

        return new OcrInteractionHistoryContainer(topLevelHistoryMap, innerHistoryMap);
    }

    /**
     * 处理 OCR 请求 (生产环境)：保存图片、调用 Python 服务、直接返回 Python 服务的响应。
     *
     * @param image 上传的图片文件。
     * @return Python OCR 服务返回的原始 Map 结果，或包含错误信息的 Map。
     */
    public Map<String, Object> processOcrProd(MultipartFile image) {
        if (image == null || image.isEmpty()) {
            log.warn("[PROD OCR] Request did not include an image file.");
            return Collections.singletonMap("error", "需要提供图片文件。");
        }

        Path savedFilePath = null;
        Map<String, Object> result = null; // To store the result to be returned
        Map<String, Object> imageMetadata = null;
        String gridFsId = null;
        long startTime = System.currentTimeMillis();

        try {
            // 1. 将图片保存到本地临时目录
            savedFilePath = saveImageToDesignatedLocationProd(image);
            log.info("[PROD OCR] Image saved: {}", savedFilePath);
            
            // 2. 将图片存储到GridFS
            imageMetadata = imageService.storeImage(image, null, "OCR");
            gridFsId = (String) imageMetadata.get("gridFsId");
            log.info("[PROD OCR] Image stored in GridFS with ID: {}", gridFsId);

            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("image_path", savedFilePath.toAbsolutePath().toString());
            requestBody.put("image_id", gridFsId); // 添加GridFS ID供Python服务使用

            // 3. 调用带有重试功能的方法
            Map<String, Object> pythonResponseMap = callPythonOcrServiceWithRetry(requestBody, null, "PROD OCR");
            long processingTime = System.currentTimeMillis() - startTime;
            
            if (pythonResponseMap != null) {
                log.debug("[PROD OCR] Received OCR response. Response: {}", pythonResponseMap);
                
                // 4. 将OCR结果保存到MongoDB
                OcrResult ocrResult = new OcrResult();
                ocrResult.setImageId(gridFsId);
                ocrResult.setOriginalFilename(image.getOriginalFilename());
                ocrResult.setLocalFilePath(savedFilePath.toAbsolutePath().toString());
                ocrResult.setContent(pythonResponseMap);
                ocrResult.setMetadata(new HashMap<>(imageMetadata));
                ocrResult.setSuccess(!pythonResponseMap.containsKey("error"));
                ocrResult.setProcessingTimeMs(processingTime);
                
                OcrResult savedResult = ocrResultRepository.save(ocrResult);
                log.info("[PROD OCR] OCR result saved with ID: {}", savedResult.getId());
                
                // 5. 将结果ID添加到返回数据中
                result = new HashMap<>(pythonResponseMap);
                result.put("ocrResultId", savedResult.getId());
                result.put("imageId", gridFsId);
                result.put("processingTimeMs", processingTime);
            } else {
                log.error("[PROD OCR] Python OCR service returned null response.");
                Map<String, Object> errorMap = new HashMap<>();
                errorMap.put("error", "OCR 服务调用失败");
                errorMap.put("details", "Service returned empty response after retry attempts.");
                result = errorMap;
            }
            return result;

        } catch (IOException e) {
            log.error("[PROD OCR] Error processing uploaded image or saving file: {}", e.getMessage(), e);
            result = Collections.singletonMap("error", "处理上传图片时出错: " + e.getMessage());
            return result;
        } catch (Exception e) {
            log.error("[PROD OCR] Unexpected error during OCR processing: {}", e.getMessage(), e);
            result = Collections.singletonMap("error", "OCR 服务发生意外错误: " + e.getMessage());
            return result;
        } finally {
            if (savedFilePath != null) {
                try {
                    boolean deleted = Files.deleteIfExists(savedFilePath);
                    if (deleted) {
                        log.info("[PROD OCR] Successfully deleted temporary OCR image: {}", savedFilePath);
                    } else {
                        log.warn("[PROD OCR] Temporary OCR image could not be deleted (or was already gone): {}", savedFilePath);
                    }
                } catch (IOException ex) {
                    log.error("[PROD OCR] Error attempting to delete temporary OCR image {}: {}", savedFilePath, ex.getMessage(), ex);
                }
            }
        }
    }

    /**
     * 调用 python /ocr 接口，仅提取姓名/性别/年龄等基础信息，不做持久化。
     * @param image 上传图片
     * @return 识别结果 Map 或 error
     */
    public Map<String, Object> processBaseInfoProd(MultipartFile image) {
        final String logPrefix = "PROD BASEINFO";
        if (image == null || image.isEmpty()) {
            log.warn("[{}] Empty image in request", logPrefix);
            return Collections.singletonMap("error", "需要提供图片文件。");
        }

        Path savedFilePath = null;
        try {
            long start = System.currentTimeMillis();
            // 保存图片到临时目录，便于通过 FileSystemResource 上传
            savedFilePath = saveImageToDesignatedLocationProd(image);
            log.info("[{}] Image saved at {}", logPrefix, savedFilePath);

            // 构造 multipart 请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("image", new FileSystemResource(savedFilePath.toFile()));

            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            log.info("[{}] Calling python baseinfo URL: {}", logPrefix, pythonProdBaseinfoUrl);
            ResponseEntity<Map> responseEntity = restTemplate.postForEntity(pythonProdBaseinfoUrl, requestEntity, Map.class);

            long cost = System.currentTimeMillis() - start;
            log.info("[{}] Call finished, status {} in {} ms", logPrefix, responseEntity.getStatusCode(), cost);

            if (responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getBody() != null) {
                return responseEntity.getBody();
            }
            log.error("[{}] Python baseinfo service returned non-OK or empty body", logPrefix);
            return Collections.singletonMap("error", "OCR 服务调用失败");
        } catch (Exception e) {
            log.error("[{}] Exception during baseinfo OCR: {}", logPrefix, e.getMessage(), e);
            return Collections.singletonMap("error", "OCR 服务发生错误: " + e.getMessage());
        } finally {
            if (savedFilePath != null) {
                try {
                    Files.deleteIfExists(savedFilePath);
                } catch (IOException ioe) {
                    log.warn("[{}] Failed to delete temp file {}", logPrefix, savedFilePath);
                }
            }
        }
    }

    /**
     * 处理带数据库交互的 OCR 请求 (生产环境)。
     */
    @Transactional
    public Object processOcrWithDbInteractionProd(MultipartFile image, String id, String name) {
        final String logPrefix = "PROD OCR DB";
        if (image == null || image.isEmpty()) {
            log.warn("[{}] Request for case ID {} did not include an image file.", logPrefix, id);
            return Collections.singletonMap("error", "需要提供图片文件。");
        }
        if (id == null || id.trim().isEmpty()) {
            log.warn("[{}] Request is missing the case ID.", logPrefix);
            return Collections.singletonMap("error", "需要提供案例 ID。");
        }

        Path savedFilePath = null;
        Object originalOcrContent = null;
        Map<String, Object> imageMetadata = null;
        String gridFsId = null;
        long startTime = System.currentTimeMillis();

        try {
            // 1. 将图片保存到本地临时目录
            savedFilePath = saveImageToDesignatedLocationProd(image);
            log.info("[{}] Image saved for case ID {}: {}", logPrefix, id, savedFilePath);
            
            // 2. 将图片存储到GridFS
            imageMetadata = imageService.storeImage(image, id, "OCR");
            gridFsId = (String) imageMetadata.get("gridFsId");
            log.info("[{}] Image stored in GridFS with ID: {} for case ID: {}", logPrefix, gridFsId, id);

            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("image_path", savedFilePath.toAbsolutePath().toString());
            requestBody.put("image_id", gridFsId); // 添加GridFS ID供Python服务使用
            if (name != null) {
                requestBody.put("name", name);
            }

            // 3. 调用带重试的服务
            String responseBody = callPythonOcrTextServiceWithRetry(requestBody, id, logPrefix);
            long processingTime = System.currentTimeMillis() - startTime;
            
            if (responseBody != null) {
                log.debug("[{}] Received OCR response for case ID {} (first 500 chars): {}", logPrefix, id, responseBody.substring(0, Math.min(responseBody.length(), 500)));

                Map<String, Object> parsedOcrMap = OcrTextToJson.parseJsonToMap(responseBody);
                if (parsedOcrMap.isEmpty()) {
                     log.error("[{}] Parsing OCR response failed or response was empty for case ID {}.", logPrefix, id);
                     return Collections.singletonMap("error", "解析 OCR 响应失败。" + (responseBody.length() < 200 ? " Body: " + responseBody : ""));
                }
                
                // 4. 保存OCR结果到MongoDB
                OcrResult ocrResult = new OcrResult();
                ocrResult.setCaseId(id);
                ocrResult.setImageId(gridFsId);
                ocrResult.setOriginalFilename(image.getOriginalFilename());
                ocrResult.setLocalFilePath(savedFilePath.toAbsolutePath().toString());
                ocrResult.setContent(parsedOcrMap);
                ocrResult.setMetadata(new HashMap<>(imageMetadata));
                ocrResult.setSuccess(!parsedOcrMap.containsKey("error") && 
                                   !(parsedOcrMap.containsKey("content") && "识别失败".equals(parsedOcrMap.get("content"))));
                ocrResult.setProcessingTimeMs(processingTime);
                
                OcrResult savedResult = ocrResultRepository.save(ocrResult);
                log.info("[{}] OCR result saved with ID: {} for case ID: {}", logPrefix, savedResult.getId(), id);

                if (parsedOcrMap.containsKey("error")) {
                    log.warn("[{}] OCR service returned error for case ID {}: {}", logPrefix, id, parsedOcrMap.get("error"));
                    parsedOcrMap.put("ocrResultId", savedResult.getId());
                    parsedOcrMap.put("imageId", gridFsId);
                    return parsedOcrMap;
                }
                if (parsedOcrMap.containsKey("content") && ("识别失败".equals(parsedOcrMap.get("content")))) {
                     log.warn("[{}] OCR service returned specific failure content for case ID {}: {}", logPrefix, id, parsedOcrMap.get("content"));
                     parsedOcrMap.put("ocrResultId", savedResult.getId());
                     parsedOcrMap.put("imageId", gridFsId);
                     return parsedOcrMap;
                }

                originalOcrContent = parsedOcrMap.get("content");
                if (originalOcrContent == null) {
                    log.warn("[{}] OCR response parsed successfully, but is missing 'content' field for case ID {}.", logPrefix, id);
                    parsedOcrMap.put("ocrResultId", savedResult.getId());
                    parsedOcrMap.put("imageId", gridFsId);
                    return Collections.singletonMap("error", "OCR 响应缺少 'content' 字段。");
                }

                List<Map<String, Object>> testResults = extractTestResultsProd(originalOcrContent);

                if (testResults != null && !testResults.isEmpty()) {
                    log.info("[{}] Extracted {} test results for case ID {}. Preparing to update database...",logPrefix, testResults.size(), id);

                    CaseItem caseItem = caseItemMapper.findById(id);
                    if (caseItem == null) {
                        log.error("[{}] CaseItem not found for ID {}, cannot update test results.", logPrefix, id);
                        parsedOcrMap.put("ocrResultId", savedResult.getId());
                        parsedOcrMap.put("imageId", gridFsId);
                        return parsedOcrMap;
                    }

                    OcrInteractionHistoryContainer container = getOrCreateInteractionHistoryMapsForOcr(id, caseItem, logPrefix);
                    if (container == null) { // Should not happen if caseItem is not null
                        log.error("[{}] Failed to get or create interaction history maps for case ID: {}", logPrefix, id);
                        parsedOcrMap.put("ocrResultId", savedResult.getId());
                        parsedOcrMap.put("imageId", gridFsId);
                        return parsedOcrMap; // Return OCR content with added metadata
                    }

                    Map<String, Object> innerHistoryMap = container.innerHistoryMap;
                    Map<String, Object> topLevelHistoryMap = container.topLevelHistoryMap;
                    
                    Object existingInnerRecommendations = innerHistoryMap.get("test_recommendation");
                    List<Map<String, Object>> targetRecommendationList;

                    if (existingInnerRecommendations instanceof List) {
                        log.debug("[{}] Found existing test_recommendation list in inner history for case ID {}.", logPrefix, id);
                        try {
                             targetRecommendationList = (List<Map<String, Object>>) existingInnerRecommendations;
                        } catch (ClassCastException e) {
                            log.warn("[{}] Existing 'test_recommendation' is a List, but not of Map<String, Object> for case ID {}. Creating new list.", logPrefix, id);
                            targetRecommendationList = new ArrayList<>();
                            innerHistoryMap.put("test_recommendation", targetRecommendationList);
                        }
                    } else {
                        if (existingInnerRecommendations != null) {
                             log.warn("[{}] Existing interaction_history.test_recommendation is not a List (Type: {}) for case ID {}. Creating new list.", logPrefix, existingInnerRecommendations.getClass().getName(), id);
                        }
                        log.debug("[{}] test_recommendation list not found or not a list in inner history for case ID {}. Creating new list.", logPrefix, id);
                        targetRecommendationList = new ArrayList<>();
                        innerHistoryMap.put("test_recommendation", targetRecommendationList);
                    }

                    targetRecommendationList.addAll(testResults);
                    log.info("[{}] Added OCR test results to list. New size: {}. Case ID: {}", logPrefix, targetRecommendationList.size(), id);
                    
                    // 5. 添加图片信息到交互历史
                    if (!innerHistoryMap.containsKey("images")) {
                        innerHistoryMap.put("images", new ArrayList<Map<String, Object>>());
                    }
                    
                    List<Map<String, Object>> imagesList = (List<Map<String, Object>>) innerHistoryMap.get("images");
                    
                    Map<String, Object> imageInfo = new HashMap<>();
                    imageInfo.put("id", gridFsId);
                    imageInfo.put("category", "OCR");
                    imageInfo.put("originalName", image.getOriginalFilename());
                    imageInfo.put("uploadTime", imageMetadata.get("uploadTime"));
                    imageInfo.put("ocrResultId", savedResult.getId());
                    
                    imagesList.add(imageInfo);

                    int updateCount = caseItemMapper.updateInteractionHistoryById(id, topLevelHistoryMap);
                    if (updateCount > 0) {
                        log.info("[{}] Successfully updated database with OCR test results for case ID: {}", logPrefix, id);
                        // 6. 添加元数据到OCR结果
                        Map<String, Object> resultMap;
                        if (parsedOcrMap.containsKey("content")) {
                            resultMap = new HashMap<>(parsedOcrMap);
                        } else {
                            resultMap = new HashMap<>();
                            resultMap.put("content", parsedOcrMap);
                        }
                        resultMap.put("ocrResultId", savedResult.getId());
                        resultMap.put("imageId", gridFsId);
                        resultMap.put("processingTimeMs", processingTime);
                        resultMap.put("caseId", id);
                        return resultMap;
                    } else {
                        log.error("[{}] Failed to update database with OCR test results for case ID: {}. Update count was 0.", logPrefix, id);
                        // Return raw OCR content with added metadata
                        Map<String, Object> resultMap = new HashMap<>(parsedOcrMap);
                        resultMap.put("ocrResultId", savedResult.getId());
                        resultMap.put("imageId", gridFsId);
                        return resultMap;
                    }
                } else {
                    log.info("[{}] No structured test results extracted from OCR content for case ID {}. Database not updated with test items.", logPrefix, id);
                    Map<String, Object> resultMap = new HashMap<>(parsedOcrMap);
                    resultMap.put("ocrResultId", savedResult.getId());
                    resultMap.put("imageId", gridFsId);
                    resultMap.put("processingTimeMs", processingTime);
                    resultMap.put("caseId", id);
                    return resultMap;
                }
            } else {
                log.error("[{}] Python OCR service returned null response after retries for case ID {}", logPrefix, id);
                Map<String, Object> errorMap = new HashMap<>();
                errorMap.put("error", "OCR 服务调用失败 (未收到有效响应)");
                
                // 即使OCR失败，也保存记录
                OcrResult ocrResult = new OcrResult();
                ocrResult.setCaseId(id);
                ocrResult.setImageId(gridFsId);
                ocrResult.setOriginalFilename(image.getOriginalFilename());
                ocrResult.setLocalFilePath(savedFilePath.toAbsolutePath().toString());
                ocrResult.setContent(errorMap);
                ocrResult.setMetadata(new HashMap<>(imageMetadata));
                ocrResult.setSuccess(false);
                ocrResult.setProcessingTimeMs(System.currentTimeMillis() - startTime);
                
                OcrResult savedResult = ocrResultRepository.save(ocrResult);
                errorMap.put("ocrResultId", savedResult.getId());
                errorMap.put("imageId", gridFsId);
                
                return errorMap;
            }
        } catch (IOException e) {
            log.error("[PROD OCR DB] Error processing uploaded image or saving file for case ID {}: {}", id, e.getMessage(), e);
            return Collections.singletonMap("error", "处理上传图片时出错: " + e.getMessage());
        } catch (Exception e) {
            log.error("[PROD OCR DB] Unexpected error during OCR processing for case ID {}: {}", id, e.getMessage(), e);
            return Collections.singletonMap("error", "OCR 服务发生意外错误: " + e.getMessage());
        } finally {
            if (savedFilePath != null) {
                try {
                    boolean deleted = Files.deleteIfExists(savedFilePath);
                    if (deleted) {
                        log.info("[{}] Successfully deleted temporary OCR image for case ID {}: {}", logPrefix, id, savedFilePath);
                    } else {
                        log.warn("[{}] Temporary OCR image for case ID {} could not be deleted (or was already gone): {}", logPrefix, id, savedFilePath);
                    }
                } catch (IOException ex) {
                    log.error("[{}] Error attempting to delete temporary OCR image {} for case ID {}: {}", logPrefix, savedFilePath, id, ex.getMessage(), ex);
                }
            }
        }
    }

    /**
     * 带有重试机制的Python OCR服务调用 - 返回Map对象
     * 
     * @param requestBody 请求体
     * @param id 案例ID，可为null
     * @param logPrefix 日志前缀
     * @return 响应内容，如果所有重试都失败则返回null
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> callPythonOcrServiceWithRetry(Map<String, String> requestBody, String id, String logPrefix) {
        int attempts = 0;
        int currentDelay = initialRetryDelay;
        RestClientException lastException = null;
        
        while (attempts < maxRetryAttempts) {
            try {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                headers.add("Connection", "close");
                HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);

                log.info("[{}] Calling Python OCR service URL: {} {}，尝试次数: {}/{}",
                        logPrefix, pythonProdOcrServiceUrl, id != null ? "for case ID: " + id : "", 
                        attempts + 1, maxRetryAttempts);
                
                ResponseEntity<Map> responseEntity = restTemplate.postForEntity(
                    pythonProdOcrServiceUrl, requestEntity, Map.class);
                
                if (responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getBody() != null) {
                    if (attempts > 0) {
                        log.info("[{}] Successfully called OCR service on attempt #{}", logPrefix, attempts + 1);
                    }
                    return responseEntity.getBody();
                } else {
                    log.error("[{}] Python OCR service returned non-OK status: {} or empty body.", 
                            logPrefix, responseEntity.getStatusCode());
                    
                    // 如果是服务器错误(5xx)，尝试重试
                    if (responseEntity.getStatusCode().is5xxServerError()) {
                        attempts++;
                        if (attempts < maxRetryAttempts) {
                            log.warn("[{}] Server error, will retry in {}ms (attempt {}/{})", 
                                    logPrefix, currentDelay, attempts + 1, maxRetryAttempts);
                            Thread.sleep(currentDelay);
                            currentDelay *= retryMultiplier; // 指数退避
                        }
                    } else {
                        // 对于非服务器错误(如4xx)，不重试
                        Map<String, Object> errorMap = new HashMap<>();
                        errorMap.put("error", "OCR 服务调用失败");
                        errorMap.put("status", responseEntity.getStatusCodeValue());
                        if (responseEntity.getBody() != null) { 
                            errorMap.put("details", responseEntity.getBody());
                        } else {
                            errorMap.put("details", "Service returned non-OK status or empty body.");
                        }
                        return errorMap;
                    }
                }
            } catch (RestClientException e) {
                lastException = e;
                attempts++;
                if (attempts < maxRetryAttempts) {
                    try {
                        log.warn("[{}] Error calling OCR service, will retry in {}ms (attempt {}/{}): {}", 
                                logPrefix, currentDelay, attempts + 1, maxRetryAttempts, e.getMessage());
                        Thread.sleep(currentDelay);
                        currentDelay *= retryMultiplier; // 指数退避
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.error("[{}] Retry wait interrupted", logPrefix, ie);
                        break;
                    }
                } else {
                    log.error("[{}] Failed to call OCR service after {} attempts: {}", 
                            logPrefix, maxRetryAttempts, e.getMessage(), e);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("[{}] Retry wait interrupted", logPrefix, e);
                break;
            } catch (Exception e) {
                log.error("[{}] Unexpected error calling OCR service: {}", logPrefix, e.getMessage(), e);
                break;
            }
        }
        
        if (lastException != null) {
            log.error("[{}] All retry attempts failed, last exception: {}", logPrefix, lastException.getMessage());
            Map<String, Object> errorMap = new HashMap<>();
            errorMap.put("error", "连接 OCR 服务失败: " + lastException.getMessage());
            return errorMap;
        }
        
        return null;
    }

    /**
     * 带有重试机制的Python OCR服务调用 - 返回String响应
     * 
     * @param requestBody 请求体
     * @param id 案例ID，不为null
     * @param logPrefix 日志前缀
     * @return 响应内容，如果所有重试都失败则返回null
     */
    private String callPythonOcrTextServiceWithRetry(Map<String, String> requestBody, String id, String logPrefix) {
        int attempts = 0;
        int currentDelay = initialRetryDelay;
        RestClientException lastException = null;
        
        while (attempts < maxRetryAttempts) {
            try {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                headers.add("Connection", "close");
                HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);

                log.info("[{}] Calling Python OCR service URL: {} for case ID: {}, 尝试次数: {}/{}",
                        logPrefix, pythonProdOcrServiceUrl, id, attempts + 1, maxRetryAttempts);
                
                ResponseEntity<String> responseEntity = restTemplate.postForEntity(
                    pythonProdOcrServiceUrl, requestEntity, String.class);
                
                if (responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getBody() != null) {
                    if (attempts > 0) {
                        log.info("[{}] Successfully called OCR service on attempt #{} for case ID: {}", 
                                logPrefix, attempts + 1, id);
                    }
                    return responseEntity.getBody();
                } else {
                    log.error("[{}] Python OCR service returned non-OK status: {} or empty body for case ID: {}.", 
                            logPrefix, responseEntity.getStatusCode(), id);
                    
                    // 如果是服务器错误(5xx)，尝试重试
                    if (responseEntity.getStatusCode().is5xxServerError()) {
                        attempts++;
                        if (attempts < maxRetryAttempts) {
                            log.warn("[{}] Server error for case ID {}, will retry in {}ms (attempt {}/{})", 
                                    logPrefix, id, currentDelay, attempts + 1, maxRetryAttempts);
                            Thread.sleep(currentDelay);
                            currentDelay *= retryMultiplier; // 指数退避
                        }
                    } else {
                        // 对于非服务器错误(如4xx)，不重试
                        return null;
                    }
                }
            } catch (RestClientException e) {
                lastException = e;
                attempts++;
                if (attempts < maxRetryAttempts) {
                    try {
                        log.warn("[{}] Error calling OCR service for case ID {}, will retry in {}ms (attempt {}/{}): {}", 
                                logPrefix, id, currentDelay, attempts + 1, maxRetryAttempts, e.getMessage());
                        Thread.sleep(currentDelay);
                        currentDelay *= retryMultiplier; // 指数退避
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.error("[{}] Retry wait interrupted for case ID {}", logPrefix, id, ie);
                        break;
                    }
                } else {
                    log.error("[{}] Failed to call OCR service for case ID {} after {} attempts: {}", 
                            logPrefix, id, maxRetryAttempts, e.getMessage(), e);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("[{}] Retry wait interrupted for case ID {}", logPrefix, id, e);
                break;
            } catch (Exception e) {
                log.error("[{}] Unexpected error calling OCR service for case ID {}: {}", logPrefix, id, e.getMessage(), e);
                break;
            }
        }
        
        if (lastException != null) {
            log.error("[{}] All retry attempts failed for case ID {}, last exception: {}", logPrefix, id, lastException.getMessage());
        }
        
        return null;
    }

    /**
     * Extracts the "检查结果" list from the parsed content object.
     * (Adapted for Prod Service)
     *
     * @param content The parsed content object from OCR.
     * @return The List of test results maps, or null if not found or structure mismatch.
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> extractTestResultsProd(Object content) {
        if (content instanceof String) {
            // If it's a simple string, try to parse it as JSON array of maps
            // This path may not be hit if Python directly returns structured data within the main map
            try {
                ObjectMapper localMapper = new ObjectMapper(); // Use a local ObjectMapper for this specific parsing task
                return localMapper.readValue((String) content, new TypeReference<List<Map<String, Object>>>() {});
            } catch (IOException e) {
                log.warn("[PROD OCR Extract] Could not parse OCR content string as List<Map<String, Object>>. Content: {}", ((String) content).substring(0, Math.min(((String) content).length(), 200)), e);
                return Collections.emptyList();
            }
        } else if (content instanceof List) {
            // If Python already structured it as a List<Map<String, Object>> (or similar) under the 'content' key
            try {
                return (List<Map<String, Object>>) content;
            } catch (ClassCastException e) {
                log.warn("[PROD OCR Extract] OCR content is a List, but not of Map<String, Object>. Details: {}", e.getMessage());
                return Collections.emptyList();
            }
        } else if (content instanceof Map && ((Map<?,?>)content).containsKey("test_items")) {
             // If content is a map and contains a specific key like "test_items" which is a list
            Object testItemsObj = ((Map<?,?>)content).get("test_items");
            if (testItemsObj instanceof List) {
                 try {
                    return (List<Map<String, Object>>) testItemsObj;
                } catch (ClassCastException e) {
                    log.warn("[PROD OCR Extract] OCR content.test_items is a List, but not of Map<String, Object>. Details: {}", e.getMessage());
                    return Collections.emptyList();
                }
            }
        }
        log.warn("[PROD OCR Extract] OCR content is not a recognized format for test results extraction. Type: {}", content != null ? content.getClass().getName() : "null");
        return Collections.emptyList();
    }

    /**
     * 将上传的文件保存到 application.properties 中配置的指定目录中 (Prod version)。
     *
     * @param file 上传的文件
     * @return 保存后的文件的路径
     * @throws IOException 如果创建目录或保存文件时发生 I/O 错误
     */
    private Path saveImageToDesignatedLocationProd(MultipartFile file) throws IOException {
        // Use the prod-specific configuration path
        Path saveDirectory = Paths.get(imageProdSavePath);

        if (!Files.exists(saveDirectory)) {
            log.info("[PROD OCR Save] Image save directory does not exist, creating: {}", saveDirectory);
            Files.createDirectories(saveDirectory);
        }
        if (!Files.isDirectory(saveDirectory)) {
            throw new IOException("Configured prod image save path is not a valid directory: " + imageProdSavePath);
        }

        String originalFilename = file.getOriginalFilename();
        String extension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }
        String uniqueFilename = UUID.randomUUID().toString() + extension;
        Path destinationFile = saveDirectory.resolve(uniqueFilename);

        try (InputStream inputStream = file.getInputStream()) {
            Files.copy(inputStream, destinationFile, StandardCopyOption.REPLACE_EXISTING);
        }

        return destinationFile;
    }
    
    /**
     * 根据案例ID获取所有OCR结果
     * 
     * @param caseId 案例ID
     * @return OCR结果列表
     */
    public List<OcrResult> getOcrResultsByCaseId(String caseId) {
        try {
            return ocrResultRepository.findByCaseIdOrderByCreatedAtDesc(caseId);
        } catch (Exception e) {
            log.error("[PROD OCR] Error retrieving OCR results for case ID {}: {}", caseId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 根据ID获取OCR结果
     * 
     * @param resultId OCR结果ID
     * @return OCR结果，如果存在
     */
    public Optional<OcrResult> getOcrResultById(String resultId) {
        try {
            return ocrResultRepository.findById(resultId);
        } catch (Exception e) {
            log.error("[PROD OCR] Error retrieving OCR result with ID {}: {}", resultId, e.getMessage(), e);
            return Optional.empty();
        }
    }
    
    /**
     * 获取所有OCR结果
     * 
     * @return 所有OCR结果的列表
     */
    public List<OcrResult> getAllOcrResults() {
        return ocrResultRepository.findAll();
    }

    public Page<OcrResult> getAllOcrResults(Pageable pageable) {
        return ocrResultRepository.findAll(pageable);
    }
}