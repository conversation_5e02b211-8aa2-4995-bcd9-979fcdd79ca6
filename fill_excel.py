import pandas as pd

# List of functions from function.md
functions = [
    'encode_image',
    'clean_treatment_recommendation',
    'api_call',
    'ocr',
    'final_diagnosis_parser',
    'batch_test_patient',
    'batch_process_jsonl',
    'batch_process',
    'api_next_response',
    'api_diagnose_response',
    'ocr_api',
    'ai_doctor_preliminary_diagnosis',
    'ai_doctor_diagnose',
    'ai_doctor_v2_inquiry',
    'ai_doctor_v2_quick_inquiry',
    'ai_doctor_v2_preliminary_diagnosis',
    'ai_doctor_v2_diagnosis',
    'ai_doctor_v2',
    'Executor.__init__',
    'Executor.next_question',
    'Executor.inquire',
    'Executor._AI_patient_response',
    'Executor._extract_observation_feedback',
    'Executor._generate_question',
    'Executor._construct_question_prompt',
    'Executor._check_end_of_inquiry',
    'Executor._construct_end_inquiry_prompt',
    'Executor.update_observation',
    'Executor._construct_observation_prompt',
    'Executor.provide_feedback',
    'Executor._construct_feedback_prompt',
    'Executor.get_observation',
    'Executor.format_dialogue_history',
    'Planner.__init__',
    'Planner._init_cot_retrieval',
    'Planner._extract_symptoms',
    'Planner._generate_embedding',
    'Planner.retrieve_cot_examples',
    'Planner._construct_strategy_prompt',
    'Planner.generate_strategy',
    'Planner._generate_new_strategy',
    'Planner._parse_strategy_response',
    'Planner._check_termination',
    'Planner.get_final_diagnosis',
    'Planner.format_medical_advice',
    'Planner.get_treatments',
    'Planner.update_observation_from_diagnosis',
    'Planner.update_observation_only',
    'Planner._build_condition_prompt',
    'Planner.final_diagnosis_and_condition',
    'Planner.generate_final_diagnosis_prompt',
    'Planner.generate_secondary_diagnosis',
    'Planner.generate_preliminary_diagnosis',
    'get_guide',
    'LLMClient.generate_response',
    'OpenAIClient.generate_response',
    'VolcanoClient.generate_response',
    'ClaudeClient.generate_response',
    'QwenClient.generate_response',
    'QwenClient.generate_stream_response',
    'JiuzhangClient.generate_response'
]

# Create DataFrame with empty first row for column header
df = pd.DataFrame({'Function Name': [''] + functions})

# Save to Excel file
df.to_excel('api.xlsx', index=False)

print("Excel file 'api.xlsx' created successfully with function names.") 