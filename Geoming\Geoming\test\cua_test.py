import json
import os
import pickle
import subprocess
import time
import base64
from openai import OpenAI
from dotenv import load_dotenv
from playwright.sync_api import sync_playwright

load_dotenv('../.env')  # 加载环境变量

api_key = os.getenv("OPENAI_API_KEY")

client = OpenAI(
    api_key=api_key,
)
def get_screenshot(page):
    """
    Take a full-page screenshot using Playwright and return the image bytes.
    """
    return page.screenshot(timeout=6000)

def handle_model_action(page, action):
    """
    Given a computer action (e.g., click, double_click, scroll, etc.),
    execute the corresponding operation on the Playwright page.
    """
    action_type = action.type
    
    try:
        match action_type:

            case "click":
                x, y = action.x, action.y
                button = action.button
                print(f"Action: click at ({x}, {y}) with button '{button}'")
                # Not handling things like middle click, etc.
                if button != "left" and button != "right":
                    button = "left"
                page.mouse.click(x, y, button=button)

            case "scroll":
                x, y = action.x, action.y
                scroll_x, scroll_y = action.scroll_x, action.scroll_y
                print(f"Action: scroll at ({x}, {y}) with offsets (scroll_x={scroll_x}, scroll_y={scroll_y})")
                page.mouse.move(x, y)
                page.evaluate(f"window.scrollBy({scroll_x}, {scroll_y})")

            case "keypress":
                keys = action.keys
                for k in keys:
                    print(f"Action: keypress '{k}'")
                    # A simple mapping for common keys; expand as needed.
                    if k.lower() == "enter":
                        page.keyboard.press("Enter")
                    elif k.lower() == "space":
                        page.keyboard.press(" ")
                    else:
                        page.keyboard.press(k)
            
            case "type":
                text = action.text
                print(f"Action: type text: {text}")
                page.keyboard.type(text)
            
            case "wait":
                print(f"Action: wait")
                time.sleep(2)

            case "screenshot":
                # Nothing to do as screenshot is taken at each turn
                print(f"Action: screenshot")

            # Handle other actions here

            case _:
                print(f"Unrecognized action: {action}")

    except Exception as e:
        print(f"Error handling action {action}: {e}")

def computer_use_loop(instance, response):
    """
    Run the loop that executes computer actions until no 'computer_call' is found.
    """
    while True:
        computer_calls = [item for item in response.output if item.type == "computer_call"]
        if not computer_calls:
            print("No computer call found. Output from model:")
            for item in response.output:
                print(item)
            break  # Exit when no computer calls are issued.

        # We expect at most one computer call per response.
        computer_call = computer_calls[0]
        last_call_id = computer_call.call_id
        action = computer_call.action

        # Execute the action (function defined in step 3)
        handle_model_action(instance, action)
        time.sleep(1)  # Allow time for changes to take effect.

        # Take a screenshot after the action (function defined in step 4)
        screenshot_bytes = get_screenshot(instance)
        screenshot_base64 = base64.b64encode(screenshot_bytes).decode("utf-8")

        # Send the screenshot back as a computer_call_output
        response = client.responses.create(
            model="computer-use-preview",
            previous_response_id=response.id,
            tools=[
                {
                    "type": "computer_use_preview",
                    "display_width": 1024,
                    "display_height": 768,
                    "environment": "browser"
                }
            ],
            input=[
                {
                    "call_id": last_call_id,
                    "type": "computer_call_output",
                    "output": {
                        "type": "input_image",
                        "image_url": f"data:image/png;base64,{screenshot_base64}"
                    }
                }
            ],
            truncation="auto"
        )

    return response


def main():
    print("Starting computer use loop...")
    # Create a new instance of the Playwright browser
    # 启动 Playwright 并打开一个浏览器实例
    # chrome_path = r"C:\Program Files\Google\Chrome\Application\chrome.exe"  # 使用原始字符串

    # debugging_port = "--remote-debugging-port=9222"

    # command = f"{chrome_path} {debugging_port}"
    # subprocess.Popen(command, shell=True)
    
    with sync_playwright() as p:
        # browser = p.chromium.launch(headless=False)  # 这里 headless=False 让它可视化
        browser = p.chromium.launch_persistent_context(
        # 指定本机用户缓存地址
        user_data_dir=r"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data\Default",
        # 指定本机google客户端exe的路径
        executable_path=r"C:\Program Files\Google\Chrome\Application\chrome.exe" , # 使用原始字符串
        # 要想通过这个下载文件这个必然要开  默认是False
        accept_downloads=True,
        # 设置不是无头模式
        headless=False,
        bypass_csp=True,
        slow_mo=10,
        # 跳过检测
        args = ['--disable-blink-features=AutomationControlled','--remote-debugging-port=9222']
 
    )

        # context = browser.new_context()
        # page = context.new_page()  # 这里的 page 就是 instance
        page = browser.new_page()

        # 让 page 访问 Google
        page.goto("https://www.bing.com")

        # 访问知乎官网
        page.goto("https://www.zhihu.com/")

        # 等待页面加载
        page.wait_for_timeout(2000)  # 等待 2 秒，确保页面加载

        # # 点击登录按钮（通过按钮的 XPath 定位）
        # page.click('text="密码登录"')  # 找到登录按钮，点击

        # # 等待登录框加载
        # page.wait_for_timeout(2000)

        # # 输入手机号
        # page.fill('input[name="username"]', '19837000193')

        # # 输入密码
        # page.fill('input[name="password"]', 'Zhihu_1008')

        # # 点击登录按钮
        # page.click('button[type="submit"]')

        # 等待页面加载，确保登录成功
        page.wait_for_timeout(5555)  # 等待 5 秒

        # 获取 cookies 并保存到本地
        cookies = page.context.cookies()
        with open("cookies.pkl", "wb") as f:
            pickle.dump(cookies, f)
        
        print("Cookies saved successfully!")

        # 创建初始的 AI 响应
        response = client.responses.create(
            model="computer-use-preview",
            tools=[
                {
                    "type": "computer_use_preview",
                    "display_width": 1024,
                    "display_height": 768,
                    "environment": "browser"
                }
            ],
            input=r"请你进入知乎，查看热点排行榜，点开一个和AI相关的问题，阅读三个人的回答，然后给我一个总结",
            # input=r"请你通过这个人机验证",
            truncation="auto"
        )

        print("Initial response:")
        print(json.dumps(response.model_dump(), indent=4, ensure_ascii=False))

        # 运行交互循环
        response = computer_use_loop(page, response)

        print("Final response:")
        print(json.dumps(response.model_dump(), indent=4, ensure_ascii=False))

        # 关闭浏览器
        browser.close()

if __name__ == "__main__":
    main()