# AI Doctor V2 数据库表结构设计 (MySQL - 中文版)

## 1. `medical_sessions` (医疗会话表 - 修改现有表)

*   `id` (INT, 主键, 自增)
*   `user_id` (INT, 外键, 关联 `users` 表)
*   `created_at` (TIMESTAMP, 默认当前时间)
*   `updated_at` (TIMESTAMP)
*   **`format_version` (INT, 默认 1, 注释: '1 代表 V1 文本消息, 2 代表 V2 交互历史格式')** - *新增列以区分格式*
*   *(... 其他可能已存在的列 ...)*
*   **索引:** `idx_format_version` (`format_version`)

**SQL 示例 (修改部分):**
```sql
ALTER TABLE `medical_sessions`
ADD COLUMN `format_version` INT NOT NULL DEFAULT 1 COMMENT '1 代表 V1 文本消息, 2 代表 V2 交互历史格式',
ADD INDEX `idx_format_version` (`format_version`);
```

## 2. `patient_cases` (患者病例表 - 新表)
   *(存储 V2 会话的顶层信息)*

*   `id` (INT, 主键, 自增)
*   `session_id` (INT, 外键, 关联 `medical_sessions.id`, 唯一, 注释: '关联的 V2 会话 (format_version=2)')
*   `final_diagnosis` (TEXT, 可为空, 注释: '最终诊断文本')
*   `preliminary_diagnosis` (TEXT, 可为空, 注释: '初步诊断文本')
*   `created_at` (TIMESTAMP, 默认当前时间)
*   `updated_at` (TIMESTAMP, 默认当前时间, 更新时自动更新)

**SQL 示例:**
```sql
CREATE TABLE `patient_cases` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `session_id` INT NOT NULL UNIQUE COMMENT '关联的 V2 会话 (format_version=2)',
  `final_diagnosis` TEXT NULL COMMENT '最终诊断文本',
  `preliminary_diagnosis` TEXT NULL COMMENT '初步诊断文本',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (`session_id`) REFERENCES `medical_sessions`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='存储患者病例信息';
```

## 3. `consultation_rounds` (问诊轮次表 - 新表)
   *(存储 `patient_case.consultation_rounds` 数组中的每个条目)*

*   `id` (INT, 主键, 自增)
*   `case_id` (INT, 外键, 关联 `patient_cases.id`, 注释: '关联的患者病例')
*   `round_number` (INT, 注释: '条目在数组中的顺序 (轮次)')
*   `strategy` (TEXT, 可为空, 注释: '问诊策略')
*   `reasoning` (TEXT, 可为空, 注释: '临床推理过程')
*   `observation` (TEXT, 可为空, 注释: '观察记录')
*   `feedback` (TEXT, 可为空, 注释: '反馈评价')
*   `inquiry_target` (VARCHAR(10), 可为空, 注释: '询问目标: "doctor"表示询问医生, "patient"表示询问患者')
*   `created_at` (TIMESTAMP, 默认当前时间)
*   **唯一约束:** (`case_id`, `round_number`)

**SQL 示例:**
```sql
CREATE TABLE `consultation_rounds` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `case_id` INT NOT NULL COMMENT '关联的患者病例',
  `round_number` INT NOT NULL COMMENT '条目在数组中的顺序 (轮次)',
  `strategy` TEXT NULL COMMENT '问诊策略',
  `reasoning` TEXT NULL COMMENT '临床推理过程',
  `observation` TEXT NULL COMMENT '观察记录',
  `feedback` TEXT NULL COMMENT '反馈评价',
  `inquiry_target` VARCHAR(10) NULL COMMENT '询问目标: "doctor"表示询问医生, "patient"表示询问患者',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`case_id`) REFERENCES `patient_cases`(`id`) ON DELETE CASCADE,
  UNIQUE KEY `idx_case_round` (`case_id`, `round_number`) -- 确保每个病例的轮次唯一
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='存储问诊过程中的轮次记录';
```

## 4. `conversation_messages` (对话消息表 - 新表)
   *(存储 `consultation_round.dialogue_history` 数组中的每个对话)*

*   `id` (INT, 主键, 自增)
*   `round_id` (INT, 外键, 关联 `consultation_rounds.id`, 注释: '关联的问诊轮次')
*   `message_number` (INT, 注释: '消息在对话中的顺序')
*   `speaker_role` (VARCHAR(10), 注释: "'doctor' 或 'patient'")
*   `message_content` (TEXT, 注释: '对话内容')
*   `created_at` (TIMESTAMP, 默认当前时间)
*   **唯一约束:** (`round_id`, `message_number`)

**SQL 示例:**
```sql
CREATE TABLE `conversation_messages` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `round_id` INT NOT NULL COMMENT '关联的问诊轮次',
  `message_number` INT NOT NULL COMMENT '消息在对话中的顺序',
  `speaker_role` VARCHAR(10) NOT NULL COMMENT '"doctor" 或 "patient"',
  `message_content` TEXT NOT NULL COMMENT '对话内容',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`round_id`) REFERENCES `consultation_rounds`(`id`) ON DELETE CASCADE,
  UNIQUE KEY `idx_round_message` (`round_id`, `message_number`) -- 确保每个轮次内的消息序号唯一
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='存储医患对话的具体消息';
```

## 5. `diagnostic_tests` (诊断检查表 - 新表)
   *(存储 `patient_case.test_recommendation` 数组中的条目)*

*   `id` (INT, 主键, 自增)
*   `case_id` (INT, 外键, 关联 `patient_cases.id`, 注释: '关联的患者病例')
*   `test_description` (TEXT, 注释: '检查建议描述')
*   `created_at` (TIMESTAMP, 默认当前时间)

**SQL 示例:**
```sql
CREATE TABLE `diagnostic_tests` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `case_id` INT NOT NULL COMMENT '关联的患者病例',
  `test_description` TEXT NOT NULL COMMENT '检查建议描述',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`case_id`) REFERENCES `patient_cases`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='存储诊断检查建议';
```

## 6. `treatment_plans` (治疗方案表 - 新表)
   *(存储 `patient_case.treatment_recommendation` 数组中的条目)*

*   `id` (INT, 主键, 自增)
*   `case_id` (INT, 外键, 关联 `patient_cases.id`, 注释: '关联的患者病例')
*   `treatment_description` (TEXT, 注释: '治疗方案描述')
*   `created_at` (TIMESTAMP, 默认当前时间)

**SQL 示例:**
```sql
CREATE TABLE `treatment_plans` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `case_id` INT NOT NULL COMMENT '关联的患者病例',
  `treatment_description` TEXT NOT NULL COMMENT '治疗方案描述',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`case_id`) REFERENCES `patient_cases`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='存储治疗方案建议';
```

## 7. `clinical_notes` (临床备注表 - 新表)
   *(存储 `patient_case.doctor_supplementary_info` 数组中的条目)*

*   `id` (INT, 主键, 自增)
*   `case_id` (INT, 外键, 关联 `patient_cases.id`, 注释: '关联的患者病例')
*   `note_content` (TEXT, 注释: '临床备注内容')
*   `created_at` (TIMESTAMP, 默认当前时间)

**SQL 示例:**
```sql
CREATE TABLE `clinical_notes` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `case_id` INT NOT NULL COMMENT '关联的患者病例',
  `note_content` TEXT NOT NULL COMMENT '临床备注内容',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`case_id`) REFERENCES `patient_cases`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='存储医生的临床补充信息';
```

## 自动判断询问目标的触发器

以下触发器可以根据`strategy`字段内容自动设置`inquiry_target`字段的值：

```sql
DELIMITER //
CREATE TRIGGER set_inquiry_target_insert BEFORE INSERT ON `consultation_rounds`
FOR EACH ROW
BEGIN
    IF NEW.strategy LIKE '%[查体策略:]%' THEN
        SET NEW.inquiry_target = 'doctor';
    ELSE
        SET NEW.inquiry_target = 'patient';
    END IF;
END //

CREATE TRIGGER set_inquiry_target_update BEFORE UPDATE ON `consultation_rounds`
FOR EACH ROW
BEGIN
    IF NEW.strategy LIKE '%[查体策略:]%' THEN
        SET NEW.inquiry_target = 'doctor';
    ELSE
        SET NEW.inquiry_target = 'patient';
    END IF;
END //
DELIMITER ;
```

## 详解:

1.  **`medical_sessions` 表:** 通过 `format_version` 字段区分新旧格式，确保 V1 会话不受影响。
2.  **`patient_cases` 表:** 这是 V2 数据的核心入口，每个 V2 会话对应一条记录，存储会话级别的诊断信息。
3.  **`consultation_rounds` 表:** 存储每轮问诊的思考链信息，新增了`inquiry_target`字段用于标识询问目标是医生还是患者。
4.  **`conversation_messages` 表:** 存储每轮问诊中的具体对话消息，通过 `round_id` 关联回对应的问诊轮次。
5.  **`diagnostic_tests`, `treatment_plans`, `clinical_notes` 表:** 分别存储诊断检查建议、治疗方案和临床补充信息。

通过添加触发器，系统可以根据策略内容自动判断询问目标，使数据结构更加合理和实用。

## E-R 图 (Entity-Relationship Diagram)

```mermaid
erDiagram
    medical_sessions {
        INT id PK
        INT user_id FK
        INT format_version "区分格式 (1=V1, 2=V2)"
        -- 其他列 --
    }

    patient_cases {
        INT id PK
        INT session_id FK "关联的V2会话"
        TEXT final_diagnosis "最终诊断"
        TEXT preliminary_diagnosis "初步诊断"
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }

    consultation_rounds {
        INT id PK
        INT case_id FK "关联患者病例"
        INT round_number "轮次"
        TEXT strategy "问诊策略"
        TEXT reasoning "临床推理"
        TEXT observation "观察记录"
        TEXT feedback "反馈评价"
        VARCHAR(10) inquiry_target "询问目标"
        TIMESTAMP created_at
    }

    conversation_messages {
        INT id PK
        INT round_id FK "关联问诊轮次"
        INT message_number "消息顺序"
        VARCHAR(10) speaker_role "'doctor' 或 'patient'"
        TEXT message_content "对话内容"
        TIMESTAMP created_at
    }

    diagnostic_tests {
        INT id PK
        INT case_id FK "关联患者病例"
        TEXT test_description "检查建议描述"
        TIMESTAMP created_at
    }

    treatment_plans {
        INT id PK
        INT case_id FK "关联患者病例"
        TEXT treatment_description "治疗方案描述"
        TIMESTAMP created_at
    }

    clinical_notes {
        INT id PK
        INT case_id FK "关联患者病例"
        TEXT note_content "临床备注内容"
        TIMESTAMP created_at
    }

    medical_sessions ||--o{ patient_cases : "包含 (若 format_version=2)"
    patient_cases ||--|{ consultation_rounds : "包含问诊轮次"
    consultation_rounds ||--|{ conversation_messages : "包含对话消息"
    patient_cases ||--|{ diagnostic_tests : "包含诊断检查"
    patient_cases ||--|{ treatment_plans : "包含治疗方案"
    patient_cases ||--|{ clinical_notes : "包含临床备注"

``` 