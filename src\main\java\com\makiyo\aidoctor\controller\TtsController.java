package com.makiyo.aidoctor.controller;

import com.makiyo.aidoctor.service.TtsService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Tag(name = "语音合成", description = "文字转语音相关的 API 接口")
@RestController
@RequestMapping("/api/tts")
@RequiredArgsConstructor
public class TtsController {

    private final TtsService ttsService;

    @Operation(
        summary = "基础语音转换",
        description = "将文本转换为语音，使用默认参数进行转换",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "转换成功",
                content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(
                        implementation = TtsResponse.class
                    )
                )
            )
        }
    )
    @PostMapping("/convert")
    public Map<String, Object> convertToSpeech(@RequestParam String text) {
        Map<String, Object> response = new HashMap<>();
        try {
            String audioUrl = ttsService.generateTtsUrl(text);
            response.put("success", true);
            response.put("message", "转换成功");
            response.put("data", audioUrl);
        } catch (IOException e) {
            log.error("文字转语音失败", e);
            response.put("success", false);
            response.put("message", "转换失败: " + e.getMessage());
        }
        return response;
    }

    @Operation(
        summary = "高级语音转换",
        description = "使用自定义参数将文本转换为语音，支持调整语速、音量、音高等参数",
        requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "语音转换参数",
            required = true,
            content = @Content(
                schema = @Schema(implementation = TtsRequest.class)
            )
        ),
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "转换成功",
                content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(
                        implementation = TtsResponse.class
                    )
                )
            )
        }
    )
    @PostMapping("/convert/advanced")
    public Map<String, Object> convertToSpeechWithParams(@RequestBody TtsRequest request) {
        Map<String, Object> response = new HashMap<>();
        try {
            String audioUrl = ttsService.generateTtsUrl(request.text);
            response.put("success", true);
            response.put("message", "转换成功");
            response.put("data", audioUrl);
        } catch (IOException e) {
            log.error("文字转语音失败", e);
            response.put("success", false);
            response.put("message", "转换失败: " + e.getMessage());
        }
        return response;
    }

    @Schema(description = "TTS转换请求参数")
    @Data
    public static class TtsRequest {
        @Schema(description = "要转换的文本内容", required = true, example = "您好，我是AI医生")
        private String text;
        
        @Schema(description = "语速比例，范围0.5-2.0", example = "1.0")
        private Double speedRatio;
        
        @Schema(description = "音量比例，范围0.5-2.0", example = "1.0")
        private Double volumeRatio;
        
        @Schema(description = "音高比例，范围0.5-2.0", example = "1.0")
        private Double pitchRatio;
        
        @Schema(description = "发音人声音类型", example = "female")
        private String voiceType;
    }

    @Schema(description = "TTS转换响应")
    @Data
    public static class TtsResponse {
        @Schema(description = "是否成功", example = "true")
        private Boolean success;

        @Schema(description = "响应消息", example = "转换成功")
        private String message;

        @Schema(description = "音频文件URL", example = "http://example.com/audio/123.mp3")
        private String data;
    }
} 