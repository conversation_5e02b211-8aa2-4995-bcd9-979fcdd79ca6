<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

    <springProperty scope="context" name="APP_NAME" source="spring.application.name" defaultValue="ai-doctor-prod-app"/>
    <!-- Define a property for the log file path and name -->
    <property name="LOG_PATH" value="logs"/>
    <property name="LOG_FILE_NAME" value="${APP_NAME}"/>

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <!-- File Appender for writing logs directly to a file -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/${LOG_FILE_NAME}.log</file> <!-- Example: /home/<USER>/ai-doctor-prod-app.log -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!-- You can use a pattern similar to CONSOLE_LOG_PATTERN or customize it -->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [${APP_NAME},%X{traceId:-},%X{spanId:-}] [%thread] %-5level %logger{36} - %msg%n</pattern>
            <charset>utf8</charset>
        </encoder>

        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- daily rollover -->
            <fileNamePattern>${LOG_PATH}/archived/${LOG_FILE_NAME}.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>10MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <!-- keep 30 days' worth of history -->
            <maxHistory>30</maxHistory>
            <totalSizeCap>1GB</totalSizeCap> <!-- Optional: Total size cap for archived logs -->
        </rollingPolicy>
    </appender>

    <root level="WARN">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" /> <!-- Add FILE appender to root logger -->
    </root>

    <!-- 统一业务包日志级别为 INFO -->
    <logger name="com.makiyo" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </logger>

    <!-- Specific logger for mappers, will also write to CONSOLE and FILE due to root config -->
    <logger name="com.makiyo.ai_doctor_prod.mapper" level="DEBUG" additivity="false"> <!-- additivity=false means it won't use root's appenders unless specified again -->
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </logger>

    <!-- Specific logger for TtsService -->
    <logger name="com.makiyo.ai_doctor_prod.service.TtsService" level="WARN" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </logger>

</configuration> 