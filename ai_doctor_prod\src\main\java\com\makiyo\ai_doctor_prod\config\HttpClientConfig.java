package com.makiyo.ai_doctor_prod.config;

import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PreDestroy;
import java.util.concurrent.TimeUnit;

/**
 * HTTP Client Configuration
 * Manages connection pooling, connection cleanup, and application lifecycle events
 */
@Configuration
public class HttpClientConfig implements ApplicationListener<ContextClosedEvent> {

    private static final Logger log = LoggerFactory.getLogger(HttpClientConfig.class);

    @Autowired(required = false)
    private RestTemplate restTemplate;

    /**
     * Handle application shutdown event to clean up HTTP connections
     */
    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        log.info("Application context closing - cleaning up HTTP client resources");
        closeHttpClient();
    }

    /**
     * Pre-destroy method to clean up HTTP connections when bean is destroyed
     */
    @PreDestroy
    public void destroy() {
        log.info("HttpClient bean being destroyed - cleaning up resources");
        closeHttpClient();
    }

    /**
     * Scheduled task to clean up expired and idle connections
     * Runs every 5 seconds for more aggressive cleanup
     */
    @Scheduled(fixedRate = 5555)
    public void cleanExpiredAndIdleConnections() {
        try {
            // Only attempt cleanup if RestTemplate is available
            if (restTemplate == null) {
                return;
            }

            // Check if the request factory is of the correct type
            if (!(restTemplate.getRequestFactory() instanceof HttpComponentsClientHttpRequestFactory requestFactory)) {
                return;
            }

            // Try to get the HttpClient
            HttpClient httpClient = requestFactory.getHttpClient();
            if (httpClient == null) {
                return;
            }

            // 记录当前连接状态
            log.debug("Starting HTTP connection cleanup cycle");
            
            // Check if this is a client with a connection manager
            if (HttpClientBuilder.class.isAssignableFrom(httpClient.getClass())) {
                // Access connection manager via reflection if needed
                try {
                    java.lang.reflect.Field connectionManagerField = httpClient.getClass().getDeclaredField("connectionManager");
                    connectionManagerField.setAccessible(true);
                    Object connectionManager = connectionManagerField.get(httpClient);
                    
                    if (connectionManager instanceof PoolingHttpClientConnectionManager manager) {
                        // 获取清理前的连接信息
                        int leased = manager.getTotalStats().getLeased();
                        int available = manager.getTotalStats().getAvailable();
                        int max = manager.getTotalStats().getMax();
                        int pending = manager.getTotalStats().getPending();
                        
                        log.debug("Connection pool before cleanup - Leased: {}, Available: {}, Max: {}, Pending: {}", 
                                leased, available, max, pending);
                        
                        // Close expired connections
                        manager.closeExpiredConnections();
                        
                        // Close connections that have been idle for longer than 10 seconds (更积极的清理)
                        manager.closeIdleConnections(10, TimeUnit.SECONDS);
                        
                        // 获取清理后的连接信息
                        leased = manager.getTotalStats().getLeased();
                        available = manager.getTotalStats().getAvailable();
                        max = manager.getTotalStats().getMax();
                        pending = manager.getTotalStats().getPending();
                        
                        log.debug("Connection pool after cleanup - Leased: {}, Available: {}, Max: {}, Pending: {}", 
                                leased, available, max, pending);
                        
                        // 如果有大量租用的连接，记录警告
                        if (leased > 20) {
                            log.warn("High number of leased connections ({}) detected, possible resource leak", leased);
                        }
                        
                        log.debug("Successfully cleaned expired and idle HTTP connections");
                    }
                } catch (NoSuchFieldException | IllegalAccessException e) {
                    // Field not found or not accessible - try a different approach
                    log.debug("Could not access connection manager through reflection: {}", e.getMessage());
                }
            } else if (httpClient.getClass().getName().contains("InternalHttpClient")) {
                // This is likely Apache HttpClient 4.5+ with a different structure
                log.debug("Detected InternalHttpClient - attempting to force connection cleanup");
                
                // 尝试关闭底层连接池的方法
                try {
                    java.lang.reflect.Method connManagerMethod = httpClient.getClass().getDeclaredMethod("getConnectionManager");
                    connManagerMethod.setAccessible(true);
                    Object connManager = connManagerMethod.invoke(httpClient);
                    
                    if (connManager != null) {
                        java.lang.reflect.Method closeExpireds = connManager.getClass().getDeclaredMethod("closeExpiredConnections");
                        closeExpireds.setAccessible(true);
                        closeExpireds.invoke(connManager);
                        
                        java.lang.reflect.Method closeIdleConns = connManager.getClass().getDeclaredMethod("closeIdleConnections", long.class, TimeUnit.class);
                        closeIdleConns.setAccessible(true);
                        closeIdleConns.invoke(connManager, 10L, TimeUnit.SECONDS);
                        
                        log.debug("Successfully forced cleanup on InternalHttpClient");
                    }
                } catch (Exception e) {
                    log.debug("Could not force connection cleanup on InternalHttpClient: {}", e.getMessage());
                }
            }
        } catch (Exception e) {
            // Don't let exceptions in the cleanup task affect the application
            log.warn("Error cleaning up HTTP connections: {}", e.getMessage());
        }
    }

    /**
     * Close the HTTP client and release all connections
     */
    private void closeHttpClient() {
        try {
            if (restTemplate != null && restTemplate.getRequestFactory() instanceof HttpComponentsClientHttpRequestFactory requestFactory) {
                requestFactory.destroy();
                log.info("Successfully closed HttpClient and released all connections");
            }
        } catch (Exception e) {
            log.error("Error closing HttpClient: {}", e.getMessage(), e);
        }
    }
} 