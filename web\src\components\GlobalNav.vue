<template>
  <div class="global-header">
    <div class="logo">
<!--      <span class="logo-text">明</span> -->
      <span class="company-name"> AI基层儿科医生（内测版）</span>
    </div>
    <div class="menu-bar">
      <a-menu mode="horizontal" class="custom-menu">
        <a-menu-item key="home" @click="router.push('/')">首页</a-menu-item>
        <a-menu-item key="chatV1" @click="navigateToChat('chat')">V1 问诊</a-menu-item>
        <a-menu-item key="chatV2" @click="navigateToChat('chatv2')">V2 问诊</a-menu-item>
        <a-menu-item key="quickChat" @click="navigateToChat('quickchat')">快速问诊</a-menu-item>
        <a-menu-item key="adminData" @click="router.push('/admin-data')">数据管理</a-menu-item>
        <a-menu-item key="allCases" @click="router.push('/all-cases')">APP数据管理</a-menu-item>
        <a-menu-item key="ocrViewer" @click="router.push('/ocr-viewer')">OCR数据</a-menu-item>
<!--        <a-menu-item key="about" @click="router.push('/about')">关于我们</a-menu-item>-->
<!--        <a-menu-item key="contact" @click="router.push('/contact')">联系我们</a-menu-item>-->
      </a-menu>
    </div>
  </div>
</template>

<script>
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user' // 导入用户 store
import { computed } from 'vue'              // 导入 computed
import { message } from 'ant-design-vue'      // 导入 message

export default {
  name: 'GlobalNav',
  setup() {
    const router = useRouter()
    const userStore = useUserStore()         // 获取用户 store 实例
    const userInfo = computed(() => userStore.userInfo) // 获取用户信息

    const navigateToChat = (routeName) => {
      if (userInfo.value?.userId) {
        // 如果用户已登录，导航到对应的聊天页面
        router.push({ name: routeName, params: { userId: userInfo.value.userId } });
      } else {
        // 如果用户未登录，提示用户
        message.warning('请先通过首页登录');
        // 可选：导航到登录页面或首页
        // router.push('/'); 
      }
    };

    return {
      router,
      navigateToChat // 暴露导航函数给模板
    }
  }
}
</script>

<style scoped>
.global-header {
  height: 15vh;
  min-height: 48px;
  max-height: 100px;
  background-color: #fff;
  border-bottom: none;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 100;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  height: 100%;
}

.logo-text {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  font-family: "SimSun", "宋体", serif;
  background: linear-gradient(45deg, #4D6BFE, #3D5BEE);
  color: white;
  border-radius: 8px;
  line-height: 1;
  -webkit-text-fill-color: white;
  transform: rotate(-5deg);
  box-shadow: 2px 2px 8px rgba(77, 107, 254, 0.3);
}

.company-name {
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
  background: linear-gradient(45deg, #4D6BFE, #3D5BEE);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  white-space: nowrap;
  line-height: 1.2;
}

.custom-menu {
  border-bottom: none;
}

.custom-menu :deep(.ant-menu-item) {
  margin: 0 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
  font-weight: 500;
  letter-spacing: 0.3px;
}

.custom-menu :deep(.ant-menu-item:hover) {
  color: #4D6BFE;
  background-color: rgba(77, 107, 254, 0.1);
}

.custom-menu :deep(.ant-menu-item-selected) {
  color: #4D6BFE !important;
  background-color: rgba(77, 107, 254, 0.1);
}
</style> 