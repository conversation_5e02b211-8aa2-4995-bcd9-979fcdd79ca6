package com.makiyo.ai_doctor_prod.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.postgresql.util.PGobject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;

/**
 * MyBatis TypeHandler for PostgreSQL JSONB type.
 * Maps jsonb database type to Java Object (specifically Map<String, Object> in this context).
 */
@MappedJdbcTypes(JdbcType.OTHER) // Bind to JDBC OTHER type as MyBatis doesn't have a specific JSONB type
@MappedTypes(Object.class)      // This handler can work with generic Objects (like Maps)
public class JsonbTypeHandler extends BaseTypeHandler<Object> {

    private static final Logger log = LoggerFactory.getLogger(JsonbTypeHandler.class);
    // Reuse ObjectMapper for efficiency
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final TypeReference<Map<String, Object>> MAP_TYPE_REFERENCE = new TypeReference<>() {};

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Object parameter, JdbcType jdbcType) throws SQLException {
        PGobject jsonObject = new PGobject();
        jsonObject.setType("jsonb"); // Explicitly set the PostgreSQL type
        try {
            // Serialize the Java object (Map) into a JSON String
            String jsonString = objectMapper.writeValueAsString(parameter);
            jsonObject.setValue(jsonString);
            ps.setObject(i, jsonObject); // Pass the PGobject to the JDBC driver
        } catch (JsonProcessingException e) {
            log.error("Error serializing object to JSONB string: {}", parameter, e);
            throw new SQLException("Error serializing object to JSONB string: " + e.getMessage(), e);
        }
    }

    @Override
    public Object getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return parseJson(rs.getObject(columnName));
    }

    @Override
    public Object getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return parseJson(rs.getObject(columnIndex));
    }

    @Override
    public Object getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return parseJson(cs.getObject(columnIndex));
    }

    /**
     * Parses the object retrieved from the database (expected to be JSONB) into a Java Map.
     * @param dbObject The object from the ResultSet or CallableStatement.
     * @return A Map<String, Object> representing the JSON data, or null.
     * @throws SQLException If parsing fails.
     */
    private Object parseJson(Object dbObject) throws SQLException {
        if (dbObject == null) {
            return null;
        }

        String jsonString;
        // The JDBC driver might return PGobject or sometimes just String for json/jsonb
        if (dbObject instanceof PGobject) {
            jsonString = ((PGobject) dbObject).getValue();
        } else if (dbObject instanceof String) {
            jsonString = (String) dbObject;
        } else {
            // Log a warning if the type is unexpected, but try toString() as a fallback
            log.warn("Unexpected object type received from database for JSONB column: {}. Attempting toString() conversion.",
                     dbObject.getClass().getName());
            jsonString = dbObject.toString();
            // Alternatively, throw an exception for strict handling:
            // throw new SQLException("Unexpected type received for JSONB column: " + dbObject.getClass().getName());
        }

        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }

        try {
            // Deserialize the JSON string into a Map<String, Object>
            // Change MAP_TYPE_REFERENCE if your Java object structure is different
            return objectMapper.readValue(jsonString, MAP_TYPE_REFERENCE);
        } catch (IOException e) {
            log.error("Error parsing JSONB string from database: {}", jsonString, e);
            throw new SQLException("Error parsing JSONB string from database: " + e.getMessage(), e);
        }
    }
} 