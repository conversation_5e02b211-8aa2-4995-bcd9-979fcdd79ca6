package com.makiyo.ai_doctor_prod.form;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import java.util.Map;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

public class AddTestRecommendationRequest {

    @Schema(description = "CaseItem 的唯一标识符", required = true, example = "case_uuid_123")
    @NotBlank(message = "案例 ID 不能为空")
    private String id;

    @Schema(description = "要添加到 interaction_history.test_recommendation 的检查建议列表，每个建议是一个包含详细信息的 Map", required = true,
            example = "[{\"项目名称\":\"肺炎支原体(MP)\",\"结果\":\"阴性\", ...}, {\"项目名称\":\"甲型流感病毒(InfA)\",\"结果\":\"阴性\", ...}]")
    @NotEmpty(message = "检查建议列表不能为空")
    @NotNull // 确保列表本身不为null
    private List<Map<String, Object>> testRecommendation;

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public List<Map<String, Object>> getTestRecommendation() {
        return testRecommendation;
    }

    public void setTestRecommendation(List<Map<String, Object>> testRecommendation) {
        this.testRecommendation = testRecommendation;
    }
} 