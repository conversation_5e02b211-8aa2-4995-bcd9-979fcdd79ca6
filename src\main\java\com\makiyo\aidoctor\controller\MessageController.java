package com.makiyo.aidoctor.controller;

import com.makiyo.aidoctor.entity.Message;
import com.makiyo.aidoctor.response.Response;
import com.makiyo.aidoctor.service.MessageService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import org.springframework.http.MediaType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.util.List;
import java.util.Map;

@Tag(name = "消息管理", description = "提供消息相关的接口，包括获取会话消息和与AI医生对话")
@RestController
@RequestMapping("/message")
public class MessageController {

    private static final Logger log = LoggerFactory.getLogger(MessageController.class);

    @Resource
    private MessageService messageService;

    @Operation(
        summary = "获取会话的所有消息",
        description = "根据会话ID获取该会话下的所有历史消息记录，按时间顺序排列"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "成功获取消息列表",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = Response.class)
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "无效的会话ID"
        ),
        @ApiResponse(
            responseCode = "500",
            description = "服务器内部错误"
        )
    })
    @GetMapping("/getSessionMessages")
    public Response<List<Message>> getSessionMessages(
        @Parameter(description = "会话ID", required = true, example = "1")
        @RequestParam Integer sessionId
    ) {
        try {
            List<Message> messages = messageService.getSessionMessages(sessionId);
            return Response.ok(messages);
        } catch (Exception e) {
            return Response.fail("获取会话消息失败：" + e.getMessage());
        }
    }

    @Operation(
        summary = "与AI医生对话",
        description = "发送消息给AI医生并获取流式响应。使用Server-Sent Events (SSE)实现实时对话。" +
                    "系统会保存用户发送的消息，并将AI医生的回复通过SSE流式返回。" +
                    "每个消息都会被保存到数据库中，并与特定的会话关联。"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200", 
            description = "成功建立SSE连接",
            content = @Content(
                mediaType = MediaType.TEXT_EVENT_STREAM_VALUE
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "请求参数无效"
        ),
        @ApiResponse(
            responseCode = "500",
            description = "服务器内部错误或AI服务连接失败"
        )
    })
    @GetMapping(value = "/chat", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter chatWithAiDoctor(
        @Parameter(description = "用户ID", required = true, example = "1")
        @RequestParam @NotNull(message = "用户ID不能为空") 
        @Positive(message = "用户ID必须为正整数") Integer userId,
        
        @Parameter(description = "会话ID", required = true, example = "1")
        @RequestParam @NotNull(message = "会话ID不能为空") 
        @Positive(message = "会话ID必须为正整数") Integer sessionId,
        
        @Parameter(description = "用户发送的消息内容", required = true, example = "我最近感觉头痛")
        @RequestParam @NotNull(message = "消息内容不能为空") String message
    ) {
        return messageService.chatAiDoctor(sessionId, userId, message);
    }

    @Operation(
        summary = "与AI医生进行问诊对话",
        description = "发送消息给AI医生进行问诊并获取流式响应。使用Server-Sent Events (SSE)实现实时问诊对话。" +
                    "系统会保存用户发送的消息，并将AI医生的问诊回复通过SSE流式返回。" +
                    "每个消息都会被保存到数据库中，并与特定的会话关联。"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200", 
            description = "成功建立SSE连接",
            content = @Content(
                mediaType = MediaType.TEXT_EVENT_STREAM_VALUE
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "请求参数无效"
        ),
        @ApiResponse(
            responseCode = "500",
            description = "服务器内部错误或AI服务连接失败"
        )
    })
    @GetMapping(value = "/inquiry", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter inquiryWithAiDoctor(
        @Parameter(description = "用户ID", required = true, example = "1")
        @RequestParam @NotNull(message = "用户ID不能为空") 
        @Positive(message = "用户ID必须为正整数") Integer userId,
        
        @Parameter(description = "会话ID", required = true, example = "1")
        @RequestParam @NotNull(message = "会话ID不能为空") 
        @Positive(message = "会话ID必须为正整数") Integer sessionId,
        
        @Parameter(description = "用户发送的问诊消息内容", required = true, example = "我最近感觉头痛")
        @RequestParam @NotNull(message = "消息内容不能为空") String message
    ) {
        return messageService.inquiryAiDoctor(sessionId, userId, message);
    }

    @Operation(
        summary = "与AI医生进行诊断对话",
        description = "发送诊断方案给AI医生并获取流式响应。使用Server-Sent Events (SSE)实现实时诊断对话。" +
                    "系统会将AI医生的诊断回复通过SSE流式返回。" +
                    "每个消息都会被保存到数据库中，并与特定的会话关联。"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200", 
            description = "成功建立SSE连接",
            content = @Content(
                mediaType = MediaType.TEXT_EVENT_STREAM_VALUE
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "请求参数无效"
        ),
        @ApiResponse(
            responseCode = "500",
            description = "服务器内部错误或AI服务连接失败"
        )
    })
    @GetMapping(value = "/diagnose", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter diagnoseWithAiDoctor(
        @Parameter(description = "用户ID", required = true, example = "1")
        @RequestParam @NotNull(message = "用户ID不能为空") 
        @Positive(message = "用户ID必须为正整数") Integer userId,
        
        @Parameter(description = "会话ID", required = true, example = "1")
        @RequestParam @NotNull(message = "会话ID不能为空") 
        @Positive(message = "会话ID必须为正整数") Integer sessionId,
        
        @Parameter(description = "医生文本", required = true, example = "患者病例补充...")
        @RequestParam  String solutionText
    ) {
        return messageService.diagnoseAiDoctor(sessionId, userId, solutionText);
    }
    
    @Operation(
        summary = "与AI医生V2进行问诊对话",
        description = "发送消息给AI医生V2进行问诊并获取流式响应。使用Server-Sent Events (SSE)实现实时问诊对话。" +
                    "系统会保存用户发送的消息，并将AI医生的问诊回复通过SSE流式返回。" +
                    "每个消息都会被保存到数据库中，并与特定的会话关联。"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200", 
            description = "成功建立SSE连接",
            content = @Content(
                mediaType = MediaType.TEXT_EVENT_STREAM_VALUE
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "请求参数无效"
        ),
        @ApiResponse(
            responseCode = "500",
            description = "服务器内部错误或AI服务连接失败"
        )
    })
    @GetMapping(value = "/chat_ai_doctor_v2", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter chatWithAiDoctorV2(
        @Parameter(description = "会话ID", required = true, example = "1")
        @RequestParam @NotNull(message = "会话ID不能为空") 
        @Positive(message = "会话ID必须为正整数") Integer sessionId,
        
        @Parameter(description = "用户发送的问诊消息内容", required = true, example = "我最近感觉头痛")
        @RequestParam @NotNull(message = "消息内容不能为空") String message
    ) {
        return messageService.inquiryAiDoctorV2(sessionId, message);
    }

    @Operation(
        summary = "与AI医生V2进行快速问诊对话",
        description = "发送消息给AI医生V2进行快速问诊并获取流式响应（快速模式）。"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "成功建立SSE连接"),
        @ApiResponse(responseCode = "400", description = "请求参数无效"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误或AI服务连接失败")
    })
    @GetMapping(value = "/chat_ai_doctor_v2_quick", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter chatWithAiDoctorV2Quick(
        @Parameter(description = "会话ID", required = true, example = "1")
        @RequestParam @NotNull(message = "会话ID不能为空") 
        @Positive(message = "会话ID必须为正整数") Integer sessionId,
        
        @Parameter(description = "用户发送的问诊消息内容", required = true, example = "我最近感觉头痛")
        @RequestParam @NotNull(message = "消息内容不能为空") String message
    ) {
        log.info("收到 V2 快速问诊请求，Session ID: {}, Message: {}", sessionId, message);
        return messageService.inquiryAiDoctorV2Quick(sessionId, message);
    }

    @Operation(
        summary = "与AI医生V2进行诊断",
        description = "根据会话ID获取完整的交互历史，调用V2诊断服务获取诊断、病情和治疗方案。"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "成功获取诊断结果",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = Response.class))
        ),
        @ApiResponse(
            responseCode = "400",
            description = "请求参数无效或消息历史结构错误"
        ),
        @ApiResponse(
            responseCode = "404",
            description = "未找到指定会话的消息记录"
        ),
         @ApiResponse(
            responseCode = "500",
            description = "服务器内部错误或AI服务连接/处理失败"
        ),
         @ApiResponse(
            responseCode = "503",
            description = "无法连接到AI诊断服务"
        )
    })
    @PostMapping("/diagnoseV2") // Using POST as it triggers a complex process
    public Response<?> diagnoseWithAiDoctorV2(
        @Parameter(description = "会话ID", required = true, example = "1")
        @RequestParam @NotNull(message = "会话ID不能为空")
        @Positive(message = "会话ID必须为正整数") Integer sessionId
    ) {
        log.info("收到 V2 诊断请求，Session ID: {}", sessionId);
        try {
            Map<String, Object> result = messageService.diagnoseAiDoctorV2(sessionId);
            
            if (result.containsKey("error")) {
                log.warn("V2 诊断失败 (Session ID: {}): {}", sessionId, result.get("error"));
                // Determine appropriate status code based on error message if needed
                return Response.fail("V2 诊断失败: " + result.get("error"));
            } else {
                log.info("V2 诊断成功 (Session ID: {})", sessionId);
                return Response.ok(result); // Contains diagnosis, condition, treatment_plan
            }
        } catch (Exception e) {
            log.error("处理 V2 诊断请求时发生控制器异常 (Session ID: {}): {}", sessionId, e.getMessage(), e);
            return Response.fail("处理 V2 诊断请求时发生服务器内部错误");
        }
    }

    @Operation(
        summary = "添加医生补充信息",
        description = "向指定会话的最新消息记录中添加医生提供的补充信息。"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "成功添加补充信息",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = Response.class))
        ),
        @ApiResponse(
            responseCode = "400",
            description = "请求参数无效或补充信息为空"
        ),
        @ApiResponse(
            responseCode = "404",
            description = "未找到指定会话的消息记录"
        ),
        @ApiResponse(
            responseCode = "500",
            description = "服务器内部错误（如JSON处理失败或数据库更新失败）"
        )
    })
    @PostMapping("/add_supplementary_info")
    public Response<?> addDoctorSupplementaryInfo(
        @Parameter(description = "会话ID", required = true, example = "1")
        @RequestParam @NotNull(message = "会话ID不能为空")
        @Positive(message = "会话ID必须为正整数") Integer sessionId,

        @Parameter(description = "医生补充信息", required = true, example = "患者既往有高血压病史")
        @RequestParam @NotNull(message = "补充信息不能为空") String supplementary_info
    ) {
        try {
            boolean success = messageService.addDoctorSupplementaryInfo(sessionId, supplementary_info);
            if (success) {
                return Response.ok("补充信息添加成功");
            } else {
                // Service method handles logging details, return a generic failure response
                return Response.fail("添加补充信息失败，请检查日志获取详情");
            }
        } catch (Exception e) {
            log.error("添加医生补充信息时发生控制器异常 (Session ID: {}): {}", sessionId, e.getMessage(), e);
            return Response.fail("添加补充信息时发生服务器内部错误");
        }
    }

    @Operation(
        summary = "与AI医生V2进行快速诊断",
        description = "根据会话ID获取完整的交互历史，调用V2快速诊断服务获取诊断、病情和治疗方案。"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "成功获取诊断结果",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = Response.class))
        ),
        @ApiResponse(
            responseCode = "400",
            description = "请求参数无效或消息历史结构错误"
        ),
        @ApiResponse(
            responseCode = "404",
            description = "未找到指定会话的消息记录"
        ),
         @ApiResponse(
            responseCode = "500",
            description = "服务器内部错误或AI服务连接/处理失败"
        ),
         @ApiResponse(
            responseCode = "503",
            description = "无法连接到AI诊断服务"
        )
    })
    @PostMapping("/diagnoseV2_quick") // Using POST as it triggers a complex process
    public Response<?> diagnoseWithAiDoctorV2Quick(
        @Parameter(description = "会话ID", required = true, example = "1")
        @RequestParam @NotNull(message = "会话ID不能为空")
        @Positive(message = "会话ID必须为正整数") Integer sessionId
    ) {
        log.info("收到 V2 快速诊断请求，Session ID: {}", sessionId);
        try {
            Map<String, Object> result = messageService.diagnoseAiDoctorV2Quick(sessionId);
            
            if (result.containsKey("error")) {
                log.warn("V2 快速诊断失败 (Session ID: {}): {}", sessionId, result.get("error"));
                // Determine appropriate status code based on error message if needed
                return Response.fail("V2 快速诊断失败: " + result.get("error"));
            } else {
                log.info("V2 快速诊断成功 (Session ID: {})", sessionId);
                return Response.ok(result); // Contains diagnosis, condition, treatment_plan
            }
        } catch (Exception e) {
            log.error("处理 V2 快速诊断请求时发生控制器异常 (Session ID: {}): {}", sessionId, e.getMessage(), e);
            return Response.fail("处理 V2 快速诊断请求时发生服务器内部错误");
        }
    }

    @Operation(
        summary = "与AI医生V2进行拟诊",
        description = "根据会话ID获取完整的交互历史，调用V2拟诊服务获取诊断、检查建议和指南内容。"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "成功获取拟诊结果",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = Response.class))
        ),
        @ApiResponse(
            responseCode = "400",
            description = "请求参数无效或消息历史结构错误"
        ),
        @ApiResponse(
            responseCode = "404",
            description = "未找到指定会话的消息记录"
        ),
         @ApiResponse(
            responseCode = "500",
            description = "服务器内部错误或AI服务连接/处理失败"
        ),
         @ApiResponse(
            responseCode = "503",
            description = "无法连接到AI拟诊服务"
        )
    })
    @PostMapping("/preliminaryDiagnoseV2") 
    public Response<?> preliminaryDiagnoseWithAiDoctorV2(
        @Parameter(description = "会话ID", required = true, example = "1")
        @RequestParam @NotNull(message = "会话ID不能为空")
        @Positive(message = "会话ID必须为正整数") Integer sessionId
    ) {
        try {
            Map<String, Object> result = messageService.preliminaryDiagnoseAiDoctorV2(sessionId);
            
            if (result.containsKey("error")) {
                String errorMsg = (String) result.get("error");
                log.error("V2 拟诊失败: {}", errorMsg);
                
                if (errorMsg.contains("未找到会话消息记录")) {
                    return Response.error(404, errorMsg);
                } else if (errorMsg.contains("消息内容格式错误") || errorMsg.contains("消息格式错误")) {
                    return Response.error(400, errorMsg);
                } else if (errorMsg.contains("连接 V2 拟诊服务失败")) {
                    return Response.error(503, errorMsg);
                } else {
                    return Response.error(500, errorMsg);
                }
            }
            
            return Response.success(result);
            
        } catch (Exception e) {
            log.error("处理 V2 拟诊请求时发生异常: {}", e.getMessage(), e);
            return Response.error(500, "处理拟诊请求时发生异常: " + e.getMessage());
        }
    }
}
