"""
Executor module for the AI Doctor system.
"""

import asyncio
import re
from src.logger import get_logger
from src.llm_client import LLMClient
from src.planner import clean_r1_response
from src.config import (
    OPENAI_API_KEY, OPENAI_MODEL, OPENAI_TEMPERATURE,
    VOLCANO_API_KEY, VOLCANO_API_BASE, VOLCANO_MODEL, VOLCANO_TEMPERATURE_PLANNER, VOLCANO_TEMPERATURE_EXECUTOR,
    CLAUDE_API_KEY, CLAUDE_MODEL, CLAUDE_TEMPERATURE,
    QWEN_API_KEY, QWEN_API_BASE, QWEN_MODEL, QWEN_TEMPERATURE
)
from src.prompts import (
    _sys_prompt_patient, _sys_prompt_question, _user_prompt_question,
    _sys_prompt_update_observation_executor, _user_prompt_update_observation_executor,
    _initial_inquiry_guide
)

logger = get_logger("executor")


doctor_info_default = """
女，12岁0月3天，就诊日期2024/3/20&#xA;【主诉】4月前反复咳嗽，初夜咳嗽，运动加重。1月前，外院诊断肺炎？，无发热，有啰音。28/2胸片：右下较左侧斑点影。20/3肺功能（-）；心电图：窦性心动过速。口服仙特明+孟鲁司特1周，眼睛刚开始痒。既往常年清嗓子&#xA;【现病史】&#xA;【既往史】&#xA;【传染病接触史】无&#xA;【个人史】&#xA;【家族史】无&#xA;【体格检查】见专科查体&#xA;【专科查体】精神反应好，咽部无异常，未见呼吸困难，双肺呼吸音清，心腹（-）。&#xA;【辅助检查】20/3血常规+CRP：（-）；
"""

class Executor:
    """
    The Executor is responsible for interacting with patients based on strategies provided by the planner.
    """

    def __init__(self, max_rounds=10, doctor_info=None, interaction_history=None):
        """
        Initialize the Executor.
        
        Args:
            max_rounds (int): Maximum number of interaction rounds.
            doctor_info (str): Doctor information.
            interaction_history: Global interaction history object.
        """
        self.max_rounds = max_rounds
        self.doctor_info = doctor_info if doctor_info is not None else doctor_info_default
        self.interaction_history = interaction_history
        self.quick_demo_mode = False  # 添加快速演示模式标志
        logger.info(f"Executor initialized with max_rounds={max_rounds}")
    


    async def next_question(self):
        question = await self._generate_question()
        feedback, question, end_of_inquiry = self._extract_observation_feedback(question)
        return feedback, question, end_of_inquiry
    
    async def inquire(self):
        """
        根据当前策略执行询问过程。

        返回:
            tuple: (last_question, end_of_inquiry)
        """
        logger.info("Starting inquiry process")

        end_of_inquiry = False

        for _ in range(self.max_rounds):
            # 根据当前策略和对话历史生成问题
            
            current_round = (len(self.interaction_history.cot_entries[-1]["dialogue_history"]) + 1) // 2 + 1
            # print(f"当前轮数：{current_round}")
            if current_round == 1 and len(self.interaction_history.cot_entries) == 1: 
                question = "<question>您好，我是本次的医生，请问孩子的年龄，性别，主要的症状是什么？</question>"
                
            else:
                while True:
                    feedback, question, end_of_inquiry = await self.next_question()
                    if question or end_of_inquiry:
                        break
                    logger.warning("Generated question and end_of_inquiry are both False. Regenerating question.")

            logger.debug(f"Generated question: {question}")

            print("\nAI Doctor:", question)

            if question:
                self.interaction_history.cot_entries[-1]["dialogue_history"].append({"role": "doctor", "content": question})
            if end_of_inquiry:
                logger.info("End of inquiry reached")
                break

            # 获取患者响应
            print("\nPatient (you): ", end="")
            # user_input = input().strip()
            # if user_input:
            #     response = user_input
            # else:
            #     response = await self._AI_patient_response()
            #     logger.info(f"AI_patient_response: {response}")
            response = await self._AI_patient_response()
            print("\nAI Patient:", response)
            logger.debug(f"AI_Patient response: {response}")
            self.interaction_history.cot_entries[-1]["dialogue_history"].append({"role": "patient", "content": response})

        # 如果在没有自然结束的情况下达到最大轮次，强制结束询问
        if not end_of_inquiry:
            logger.info(f"Reached maximum rounds ({self.max_rounds}) for this strategy")
            end_of_inquiry = True

        # 更新 InteractionHistory
        self.interaction_history.cot_entries[-1]["feedback"] = feedback  # 更新反馈

        return feedback, end_of_inquiry
    
    async def _AI_patient_response(self):
        """
        生成 AI 患者的响应。

        返回:
            str: AI 患者的响应。
        """
        sysprompt = _sys_prompt_patient

        doctor_info = self.doctor_info
        
        user_prompt = f"<info>\n{doctor_info}\n</info>\n"


        #将InteractionHistory中的每一个entry的对话历史添加到user_prompt中，而不仅仅是最后一个entry
        for entry in self.interaction_history.cot_entries:
            for dialogue in entry["dialogue_history"]:
                if dialogue["role"] == "doctor":
                    user_prompt += f"<observation>\n{dialogue['content']}\n</observation>\n"
                elif dialogue["role"] == "patient":
                    user_prompt += f"<text>\n{dialogue['content']}\n</text>\n"
            user_prompt += "\n"

        # logger.debug(f"AI_patient输入{user_prompt}\n")
        AI_patient_response = await LLMClient.generate_response(provider="qwen", prompt=user_prompt, system_message=sysprompt, temperature=QWEN_TEMPERATURE)
        AI_patient_response = re.search(r"<text>(.*?)</text>", AI_patient_response, re.DOTALL)
        if AI_patient_response:
            AI_patient_response = AI_patient_response.group(1).strip()
        else:
            AI_patient_response = ""
        return AI_patient_response

    def _extract_observation_feedback(self, question):
        """
        从问题文本中提取观察反馈和问题。

        参数:
            question (str): 包含问题和反馈的文本。

        返回:
            tuple: (feedback, question, end_of_inquiry)
        """
        # 使用正则表达式提取 feedback 和 question
        feedback_match = re.search(r'<feedback>(.*?)</feedback>', question, re.DOTALL)
        question_match = re.search(r'<question>(.*?)</question>', question, re.DOTALL)

        feedback = feedback_match.group(1).strip() if feedback_match else ""
        question_text = question_match.group(1).strip() if question_match else ""
        
        # 计算当前轮数
        current_round = 0
        if self.interaction_history.cot_entries:
            dialogue_history = self.interaction_history.cot_entries[-1]["dialogue_history"]
            current_round = (len(dialogue_history) + 1) // 2 + 1
        
        # 确定是否结束询问的逻辑
        end_of_inquiry = False
        
        # 原始逻辑：如果有反馈则结束
        if feedback:
            end_of_inquiry = True
            logger.info("End of inquiry set to True due to feedback present")
        
        # 安全机制：如果达到最大轮数则强制结束
        elif current_round >= self.max_rounds:
            end_of_inquiry = True
            feedback = f"已达到最大问诊轮数({self.max_rounds})，结束当前策略的问诊。基于已收集的信息，当前策略执行完毕。"
            logger.info(f"End of inquiry forced to True due to max rounds ({self.max_rounds}) reached")
        
        # 快速演示模式的额外逻辑
        elif hasattr(self, 'quick_demo_mode') and self.quick_demo_mode and current_round >= 3:
            end_of_inquiry = True
            feedback = "快速演示模式：已收集基本信息，结束当前策略问诊。"
            logger.info("End of inquiry set to True due to quick demo mode limit")
        
        return feedback, question_text, end_of_inquiry


    async def _generate_question(self):
        """
        根据当前策略和对话历史生成问题。
        
        返回:
            str: 生成的问题。
        """
        # 为 LLM 构建提示
        prompt = self._construct_question_prompt()
        
        # 从 LLM 获取响应
        system_message = _sys_prompt_question
        
        response = await LLMClient.generate_response(provider="jiuzhang", prompt=prompt, system_message=system_message, temperature=VOLCANO_TEMPERATURE_EXECUTOR,model="deepseek-reasoner")
        
        # 清理R1模型的响应，移除推理过程
        cleaned_response = clean_r1_response(response)
        
        return cleaned_response
    
    def _construct_question_prompt(self):
        """
        构建用于生成下一个问题的提示。
        
        返回:
            str: 构建的提示。
        """

        history = ""

        current_strategy = self.interaction_history.cot_entries[-1]['strategy']
        history += f"## 当前策略：\n{current_strategy}\n\n"
        
        is_initial_strategy = len(self.interaction_history.cot_entries) == 1
        
        if is_initial_strategy and "询问患者的基本信息" in current_strategy:
            history += _initial_inquiry_guide + "\n\n"

        if self.interaction_history.cot_entries[-1]["observation"]:
            history += f"## 当前患者病史：\n{self.interaction_history.cot_entries[-1]['observation']}\n\n"
        
        # 添加医生补充信息
        if self.interaction_history.doctor_supplementary_info:
            history += "## 医生补充信息：\n"
            for info in self.interaction_history.doctor_supplementary_info:
                history += f"- {info}\n"
            history += "\n"

        # 添加对话历史，包括所有策略的对话历史
        for entry in self.interaction_history.cot_entries:
            history += f"## 当前对话历史：\n"
            for dialogue in entry["dialogue_history"]:
                role = "AI医生" if dialogue["role"] == "doctor" else "患者"
                history += f"{role}: {dialogue['content']}\n"
            history += "\n"
        
        # 添加指令
        prompt = _user_prompt_question.format(history=history)

        # 当是初始策略且对话轮数小于5轮时，增加提示词
        # 计算当前对话轮数，每一对医生问题和患者回答算一轮
        # dialogue_history中每两条记录（医生+患者）算一轮对话
        # 如果最后一条是医生的问题还没有患者回答，算作当前轮
        dialogue_history = self.interaction_history.cot_entries[-1]["dialogue_history"]
        current_round = (len(dialogue_history) + 1) // 2 + 1
        
        # 在快速演示模式下，减少问诊轮次，快速进入反馈阶段
        if hasattr(self, 'quick_demo_mode') and self.quick_demo_mode:
            if is_initial_strategy and current_round < 3:  # 快速模式下减少到3轮
                prompt += """\n## 快速演示模式提示！！
当前处于快速演示模式，请快速收集关键信息后进入反馈阶段：
1. 优先询问最核心的症状信息
2. 避免过度详细的问诊
3. 在获得基本信息后应尽快生成<feedback>
4. 推进到下一阶段（查体或诊断）
                """
            elif current_round >= 3:  # 快速模式下3轮后强制结束
                prompt += """
                ## 快速演示模式强制结束提示！！
                当前处于快速演示模式且已收集基本信息，必须生成<feedback>结束当前策略！
                不要再提出新的问题，直接总结当前策略的问诊成果。
                """
        else:
            if is_initial_strategy and current_round < 6:
                prompt += """\n## 继续问诊提示！！
你对于患者现病史的信息收集太简单！不要feedback，仔细考虑问诊指导中的内容是否已全部询问，确保收集完整的患者信息。如有遗漏，请继续询问，不要过早feedback！
                """

        if current_round >= self.max_rounds - 1:
            prompt += """
            ## 强制结束提示！！
            当前策略已到达最后一轮，不要再提出新的问题！！！
            你必须选择生成<feedback>，总结当前策略的问诊成果，并更新患者病史。
            """
        return prompt
    
    async def update_observation(self):
        """
        根据对话历史和策略更新全局观察。
        
        返回:
            str: 更新后的观察。
        """
        logger.info("Updating observation")
        
        # 为 LLM 构建提示
        prompt = self._construct_observation_prompt()
        
        # 从 LLM 获取响应
        system_message = _sys_prompt_update_observation_executor
        
        response = await LLMClient.generate_response(provider="jiuzhang", prompt=prompt, system_message=system_message, temperature=VOLCANO_TEMPERATURE_EXECUTOR, model="deepseek-reasoner")
        
        # 清理R1模型的响应，移除推理过程
        cleaned_response = clean_r1_response(response)
        
        # 用新的综合观察替换整个观察
        # self.observation = response

        self.interaction_history.cot_entries[-1]["observation"] = cleaned_response # 更新观察
        
        # logger.debug(f"Updated observation: {cleaned_response}...")
        return cleaned_response
    
    def _construct_observation_prompt(self):
        """
        构建用于更新观察的提示。
        
        返回:
            str: 构建的提示。
        """
        prompt = "根据以下信息，更新患者观察记录：\n\n"
        
        # 添加当前策略
        prompt += f"当前诊断策略：\n{self.interaction_history.cot_entries[-1]['strategy']}\n\n"
        if "feedback" in self.interaction_history.cot_entries[-1]:
            prompt += f"当前策略反馈：\n{self.interaction_history.cot_entries[-1]['feedback']}\n\n"
        # 添加当前观察
        if self.interaction_history.cot_entries[-1]["observation"]:
            prompt += f"当前患者观察：\n{self.interaction_history.cot_entries[-1]['observation']}\n\n"

        # 添加检查字段
        if hasattr(self.interaction_history, 'test_recommendation') and self.interaction_history.test_recommendation:
            prompt += "医生提供的检查信息：\n"
            for test in self.interaction_history.test_recommendation:
                prompt += f"- 项目名称：{test.get('项目名称', '')}\n"
                if '结果' in test:
                    prompt += f"  结果：{test.get('结果', '')}\n"
                if '结果状态' in test:
                    prompt += f"  结果状态：{test.get('结果状态', '')}\n"
                if '参考范围' in test:
                    prompt += f"  参考范围：{test.get('参考范围', '')}\n"
                if '单位' in test:
                    prompt += f"  单位：{test.get('单位', '')}\n"
                if '检测方法' in test:
                    prompt += f"  检测方法：{test.get('检测方法', '')}\n"
                if '样本采集时间' in test:
                    prompt += f"  样本采集时间：{test.get('样本采集时间', '')}\n"
                if '异常标记' in test and test.get('异常标记', ''):
                    prompt += f"  异常标记：{test.get('异常标记', '')}\n"
                if '诊断' in test and test.get('诊断', ''):
                    prompt += f"  诊断：{test.get('诊断', '')}\n"
                if '处理建议' in test and test.get('处理建议', ''):
                    prompt += f"  处理建议：{test.get('处理建议', '')}\n"
                if '检查描述' in test and test.get('检查描述', ''):
                    prompt += f"  检查描述：{test.get('检查描述', '')}\n"
                if '其他' in test and test.get('其他', ''):
                    prompt += f"  其他：{test.get('其他', '')}\n"
            prompt += "\n"

        # 添加医生补充信息
        if hasattr(self.interaction_history, 'doctor_supplementary_info') and self.interaction_history.doctor_supplementary_info:
            prompt += "医生补充信息：\n"
            for info in self.interaction_history.doctor_supplementary_info:
                prompt += f"- {info}\n"
            prompt += "\n"
        
        # 添加所有策略的对话历史
        if len(self.interaction_history.cot_entries) > 0:
            prompt += "所有问诊历史：\n"
            for i, entry in enumerate(self.interaction_history.cot_entries):
                for question in entry["dialogue_history"]:
                    if question["role"] == "doctor":
                        prompt += f"问题: {question['content']}\n"
            prompt += "\n"
        
        # 添加当前对话历史
        if len(self.interaction_history.cot_entries) > 0:
            prompt += "当前对话历史：\n"
            for i, entry in enumerate(self.interaction_history.cot_entries):
                for dialogue in entry["dialogue_history"]:
                    role = "AI医生" if dialogue["role"] == "doctor" else "患者"
                    prompt += f"{role}: {dialogue['content']}\n"
                prompt += "\n"
        
        # 添加指令
        prompt += _user_prompt_update_observation_executor
        
        return prompt
    
    def get_observation(self):
        """
        获取当前观察。

        返回:
            str: 当前观察。
        """
        return self.interaction_history.cot_entries[-1]["observation"] if self.interaction_history.cot_entries else ""
    
    def format_dialogue_history(self):
        """
        将对话历史格式化为可读格式。

        返回:
            str: 格式化的对话历史。
        """

        formatted_history = "对话记录：\n"
        if len(self.interaction_history.cot_entries) > 0:
            for entry in self.interaction_history.cot_entries:
                for dialogue in entry["dialogue_history"]:
                    role = "AI医生" if dialogue["role"] == "doctor" else "患者"
                    formatted_history += f"{role}: {dialogue['content']}\n"

        else:
            formatted_history += "没有对话记录。\n"

        return formatted_history.strip()