from typing import Dict, List, Tuple
from loguru import logger
from llm_client import LLMFactory
from markdown_parser import parse_markdown_titles
import os
import re
import json

class ContentAnalyzer:
    """内容分析器"""
    
    def __init__(self, llm_client_type: str = "volcano"):
        """
        初始化内容分析器
        
        Args:
            llm_client_type: LLM客户端类型
        """
        logger.info(f"初始化内容分析器，LLM类型: {llm_client_type}")
        self.llm_client = LLMFactory.create_client(llm_client_type)
        self.topic = "治疗方案"  # 固定主题
        
    def preprocess_file(self, file_path: str) -> str:
        """
        预处理markdown文件，删除参考文献部分
        
        Args:
            file_path: 原始文件路径
            
        Returns:
            str: 预处理后的文件路径
        """
        logger.info(f"开始预处理文件: {file_path}")
        
        # 创建预处理后的文件路径
        base_name = os.path.basename(file_path)
        preprocessed_path = os.path.join(
            os.path.dirname(file_path),
            f"preprocessed_{base_name}"
        )
        
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()


        lines_count = len(lines)
        logger.info(f"文件 {file_path} 共有 {lines_count} 行")
            
        # 查找参考文献
        ref_markers = [
            r'^#\s*参考文献\s*',
            r'^#\s*references\s*',
            r'^#\s*References\s*',
            r'^##\s*参考文献\s*',
            r'^##\s*references\s*',
            r'^##\s*References\s*',
            r'^##\s*REFERENCES\s*',
            r'^参考文献\s*',
            r'^references\s*',
            r'^References\s*',
            r'^参\s*考\s*文\s*献\s*',
            r'^【参考文献】\s*',
            r'^\[参考文献\]\s*',
            r'^【参\s*考\s*文\s*献】\s*',
            r'^【References】\s*',
            r'^\[References\]\s*',
            r'^【references】\s*',
            r'^\[references\]\s*',
            r'^\[参\s*考\s*文\s*献\]\s*',
            r'^#\s*References²\s*',
            r'^##\s*参\s*考\s*文\s*献\s*',
            r'^##\s*References²\s*',
        ]

        # 查找致谢
        acknowledgments_markers = [
            r'^#\s*致谢\s*',
            r'^#\s*acknowledgments\s*',
            r'^#\s*acknowledgements\s*',
            r'^##\s*致谢\s*',
            r'^##\s*acknowledgments\s*',
            r'^##\s*acknowledgements\s*',
            r'^致谢\s*',
            r'^acknowledgments\s*',
            r'^acknowledgements\s*',
            r'^【致谢】\s*',
            r'^\[致谢\]\s*',
            r'^Acknowledgments\s*',
            r'^【Acknowledgments】\s*',
            r'^\[Acknowledgments\]\s*',
            r'^【Acknowledgements】\s*',
            r'^\[Acknowledgements\]\s*',
            r'^##\s*ACKNOWLEDGMENTS\s*',
            r'^致\s*谢\s*',
            r'^ACKNOWLEDGMENTS\s*',
            r'^ACKNOWLEDGEMENTS\s*',
            r'^##\s*Acknowledgements\s*',
            r'^##\s*ACKNOWLEDGEMENTS\s*',
        ]

        # 查找专家组成员
        experts_markers = [
            r'^#\s*专家组成员\s*',
            r'^#\s*专家组\s*',
            r'^##\s*专家组成员\s*',
            r'^##\s*专家组\s*',
            r'^专家组成员\s*',
            r'^专家组\s*',
            r'^【专家组成员】\s*',
            r'^\[专家组成员\]\s*',
            r'^#\s*指南执笔人\s*',
            r'^#\s*执笔人\s*',
            r'^##\s*指南执笔人\s*',
            r'^##\s*执笔人\s*',
            r'^指南执笔人\s*',
            r'^执笔人\s*',
            r'^【指南执笔人】\s*',
            r'^\[指南执笔人\]\s*',
            r'^##\s*编写审定专家名单\s*',
            r'^执笔专家\s*',
            r'^核心专家组成员\s*',
            r'^核心专家组\s*',
            r'^指南指导组\s*',
            r'^指南指导委员会\s*',
            r'^指南专家组\s*',
            r'^指南工作组\s*',
            r'^主要起草人：\s*',
            r'^主要起草人\s*',
            r'^制定本指南专家组成员\s*',
            r'^共识制订专家及其单位\s*',
            r'^成员（按首字笔划排序，排名不分先后）\s*',
            r'^指南编写专家\s*',
            r'^指南制订专家委员会名单\s*',
            r'^指南制订组联席主席\s*',
            r'^指南方法学家\s*',
            r'^执笔者（按姓氏汉语拼音排序）\s*',
            r'^指南制订核心专家（按姓氏汉语拼音排序）\s*',
            r'^指南审阅专家（按姓氏汉语拼音排序）\s*',
            r'^学术秘书\s*',
            r'^参与本指南制定和审校的专家\s*',
            r'^参与本共识制定的专家名单\s*',
        ]

        # 查找利益冲突
        conflict_markers = [
            r'^利益冲突\s*',
            r'^利益冲突声明\s*',
            r'^本指南无利益冲突\s*',
            r'^#\s*利益冲突\s*',
            r'^##\s*利益冲突\s*',
            r'^【利益冲突】\s*',
            r'^\[利益冲突\]\s*',
            r'^利益冲突声明：\s*',
            r'^CONFLICT OF INTEREST\s*',
            r'^Conflict of Interest\s*',
            r'^##\s*CONFLICT OF INTEREST\s*',
            r'^##\s*Conflict of Interest\s*',
        ]

        # 查找编写组
        writing_group_markers = [
            r'^#\s*编写组\s*',
            r'^#\s*编写委员会\s*',
            r'^##\s*编写组\s*',
            r'^##\s*编写委员会\s*',
            r'^编写组\s*',
            r'^编写委员会\s*',
            r'^【编写组】\s*',
            r'^\[编写组\]\s*',
            r'^【编写委员会】\s*',
            r'^\[编写委员会\]\s*',
            r'^#\s*共识专家组\s*',
            r'^#\s*共识编写组\s*',
            r'^##\s*共识专家组\s*',
            r'^##\s*共识编写组\s*',
            r'^共识专家组\s*',
            r'^共识编写组\s*',
            r'^【共识专家组】\s*',
            r'^\[共识专家组\]\s*',
            r'^【共识编写组】\s*',
            r'^\[共识编写组\]\s*',
            r'^##\s*Author Contributions\s*',
            r'^##\s*Funding Sources\s*',
            r'^Author Contributions\s*',
            r'^Funding\s*',
            r'^Funding Sources\s*',
            r'^##\s*AUTHOR CONTRIBUTIONS\s*',
            r'^##\s*FUNDING SOURCES\s*',
            r'^##\s*Authors\' contributions\s*',
            r'^##\s*Authors\' Contributions\s*',
            r'^##\s*Authors\' Contributions\s*',
            r'^##\s*Funding\s*',
            r'^###\s*Author details\s*',
        ]

        # 查找附件
        appendix_markers = [
            r'^##\s*附件\s*',
            r'^##\s*Appendix\s*',
            r'^#\s*附件\s*',
            r'^#\s*Appendix\s*',
            r'^附件\s*',
            r'^Appendix\s*',
            r'^【附件】\s*',
            r'^\[附件\]\s*',
            r'^【Appendix】\s*',
            r'^\[Appendix\]\s*',
            r'^##\s*APPENDIX\s*',
            r'^##\s*APPENDIXES\s*',
            r'^##\s*续表\s*',
            r'^Supplementary materials\s*',
            r'^##\s*Supplementary materials\s*',
            r'^Appendix 1\s*',
            r'^##\s*Appendix 1\s*',
            r'^#\s*附录\s*',
            r'^# 附表\s*',
            r'^##\s*附表\s*',
        ]
        
        
        
        # 依次处理 参考文献、致谢、专家组成员、利益冲突、编写组

        # 寻找最后一个参考文献
        last_ref_start = None
        last_ref_marker = None
        for i, line in enumerate(lines):
            for pattern in ref_markers:
                if re.match(pattern, line.strip()):
                    if i < lines_count * 0.2:
                        continue
                    last_ref_start = i
                    last_ref_marker = line.strip()

        # 寻找最后一个致谢
        last_ack_start = None
        last_ack_marker = None
        for i, line in enumerate(lines):
            for pattern in acknowledgments_markers:
                if re.match(pattern, line.strip()):
                    if i < lines_count * 0.2:
                        continue
                    last_ack_start = i
                    last_ack_marker = line.strip()

        # 寻找第一个专家组成员
        first_expert_start = None
        first_expert_marker = None
        match_success = False
        for i, line in enumerate(lines):
            for pattern in experts_markers:
                if re.match(pattern, line.strip()):
                    if i < lines_count * 0.2:
                        continue
                    first_expert_start = i
                    first_expert_marker = line.strip()
                    match_success = True
                    break
            if match_success:
                break

        # 寻找最后一个利益冲突
        last_conflict_start = None
        last_conflict_marker = None
        for i, line in enumerate(lines):
            for pattern in conflict_markers:    
                if re.match(pattern, line.strip()):
                    if i < lines_count * 0.2:
                        continue
                    last_conflict_start = i
                    last_conflict_marker = line.strip()

        # 寻找最后一个编写组
        last_writing_group_start = None
        last_writing_group_marker = None
        for i, line in enumerate(lines):
            for pattern in writing_group_markers:
                if re.match(pattern, line.strip()):
                    if i < lines_count * 0.2:
                        continue
                    last_writing_group_start = i
                    last_writing_group_marker = line.strip()

        # 寻找第一个附件
        first_appendix_start = None
        first_appendix_marker = None
        for i, line in enumerate(lines):
            match_success = False
            for pattern in appendix_markers:
                if re.match(pattern, line.strip()):
                    if i < lines_count * 0.2:
                        continue
                    first_appendix_start = i
                    first_appendix_marker = line.strip()
                    match_success = True
                    break
            if match_success:
                break
            
        # 输出找到的标记
        if last_ref_start is not None:
            logger.info(f"找到最后一个参考文献: {last_ref_marker} (行号: {last_ref_start + 1})")
        if last_ack_start is not None:
            logger.info(f"找到最后一个致谢: {last_ack_marker} (行号: {last_ack_start + 1})")
        if first_expert_start is not None:
            logger.info(f"找到第一个专家组成员: {first_expert_marker} (行号: {first_expert_start + 1})")
        if last_conflict_start is not None:
            logger.info(f"找到最后一个利益冲突: {last_conflict_marker} (行号: {last_conflict_start + 1})")
        if last_writing_group_start is not None:
            logger.info(f"找到最后一个编写组: {last_writing_group_marker} (行号: {last_writing_group_start + 1})")
        if first_appendix_start is not None:
            logger.info(f"找到第一个附件: {first_appendix_marker} (行号: {first_appendix_start + 1})")

        # 删除找到的标记
        # 收集所有非None的行号
        valid_lines = []
        if last_ref_start is not None:
            valid_lines.append(last_ref_start)
        if last_ack_start is not None:
            valid_lines.append(last_ack_start)
        if first_expert_start is not None:
            valid_lines.append(first_expert_start)
        if last_conflict_start is not None:
            valid_lines.append(last_conflict_start)
        if last_writing_group_start is not None:
            valid_lines.append(last_writing_group_start)
        if first_appendix_start is not None:
            valid_lines.append(first_appendix_start)

        if valid_lines:
            # 找到最小的行号
            min_line = min(valid_lines)
            # 删除从min_line到文件末尾的内容
            lines = lines[:min_line]
            logger.info(f"删除从 {min_line + 1} 到文件末尾的内容")
        else:
            logger.info("未找到任何需要删除的标记")

        # 写入预处理后的文件
        with open(preprocessed_path, 'w', encoding='utf-8') as f:
            f.writelines(lines)
            
        logger.info(f"预处理完成，新文件保存为: {preprocessed_path}")
        return preprocessed_path
        
    def extract_content(self, file_path: str, start_line: int, end_line: int = None) -> str:
        """
        从文件中提取指定行号范围的内容
        
        Args:
            file_path: 文件路径
            start_line: 起始行号
            end_line: 结束行号（可选）
            
        Returns:
            str: 提取的内容
        """
        logger.debug(f"从文件 {file_path} 提取内容，行号范围: {start_line}-{end_line}")
        content = []
        with open(file_path, 'r', encoding='utf-8') as f:
            for i, line in enumerate(f, 1):
                if i >= start_line:
                    if end_line and i > end_line:
                        break
                    content.append(line)
        result = ''.join(content)
        logger.debug(f"提取到 {len(result)} 个字符的内容")
        return result
        
    async def is_content_relevant(self, title: str, content: str, disease: str) -> bool:
        """
        判断标题和内容是否与疾病治疗方案相关
        
        Args:
            title: 标题
            content: 内容
            disease: 疾病名称
            
        Returns:
            bool: 是否相关
        """
        logger.info(f"开始分析内容相关性 - 标题: {title}, 疾病: {disease}")
        
        prompt = f"""
        我现在要从一篇医学指南中，抽取与"{disease}"的治疗方案相关的部分，目的是用这些内容来指导医生对"{disease}"的治疗，请你判断以下内容是否能够帮助医生对"{disease}"进行治疗。

        标题：{title}
        内容：{content}

        注意：
        1. 请综合考虑标题和内容的相关性，只回答"是"或"否"，不要包含其他文字。
        2. 如果内容包含以下任何部分，请直接输出"否"：
           - 作者信息（如：王雪峰, 王力宁, 邓力等）
           - 基金项目（如：基金项目:国家传承创新中心重点病种建设项目）
           - 作者单位（如：110032 沈阳,辽宁中医药大学附属医院儿科）
           - 作者简介（如：王雪峰(1957—),女,医学博士,教授、主任医师）
           - 通信作者（如：通信作者:王雪峰, E-mail:<EMAIL>）
           - 摘要（如：【摘要】或 Abstract）
           - 关键词（如：【关键词】或 Keywords）
           - 参考文献（如：【参考文献】或 References）
           - DOI号（如：doi:10.3969/j.issn.1674-3865.2024.02.001）
           - 中图分类号（如：【中图分类号】R725.6）
           - 文章编号（如：【文章编号】1674-3865(2024)02-0093-09）
        3. 如果内容是文章的开头部分（如作者信息、基金项目等），即使标题看起来相关，也请输出"否"。
        4. 只有真正包含治疗方案相关内容的部分才输出"是"。
        """
        
        try:
            result = await self.llm_client.generate_response(prompt)
            is_relevant = result.lower() == "是"
            logger.info(f"内容分析结果 - 标题: {title}, 相关: {is_relevant}")
            return is_relevant
        except Exception as e:
            logger.error(f"内容相关性判断失败: {str(e)}")
            return False
            
    def extract_article_info(self, file_path: str) -> Dict[str, str]:
        """
        提取文章的基本信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict[str, str]: 包含文章信息的字典
        """
        logger.info(f"开始提取文章信息: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 提取大标题（第一个一级标题）
        title_match = re.search(r'^#\s+(.+)$', content, re.MULTILINE)
        title = title_match.group(1) if title_match else "未知标题"
        
        # 提取摘要（支持中英文，有/无冒号，带/不带方括号）
        abstract_patterns = [
            r'^摘要[：:]\s*(.+)$',
            r'^摘要\s*(.+)$',
            r'^【摘要】\s*(.+)$',
            r'^\[摘要\]\s*(.+)$',
            r'^Abstract[：:]\s*(.+)$',
            r'^Abstract\s*(.+)$',
            r'^【Abstract】\s*(.+)$',
            r'^\[Abstract\]\s*(.+)$',
            r'^ABSTRACT[：:]\s*(.+)$',
            r'^ABSTRACT\s*(.+)$',
            r'^【ABSTRACT】\s*(.+)$',
            r'^\[ABSTRACT\]\s*(.+)$'
        ]
        abstract = ""
        for pattern in abstract_patterns:
            match = re.search(pattern, content, re.MULTILINE)
            if match:
                abstract = match.group(1)
                logger.info(f"找到摘要: {abstract[:50]}...")
                break
                
        # 提取关键词（支持中英文，有/无冒号，带/不带方括号）
        keywords_patterns = [
            r'^关键词[：:]\s*(.+)$',
            r'^关键词\s*(.+)$',
            r'^【关键词】\s*(.+)$',
            r'^\[关键词\]\s*(.+)$',
            r'^Keywords[：:]\s*(.+)$',
            r'^Keywords\s*(.+)$',
            r'^【Keywords】\s*(.+)$',
            r'^\[Keywords\]\s*(.+)$',
            r'^KEYWORDS[：:]\s*(.+)$',
            r'^KEYWORDS\s*(.+)$',
            r'^【KEYWORDS】\s*(.+)$',
            r'^\[KEYWORDS\]\s*(.+)$'
        ]
        keywords = ""
        for pattern in keywords_patterns:
            match = re.search(pattern, content, re.MULTILINE)
            if match:
                keywords = match.group(1)
                logger.info(f"找到关键词: {keywords}")
                break
                
        return {
            "title": title,
            "abstract": abstract,
            "keywords": keywords
        }
        
    async def analyze_disease(self, file_path: str) -> str:
        """
        使用LLM分析文章主要讲解的疾病名称
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 疾病名称
        """
        logger.info("开始分析文章主要疾病")
        
        # 读取整个文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        prompt = f"""
        请根据以下文章内容判断这篇文章主要讲解的是什么疾病：

        文章标题：{file_path}
        
        文章内容：
        {content}
        
        请只回答疾病名称，不要包含其他文字。
        """
        
        try:
            result = await self.llm_client.generate_response(prompt)
            logger.info(f"分析得到主要疾病: {result}")
            return result.strip()
        except Exception as e:
            logger.error(f"疾病分析失败: {str(e)}")
            return "未知疾病"
            
    def save_analysis_result(self, file_path: str, result: Dict[str, str], relevant_content: Dict[str, str]):
        """
        保存分析结果到JSON文件
        
        Args:
            file_path: 原始文件路径
            result: 文章基本信息
            relevant_content: 相关的内容
        """
        logger.info("开始保存分析结果")
        
        # 读取原文内容
        with open(file_path, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        # 将相关内容转换为列表格式
        content_list = []
        for title, content in relevant_content.items():
            content_list.append({
                "title": title,
                "content": content
            })
        
        # 构建完整的分析结果
        analysis_result = {
            "title": result["title"],
            "abstract": result["abstract"],
            "keywords": result["keywords"],
            "main_disease": result["main_disease"],
            "diagnostic_symptoms": result["diagnostic_symptoms"],
            "topic": self.topic,
            "relevant_content": content_list,
            "original_content": original_content  # 添加原文内容
        }
        
        # 生成输出文件路径
        output_path = os.path.join(
            os.path.dirname(file_path),
            f"{os.path.splitext(os.path.basename(file_path))[0]}_analysis.json"
        )
        
        # 保存为JSON文件
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(analysis_result, f, ensure_ascii=False, indent=2)
            
        logger.info(f"分析结果已保存到: {output_path}")

        return analysis_result
        
    async def extract_diagnostic_symptoms(self, content: str, disease: str) -> str:
        """
        使用LLM从原文中提取疾病的诊断症状
        
        Args:
            content: 原文内容
            disease: 疾病名称
            
        Returns:
            str: 诊断症状描述
        """
        logger.info(f"开始提取{disease}的诊断症状")
        
        prompt = f"""
        请根据以下医学指南内容，用一句话总结"{disease}"的主要诊断症状，用于指导医生进行临床诊断。

        原文内容：
        {content}

        要求：
        1. 只输出一句话，不要分段或编号
        2. 直接描述症状和体征，不要包含"主要诊断症状为"、"诊断标准包括"等前缀
        3. 保持专业性和准确性
        4. 不要包含疾病名称
        5. 不要输出"根据以上内容"等无效字样
        """
        
        try:
            symptoms = await self.llm_client.generate_response(prompt)
            logger.info(f"成功提取{disease}的诊断症状")
            return symptoms
        except Exception as e:
            logger.error(f"提取诊断症状失败: {str(e)}")
            return "提取诊断症状失败"

    async def analyze_file(self, file_path: str) -> Dict[str, str]:
        """
        分析文件中的所有标题和内容，提取与治疗方案相关的内容
        
        Args:
            file_path: markdown文件路径
            
        Returns:
            Dict[str, str]: 标题到相关内容的映射
        """
        logger.info(f"开始分析文件: {file_path}, 主题: {self.topic}")
        
        # 预处理文件
        preprocessed_path = self.preprocess_file(file_path)
        
        # 提取文章基本信息
        article_info = self.extract_article_info(preprocessed_path)
        
        # 分析主要疾病
        main_disease = await self.analyze_disease(preprocessed_path)
        article_info["main_disease"] = main_disease
        
        # 提取诊断症状
        with open(preprocessed_path, 'r', encoding='utf-8') as f:
            content = f.read()
        diagnostic_symptoms = await self.extract_diagnostic_symptoms(content, main_disease)
        article_info["diagnostic_symptoms"] = diagnostic_symptoms
        
        # 使用预处理后的文件进行分析
        titles = parse_markdown_titles(preprocessed_path)
        logger.info(f"找到 {len(titles)} 个标题")
        
        relevant_content = {}
        total_titles = len(titles)
        
        for i, (title, line_num) in enumerate(titles, 1):
            logger.info(f"处理第 {i}/{total_titles} 个标题: {title} (行号: {line_num})")
            
            # 先提取内容
            start_line = line_num
            end_line = titles[i][1] - 1 if i < len(titles) else None
            content = self.extract_content(preprocessed_path, start_line, end_line)
            
            # 使用标题和内容一起判断相关性
            is_relevant = await self.is_content_relevant(title, content, main_disease)
            
            if is_relevant:
                logger.info(f"内容相关，保存内容: {title}")
                relevant_content[title] = content
                logger.info(f"成功保存内容，长度: {len(content)} 字符")
            else:
                logger.info(f"内容不相关，跳过: {title}")
                
        # 保存分析结果
        analysis_result = self.save_analysis_result(file_path, article_info, relevant_content)
                

        logger.info(f"分析完成，找到 {len(relevant_content)} 个相关内容")
        return analysis_result 