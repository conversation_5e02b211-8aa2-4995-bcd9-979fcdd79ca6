package com.makiyo.ai_doctor_prod.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;

/**
 * 连接监控配置
 * 监控系统连接状态，特别是CLOSE_WAIT连接
 */
@Configuration
public class ConnectionMonitorConfig {

    @Component
    public static class ConnectionHealthIndicator implements HealthIndicator {
        
        private static final Logger log = LoggerFactory.getLogger(ConnectionHealthIndicator.class);
        
        @Override
        public Health health() {
            Map<String, Object> details = new HashMap<>();
            try {
                Map<String, Integer> connectionStats = getConnectionStats();
                details.putAll(connectionStats);
                
                int closeWaitCount = connectionStats.getOrDefault("CLOSE_WAIT", 0);
                if (closeWaitCount > 10) {
                    return Health.down()
                            .withDetails(details)
                            .withDetail("warning", "High number of CLOSE_WAIT connections detected")
                            .build();
                } else if (closeWaitCount > 5) {
                    return Health.up()
                            .withDetails(details)
                            .withDetail("warning", "Moderate number of CLOSE_WAIT connections")
                            .build();
                }
                
                return Health.up().withDetails(details).build();
            } catch (Exception e) {
                return Health.down()
                        .withDetail("error", "Failed to check connection status: " + e.getMessage())
                        .build();
            }
        }
        
        /**
         * 定时监控连接状态
         */
        @Scheduled(fixedRate = 60000) // 每分钟检查一次
        public void monitorConnections() {
            try {
                Map<String, Integer> stats = getConnectionStats();
                int closeWait = stats.getOrDefault("CLOSE_WAIT", 0);
                int established = stats.getOrDefault("ESTABLISHED", 0);
                int timeWait = stats.getOrDefault("TIME_WAIT", 0);
                
                log.info("Connection Status - ESTABLISHED: {}, CLOSE_WAIT: {}, TIME_WAIT: {}", 
                        established, closeWait, timeWait);
                
                if (closeWait > 5) {
                    log.warn("High number of CLOSE_WAIT connections detected: {}. " +
                            "This may indicate connection leaks.", closeWait);
                }
            } catch (Exception e) {
                log.error("Error monitoring connections: {}", e.getMessage());
            }
        }
        
        /**
         * 获取连接统计信息
         */
        private Map<String, Integer> getConnectionStats() throws Exception {
            Map<String, Integer> stats = new HashMap<>();
            
            // 在Linux/Unix系统上使用netstat命令
            String os = System.getProperty("os.name").toLowerCase();
            String command;
            
            if (os.contains("win")) {
                command = "netstat -an";
            } else {
                command = "netstat -an";
            }
            
            Process process = Runtime.getRuntime().exec(command);
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.contains("CLOSE_WAIT")) {
                    stats.put("CLOSE_WAIT", stats.getOrDefault("CLOSE_WAIT", 0) + 1);
                } else if (line.contains("ESTABLISHED")) {
                    stats.put("ESTABLISHED", stats.getOrDefault("ESTABLISHED", 0) + 1);
                } else if (line.contains("TIME_WAIT")) {
                    stats.put("TIME_WAIT", stats.getOrDefault("TIME_WAIT", 0) + 1);
                } else if (line.contains("LISTEN")) {
                    stats.put("LISTEN", stats.getOrDefault("LISTEN", 0) + 1);
                }
            }
            
            reader.close();
            process.waitFor();
            
            return stats;
        }
    }
} 