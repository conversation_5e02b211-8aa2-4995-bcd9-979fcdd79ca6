<template>
  <div class="admin-data">
    <h1 class="admin-title">会话数据管理</h1>
    
    <div class="session-table-container">
      <a-spin :spinning="loading">
        <div v-if="!loading && sessions.length > 0">
          <a-table
            :columns="columns"
            :data-source="sessions"
            :pagination="pagination"
            @change="handleTableChange"
            :scroll="{ x: '800px' }" 
            :row-key="record => record.id"
          >
            <template #bodyCell="{ column, record }">
              <!-- 会话ID列 -->
              <template v-if="column.key === 'id'">
                <span class="session-id">{{ record.id }}</span>
              </template>
              
              <!-- 用户ID列 -->
              <template v-if="column.key === 'userId'">
                <span>{{ record.userId }}</span>
              </template>
              
              <!-- 创建时间列 -->
              <template v-if="column.key === 'createdAt'">
                <span>{{ formatDateTime(record.createdAt) }}</span>
              </template>
              
              <!-- 更新时间列 -->
              <template v-if="column.key === 'updatedAt'">
                <span>{{ formatDateTime(record.updatedAt) }}</span>
              </template>
              
              <!-- 消息数量列 -->
              <template v-if="column.key === 'messageCount'">
                <a-tag color="blue">{{ record.messageCount !== undefined ? record.messageCount : (record.messages ? record.messages.length : 0) }}</a-tag>
              </template>
              
              <!-- 会话状态列 -->
              <template v-if="column.key === 'sessionStatus'">
                  <div style="display: flex; flex-wrap: wrap; gap: 4px;">
                  <a-tag :color="getStatusTag(record.sessionStatus).color">{{ getStatusTag(record.sessionStatus).text }}</a-tag>
                  </div>
              </template>
              
              <!-- 操作列 -->
              <template v-if="column.key === 'action'">
                <div class="action-buttons">
                  <a-button type="primary" size="small" @click="showSessionDetail(record)">查看详情</a-button>
                  <a-button type="default" size="small" @click="showCaseImages(record)" style="margin-left: 8px;">查看图片</a-button>
                </div>
              </template>
            </template>
          </a-table>
        </div>
        <div v-else-if="!loading && sessions.length === 0">
          <a-empty description="暂无会话数据" />
        </div>
      </a-spin>
    </div>
    
    <!-- 会话详情抽屉 -->
    <a-drawer
      title="会话详情"
      v-model:open="drawerVisible"
      :width="'100%'"
      @after-open-change="onDrawerVisibleChange" 
      placement="right"
      :mask-closable="true"
      :destroy-on-close="true"
    >
      <div v-if="selectedSession && renderDrawerContent">
        <div class="drawer-header">
          <div class="drawer-header-top">
            <h3>会话ID: {{ selectedSession.id }}</h3>
            <a-button type="primary" @click="drawerVisible = false">关闭详情</a-button>
          </div>
          <p>用户ID: {{ selectedSession.userId }}</p>
          <p>创建时间: {{ formatDateTime(selectedSession.createdAt) }}</p>
          <p>更新时间: {{ formatDateTime(selectedSession.updatedAt) }}</p>
          <div class="session-meta-tags">
            <a-tag color="blue">
              消息数量: {{ selectedSession.messages ? selectedSession.messages.length : 0 }}
            </a-tag>
            <a-tag color="green" v-if="selectedSession.messages && selectedSession.messages.some(msg => msg.interactionData && msg.interactionData.interaction_history && msg.interactionData.interaction_history.diagnosis)">
              已诊断
            </a-tag>
            <a-tag color="orange" v-else-if="selectedSession.messages && selectedSession.messages.some(msg => msg.interactionData && msg.interactionData.interaction_history && msg.interactionData.interaction_history.preliminary_diagnosis)">
              已初诊
            </a-tag>
            <a-tag color="orange" v-else>
              问诊阶段
            </a-tag>
            <a-tag color="purple" v-if="selectedSession.messages && selectedSession.messages.some(msg => msg.interactionData && msg.interactionData.interaction_history && msg.interactionData.interaction_history.inspection_suggestions)">
              有检查建议
            </a-tag>
            <a-tag color="purple" v-if="selectedSession.messages && selectedSession.messages.some(msg => msg.interactionData && msg.interactionData.interaction_history && msg.interactionData.interaction_history.test_recommendation && msg.interactionData.interaction_history.test_recommendation.length > 0)">
              有检查报告
            </a-tag>
          </div>
        </div>
        
        <a-divider />
        
        <div class="session-statistics">
          <h3>会话统计</h3>
          <div class="statistics-grid">
            <div class="stat-item">
              <div class="stat-value">{{ selectedSession.messages ? selectedSession.messages.length : 0 }}</div>
              <div class="stat-label">总消息数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ selectedSession.messages ? selectedSession.messages.filter(msg => msg.speaker === 'patient').length : 0 }}</div>
              <div class="stat-label">患者消息</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ selectedSession.messages ? selectedSession.messages.filter(msg => msg.speaker === 'doctor').length : 0 }}</div>
              <div class="stat-label">医生消息</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ selectedSession.messages && selectedSession.messages.length > 0 ? formatDateTime(selectedSession.messages[selectedSession.messages.length - 1].createdAt) : '-' }}</div>
              <div class="stat-label">最后活动</div>
            </div>
          </div>
        </div>
        
        <a-divider />
        
        <div class="message-container">
          <h3>消息列表</h3>
          <div v-if="selectedSession.messages && selectedSession.messages.length > 0">
            <div 
              v-for="(msg, index) in selectedSession.messages" 
              :key="index"
              class="message-item"
              :class="{'doctor-message': msg.speaker === 'doctor', 'patient-message': msg.speaker === 'patient'}"
            >
              <div class="message-header">
                <div class="message-header-info">
                  <a-tag :color="msg.speaker === 'doctor' ? 'blue' : 'green'">{{ msg.speaker === 'doctor' ? '医生' : '患者' }}</a-tag>
                  <span class="message-id">ID: {{ msg.id }}</span>
                </div>
                <span class="message-time">{{ formatDateTime(msg.createdAt) }}</span>
              </div>

              <div class="message-content-wrapper">
                <template v-if="msg.interactionData"> 
                  <div class="interaction-history-display">
                    <div v-for="(entry, entryIndex) in msg.interactionData.interaction_history.cot_entries || []" :key="`entry-${entryIndex}`" class="cot-entry-section">
                      <h5 v-if="msg.interactionData.interaction_history.cot_entries && msg.interactionData.interaction_history.cot_entries.length > 0">
                        <span v-if="entryIndex === 0">问诊对话</span>
                        <span v-else-if="entryIndex === 1">查体对话</span>
                        <span v-else-if="entryIndex === 2">拟诊</span>
                        <span v-else>交互轮次 {{ entryIndex + 1 }}</span>
                      </h5>
                      <a-tabs size="small" :animated="false">
                        <a-tab-pane key="dialogue" tab="对话历史">
                          <div class="dialogue-history-log">
                            <div v-for="(dialogue, diaIdx) in entry.dialogue_history" :key="`diag-${diaIdx}`"
                                :class="['dialogue-entry', dialogue.role === 'doctor' ? 'doctor-entry' : 'patient-entry']">
                              <div class="dialogue-speaker">
                                <a-avatar size="small" :style="{ backgroundColor: dialogue.role === 'doctor' ? '#1890ff' : '#52c41a', color: '#fff', marginRight: '8px' }">
                                  {{ dialogue.role === 'doctor' ? '医' : '患' }}
                                </a-avatar>
                                <span class="dialogue-timestamp" v-if="dialogue.timestamp">
                                  {{ formatDateTime(dialogue.timestamp) }}
                                </span>
                              </div>
                              <div class="dialogue-text markdown-body" v-html="renderMarkdown(dialogue.content || '')"></div>
                            </div>
                          </div>
                        </a-tab-pane>
                        <a-tab-pane key="reasoning" tab="推理过程" v-if="entry.reasoning && entry.reasoning.trim() !== ''">
                          <div class="section-content markdown-body" v-html="renderMarkdown(entry.reasoning)"></div>
                        </a-tab-pane>
                        <a-tab-pane key="strategy" tab="问诊策略" v-if="entry.strategy && entry.strategy.trim() !== ''">
                          <div class="section-content markdown-body" v-html="renderMarkdown(entry.strategy)"></div>
                        </a-tab-pane>
                        <a-tab-pane key="observation" tab="观察记录" v-if="entry.observation && entry.observation.trim() !== ''">
                          <div class="section-content markdown-body" v-html="renderMarkdown(entry.observation)"></div>
                        </a-tab-pane>
                        <a-tab-pane key="feedback" tab="AI反馈" v-if="entry.feedback && entry.feedback.trim() !== ''">
                          <div class="section-content markdown-body" v-html="renderMarkdown(entry.feedback)"></div>
                        </a-tab-pane>
                      </a-tabs>
                    </div>

                    <div class="overall-diagnosis-section" v-if="msg.interactionData.interaction_history">
                      <h5 style="margin-top: 16px;">综合评估</h5>
                      <a-tabs size="small" :animated="false">
                        <a-tab-pane key="preliminary_diagnosis" tab="初步诊断" v-if="msg.interactionData.interaction_history.preliminary_diagnosis">
                          <div class="section-content markdown-body" v-html="renderMarkdown(msg.interactionData.interaction_history.preliminary_diagnosis || '无')"></div>
                        </a-tab-pane>
                        <a-tab-pane key="inspection_suggestions" tab="检查建议" v-if="msg.interactionData.interaction_history.inspection_suggestions">
                          <div class="section-content markdown-body" v-if="typeof msg.interactionData.interaction_history.inspection_suggestions === 'string'">
                            <div v-html="renderMarkdown(msg.interactionData.interaction_history.inspection_suggestions)"></div>
                          </div>
                          <div class="section-content markdown-body" v-else-if="Array.isArray(msg.interactionData.interaction_history.inspection_suggestions)">
                            <ul class="inspection-list">
                              <li v-for="(item, idx) in msg.interactionData.interaction_history.inspection_suggestions" :key="`insp-${idx}`">
                                <div v-if="typeof item === 'string'" v-html="renderMarkdown(item)"></div>
                                <div v-else class="inspection-item-object">
                                  <div v-for="(value, key) in item" :key="key" class="inspection-detail">
                                    <span class="inspection-label">{{ key }}:</span>
                                    <span class="inspection-value">{{ value }}</span>
                                  </div>
                                </div>
                              </li>
                            </ul>
                          </div>
                          <div class="section-content markdown-body" v-else>
                            <pre>{{ JSON.stringify(msg.interactionData.interaction_history.inspection_suggestions, null, 2) }}</pre>
                          </div>
                        </a-tab-pane>
                        <a-tab-pane key="diagnosis" tab="最终诊断" v-if="msg.interactionData.interaction_history.diagnosis">
                          <div class="section-content markdown-body" v-html="renderMarkdown(msg.interactionData.interaction_history.diagnosis || '无')"></div>
                        </a-tab-pane>
                        <a-tab-pane key="test_recommendation" tab="检查结果" v-if="msg.interactionData.interaction_history.test_recommendation && msg.interactionData.interaction_history.test_recommendation.length > 0">
                          <div class="test-results-text-blocks">
                            <div v-for="(test, testIdx) in msg.interactionData.interaction_history.test_recommendation" :key="`test-${testIdx}`" class="test-text-block">
                              <a-collapse :key="`ocr-collapse-${testIdx}`" accordion>
                                <a-collapse-panel :key="1" :header="getItemName(test) || '检查报告详情'">
                                  <a-tabs size="small">
                                    <a-tab-pane key="parsed" tab="解析数据" v-if="hasOcrStructuredData(test)">
                                      <a-table
                                        :columns="resultTableColumns"
                                        :data-source="getOcrParsedResults(test)"
                                        size="small"
                                        :pagination="false"
                                        :row-class-name="getRowClassName"
                                      >
                                        <template #bodyCell="{ column, record }">
                                          <template v-if="column.key === 'itemName'">
                                            {{ getItemName(record) }}
                                          </template>
                                          <template v-if="column.key === 'result'">
                                            <span :class="{'abnormal-result': record.异常标记 || record.结果状态 === '异常' || record.结果状态 === '偏高' || record.结果状态 === '偏低'}">
                                              {{ record.结果 }} {{ record.单位 || '' }}
                                            </span>
                                            <span v-if="record.异常标记" class="abnormal-marker">{{ record.异常标记 }}</span>
                                          </template>
                                          <template v-if="column.key === 'status'">
                                            <a-tag size="small" :color="getResultStatusColor(record.结果状态)">
                                              {{ record.结果状态 || '未知' }}
                                            </a-tag>
                                          </template>
                                          <template v-if="column.key === 'action'">
                                            <a-button 
                                              v-if="record.检查描述 || record.诊断 || record.处理建议"
                                              type="link" 
                                              size="small"
                                              @click="showResultDetail(record)"
                                            >
                                              详情
                                            </a-button>
                                          </template>
                                        </template>
                                      </a-table>
                                    </a-tab-pane>
                                    <a-tab-pane key="raw" tab="原始数据">
                                      <pre style="white-space: pre-wrap; word-wrap: break-word; max-height: 400px; overflow-y: auto;">{{ JSON.stringify(test, null, 2) }}</pre>
                                    </a-tab-pane>
                                  </a-tabs>
                                </a-collapse-panel>
                              </a-collapse>
                            </div>
                          </div>
                        </a-tab-pane>
                        <a-tab-pane key="treatment_recommendation" tab="治疗建议" v-if="msg.interactionData.interaction_history.treatment_recommendation && typeof msg.interactionData.interaction_history.treatment_recommendation === 'string' && msg.interactionData.interaction_history.treatment_recommendation.trim() !== ''">
                          <div class="section-content markdown-body" v-html="renderMarkdown(msg.interactionData.interaction_history.treatment_recommendation)"></div>
                        </a-tab-pane>
                        <a-tab-pane key="treatment_recommendation_array" tab="治疗建议 (列表)" v-if="msg.interactionData.interaction_history.treatment_recommendation && Array.isArray(msg.interactionData.interaction_history.treatment_recommendation) && msg.interactionData.interaction_history.treatment_recommendation.length > 0">
                          <ul class="recommendation-list markdown-body">
                            <li v-for="(treat, treatIdx) in msg.interactionData.interaction_history.treatment_recommendation" :key="`treat-${treatIdx}`" v-html="renderMarkdown(treat || '')"></li>
                          </ul>
                        </a-tab-pane>
                      </a-tabs>
                    </div>
                  </div>
                </template>
                <template v-else-if="msg.isRawJson">
                  <a-collapse>
                    <a-collapse-panel key="1" header="原始JSON数据">
                      <pre>{{ formatJson(msg.message) }}</pre>
                    </a-collapse-panel>
                  </a-collapse>
                </template>
                <template v-else-if="msg.message && msg.message.trim() !== ''">
                  <div class="markdown-body" v-html="renderMarkdown(msg.message)"></div>
                  </template>
                <template v-else>
                  <p><em>(空消息)</em></p>
          </template>
        </div>
      </div>
          </div>
          <a-empty v-else description="暂无消息" />
        </div>
        
        <!-- 添加底部操作栏 -->
        <div class="drawer-footer">
          <a-button type="primary" size="large" @click="drawerVisible = false">返回列表</a-button>
        </div>
      </div>
      <div v-else>
        <a-skeleton active :paragraph="{ rows: 10 }" />
      </div>
    </a-drawer>
    
    <!-- 在会话详情抽屉后添加图片抽屉 -->
    <a-drawer
      title="会话图片"
      v-model:open="imageDrawerVisible"
      :width="600"
      placement="right"
      :mask-closable="true"
      :destroy-on-close="true"
    >
      <div v-if="selectedSessionId">
        <div class="image-filter-bar">
          <a-select
            v-model:value="imageFilterCategory"
            style="width: 200px"
            placeholder="按类别筛选"
            @change="filterImages"
          >
            <a-select-option value="all">全部</a-select-option>
            <a-select-option value="OCR">OCR</a-select-option>
            <a-select-option value="其他">其他</a-select-option>
          </a-select>
        </div>

        <div v-if="caseImages && caseImages.length > 0" class="image-gallery">
          <div v-for="(image, index) in filteredImages" :key="index" class="image-item">
            <div class="image-card">
              <div class="image-header">
                <span class="image-title">{{ image.originalName || '未命名图片' }}</span>
                <a-tag color="blue">{{ image.category || '未分类' }}</a-tag>
              </div>
              <div class="image-content">
                <img :src="getImageUrl(image)" alt="图片" @click="showImagePreview(image)" @error="handleImageError($event, image)" />
              </div>
              <div class="image-footer">
                <span class="image-time">{{ formatDateTime(image.uploadTime) }}</span>
              </div>
            </div>
          </div>
        </div>
        <a-empty v-else description="暂无图片" />
      </div>
      
      <!-- 图片预览模态框 -->
      <a-modal
        v-model:open="previewVisible"
        :footer="null"
        :width="800"
        @cancel="previewVisible = false"
      >
        <img v-if="previewImage" :src="getImageUrl(previewImage)" alt="预览" style="width: 100%;" @error="handleImageError($event, previewImage, true)" />
      </a-modal>
    </a-drawer>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted, nextTick, h, onBeforeUnmount, computed } from 'vue'
import request from '@/utils/request'
import { message } from 'ant-design-vue'
import { marked } from 'marked'
import { Modal } from 'ant-design-vue'
import { Empty } from 'ant-design-vue'
import { resolveComponent } from 'vue'
import { DownOutlined, UpOutlined } from '@ant-design/icons-vue'

// 配置 marked (可选, 例如启用 GitHub Flavored Markdown, 自动换行等)
marked.setOptions({
  gfm: true,
  breaks: true,
});

export default defineComponent({
  name: 'AdminData',
  components: {
    DownOutlined,
    UpOutlined
  },
  setup() {
    const loading = ref(false)
    const sessions = ref([])
    const total = ref(0)
    const drawerVisible = ref(false)
    const selectedSession = ref(null)
    const renderDrawerContent = ref(false)
    const resultFilter = ref('all')
    const searchQuery = ref('')
    const activeCollapseKeys = ref(['standard', 'imaging', 'special', 'other'])
    
    // 添加折叠状态管理
    const collapsedCards = ref({})
    
    const pagination = ref({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      pageSizeOptions: ['10', '20', '50', '100'],
    })
    
    const customPanelStyle = {
      background: '#f7f7f7',
      borderRadius: '4px',
      marginBottom: '16px',
      border: '0px'
    }
    
    const resultTableColumns = [
      { title: '检查项目', key: 'itemName', width: '30%' },
      { title: '结果', key: 'result', width: '30%' },
      { title: '参考范围', dataIndex: '参考范围', width: '25%' },
      { title: '状态', key: 'status', width: '10%' },
      { title: '', key: 'action', width: '5%' }
    ]
    
    const columns = [
      { title: '会话ID', dataIndex: 'id', key: 'id' },
      { title: '用户ID', dataIndex: 'userId', key: 'userId' },
      { title: '创建时间', dataIndex: 'createdAt', key: 'createdAt' },
      { title: '更新时间', dataIndex: 'updatedAt', key: 'updatedAt' },
      { title: '消息数量', key: 'messageCount' },
      { title: '会话状态', key: 'sessionStatus' },
      { title: '操作', key: 'action' }
    ]
    
    const parseAndCheckInteractionHistory = (str) => {
      if (!str || typeof str !== 'string') return null;
      try {
        const parsed = JSON.parse(str);
        if (parsed && typeof parsed === 'object' &&
            parsed.interaction_history &&
            Array.isArray(parsed.interaction_history.cot_entries)) {
          return parsed;
        }
        return null;
      } catch (e) {
        return null;
      }
    };

    const fetchSessions = async (page = 1, size = 10) => {
      loading.value = true;
      try {
        const res = await request({
          url: '/session/getAllSessionsWithMessages',
          method: 'get',
          params: { page, size }
        });
        
        if (res && res.content) {
          sessions.value = res.content.list || [];
          pagination.value.total = res.content.total || 0;
          pagination.value.current = res.content.pageNum || 1;
          pagination.value.pageSize = res.content.pageSize || 10;
        } else {
          sessions.value = [];
          pagination.value.total = 0;
        }
      } catch (error) {
        console.error("获取会话数据失败:", error);
        sessions.value = [];
        pagination.value.total = 0;
      } finally {
        loading.value = false;
      }
    };
    
    const handleTableChange = (pager) => {
      pagination.value = pager;
      fetchSessions(pager.current, pager.pageSize);
    };
    
    const showSessionDetail = async (session) => {
      selectedSession.value = { ...session, messages: [] }; // 先设置基本信息
      drawerVisible.value = true;
      renderDrawerContent.value = false; // 确保内容重新渲染

      try {
        // 异步获取消息
        const res = await request({
          url: `/session/${session.id}/messages`,
          method: 'get'
        });
        if (res && res.content) {
          // 处理消息数据
          const processedMessages = res.content.map(msg => {
          const interactionData = parseAndCheckInteractionHistory(msg.message);
          return {
            ...msg,
            interactionData: interactionData, 
            isRawJson: !interactionData && isJsonString(msg.message) 
          };
        });
          selectedSession.value.messages = processedMessages;
        }
      } catch (error) {
        console.error(`获取会话 ${session.id} 的消息失败:`, error);
        message.error('加载消息详情失败');
    }
    };
    
    const onDrawerVisibleChange = (open) => {
      if (open && selectedSession.value) {
        nextTick(() => { renderDrawerContent.value = true; });
      } else if (!open) {
        renderDrawerContent.value = false;
        selectedSession.value = null;
      }
    }
    
    const isJsonString = (str) => {
      if (!str || typeof str !== 'string') return false;
      try {
        const json = JSON.parse(str);
        return typeof json === 'object' && json !== null;
      } catch (e) { return false; }
    }
    
    const formatJson = (jsonString) => {
      try { return JSON.stringify(JSON.parse(jsonString), null, 2); }
      catch (e) { return jsonString; }
    }

    const renderMarkdown = (markdownText) => {
      if (!markdownText) return '';
        return marked.parse(markdownText);
    };
    
    const formatDate = (dateStr) => {
      if (!dateStr) return '-';
        const date = new Date(dateStr);
      const padZero = (num) => (num < 10 ? `0${num}` : num);
      return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())} ${padZero(date.getHours())}:${padZero(date.getMinutes())}:${padZero(date.getSeconds())}`;
    };
    
    const formatDateTime = (dateStr) => {
      if (!dateStr) return '-';
      try {
        const date = new Date(dateStr);
        const padZero = (num) => (num < 10 ? `0${num}` : num);
        return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())} ${padZero(date.getHours())}:${padZero(date.getMinutes())}:${padZero(date.getSeconds())}`;
      } catch (e) { return dateStr; }
    };
    
    const padZero = (num) => (num < 10 ? `0${num}` : num);
    
    const getResultStatusColor = (status) => {
      if (!status) return 'default';
      switch(status) {
        case '正常': return 'green';
        case '异常': return 'red';
        case '偏高': return 'orange';
        case '偏低': return 'blue';
        default: return 'default';
      }
    };
    
    const showResultDetail = (result) => {
      // 构建详情内容
      let detailContent = '';
      if (result.检查描述) {
        detailContent += `<div><strong>检查描述:</strong> ${result.检查描述}</div>`;
      }
      if (result.诊断) {
        detailContent += `<div><strong>诊断:</strong> ${result.诊断}</div>`;
      }
      if (result.处理建议) {
        detailContent += `<div><strong>处理建议:</strong> ${result.处理建议}</div>`;
      }
      
      // 显示详情弹窗
      if (detailContent) {
        // 使用Modal或其他方式展示详情
        const modal = Modal.info({
          title: `${result.项目名称 || '检查项'} 详细信息`,
          content: h('div', {
            innerHTML: detailContent,
            style: {
              maxHeight: '400px',
              overflow: 'auto'
            }
          }),
          width: '600px'
        });
      }
    };
    
    const showAllResults = (results) => {
      if (!results || results.length === 0) {
        message.info('没有检查结果可显示');
        return;
      }
      
      // 创建表格列定义
      const columns = [
        {
          title: '检查项目',
          dataIndex: 'displayName',
          width: '30%',
          sorter: (a, b) => (a.displayName || '').localeCompare(b.displayName || '')
        },
        {
          title: '结果值',
          dataIndex: 'result_display',
          width: '20%',
          customRender: ({ record }) => {
            const isAbnormal = record.异常标记 || 
                              record.结果状态 === '异常' || 
                              record.结果状态 === '偏高' || 
                              record.结果状态 === '偏低';
            
            return h('div', [
              h('span', {
                class: isAbnormal ? 'abnormal-result' : '',
                style: { marginRight: '4px' }
              }, `${record.结果 || '-'} ${record.单位 || ''}`),
              record.异常标记 ? h('span', { class: 'abnormal-marker' }, record.异常标记) : null
            ]);
          }
        },
        {
          title: '参考范围',
          dataIndex: '参考范围',
          width: '25%'
        },
        {
          title: '状态',
          dataIndex: '结果状态',
          width: '15%',
          customRender: ({ text }) => {
            return h(resolveComponent('a-tag'), {
              color: getResultStatusColor(text)
            }, () => text || '未知');
          },
          filters: [
            { text: '正常', value: '正常' },
            { text: '异常', value: '异常' },
            { text: '偏高', value: '偏高' },
            { text: '偏低', value: '偏低' }
          ],
          onFilter: (value, record) => record.结果状态 === value
        },
        {
          title: '详情',
          width: '10%',
          customRender: ({ record }) => {
            if (!record.检查描述 && !record.诊断 && !record.处理建议) {
              return null;
            }
            
            return h(resolveComponent('a-button'), {
              type: 'link',
              size: 'small',
              onClick: (e) => {
                e.stopPropagation();
                showResultDetail(record);
              }
            }, () => '查看');
          }
        }
      ];
      
      // 创建数据源，添加key属性和显示名称
      const dataSource = results.map((item, index) => ({
        ...item,
        key: `result-${index}`,
        displayName: getItemName(item) // 使用辅助方法获取正确的项目名称
      }));
      
      // 创建模态框
      Modal.info({
        title: '检查结果汇总',
        content: h('div', { style: { width: '100%', maxHeight: '70vh', overflow: 'auto' } }, [
          h('div', { class: 'search-section', style: { marginBottom: '16px' } }, [
            h(resolveComponent('a-input-search'), {
              placeholder: '搜索检查项目',
              allowClear: true,
              style: { width: '300px' },
              onChange: (e) => {
                // 直接搜索功能，不需要更新表格数据的ref
                const value = e.target.value.toLowerCase();
                const tableElement = document.querySelector('.search-result-table');
                if (tableElement) {
                  const rows = tableElement.querySelectorAll('tbody tr');
                  rows.forEach(row => {
                    const nameCell = row.querySelector('td:first-child');
                    if (nameCell) {
                      const name = nameCell.textContent.toLowerCase();
                      row.style.display = name.includes(value) ? '' : 'none';
                    }
                  });
                }
              }
            })
          ]),
          h(resolveComponent('a-table'), {
            columns,
            dataSource,
            size: 'small',
            pagination: { pageSize: 10, showSizeChanger: true },
            rowClassName: (record) => {
              if (record.结果状态 === '异常') return 'result-row-abnormal';
              if (record.结果状态 === '偏高') return 'result-row-high';
              if (record.结果状态 === '偏低') return 'result-row-low';
              return '';
            },
            scroll: { y: 400 },
            class: 'search-result-table'
          })
        ]),
        width: 900,
        maskClosable: true,
        okText: '关闭'
      });
    };
    
    const filteredResults = (results) => {
      if (!results || results.length === 0) return [];
      
      if (resultFilter.value === 'all') {
        return results;
      } else if (resultFilter.value === 'abnormal') {
        return results.filter(result => result.异常标记 || result.结果状态 === '异常' || result.结果状态 === '偏高' || result.结果状态 === '偏低');
      } else if (resultFilter.value === 'normal') {
        return results.filter(result => result.结果状态 === '正常');
      }
      
      return [];
    };
    
    // 添加折叠状态管理
    const toggleCardCollapse = (index) => {
      collapsedCards.value[index] = !collapsedCards.value[index]
    }
    
    // 添加获取项目名称的辅助方法
    const getItemName = (result) => {
      // 优先使用"项目名称"字段
      if (result.项目名称 && result.项目名称.trim() !== '') {
        return result.项目名称;
      }
      // 其次使用"检查"字段
      if (result.检查 && result.检查.trim() !== '') {
        return result.检查;
      }
      // 最后返回默认值
      return '未知项目';
    };
    
    // 搜索筛选函数
    const onSearchChange = (e) => {
      searchQuery.value = e.target.value;
    };
    
    // 添加搜索过滤逻辑
    const filteredAndSearchedResults = (results) => {
      if (!results) return [];
      
      let filtered = results;
      
      // 先按状态过滤
      if (resultFilter.value === 'abnormal') {
        filtered = filtered.filter(r => 
          r.异常标记 || 
          r.结果状态 === '异常' || 
          r.结果状态 === '偏高' || 
          r.结果状态 === '偏低'
        );
      } else if (resultFilter.value === 'normal') {
        filtered = filtered.filter(r => 
          !r.异常标记 && 
          r.结果状态 !== '异常' && 
          r.结果状态 !== '偏高' && 
          r.结果状态 !== '偏低'
        );
      }
      
      // 再按搜索词过滤
      if (searchQuery.value && searchQuery.value.trim() !== '') {
        const query = searchQuery.value.toLowerCase().trim();
        filtered = filtered.filter(r => {
          const name = getItemName(r).toLowerCase();
          return name.includes(query);
        });
      }
      
      return filtered;
    };
    
    // 按检查类别分组
    const getResultsByCategory = (results, category) => {
      if (!results) return [];
      
      // 为每个结果添加key
      results = results.map((item, index) => ({
        ...item,
        key: `result-${index}`
      }));
      
      // 根据检查项目名称或特征将结果分类
      if (category === '标准检验') {
        return results.filter(r => {
          const name = getItemName(r).toLowerCase();
          return name.includes('血') || 
                 name.includes('尿') || 
                 name.includes('常规') || 
                 name.includes('生化') || 
                 name.includes('细胞') ||
                 (r.检查类型 && r.检查类型.includes('常规检验'));
        });
      } else if (category === '影像检查') {
        return results.filter(r => {
          const name = getItemName(r).toLowerCase();
          return name.includes('ct') || 
                 name.includes('mri') || 
                 name.includes('超声') || 
                 name.includes('x线') || 
                 name.includes('造影') ||
                 name.includes('影像') ||
                 (r.检查类型 && r.检查类型.includes('影像'));
        });
      } else if (category === '特殊检查') {
        return results.filter(r => {
          const name = getItemName(r).toLowerCase();
          return name.includes('病理') || 
                 name.includes('免疫') || 
                 name.includes('基因') || 
                 name.includes('微生物') ||
                 (r.检查类型 && r.检查类型.includes('特殊'));
        });
      } else {
        // 其他所有未分类的项目
        return results.filter(r => {
          const name = getItemName(r).toLowerCase();
          return !name.includes('血') && 
                 !name.includes('尿') && 
                 !name.includes('常规') && 
                 !name.includes('生化') && 
                 !name.includes('细胞') &&
                 !name.includes('ct') && 
                 !name.includes('mri') && 
                 !name.includes('超声') && 
                 !name.includes('x线') && 
                 !name.includes('造影') &&
                 !name.includes('影像') &&
                 !name.includes('病理') && 
                 !name.includes('免疫') && 
                 !name.includes('基因') && 
                 !name.includes('微生物') &&
                 !(r.检查类型 && (
                   r.检查类型.includes('常规检验') || 
                   r.检查类型.includes('影像') || 
                   r.检查类型.includes('特殊')
                 ));
        });
      }
    };
    
    // 获取表格行类名
    const getRowClassName = (record) => {
      if (record.结果状态 === '异常') return 'result-row-abnormal';
      if (record.结果状态 === '偏高') return 'result-row-high';
      if (record.结果状态 === '偏低') return 'result-row-low';
      return '';
    };
    
    const hasJsonProperty = (jsonString, property) => {
      if (!jsonString || typeof jsonString !== 'string') return false;
      try {
        const json = JSON.parse(jsonString);
        // 检查顶层属性
        if (json && json[property] !== undefined) return true;
        
        // 检查interaction_history内的属性
        if (json && json.interaction_history && json.interaction_history[property] !== undefined) return true;
        
        return false;
      } catch (e) { return false; }
    }
    
    // 检查一个记录是否有诊断
    const hasDiagnosis = (record) => {
      try {
        if (!record || !record.messages || !Array.isArray(record.messages)) return false;
        
        return record.messages.some(msg => {
          if (!msg || !msg.message || typeof msg.message !== 'string') return false;
          return isJsonString(msg.message) && hasJsonProperty(msg.message, 'diagnosis');
        });
      } catch (error) {
        console.error('Error checking for diagnosis:', error);
        return false;
      }
    };
    
    // 检查一个记录是否有初步诊断
    const hasPreliminaryDiagnosis = (record) => {
      try {
        if (!record || !record.messages || !Array.isArray(record.messages)) return false;
        
        return record.messages.some(msg => {
          if (!msg || !msg.message || typeof msg.message !== 'string') return false;
          return isJsonString(msg.message) && hasJsonProperty(msg.message, 'preliminary_diagnosis');
        });
      } catch (error) {
        console.error('Error checking for preliminary diagnosis:', error);
        return false;
      }
    };
    
    // 检查一个记录是否有检查建议
    const hasInspectionSuggestions = (record) => {
      try {
        if (!record || !record.messages || !Array.isArray(record.messages)) return false;
        
        return record.messages.some(msg => {
          if (!msg || !msg.message || typeof msg.message !== 'string') return false;
          return isJsonString(msg.message) && hasJsonProperty(msg.message, 'inspection_suggestions');
        });
      } catch (error) {
        console.error('Error checking for inspection suggestions:', error);
        return false;
      }
    };
    
    // 图片查看相关状态
    const imageDrawerVisible = ref(false)
    const selectedSessionId = ref(null)
    const caseImages = ref([])
    const imageFilterCategory = ref('all')
    const previewVisible = ref(false)
    const previewImage = ref(null)
    
    // 过滤后的图片列表
    const filteredImages = computed(() => {
      if (!caseImages.value) return []
      if (imageFilterCategory.value === 'all') return caseImages.value
      return caseImages.value.filter(img => img.category === imageFilterCategory.value)
    })
    
    // 显示会话图片
    const showCaseImages = async (record) => {
      selectedSessionId.value = record.id
      imageDrawerVisible.value = true
      await fetchSessionImages(record.id)
    }
      
    // 获取会话图片
    const fetchSessionImages = async (sessionId) => {
      try {
        const res = await request({
          url: `/images/session/${sessionId}`,
          method: 'get'
        })
        
        console.log('获取图片响应:', res);
        
        // 处理不同的响应结构
        if (res) {
          let imagesData = [];
          
          // 处理两种可能的响应结构
          if (res.code === 200 && res.content) {
            // 结构1: { code: 200, content: [...] }
            imagesData = res.content;
          } else if (Array.isArray(res)) {
            // 结构2: 直接返回数组
            imagesData = res;
          } else if (res.status === 200 && res.data) {
            // 结构3: { status: 200, data: [...] }
            imagesData = res.data;
          }
          
          caseImages.value = imagesData || [];
          
          if (caseImages.value.length === 0) {
            message.info('该会话暂无图片');
          } else {
            console.log(`成功获取${caseImages.value.length}张图片`);
          }
        } else {
          message.error('获取图片失败：响应为空');
          caseImages.value = [];
        }
      } catch (error) {
        console.error('获取会话图片出错:', error);
        message.error('获取图片时发生错误');
        caseImages.value = [];
      }
    }
    
    // 过滤图片
    const filterImages = (value) => {
      imageFilterCategory.value = value
    }
    
    // 显示图片预览
    const showImagePreview = (image) => {
      previewImage.value = image
      previewVisible.value = true
    }
    
    // 添加生命周期钩子，确保组件销毁前清理所有引用
    onBeforeUnmount(() => {
      try {
        selectedSession.value = null;
        renderDrawerContent.value = false;
        drawerVisible.value = false;
        sessions.value = [];
      } catch (error) {
        console.error('Error in onBeforeUnmount cleanup:', error);
      }
    });
    
    onMounted(() => {
      fetchSessions(pagination.value.current, pagination.value.pageSize);
    });
    
    // 处理图片加载错误
    const handleImageError = (event, image, isPreview = false) => {
      console.error(`图片加载失败: ${isPreview ? '预览图' : '缩略图'}, ID: ${image.id}, GridFS ID: ${image.gridFsId}`);
      
      // 使用内联base64图片作为错误占位符，避免额外的HTTP请求
      event.target.src = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAAEFUlEQVR4nO3dW4hVVRzH8d+amg7eJjSfoqKgyIgKoqjAJ7sQXSh6CCIiIoKgh4qgeoiE6KWXjLrQTQ+KQl0oQhCjC1EUEpEQdDG7WVmRaTU1OY4z08Oa0TnO7L3P2WvtOWv/f08n7XXXv/zP3n32OcfMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMzMrNcktQHHgMnQWcAh4I6kDTnDJZ0CjgBnQucAB4Hbk/YjadBpwFFgdugM4ABwW9KGnOGSZgBHgFmhM4D9wK1J+5E06DTgGLA4dAawD7glaUPOcEnTgaPAktAZwF7g5qT9SBp0KnACWBo6A9gDLE/akDNc0jTgJHBF6AxgN3BT0n4kDToFOAksC50B7AKuT9qQM1zSVOAUcGXoDGAncF3SfiQNOhk4BVwVOgPYAVybtCFnuKRJwGng6tAZwHbgmqT9SBp0EnAauCZ0BrANWJG0IWe4pInAGWBl6AxgK7AiaT+SBk0AzgLXhs4AtgBXJ23IGS5pPHAOWBU6A9gMXJW0H0mDxgPngdWhM4BNwJVJG3KGSxoHjAHXhc4ANgKXJ+1H0qCxwAXghtAZwAbgsqQNOcMljQEuAjeGzgDWA5cl7UfSoIuAC8BNoTOAdcClSRtyhos/bAzgltAZwFrg4qT9SBr0A/AzcGvoDOBL4KKkDTnDxR/2C3B76AzgC2BK0n4kDfoO+BW4I3QG8DkwOWlDznDxh/0G3Bk6A/gMmJS0H0mDvgZ+B+4KnQF8CkxM2pAzXPxhfwD3hM4APgEmJO1H0qAvgT+Be0NnAB8D45M25AwXf9jfwP2hM4CPgHFJ+5E06BPgH+CB0BvAeMDppQ85w8Uf9i/wYOgM4ANgTNJ+JA16H/gPeCh0BvAeMDppQ85w8Yf9BzwcOgN4FxiVtB9Jg94B/gUeCZ0BvA2MSNqQM1z8YQAjQ2cAbwEjkvYjadDrQAM4LXQGsAYYnrQhZ7j4w1rAuaEzgKeBgUn7kTToSaAFnBe4AeAJYEDShpzh4g9rAeeHzgAeBwYk7UfSoEeANnBB4AaAR4H+SRtyhos/rA1cGDoDWA30T9qPpEEPAR3goqANAA8CfZM25AwXf1gHuDh0BvAA0DdpP5IGrQI6wCVBGwBWAX2SNuQMF39YB7g0dAZwP9AnaT+SBt0LdIHLgjYA3AP0TtqQM1z8YV3g8tAZwN1A76T9MKtJfwPXh3FoKGpLTAAAAABJRU5ErkJggg==';
      
      if (!isPreview) {
        message.error(`图片 ${image.originalName || '未命名'} 加载失败`);
      }
    };
    
    const getImageUrl = (image) => {
      const imageId = image.gridFsId || image.id;
      return `/images/${imageId}`;
    };

    const hasOcrStructuredData = (testObject) => {
      if (!testObject || typeof testObject !== 'object') return false;
      return testObject.检查结果 && Array.isArray(testObject.检查结果) && testObject.检查结果.length > 0;
    };

    const getOcrParsedResults = (testObject) => {
      if (!hasOcrStructuredData(testObject)) return [];
      return testObject.检查结果.map((result, index) => ({
        ...result,
        key: `parsed-result-${index}`
      }));
    };
    
    // 获取状态标签
    const getStatusTag = (status) => {
      switch (status) {
        case '最终诊断':
          return { text: '最终诊断', color: 'green' };
        case '初步诊断':
          return { text: '初步诊断', color: 'blue' };
        case '检查建议':
          return { text: '检查建议', color: 'purple' };
        case '问诊阶段':
        default:
          return { text: '问诊阶段', color: 'orange' };
      }
    };
    
    return {
      loading, sessions, columns, pagination, drawerVisible, selectedSession, renderDrawerContent,
      handleTableChange, showSessionDetail, onDrawerVisibleChange, 
      formatJson, renderMarkdown, getResultStatusColor, showResultDetail,
      formatDate, formatDateTime, resultFilter, filteredResults, showAllResults,
      collapsedCards, toggleCardCollapse, getItemName,
      searchQuery, onSearchChange, filteredAndSearchedResults, getResultsByCategory,
      resultTableColumns, activeCollapseKeys, customPanelStyle, getRowClassName,
      hasJsonProperty, isJsonString, hasDiagnosis, hasPreliminaryDiagnosis, hasInspectionSuggestions,
      imageDrawerVisible, selectedSessionId, caseImages, filteredImages,
      imageFilterCategory, previewVisible, previewImage,
      showCaseImages, fetchSessionImages, filterImages, showImagePreview, handleImageError,
      getImageUrl,
      hasOcrStructuredData, getOcrParsedResults,
      fetchSessions,
      getStatusTag
    }
  }
})
</script>

<style scoped>
.admin-data {
  padding: 24px;
}

.admin-title {
  font-size: 24px;
  margin-bottom: 24px;
  color: #333;
}

.session-table-container {
  background: #fff;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.session-id {
  font-weight: bold;
  color: #1890ff;
}

.session-meta-tags {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.drawer-header {
  margin-bottom: 20px;
  background-color: #f9f9f9;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.drawer-header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.drawer-header-top h3 {
  margin-bottom: 0;
}

.drawer-header-top p {
  margin-bottom: 4px;
  color: #666;
}

.message-container {
  margin-top: 16px;
  max-height: calc(100vh - 280px); /* 调整全屏模式下的高度 */
  overflow-y: auto;
  padding-right: 10px; /* 为滚动条留出空间 */
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  padding: 20px;
}

.message-container h3 {
  margin-bottom: 20px;
  font-size: 18px;
  color: #1890ff;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.message-item {
  margin-bottom: 16px;
  padding: 12px 16px;
  border-radius: 8px;
  background-color: transparent; /* 改为透明 */
  border: none; /* 移除边框 */
}

.doctor-message {
  background-color: transparent; /* 改为透明 */
  border-left: 4px solid #1890ff; /* 保留左侧条以示区分 */
  padding-left: 12px;
}

.patient-message {
  background-color: transparent; /* 改为透明 */
  border-left: 4px solid #52c41a; /* 保留左侧条以示区分 */
  padding-left: 12px;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  margin-top: 8px;
}

.message-header-info {
  display: flex;
  align-items: center;
}

.message-id {
  margin-left: 8px;
  color: #999;
}

.message-time {
  font-size: 12px;
  color: #999;
}

.message-content-wrapper {
  margin-top: 8px;
}

.interaction-history-display {
  border: none; /* 移除边框 */
  padding: 0; /* 移除内边距 */
  background-color: transparent; /* 改为透明 */
}

.interaction-history-display h5 {
  margin-top: 16px; /* 增加与上方内容的间距 */
  margin-bottom: 12px;
  font-size: 15px;
  color: #333;
}

.cot-entry-section {
  margin-bottom: 16px;
  padding: 16px;
  border-radius: 4px;
  background-color: #f9f9f9; /* 保留一个非常浅的背景色以作区分 */
  border: 1px solid #f0f0f0;
}

.cot-entry-section h5 {
   margin-bottom: 8px;
   font-size: 14px;
   color: #555;
}

.overall-diagnosis-section {
  margin-top: 24px;
  padding: 0; /* 移除内边距 */
  background-color: transparent; /* 改为透明 */
  border-radius: 0;
  box-shadow: none; /* 移除阴影 */
}

.dialogue-history-log {
  max-height: 250px; 
  overflow-y: auto;
  padding: 8px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  background: #fff;
}

.dialogue-entry {
  display: flex;
  margin-bottom: 10px;
  align-items: flex-start;
}

.dialogue-speaker {
  margin-right: 8px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.dialogue-text {
  flex-grow: 1;
  padding: 8px 10px;
  border-radius: 6px;
  word-break: break-word;
}

.dialogue-entry.doctor-entry .dialogue-text {
  background-color: #e6f7ff; 
}

.dialogue-entry.patient-entry .dialogue-text {
  background-color: #f6ffed; 
}

.section-content {
  padding: 8px;
  background-color: #ffffff;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
}

.recommendation-list {
  list-style-type: disc;
  padding-left: 20px;
  margin-top: 5px;
}

.recommendation-list li {
  margin-bottom: 5px;
}

.markdown-body :deep(p:last-child) {
  margin-bottom: 0;
}

pre {
  white-space: pre-wrap;      /* CSS3 */
  white-space: -moz-pre-wrap; /* Mozilla, since 1999 */
  white-space: -pre-wrap;      /* Opera 4-6 */
  white-space: -o-pre-wrap;    /* Opera 7 */
  word-wrap: break-word;       /* Internet Explorer 5.5+ */
  overflow-x: auto;
  max-height: 400px;
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
}

/* 继承 .markdown-body 的通用样式 */
.dialogue-text.markdown-body,
.section-content.markdown-body,
.recommendation-list.markdown-body :deep(li) {
  /* 这些元素将通过 v-html 插入 Markdown 内容 */
  /* 确保它们能继承 .markdown-body 下定义的样式 */
}

/* Markdown 内容的特定样式 */
.markdown-body :deep(h1),
.markdown-body :deep(h2),
.markdown-body :deep(h3),
.markdown-body :deep(h4),
.markdown-body :deep(h5),
.markdown-body :deep(h6) {
  margin-top: 0.8em;
  margin-bottom: 0.4em;
  font-weight: 600;
}
.markdown-body :deep(h1) { font-size: 1.6em; }
.markdown-body :deep(h2) { font-size: 1.4em; }
.markdown-body :deep(h3) { font-size: 1.2em; }

.markdown-body :deep(p) {
  margin-bottom: 0.8em;
  line-height: 1.6;
}

.markdown-body :deep(ul),
.markdown-body :deep(ol) {
  padding-left: 2em;
  margin-bottom: 0.8em;
}

.markdown-body :deep(li) > :deep(p) {
   margin-bottom: 0.2em;
}

.markdown-body :deep(blockquote) {
  margin: 0 0 0.8em 0;
  padding: 0.2em 1em;
  color: #6a737d;
  border-left: 0.25em solid #dfe2e5;
}

.markdown-body :deep(pre) {
  background-color: #f3f3f3;
  border-radius: 4px;
  font-size: 0.9em;
  line-height: 1.45;
  overflow: auto;
  padding: 1em;
  margin-bottom: 0.8em;
}

.markdown-body :deep(code) {
  padding: 0.2em 0.4em;
  margin: 0;
  font-size: 0.9em;
  background-color: #f3f3f3;
  border-radius: 3px;
  font-family: SFMono-Regular, Consolas, 'Liberation Mono', Menlo, Courier, monospace;
}

.markdown-body :deep(pre code) {
  padding: 0;
  font-size: inherit;
  background-color: transparent;
  border-radius: 0;
}

.markdown-body :deep(table) {
  border-collapse: collapse;
  margin-bottom: 0.8em;
  width: auto;
  display: block;
  overflow-x: auto;
}

.markdown-body :deep(th),
.markdown-body :deep(td) {
  border: 1px solid #ccc;
  padding: 0.4em 0.6em;
}

.markdown-body :deep(tr:nth-child(2n)) {
  background-color: #f6f8fa;
}

.markdown-body :deep(img) {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 0.8em 0;
}

/* 检查建议样式 */
.test-report {
  margin-bottom: 20px;
  padding: 15px;
  border-radius: 6px;
  background-color: #fafafa;
  border: 1px solid #eee;
}

.test-info {
  margin-bottom: 15px;
}

.info-table {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;
}

.info-row {
  width: 50%;
  padding: 4px 0;
  display: flex;
}

.info-label {
  font-weight: 600;
  margin-right: 8px;
  color: #555;
  min-width: 80px;
}

.info-value {
  color: #333;
}

.test-results {
  margin-bottom: 15px;
}

.abnormal-result {
  color: #f5222d;
  font-weight: 600;
}

h5 {
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px dashed #ddd;
  color: #1890ff;
}

/* 新增样式 */
.result-controls {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.result-grouped-table {
  margin-top: 16px;
}

.result-grouped-table :deep(.ant-collapse-header) {
  font-weight: 600;
  color: #1890ff;
  font-size: 15px;
}

.result-grouped-table :deep(.ant-table-thead > tr > th) {
  background-color: #f0f5ff;
  font-weight: 600;
  color: #444;
}

.result-grouped-table :deep(.result-row-abnormal) {
  background-color: #fff1f0;
}

.result-grouped-table :deep(.result-row-high) {
  background-color: #fff7e6;
}

.result-grouped-table :deep(.result-row-low) {
  background-color: #e6f7ff;
}

.abnormal-marker {
  margin-left: 6px;
  color: #f5222d;
}

@media (max-width: 768px) {
  .result-controls {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .result-controls > *:not(:first-child) {
    margin-top: 8px;
    margin-left: 0 !important;
  }
}

/* 保留原有卡片样式以备用 */
.result-filter {
  margin-bottom: 10px;
}

.result-compact-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.result-compact-card {
  width: calc(33.33% - 10px);
  padding: 10px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  cursor: pointer;
}

.result-card-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-card-name {
  font-weight: 600;
  color: #333;
}

.result-card-value-area {
  display: flex;
  align-items: center;
}

.result-card-value {
  font-weight: 500;
}

.result-card-unit {
  margin-left: 4px;
  color: #666;
}

.result-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.result-card-range {
  color: #999;
}

.result-status-tag {
  margin-left: 4px;
}

.search-section {
  margin-bottom: 16px;
}

.result-row-abnormal {
  background-color: #fff1f0;
}

.result-row-high {
  background-color: #fff7e6;
}

.result-row-low {
  background-color: #e6f7ff;
}

.result-card-abnormal {
  border-left: 3px solid #f5222d;
}

.result-card-high {
  border-left: 3px solid #fa8c16;
}

.result-card-low {
  border-left: 3px solid #1890ff;
}

.result-card-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding-top: 8px;
  margin-top: 8px;
  border-top: 1px dashed #f0f0f0;
}

.result-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding-bottom: 8px;
}

.result-card-collapsed {
  padding: 10px;
  min-height: 42px;
}

.result-card-collapsed .result-card-header {
  padding-bottom: 0;
}

.card-collapse-icon {
  color: #999;
}

.result-status-tag-inline {
  margin-left: 8px;
  vertical-align: middle;
}

.result-card-content {
  margin-top: 8px;
}

@media (max-width: 1200px) {
  .result-compact-card {
    width: calc(50% - 10px);
  }
}

@media (max-width: 768px) {
  .result-compact-card {
    width: 100%;
  }
}

.dialogue-timestamp {
  font-size: 12px;
  color: #999;
  margin-left: 8px;
}

.session-statistics {
  margin: 20px 0;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  padding: 20px;
}

.session-statistics h3 {
  margin-bottom: 20px;
  font-size: 18px;
  color: #1890ff;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.statistics-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.stat-item {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 8px;
  text-align: center;
  transition: all 0.3s ease;
}

.stat-item:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  transform: translateY(-2px);
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
  color: #1890ff;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

@media (max-width: 768px) {
  .statistics-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 新增 inspection_suggestions 相关样式 */
.inspection-list {
  list-style-type: disc;
  margin-left: 0;
  padding-left: 20px;
}

.inspection-item-object {
  background-color: #f9f9f9;
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 8px;
  border: 1px solid #eee;
}

.inspection-detail {
  margin-bottom: 6px;
  display: flex;
}

.inspection-label {
  font-weight: 600;
  color: #555;
  margin-right: 8px;
  min-width: 100px;
  flex-shrink: 0;
}

.inspection-value {
  color: #333;
  flex-grow: 1;
}

/* 修改标签页样式 */
.overall-diagnosis-section :deep(.ant-tabs-tab) {
  padding: 8px 16px;
}

.overall-diagnosis-section :deep(.ant-tabs-tab-active) {
  font-weight: 600;
}

.overall-diagnosis-section :deep(.ant-tabs-tab:hover) {
  color: #1890ff;
}

.markdown-body :deep(table) {
  border-collapse: collapse;
  margin-bottom: 0.8em;
  width: auto;
  display: block;
  overflow-x: auto;
}

.markdown-body :deep(th),
.markdown-body :deep(td) {
  border: 1px solid #ccc;
  padding: 0.4em 0.6em;
}

.markdown-body :deep(tr:nth-child(2n)) {
  background-color: #f6f8fa;
}

.markdown-body :deep(img) {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 0.8em 0;
}

/* 新增抽屉样式 */
.admin-data :deep(.ant-drawer-header) {
  padding: 16px 24px;
  background-color: #f0f2f5;
  border-bottom: 1px solid #e8e8e8;
}

.admin-data :deep(.ant-drawer-title) {
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

.admin-data :deep(.ant-drawer-body) {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .admin-data :deep(.ant-drawer-body) {
    padding: 16px;
  }
}

.drawer-footer {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #eee;
  text-align: center;
}

.drawer-footer button {
  min-width: 120px;
}

/* 新增图片查看相关样式 */
.image-filter-bar {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.image-gallery {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.image-item {
  border-radius: 8px;
  overflow: hidden;
}

.image-card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.image-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.image-header {
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

.image-title {
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
}

.image-content {
  height: 200px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  cursor: pointer;
}

.image-content img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.image-footer {
  padding: 8px 12px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #999;
}

.image-time {
  font-size: 12px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

@media (max-width: 768px) {
  .image-gallery {
    grid-template-columns: 1fr;
  }
}

/* 添加简化的检查结果文本块样式 */
.test-results-text-blocks {
  margin-top: 16px;
}

.test-text-block {
  padding: 16px;
  margin-bottom: 16px;
  background-color: #f9f9f9;
  border: 1px solid #eee;
  border-radius: 8px;
}

.test-results-simple {
  margin-top: 12px;
}

.test-result-line {
  margin-bottom: 8px;
  padding: 4px 0;
  border-bottom: 1px dashed #eee;
}

.test-result-line:last-child {
  border-bottom: none;
}

.test-info {
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}
</style>