package com.makiyo.aidoctor.utils;

import com.makiyo.aidoctor.entity.User;
import com.makiyo.aidoctor.form.UserInfoForm;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Supplier;

/**
 * Bean 复制工具类
 */
public class BeanCopyUtils {
    
    /**
     * 复制对象
     * @param source 源对象
     * @param targetClass 目标对象类型
     * @return 目标对象
     */
    public static <T> T copyBean(Object source, Class<T> targetClass) {
        if (source == null) {
            return null;
        }
        T target = null;
        try {
            target = targetClass.newInstance();
            BeanUtils.copyProperties(source, target);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return target;
    }

    /**
     * 复制对象（使用对象工厂）
     * @param source 源对象
     * @param targetSupplier 目标对象工厂
     * @return 目标对象
     */
    public static <T> T copyBean(Object source, Supplier<T> targetSupplier) {
        if (source == null) {
            return null;
        }
        T target = targetSupplier.get();
        BeanUtils.copyProperties(source, target);
        return target;
    }

    /**
     * 复制列表
     * @param sourceList 源列表
     * @param targetClass 目标对象类型
     * @return 目标对象列表
     */
    public static <S, T> List<T> copyList(List<S> sourceList, Class<T> targetClass) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return new ArrayList<>();
        }
        List<T> targetList = new ArrayList<>(sourceList.size());
        for (S source : sourceList) {
            T target = copyBean(source, targetClass);
            targetList.add(target);
        }
        return targetList;
    }

    /**
     * 复制列表（使用对象工厂）
     * @param sourceList 源列表
     * @param targetSupplier 目标对象工厂
     * @return 目标对象列表
     */
    public static <S, T> List<T> copyList(List<S> sourceList, Supplier<T> targetSupplier) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return new ArrayList<>();
        }
        List<T> targetList = new ArrayList<>(sourceList.size());
        for (S source : sourceList) {
            T target = copyBean(source, targetSupplier);
            targetList.add(target);
        }
        return targetList;
    }

    /**
     * 将 User 实体复制到 UserInfoForm，仅复制基本信息字段
     * @param user 用户实体
     * @return UserInfoForm 对象，如果入参为 null 则返回 null
     */
    public static UserInfoForm copyUserToUserInfoForm(User user) {
        if (user == null) {
            return null;
        }
        
        UserInfoForm userInfoForm = new UserInfoForm();
        userInfoForm.setUsername(user.getUsername());
        userInfoForm.setProfilePicture(user.getProfilePicture());
        userInfoForm.setBio(user.getBio());
        
        return userInfoForm;
    }
} 