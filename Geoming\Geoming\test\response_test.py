import json
import os
from openai import OpenAI
from dotenv import load_dotenv


load_dotenv('../.env')  # 加载环境变量

api_key = os.getenv("OPENAI_API_KEY")

client = OpenAI(
    api_key=api_key,
)

response = client.responses.create(
    model="gpt-4o",
    tools=[{"type": "web_search_preview",
        "user_location": {
            "type": "approximate",
            "country": "CN",
            "city": "Beijing",
            "region": "Beijing",
        }
    }],
    input="现在知乎热点排行榜上有什么"
)

print(json.dumps(response.model_dump(), indent=4, ensure_ascii=False))