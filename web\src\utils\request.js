import axios from 'axios'
import { message } from 'ant-design-vue'

// 创建 axios 实例
const service = axios.create({
  // 根据环境变量确定baseURL
  baseURL: '',  // 明确指定后端地址
  timeout: 300000 // 请求超时时间
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data
    return res
  },
  error => {
    console.error('响应错误:', error)
    if (error.response) {
      console.error('错误响应:', error.response)
      message.error(error.response.data?.message || '请求失败')
    } else if (error.request) {
      console.error('请求未收到响应')
      message.error('服务器无响应，请检查后端服务是否正常运行')
    } else {
      console.error('请求配置错误:', error.message)
      message.error('请求配置错误')
    }
    return Promise.reject(error)
  }
)

export default service 