package com.makiyo.ai_doctor_prod.service;

import lombok.AllArgsConstructor;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PreDestroy;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * OCR队列服务，确保OCR请求按顺序一个一个处理 (生产环境)
 */
@Service
public class OcrQueueService {
    private static final Logger log = LoggerFactory.getLogger(OcrQueueService.class);
    
    // 请求队列
    private final Queue<OcrRequest> requestQueue = new ConcurrentLinkedQueue<>();
    // 是否正在处理的标志
    private final AtomicBoolean isProcessing = new AtomicBoolean(false);
    // OCR服务
    private final OcrService ocrService;
    // 用于异步处理的线程池
    private final ExecutorService executorService;
    
    // 队列统计
    private final AtomicInteger totalProcessed = new AtomicInteger(0);
    private final AtomicInteger totalSuccess = new AtomicInteger(0);
    private final AtomicInteger totalFailed = new AtomicInteger(0);
    
    // 临时文件目录
    @Value("${ocr.image.prod.save.path:${user.dir}/images}")
    private String imageSavePath;
    
    // 临时文件保留时间(小时)
    @Value("${ocr.temp.file.max-age-hours:24}")
    private int tempFileMaxAgeHours;
    
    @Autowired
    public OcrQueueService(OcrService ocrService) {
        this.ocrService = ocrService;
        
        // 创建更可靠的线程工厂
        ThreadFactory threadFactory = r -> {
            Thread thread = Executors.defaultThreadFactory().newThread(r);
            thread.setName("ocr-queue-processor-prod");
            thread.setDaemon(true); // 设置为守护线程
            thread.setUncaughtExceptionHandler((t, e) -> {
                log.error("[PROD OCR Queue] 处理线程发生未捕获异常: {}", e.getMessage(), e);
                // 确保处理标志被重置，以便新请求能够继续处理
                isProcessing.set(false);
                // 尝试重启处理队列
                if (!requestQueue.isEmpty()) {
                    startProcessing();
                }
            });
            return thread;
        };
        
        // 创建单线程执行器确保顺序处理
        this.executorService = Executors.newSingleThreadExecutor(threadFactory);
        
        log.info("[PROD OCR Queue] OCR队列服务已初始化");
    }
    
    /**
     * 将OCR请求加入队列 (无状态版本，不与数据库交互)
     * 
     * @param image 上传的图片文件
     * @return 包含OCR处理结果的CompletableFuture
     */
    public CompletableFuture<Object> enqueueOcrRequest(MultipartFile image) {
        CompletableFuture<Object> future = new CompletableFuture<>();
        
        try {
            // 立即将文件内容读入内存，避免长时间持有文件句柄
            byte[] imageData = image.getBytes();
            String originalFilename = image.getOriginalFilename();
            String contentType = image.getContentType();
            
            // 创建深度复制的请求对象，避免引用原始MultipartFile
            OcrRequest request = new OcrRequest(imageData, originalFilename, contentType, future);
            requestQueue.add(request);
            
            log.info("[PROD OCR Queue] OCR请求已加入队列，当前队列长度: {}, 图片大小: {} 字节", 
                    requestQueue.size(), imageData.length);
            
            // 如果没有正在处理的请求，启动处理
            if (isProcessing.compareAndSet(false, true)) {
                startProcessing();
            }
        } catch (IOException e) {
            log.error("[PROD OCR Queue] 读取上传文件时出错: {}", e.getMessage(), e);
            future.completeExceptionally(e);
        }
        
        return future;
    }

    /**
     * 将带ID参数的OCR请求加入队列 (与数据库交互版本)
     * 
     * @param image 上传的图片文件
     * @param id 案例ID
     * @param name 用户名
     * @return 包含OCR处理结果的CompletableFuture
     */
    public CompletableFuture<Object> enqueueOcrRequestWithId(MultipartFile image, String id, String name) {
        CompletableFuture<Object> future = new CompletableFuture<>();
        
        try {
            // 立即将文件内容读入内存，避免长时间持有文件句柄
            byte[] imageData = image.getBytes();
            String originalFilename = image.getOriginalFilename();
            String contentType = image.getContentType();
            
            // 创建深度复制的请求对象，避免引用原始MultipartFile
            OcrRequest request = new OcrRequest(imageData, originalFilename, contentType, id, name, future);
            requestQueue.add(request);
            
            log.info("[PROD OCR Queue] OCR请求已加入队列，案例ID: {}, 当前队列长度: {}, 图片大小: {} 字节", 
                    id, requestQueue.size(), imageData.length);
            
            // 如果没有正在处理的请求，启动处理
            if (isProcessing.compareAndSet(false, true)) {
                startProcessing();
            }
        } catch (IOException e) {
            log.error("[PROD OCR Queue] 读取上传文件时出错，案例ID: {}: {}", id, e.getMessage(), e);
            future.completeExceptionally(e);
        }
        
        return future;
    }
    
    /**
     * 启动队列处理
     */
    private void startProcessing() {
        executorService.submit(() -> {
            try {
                processNextRequest();
            } catch (Exception e) {
                log.error("[PROD OCR Queue] 队列处理过程中发生未捕获的异常: {}", e.getMessage(), e);
                isProcessing.set(false); // 重置处理状态
                
                // 尝试重新启动处理
                if (!requestQueue.isEmpty() && isProcessing.compareAndSet(false, true)) {
                    startProcessing();
                }
            }
        });
    }
    
    /**
     * 递归处理队列中的下一个请求
     */
    private void processNextRequest() {
        OcrRequest request = requestQueue.poll();
        if (request == null) {
            log.debug("[PROD OCR Queue] 队列已空，OCR处理完成");
            isProcessing.set(false);
            return;
        }
        
        log.info("[PROD OCR Queue] 开始处理OCR请求，案例ID: {}, 剩余队列长度: {}", 
                request.getId() != null ? request.getId() : "无ID", requestQueue.size());
        
        ByteArrayMultipartFile multipartFile = null;
        try {
            // 从内存中的字节数组创建MultipartFile的实现
            multipartFile = new ByteArrayMultipartFile(
                    request.getImageData(), 
                    request.getOriginalFilename(),
                    request.getContentType());
            
            // 处理OCR请求
            Object result;
            if (request.getId() != null) {
                // 带ID的请求，使用数据库交互版本
                result = ocrService.processOcrWithDbInteractionProd(
                        multipartFile, 
                        request.getId(), 
                        request.getName());
            } else {
                // 无ID的请求，使用无状态版本
                result = ocrService.processOcrProd(multipartFile);
            }
            
            request.getFuture().complete(result);
            log.info("[PROD OCR Queue] 完成OCR请求处理，案例ID: {}", 
                    request.getId() != null ? request.getId() : "无ID");
            
            totalProcessed.incrementAndGet();
            totalSuccess.incrementAndGet();
            
        } catch (Exception e) {
            log.error("[PROD OCR Queue] 处理OCR请求时出错，案例ID: {}: {}", 
                    request.getId() != null ? request.getId() : "无ID", e.getMessage(), e);
            request.getFuture().completeExceptionally(e);
            
            totalProcessed.incrementAndGet();
            totalFailed.incrementAndGet();
        } finally {
            // 清理请求资源
            request.setImageData(null);
            
            // 确保释放临时文件资源
            if (multipartFile != null) {
                try {
                    // 显式调用资源释放
                    ((ByteArrayMultipartFile)multipartFile).releaseResources();
                    multipartFile = null;
                    System.gc(); // 提示JVM进行垃圾回收
                } catch (Exception e) {
                    log.warn("[PROD OCR Queue] 清理MultipartFile资源时出错: {}", e.getMessage());
                }
            }
            
            // 继续处理下一个请求
            processNextRequest();
        }
    }
    
    /**
     * 获取当前队列长度
     * @return 队列中等待处理的请求数量
     */
    public int getQueueSize() {
        return requestQueue.size();
    }
    
    /**
     * 获取队列统计信息
     * @return 包含处理统计的Map
     */
    public Map<String, Integer> getQueueStats() {
        return Map.of(
            "totalProcessed", totalProcessed.get(),
            "totalSuccess", totalSuccess.get(),
            "totalFailed", totalFailed.get(),
            "currentQueueSize", requestQueue.size()
        );
    }
    
    /**
     * 定时清理临时文件目录
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupTempFiles() {
        try {
            log.info("[PROD OCR Queue] 开始清理临时文件，目录: {}", imageSavePath);
            Path directory = Paths.get(imageSavePath);
            if (!Files.exists(directory) || !Files.isDirectory(directory)) {
                log.warn("[PROD OCR Queue] 临时文件目录不存在或不是目录: {}", imageSavePath);
                return;
            }
            
            long cutoffTime = System.currentTimeMillis() - (tempFileMaxAgeHours * 60 * 60 * 1000);
            List<Path> filesToDelete = new ArrayList<>();
            
            try (Stream<Path> paths = Files.list(directory)) {
                filesToDelete = paths
                    .filter(path -> {
                        try {
                            return Files.isRegularFile(path) &&
                                   Files.getLastModifiedTime(path).toMillis() < cutoffTime;
                        } catch (IOException e) {
                            log.warn("[PROD OCR Queue] 获取文件信息时出错: {}", e.getMessage());
                            return false;
                        }
                    })
                    .collect(Collectors.toList());
            }
            
            int deletedCount = 0;
            for (Path file : filesToDelete) {
                try {
                    Files.delete(file);
                    deletedCount++;
                } catch (IOException e) {
                    log.warn("[PROD OCR Queue] 删除临时文件失败: {}", e.getMessage());
                }
            }
            
            log.info("[PROD OCR Queue] 临时文件清理完成，共删除{}个文件", deletedCount);
        } catch (Exception e) {
            log.error("[PROD OCR Queue] 清理临时文件时出错: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 应用关闭时清理资源
     */
    @PreDestroy
    public void shutdown() {
        log.info("[PROD OCR Queue] 正在关闭OCR队列处理线程池...");
        executorService.shutdown();
        try {
            // 等待所有任务完成或超时
            if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                log.warn("[PROD OCR Queue] OCR队列处理线程池未能在60秒内完成所有任务，将强制关闭");
                executorService.shutdownNow();
                
                // 再次等待线程响应中断
                if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                    log.error("[PROD OCR Queue] OCR队列处理线程池未能完全关闭");
                }
            }
            log.info("[PROD OCR Queue] OCR队列处理线程池已成功关闭");
        } catch (InterruptedException e) {
            log.error("[PROD OCR Queue] 等待OCR队列处理线程池关闭时被中断", e);
            Thread.currentThread().interrupt();
            executorService.shutdownNow();
        }
        
        // 清空队列中的剩余请求
        int remainingRequests = requestQueue.size();
        if (remainingRequests > 0) {
            log.warn("[PROD OCR Queue] 在关闭时，OCR队列中仍有{}个未处理的请求，这些请求将被取消", remainingRequests);
            for (OcrRequest request : requestQueue) {
                request.getFuture().completeExceptionally(
                    new RuntimeException("服务关闭，请求被取消"));
                request.setImageData(null); // 清理内存
            }
            requestQueue.clear();
        }
    }
    
    /**
     * OCR请求的数据类
     */
    @Data
    private static class OcrRequest {
        private byte[] imageData;
        private String originalFilename;
        private String contentType;
        private String id;         // 案例ID，可为null
        private String name;       // 用户名，可为null
        private CompletableFuture<Object> future;
        
        // 无ID的构造函数
        public OcrRequest(byte[] imageData, String originalFilename, String contentType, 
                         CompletableFuture<Object> future) {
            this.imageData = imageData;
            this.originalFilename = originalFilename;
            this.contentType = contentType;
            this.future = future;
        }
        
        // 带ID的构造函数
        public OcrRequest(byte[] imageData, String originalFilename, String contentType, 
                         String id, String name, CompletableFuture<Object> future) {
            this.imageData = imageData;
            this.originalFilename = originalFilename;
            this.contentType = contentType;
            this.id = id;
            this.name = name;
            this.future = future;
        }
    }
    
    /**
     * 基于字节数组的MultipartFile实现，避免持有原始文件句柄
     */
    public static class ByteArrayMultipartFile implements MultipartFile {
        private byte[] content;
        private final String name;
        private final String originalFilename;
        private final String contentType;

        public ByteArrayMultipartFile(byte[] content, String originalFilename, String contentType) {
            this.content = content;
            this.name = "file";  // 表单字段名
            this.originalFilename = originalFilename;
            this.contentType = contentType;
        }

        @Override
        public String getName() {
            return name;
        }

        @Override
        public String getOriginalFilename() {
            return originalFilename;
        }

        @Override
        public String getContentType() {
            return contentType;
        }

        @Override
        public boolean isEmpty() {
            return content == null || content.length == 0;
        }

        @Override
        public long getSize() {
            return content == null ? 0 : content.length;
        }

        @Override
        public byte[] getBytes() throws IOException {
            return content;
        }

        @Override
        public java.io.InputStream getInputStream() throws IOException {
            return new java.io.ByteArrayInputStream(content);
        }

        @Override
        public void transferTo(java.io.File dest) throws IOException, IllegalStateException {
            try (java.io.FileOutputStream fos = new java.io.FileOutputStream(dest)) {
                fos.write(content);
                fos.flush(); // 确保内容写入磁盘
            }
        }

        public org.springframework.core.io.Resource getResource() {
            return new ByteArrayResource(content);
        }
        
        /**
         * 显式释放资源
         */
        public void releaseResources() {
            // 显式释放字节数组引用，帮助垃圾收集器回收内存
            if (content != null) {
                content = null;
            }
        }
    }
} 