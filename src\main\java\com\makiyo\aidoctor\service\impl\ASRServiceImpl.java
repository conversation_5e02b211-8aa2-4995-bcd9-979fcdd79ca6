package com.makiyo.aidoctor.service.impl;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.makiyo.aidoctor.service.ASRService;
import com.makiyo.aidoctor.utils.AudioConverter;
import okhttp3.*;
import okhttp3.logging.HttpLoggingInterceptor;
import okio.ByteString;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.sound.sampled.AudioFormat;
import javax.sound.sampled.AudioInputStream;
import javax.sound.sampled.AudioSystem;
import javax.sound.sampled.UnsupportedAudioFileException;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;

/**
 * 语音识别服务实现类
 * 基于火山引擎的大模型流式语音识别API
 */
@Service
public class ASRServiceImpl implements ASRService {
    private static final Logger logger = LoggerFactory.getLogger(ASRServiceImpl.class);
    
    // 协议常量，从BigASRWebsocketDemo复制
    private static final byte PROTOCOL_VERSION = 0b0001;
    private static final byte DEFAULT_HEADER_SIZE = 0b0001;
    private static final byte FULL_CLIENT_REQUEST = 0b0001;
    private static final byte AUDIO_ONLY_REQUEST = 0b0010;
    private static final byte FULL_SERVER_RESPONSE = 0b1001;
    private static final byte SERVER_ACK = 0b1011;
    private static final byte SERVER_ERROR_RESPONSE = 0b1111;
    private static final byte NO_SEQUENCE = 0b0000;
    private static final byte POS_SEQUENCE = 0b0001;
    private static final byte NEG_SEQUENCE = 0b0010;
    private static final byte NEG_WITH_SEQUENCE = 0b0011;
    private static final byte NO_SERIALIZATION = 0b0000;
    private static final byte JSON = 0b0001;
    private static final byte NO_COMPRESSION = 0b0000;
    private static final byte GZIP = 0b0001;
    
    @Value("${asr.api.url:wss://openspeech.bytedance.com/api/v3/sauc/bigmodel}")
    private String apiUrl;
    
    @Value("${asr.api.appId:8818472423}")
    private String appId;
    
    @Value("${asr.api.token:9amVr1lVrvkPWvzgY6VRSkcNSd1HY160}")
    private String token;
    
    @Value("${asr.temp.dir:${java.io.tmpdir}/asr}")
    private String tempDir;
    
    private final Gson gson = new Gson();
    
    @Override
    public void processAudioFile(File audioFile, SseEmitter emitter) throws Exception {
        // 创建临时目录
        File tempDirFile = new File(tempDir);
        if (!tempDirFile.exists()) {
            tempDirFile.mkdirs();
        }
        
        // 准备转换后的音频文件
        String convertedFileName = UUID.randomUUID() + "_16k.wav";
        File convertedFile = new File(tempDirFile, convertedFileName);
        
        try {
            // 检查文件是否存在且可读
            if (!audioFile.exists() || !audioFile.canRead()) {
                throw new IOException("音频文件不存在或无法读取: " + audioFile.getAbsolutePath());
            }
            
            // 记录文件信息
            logger.info("处理音频文件: {}, 大小: {} 字节", audioFile.getAbsolutePath(), audioFile.length());
            
            // 转换音频格式
            logger.info("开始转换音频格式...");
            AudioConverter.convertToStandardWav(audioFile, convertedFile);
            logger.info("音频格式转换完成");
            
            // 检查转换后的文件
            if (!convertedFile.exists() || convertedFile.length() == 0) {
                throw new IOException("音频转换失败，转换后的文件不存在或为空");
            }
            
            // 开始语音识别
            startASRProcess(convertedFile, emitter);
            
        } catch (Exception e) {
            logger.error("处理音频文件失败", e);
            try {
                emitter.send(SseEmitter.event()
                        .name("error")
                        .data("处理音频文件失败: " + e.getMessage()));
                emitter.complete();
            } catch (IOException ex) {
                logger.error("发送错误事件失败", ex);
            }
            throw e;
        } finally {
            // 清理临时文件
            try {
                if (convertedFile.exists()) {
                    convertedFile.delete();
                }
            } catch (Exception e) {
                logger.warn("清理临时文件失败", e);
            }
        }
    }
    
    /**
     * 启动语音识别流程
     */
    private void startASRProcess(File audioFile, SseEmitter emitter) throws Exception {
        // 读取音频文件
        final AudioInputStream ins;
        AudioFormat format;
        try {
            ins = AudioSystem.getAudioInputStream(audioFile);
            format = ins.getFormat();
        } catch (UnsupportedAudioFileException | IOException e) {
            throw new RuntimeException("音频文件格式不支持或读取失败", e);
        }
        
        // 构建WebSocket请求
        final Request request = new Request.Builder()
                .url(apiUrl)
                .header("X-Api-App-Key", appId)
                .header("X-Api-Access-Key", token)
                .header("X-Api-Resource-Id", "volc.bigasr.sauc.duration")
                .header("X-Api-Connect-Id", UUID.randomUUID().toString())
                .build();
        
        // 配置OkHttp客户端
        HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor();
        loggingInterceptor.setLevel(HttpLoggingInterceptor.Level.HEADERS);
        final OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .pingInterval(120, TimeUnit.SECONDS)  // 增加ping间隔到60秒
                .addInterceptor(loggingInterceptor)
                .readTimeout(300, TimeUnit.SECONDS)
                .writeTimeout(300, TimeUnit.SECONDS)
                .connectTimeout(30, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .build();
        
        // 创建WebSocket连接
        okHttpClient.newWebSocket(request, new ASRWebSocketListener(ins, format, emitter));
    }
    
    /**
     * WebSocket监听器，处理语音识别的请求和响应
     */
    private class ASRWebSocketListener extends WebSocketListener {
        private final AudioInputStream audioInputStream;
        private final AudioFormat audioFormat;
        private final SseEmitter emitter;
        private byte[] buffer;
        private int bufferSize;
        private int seq = 0;
        private final AtomicInteger messageCount = new AtomicInteger(0);
        
        public ASRWebSocketListener(AudioInputStream audioInputStream, AudioFormat audioFormat, SseEmitter emitter) {
            this.audioInputStream = audioInputStream;
            this.audioFormat = audioFormat;
            this.emitter = emitter;
        }
        
        @Override
        public void onOpen(WebSocket webSocket, Response response) {
            try {
                String logId = response.header("X-Tt-Logid");
                logger.info("WebSocket连接已打开, X-Tt-Logid: {}", logId);
                
                // 发送完整客户端请求
                sendFullClientRequest(webSocket);
                
                // 设置缓冲区大小
                int frames = (int) Math.min(audioInputStream.getFrameLength(), 
                        audioInputStream.getFrameLength() / 10); // 切成10段
                bufferSize = (audioFormat.getSampleSizeInBits() / Byte.SIZE) 
                        * audioFormat.getChannels() * frames;
                buffer = new byte[bufferSize];
                
                // 发送连接成功事件
                emitter.send(SseEmitter.event()
                        .name("connected")
                        .data("语音识别服务连接成功"));
                
            } catch (Exception e) {
                logger.error("WebSocket连接打开时发生错误", e);
                try {
                    emitter.send(SseEmitter.event()
                            .name("error")
                            .data("连接语音识别服务失败: " + e.getMessage()));
                    emitter.complete();
                } catch (IOException ex) {
                    logger.error("发送错误事件失败", ex);
                }
                webSocket.close(1001, "连接失败");
            }
        }
        
        @Override
        public void onMessage(WebSocket webSocket, String text) {
            logger.debug("收到文本消息: {}", text);
        }
        
        @Override
        public void onMessage(WebSocket webSocket, ByteString bytes) {
            try {
                byte[] res = bytes.toByteArray();
                int sequence = parserResponse(res);
                boolean isLastPackage = sequence < 0;
                
                if (isLastPackage) {
                    logger.info("识别完成，关闭连接");
                    webSocket.close(1000, "finished");
                    emitter.send(SseEmitter.event()
                            .name("complete")
                            .data("语音识别完成"));
                    emitter.complete();
                    return;
                }
                
                // 发送音频数据
                try {
                    final int len = audioInputStream.read(buffer, 0, bufferSize);
                    if (len <= 0) {
                        logger.info("音频数据读取完毕");
                        return;
                    }
                    
                    boolean isLast = audioInputStream.available() == 0;
                    logger.debug("读取音频数据: isLast={}, available={}", isLast, audioInputStream.available());
                    
                    sendAudioOnlyRequest(webSocket, buffer, len, isLast);
                    
                    if (isLast) {
                        audioInputStream.close();
                    }
                } catch (IOException e) {
                    logger.error("读取音频数据失败", e);
                    emitter.send(SseEmitter.event()
                            .name("error")
                            .data("读取音频数据失败: " + e.getMessage()));
                }
            } catch (Exception e) {
                logger.error("处理WebSocket消息失败", e);
                try {
                    emitter.send(SseEmitter.event()
                            .name("error")
                            .data("处理识别结果失败: " + e.getMessage()));
                } catch (IOException ex) {
                    logger.error("发送错误事件失败", ex);
                }
            }
        }
        
        @Override
        public void onClosing(WebSocket webSocket, int code, String reason) {
            logger.info("WebSocket正在关闭: code={}, reason={}", code, reason);
        }
        
        @Override
        public void onClosed(WebSocket webSocket, int code, String reason) {
            logger.info("WebSocket已关闭: code={}, reason={}", code, reason);
            // 如果不是正常关闭，发送错误事件
            if (code != 1000) {
                try {
                    String errorMessage = "语音识别服务异常关闭: " + reason;
                    if (code == 1001) {
                        errorMessage = "连接超时，请重试";
                    } else if (code == 1002) {
                        errorMessage = "协议错误，请检查网络连接";
                    } else if (code == 1003) {
                        errorMessage = "不支持的数据类型";
                    }
                    emitter.send(SseEmitter.event()
                            .name("error")
                            .data(errorMessage));
                    emitter.complete();
                } catch (IOException e) {
                    logger.error("发送错误事件失败", e);
                }
            }
        }
        
        @Override
        public void onFailure(WebSocket webSocket, Throwable t, Response response) {
            logger.error("WebSocket连接失败: {}", t.getMessage(), t);
            try {
                // 添加更详细的错误信息
                String errorMessage = "语音识别服务连接失败: " + t.getMessage();
                if (response != null) {
                    errorMessage += ", 响应码: " + response.code();
                    errorMessage += ", 响应头: " + response.headers();
                }
                emitter.send(SseEmitter.event()
                        .name("error")
                        .data(errorMessage));
                emitter.complete();
            } catch (IOException e) {
                logger.error("发送错误事件失败", e);
            }
            // 确保关闭WebSocket连接
            try {
                webSocket.close(1001, "连接失败");
            } catch (Exception e) {
                logger.warn("关闭WebSocket连接失败", e);
            }
        }
        
        /**
         * 发送完整客户端请求
         */
        private void sendFullClientRequest(WebSocket webSocket) {
            // 构建请求JSON
            JsonObject user = new JsonObject();
            user.addProperty("uid", "user_" + UUID.randomUUID().toString().substring(0, 8));
            
            JsonObject audio = new JsonObject();
            audio.addProperty("format", "pcm");
            audio.addProperty("sample_rate", (int) audioFormat.getSampleRate());
            audio.addProperty("bits", audioFormat.getSampleSizeInBits());
            audio.addProperty("channel", audioFormat.getChannels());
            audio.addProperty("codec", "raw");
            
            JsonObject request = new JsonObject();
            request.addProperty("model_name", "bigmodel");
            request.addProperty("enable_punc", true);
            
            JsonObject payload = new JsonObject();
            payload.add("user", user);
            payload.add("audio", audio);
            payload.add("request", request);
            
            String payloadStr = payload.toString();
            logger.debug("请求参数: {}", payloadStr);
            
            // 压缩payload
            byte[] payloadBytes = gzipCompress(payloadStr.getBytes());
            
            // 组装完整客户端请求
            byte[] header = getHeader(FULL_CLIENT_REQUEST, POS_SEQUENCE, JSON, GZIP, (byte) 0);
            byte[] payloadSize = intToBytes(payloadBytes.length);
            seq = 1;
            byte[] seqBytes = intToBytes(seq);
            
            byte[] fullClientRequest = new byte[header.length + seqBytes.length + payloadSize.length + payloadBytes.length];
            int destPos = 0;
            System.arraycopy(header, 0, fullClientRequest, destPos, header.length);
            destPos += header.length;
            System.arraycopy(seqBytes, 0, fullClientRequest, destPos, seqBytes.length);
            destPos += seqBytes.length;
            System.arraycopy(payloadSize, 0, fullClientRequest, destPos, payloadSize.length);
            destPos += payloadSize.length;
            System.arraycopy(payloadBytes, 0, fullClientRequest, destPos, payloadBytes.length);
            
            boolean success = webSocket.send(ByteString.of(fullClientRequest));
            if (!success) {
                logger.error("发送完整客户端请求失败");
            }
        }
        
        /**
         * 发送音频数据请求
         */
        private boolean sendAudioOnlyRequest(WebSocket webSocket, byte[] buffer, int len, boolean isLast) {
            seq++;
            logger.debug("发送音频数据: seq={}, isLast={}", seq, isLast);
            
            if (isLast) {
                seq = -seq; // 负数表示最后一个包
            }
            
            byte messageTypeSpecificFlags = isLast ? NEG_WITH_SEQUENCE : POS_SEQUENCE;
            
            // 构建请求头
            byte[] header = getHeader(AUDIO_ONLY_REQUEST, messageTypeSpecificFlags, JSON, GZIP, (byte) 0);
            
            // 序列号
            byte[] sequenceBytes = intToBytes(seq);
            
            // 压缩音频数据
            byte[] payloadBytes = gzipCompress(buffer, len);
            
            // 负载大小
            byte[] payloadSize = intToBytes(payloadBytes.length);
            
            // 组装音频请求
            byte[] audioOnlyRequest = new byte[header.length + sequenceBytes.length + payloadSize.length + payloadBytes.length];
            int destPos = 0;
            System.arraycopy(header, 0, audioOnlyRequest, destPos, header.length);
            destPos += header.length;
            System.arraycopy(sequenceBytes, 0, audioOnlyRequest, destPos, sequenceBytes.length);
            destPos += sequenceBytes.length;
            System.arraycopy(payloadSize, 0, audioOnlyRequest, destPos, payloadSize.length);
            destPos += payloadSize.length;
            System.arraycopy(payloadBytes, 0, audioOnlyRequest, destPos, payloadBytes.length);
            
            return webSocket.send(ByteString.of(audioOnlyRequest));
        }
        
        /**
         * 解析响应数据
         */
        private int parserResponse(byte[] res) throws IOException {
            if (res == null || res.length == 0) {
                return -1;
            }
            
            final byte num = 0b00001111;
            
            // 解析头部
            int messageType = (res[1] >> 4) & num;
            int messageCompression = res[2] & 0x0f;
            
            // 解析序列号
            byte[] temp = new byte[4];
            System.arraycopy(res, 4, temp, 0, temp.length);
            int sequence = bytesToInt(temp);
            
            // 解析负载大小
            System.arraycopy(res, 8, temp, 0, temp.length);
            int payloadSize = bytesToInt(temp);
            
            // 提取负载数据
            byte[] payload = new byte[res.length - 12];
            System.arraycopy(res, 12, payload, 0, payload.length);
            
            String payloadStr = null;
            
            // 处理不同类型的响应
            if (messageType == FULL_SERVER_RESPONSE) {
                if (messageCompression == GZIP) {
                    payloadStr = new String(gzipDecompress(payload));
                } else {
                    payloadStr = new String(payload);
                }
                
                logger.debug("收到识别结果: {}", payloadStr);
                
                // 解析JSON响应
                try {
                    JsonObject jsonResponse = gson.fromJson(payloadStr, JsonObject.class);
                    
                    // 提取识别结果
                    if (jsonResponse.has("result") && jsonResponse.getAsJsonObject("result").has("text")) {
                        String recognizedText = jsonResponse.getAsJsonObject("result").get("text").getAsString();
                        
                        // 发送识别结果到前端
                        emitter.send(SseEmitter.event()
                                .id(String.valueOf(messageCount.incrementAndGet()))
                                .name("result")
                                .data(recognizedText));
                    }
                } catch (Exception e) {
                    logger.error("解析识别结果失败", e);
                }
                
            } else if (messageType == SERVER_ERROR_RESPONSE) {
                // 错误响应
                payloadStr = new String(payload);
                logger.error("服务器错误: code={}, message={}", sequence, payloadStr);
                
                emitter.send(SseEmitter.event()
                        .name("error")
                        .data("识别服务错误: " + payloadStr));
            }
            
            return sequence;
        }
    }
    
    // 工具方法，从BigASRWebsocketDemo复制
    private static byte[] getHeader(byte messageType, byte messageTypeSpecificFlags, byte serialMethod, byte compressionType,
                                   byte reservedData) {
        final byte[] header = new byte[4];
        header[0] = (PROTOCOL_VERSION << 4) | DEFAULT_HEADER_SIZE;
        header[1] = (byte) ((messageType << 4) | messageTypeSpecificFlags);
        header[2] = (byte) ((serialMethod << 4) | compressionType);
        header[3] = reservedData;
        return header;
    }
    
    private static byte[] intToBytes(int a) {
        return new byte[]{
                (byte) ((a >> 24) & 0xFF),
                (byte) ((a >> 16) & 0xFF),
                (byte) ((a >> 8) & 0xFF),
                (byte) (a & 0xFF)
        };
    }
    
    private static int bytesToInt(byte[] src) {
        if (src == null || (src.length != 4)) {
            throw new IllegalArgumentException("无效的字节数组");
        }
        return ((src[0] & 0xFF) << 24)
                | ((src[1] & 0xff) << 16)
                | ((src[2] & 0xff) << 8)
                | ((src[3] & 0xff));
    }
    
    private static byte[] gzipCompress(byte[] src) {
        return gzipCompress(src, src.length);
    }
    
    private static byte[] gzipCompress(byte[] src, int len) {
        if (src == null || len == 0) {
            return new byte[0];
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        GZIPOutputStream gzip = null;
        try {
            gzip = new GZIPOutputStream(out);
            gzip.write(src, 0, len);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (gzip != null) {
                try {
                    gzip.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return out.toByteArray();
    }
    
    private static byte[] gzipDecompress(byte[] src) {
        if (src == null || src.length == 0) {
            return null;
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ByteArrayInputStream ins = new ByteArrayInputStream(src);
        GZIPInputStream gzip = null;
        try {
            gzip = new GZIPInputStream(ins);
            byte[] buffer = new byte[ins.available()];
            int len = 0;
            while ((len = gzip.read(buffer)) > 0) {
                out.write(buffer, 0, len);
            }
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (gzip != null) {
                try {
                    gzip.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return out.toByteArray();
    }
} 