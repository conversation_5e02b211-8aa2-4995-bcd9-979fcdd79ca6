package com.makiyo.aidoctor.form;

import com.makiyo.aidoctor.entity.Message;
import com.makiyo.aidoctor.entity.Session;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import java.util.Map;

/**
 * request: unique id
 * response:
 * id: 用户唯一标识
 * list<Session>: 该用户下有多少Session,id列表
 * list<Message>： 根据Session id查询每个Session对应的Message
 * UserInfo： 每个用户独有信息
 */
@Schema(description = "登录或注册后返回的数据模型")
public class LoginOrRegisterForm {
    /**
     * 用户唯一标识
     */
    @Schema(description = "用户ID", example = "1")
    private Integer userId;

    /**
     * 用户的会话列表
     */
    @Schema(description = "用户的所有会话列表")
    private List<Session> sessions;

    /**
     * 每个会话对应的消息列表，key为sessionId
     */
    @Schema(description = "每个会话的消息列表，键是会话ID")
    private Map<Integer, List<Message>> sessionMessages;

    /**
     * 用户基本信息
     */
    @Schema(description = "用户信息")
    private UserInfoForm userInfo;

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public UserInfoForm getUserInfo() {
        return userInfo;
    }

    public void setUserInfo(UserInfoForm userInfo) {
        this.userInfo = userInfo;
    }

    public List<Session> getSessions() {
        return sessions;
    }

    public void setSessions(List<Session> sessions) {
        this.sessions = sessions;
    }

    public Map<Integer, List<Message>> getSessionMessages() {
        return sessionMessages;
    }

    public void setSessionMessages(Map<Integer, List<Message>> sessionMessages) {
        this.sessionMessages = sessionMessages;
    }
}
