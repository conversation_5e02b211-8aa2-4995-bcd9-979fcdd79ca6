package com.makiyo.aidoctor.utils;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * 讯飞TTS语音合成示例类
 * 展示如何使用TtsRequest、FastJSON和OkHttp
 */
@Slf4j
public class TtsExample {

    private static final OkHttpClient CLIENT = new OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(10, TimeUnit.SECONDS)
            .writeTimeout(10, TimeUnit.SECONDS)
            .build();

    /**
     * 将文本转换为语音
     * @param text 要转换的文本
     * @return 语音数据的字节数组
     */
    public byte[] textToSpeech(String text) {
        try {
            // 创建TTS请求对象
            TtsRequest ttsRequest = new TtsRequest(text);
            
            // 使用FastJSON将对象转换为JSON字符串
            String jsonBody = JSON.toJSONString(ttsRequest);
            
            // 创建HTTP请求
            RequestBody body = RequestBody.create(
                    MediaType.parse("application/json"), jsonBody);
            
            Request request = new Request.Builder()
                    .url("https://tts-api.xfyun.cn/v2/tts") // 讯飞TTS API地址
                    .post(body)
                    .build();
            
            // 发送请求并获取响应
            try (Response response = CLIENT.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    log.error("TTS请求失败: {}", response);
                    return null;
                }
                
                // 返回响应体的字节数组
                return response.body().bytes();
            }
        } catch (IOException e) {
            log.error("TTS请求异常", e);
            return null;
        }
    }
    
    /**
     * TtsRequest类的简单实现
     * 实际使用时应该使用您项目中的完整TtsRequest类
     */
    public static class TtsRequest {
        private String text;
        
        public TtsRequest(String text) {
            this.text = text;
        }
        
        public String getText() {
            return text;
        }
        
        public void setText(String text) {
            this.text = text;
        }
    }
} 