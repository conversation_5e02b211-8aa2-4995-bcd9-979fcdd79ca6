# AI Doctor API Documentation

## OCR API
**Endpoint:** `/ocr_api`  
**Method:** POST  
**Description:** Processes medical report images to extract structured data.

**中文说明:** 处理医疗报告图像并提取结构化数据。系统会对上传的医疗检查报告图片进行OCR识别，并将内容转换为标准JSON格式。

### 基础函数示例
```python
import requests

def process_medical_report(image_path):
    """
    Process a medical report image using OCR API
    
    Args:
        image_path: Path to the medical report image
        
    Returns:
        Structured data extracted from the report
    """
    url = "https://api.aidoctor.com/ocr_api"
    payload = {"image_path": image_path}
    
    response = requests.post(url, json=payload)
    if response.status_code == 200:
        return response.json()
    else:
        return {"error": f"Request failed with status code {response.status_code}"}

# 使用示例
result = process_medical_report("path/to/medical_report.jpg")
print(f"检查名称: {result.get('检查名称')}")
```

### Request
```json
{
  "image_path": "path/to/image.jpg"
}
```

### Response
```json
{
  "检查时间": "2023-04-15 18:25",
  "检查名称": "血常规",
  "content": [
    {
      "基础信息": {
        "姓名": "张三",
        "性别": "男",
        "年龄": "45岁",
        "科室": "内科",
        "检查部位": "血液"
      }
    },
    {
      "检查结果": [
        {
          "项目名称": "白细胞计数",
          "结果": "6.5",
          "单位": "10^9/L",
          "参考范围": "4.0-10.0",
          "异常标记": "",
          "检测方法": "",
          "结果状态": "正常",
          "检查描述": "",
          "诊断": "",
          "处理建议": "",
          "样本采集时间": "",
          "其他": ""
        }
      ]
    }
  ]
}
```

**响应字段说明:**
- **检查时间:** 医疗检查的时间
- **检查名称:** 检查项目名称
- **基础信息:** 患者的基本信息（姓名、性别、年龄、科室、检查部位）
- **检查结果:** 检查的详细结果
  - **项目名称:** 检查项目名称
  - **结果:** 检查结果
  - **单位:** 结果单位
  - **参考范围:** 正常参考范围
  - **异常标记:** 异常标记，如↑或↓
  - **检测方法:** 使用的检测方法
  - **结果状态:** 结果是否正常
  - **检查描述:** 检查结果描述
  - **诊断:** 相关初步诊断
  - **处理建议:** 医生处理建议
  - **样本采集时间:** 样本采集时间
  - **其他:** 其他相关信息

### Error Response
```json
{
  "error": "Error message"
}
```

## Preliminary Diagnosis
**Endpoint:** `/ai_doctor_preliminary_diagnosis`  
**Method:** POST  
**Description:** Generates a preliminary diagnosis based on interaction history.

**中文说明:** 基于交互历史生成初步诊断结果。系统会分析患者与AI医生的对话内容，结合医疗知识给出初步诊断和检查建议。

### 基础函数示例
```python
import requests

def get_preliminary_diagnosis(interaction_history, supplementary_info=""):
    """
    Get preliminary diagnosis based on interaction history
    
    Args:
        interaction_history: Dictionary containing the interaction history
        supplementary_info: Additional information for diagnosis
        
    Returns:
        Preliminary diagnosis and inspection suggestions
    """
    url = "https://api.aidoctor.com/ai_doctor_preliminary_diagnosis"
    payload = {
        "interaction_history": interaction_history,
        "supplementary_info": supplementary_info
    }
    
    response = requests.post(url, json=payload)
    if response.status_code == 200:
        return response.json()
    else:
        return {"error": f"Request failed with status code {response.status_code}"}

# 使用示例
interaction_history = {
    "cot_entries": [
        {"role": "patient", "content": "我最近头痛得厉害，已经持续一周了。"},
        {"role": "doctor", "content": "您的头痛是持续性的还是间歇性的？"}
    ],
    "diagnosis": "",
    "test_recommendation": [],
    "treatment_recommendation": "",
    "preliminary_diagnosis": "",
    "doctor_supplementary_info": [],
    "lastObservation": ""
}

result = get_preliminary_diagnosis(interaction_history, "患者同时报告对光线敏感")
print(f"初步诊断: {result.get('preliminary_diagnosis')}")
print(f"检查建议: {result.get('inspection_suggestions')}")
```

### Request
```json
{
  "interaction_history": {
    "cot_entries": [],
    "diagnosis": "string",
    "test_recommendation": [],
    "treatment_recommendation": "string",
    "preliminary_diagnosis": "string",
    "doctor_supplementary_info": [],
    "lastObservation": "string"
  },
  "supplementary_info": "string"
}
```

### Response
```json
{
  "preliminary_diagnosis": "string",
  "inspection_suggestions": "string",
  "reasoning": "string",
  "guidelines_content": "string",
  "observation": "string"
}
```

**响应字段说明:**
- **preliminary_diagnosis:** 初步诊断结果
- **inspection_suggestions:** 检查建议
- **reasoning:** 推理过程
- **guidelines_content:** 指导内容
- **observation:** 观察结果

## Final Diagnosis
**Endpoint:** `/ai_doctor_diagnose`  
**Method:** POST  
**Description:** Generates final diagnosis with treatment plan.

**中文说明:** 生成最终诊断结果和治疗方案。系统会根据完整的病史、检查结果和初步诊断，给出最终诊断和详细治疗建议。

### 基础函数示例
```python
import requests

def get_final_diagnosis(interaction_history):
    """
    Get final diagnosis with treatment plan
    
    Args:
        interaction_history: Dictionary containing the complete interaction history
        
    Returns:
        Final diagnosis and treatment recommendations
    """
    url = "https://api.aidoctor.com/ai_doctor_diagnose"
    payload = {"interaction_history": interaction_history}
    
    response = requests.post(url, json=payload)
    if response.status_code == 200:
        return response.json()
    else:
        return {"error": f"Request failed with status code {response.status_code}"}

# 使用示例
interaction_history = {
    "cot_entries": [
        {"role": "patient", "content": "我最近头痛得厉害，已经持续一周了。"},
        {"role": "doctor", "content": "您的头痛是持续性的还是间歇性的？"},
        {"role": "patient", "content": "间歇性的，特别是早上起床后比较严重。"}
    ],
    "diagnosis": "",
    "test_recommendation": ["CT扫描显示正常"],
    "treatment_recommendation": "",
    "preliminary_diagnosis": "可能是紧张性头痛",
    "doctor_supplementary_info": [],
    "lastObservation": "患者报告对光线敏感"
}

result = get_final_diagnosis(interaction_history)
print(f"最终诊断: {result.get('diagnosis')}")
print(f"治疗建议: {result.get('treatment_recommendation')}")
```

### Request
```json
{
  "interaction_history": {
    "cot_entries": [],
    "diagnosis": "string",
    "test_recommendation": [],
    "treatment_recommendation": "string",
    "preliminary_diagnosis": "string",
    "doctor_supplementary_info": [],
    "lastObservation": "string"
  }
}
```

### Response
```json
{
  "diagnosis": "string",
  "condition": "string",
  "treatment_recommendation": "string",
  "treatment_guide": "string"
}
```

**响应字段说明:**
- **diagnosis:** 最终诊断结果
- **condition:** 病情描述
- **treatment_recommendation:** 治疗建议
- **treatment_guide:** 治疗指导

## AI Doctor Inquiry
**Endpoint:** `/ai_doctor_v2_inquiry`  
**Method:** POST  
**Description:** Generates the next question in the medical conversation.

**中文说明:** 在医疗对话中生成下一个问题。系统会根据已有的对话历史和医疗知识，生成针对性的问诊问题。

### 基础函数示例
```python
import requests

def get_next_question(interaction_history):
    """
    Get the next question in the medical conversation
    
    Args:
        interaction_history: Dictionary containing the interaction history
        
    Returns:
        Updated interaction history with the next question
    """
    url = "https://api.aidoctor.com/ai_doctor_v2_inquiry"
    payload = {"interaction_history": interaction_history}
    
    response = requests.post(url, json=payload)
    if response.status_code == 200:
        return response.json()
    else:
        return {"error": f"Request failed with status code {response.status_code}"}

# 使用示例
interaction_history = {
    "cot_entries": [
        {"role": "patient", "content": "我最近头痛得厉害，已经持续一周了。"}
    ],
    "diagnosis": "",
    "test_recommendation": [],
    "treatment_recommendation": "",
    "preliminary_diagnosis": "",
    "doctor_supplementary_info": [],
    "lastObservation": ""
}

updated_history = get_next_question(interaction_history)
latest_question = updated_history.get("cot_entries", [])[-1]["content"]
print(f"AI医生的下一个问题: {latest_question}")
```

### Request
```json
{
  "interaction_history": {
    "cot_entries": [],
    "diagnosis": "string",
    "test_recommendation": [],
    "treatment_recommendation": "string",
    "preliminary_diagnosis": "string",
    "doctor_supplementary_info": [],
    "lastObservation": "string"
  }
}
```

### Response
返回更新后的交互历史，包含AI医生的下一个问题。

## AI Doctor Quick Inquiry
**Endpoint:** `/ai_doctor_v2_quick_inquiry`  
**Method:** POST  
**Description:** Generates the next question in quick demo mode.

**中文说明:** 在快速演示模式下生成下一个问题。适用于需要快速展示AI医生能力的场景。

### 基础函数示例
```python
import requests

def get_quick_question(interaction_history):
    """
    Get the next question in quick demo mode
    
    Args:
        interaction_history: Dictionary containing the interaction history
        
    Returns:
        Updated interaction history with the next question
    """
    url = "https://api.aidoctor.com/ai_doctor_v2_quick_inquiry"
    payload = {"interaction_history": interaction_history}
    
    response = requests.post(url, json=payload)
    if response.status_code == 200:
        return response.json()
    else:
        return {"error": f"Request failed with status code {response.status_code}"}

# 使用示例
interaction_history = {
    "cot_entries": [
        {"role": "patient", "content": "我最近头痛得厉害。"}
    ],
    "diagnosis": "",
    "test_recommendation": [],
    "treatment_recommendation": "",
    "preliminary_diagnosis": "",
    "doctor_supplementary_info": []
}

updated_history = get_quick_question(interaction_history)
latest_question = updated_history.get("cot_entries", [])[-1]["content"]
print(f"AI医生的快速问题: {latest_question}")
```

### Request
```json
{
  "interaction_history": {
    "cot_entries": [],
    "diagnosis": "string",
    "test_recommendation": [],
    "treatment_recommendation": "string",
    "preliminary_diagnosis": "string",
    "doctor_supplementary_info": []
  }
}
```

### Response
返回更新后的交互历史，包含AI医生在快速模式下的下一个问题。

## AI Doctor V2 Preliminary Diagnosis
**Endpoint:** `/ai_doctor_v2_preliminary_diagnosis`  
**Method:** POST  
**Description:** V2 version for generating preliminary diagnosis.

**中文说明:** V2版本用于生成初步诊断结果，功能类似`ai_doctor_preliminary_diagnosis`但可能有实现上的差异。

### 基础函数示例
```python
import requests

def get_v2_preliminary_diagnosis(interaction_history, supplementary_info=""):
    """
    Get preliminary diagnosis using V2 API
    
    Args:
        interaction_history: Dictionary containing the interaction history
        supplementary_info: Additional information for diagnosis
        
    Returns:
        Preliminary diagnosis and inspection suggestions
    """
    url = "https://api.aidoctor.com/ai_doctor_v2_preliminary_diagnosis"
    payload = {
        "interaction_history": interaction_history,
        "supplementary_info": supplementary_info
    }
    
    response = requests.post(url, json=payload)
    if response.status_code == 200:
        return response.json()
    else:
        return {"error": f"Request failed with status code {response.status_code}"}

# 使用示例
interaction_history = {
    "cot_entries": [
        {"role": "patient", "content": "我最近头痛得厉害，并且感到恶心。"},
        {"role": "doctor", "content": "您的头痛伴随其他症状吗？"},
        {"role": "patient", "content": "有时会头晕，对光线也很敏感。"}
    ],
    "diagnosis": "",
    "test_recommendation": [],
    "treatment_recommendation": "",
    "preliminary_diagnosis": "",
    "doctor_supplementary_info": [],
    "lastObservation": ""
}

result = get_v2_preliminary_diagnosis(interaction_history)
print(f"V2初步诊断: {result.get('preliminary_diagnosis')}")
print(f"检查建议: {result.get('inspection_suggestions')}")
```

### Request
```json
{
  "interaction_history": {
    "cot_entries": [],
    "diagnosis": "string",
    "test_recommendation": [],
    "treatment_recommendation": "string",
    "preliminary_diagnosis": "string",
    "doctor_supplementary_info": [],
    "lastObservation": "string"
  },
  "supplementary_info": "string"
}
```

### Response
```json
{
  "preliminary_diagnosis": "string",
  "inspection_suggestions": "string",
  "reasoning": "string",
  "guidelines_content": "string"
}
```

## AI Doctor V2 Diagnosis
**Endpoint:** `/ai_doctor_v2_diagnosis`  
**Method:** POST  
**Description:** V2 version for generating final diagnosis.

**中文说明:** V2版本用于生成最终诊断结果，功能类似`ai_doctor_diagnose`但可能有实现上的差异。

### 基础函数示例
```python
import requests

def get_v2_final_diagnosis(interaction_history):
    """
    Get final diagnosis using V2 API
    
    Args:
        interaction_history: Dictionary containing the complete interaction history
        
    Returns:
        Final diagnosis and treatment recommendations
    """
    url = "https://api.aidoctor.com/ai_doctor_v2_diagnosis"
    payload = {"interaction_history": interaction_history}
    
    response = requests.post(url, json=payload)
    if response.status_code == 200:
        return response.json()
    else:
        return {"error": f"Request failed with status code {response.status_code}"}

# 使用示例
interaction_history = {
    "cot_entries": [
        {"role": "patient", "content": "我最近头痛得厉害，并且感到恶心。"},
        {"role": "doctor", "content": "您的头痛伴随其他症状吗？"},
        {"role": "patient", "content": "有时会头晕，对光线也很敏感。"}
    ],
    "diagnosis": "",
    "test_recommendation": ["脑CT扫描无异常", "血常规检查正常"],
    "treatment_recommendation": "",
    "preliminary_diagnosis": "可能是偏头痛",
    "doctor_supplementary_info": [],
    "lastObservation": "患者报告头痛发作时需要在安静黑暗的房间休息"
}

result = get_v2_final_diagnosis(interaction_history)
print(f"V2最终诊断: {result.get('diagnosis')}")
print(f"治疗建议: {result.get('treatment_recommendation')}")
print(f"治疗指导: {result.get('treatment_guide')}")
```

### Request
```json
{
  "interaction_history": {
    "cot_entries": [],
    "diagnosis": "string",
    "test_recommendation": [],
    "treatment_recommendation": "string",
    "preliminary_diagnosis": "string",
    "doctor_supplementary_info": [],
    "lastObservation": "string"
  }
}
```

### Response
```json
{
  "diagnosis": "string",
  "condition": "string",
  "treatment_recommendation": "string",
  "treatment_guide": "string"
}
```

## AI Doctor V2
**Endpoint:** `/ai_doctor_v2`  
**Method:** POST  
**Description:** Main AI doctor endpoint that processes interaction history.

**中文说明:** AI医生系统的主要接口，处理完整的交互历史并返回更新后的结果。可用于整体流程控制。

### 基础函数示例
```python
import requests

def process_ai_doctor_interaction(interaction_history):
    """
    Process the complete AI doctor interaction
    
    Args:
        interaction_history: Dictionary containing the interaction history
        
    Returns:
        Updated interaction history with AI doctor response
    """
    url = "https://api.aidoctor.com/ai_doctor_v2"
    payload = {"interaction_history": interaction_history}
    
    response = requests.post(url, json=payload)
    if response.status_code == 200:
        return response.json()
    else:
        return {"error": f"Request failed with status code {response.status_code}"}

# 使用示例
interaction_history = {
    "cot_entries": [
        {"role": "patient", "content": "我最近感觉很疲劳，而且食欲不振。"}
    ],
    "diagnosis": "",
    "test_recommendation": [],
    "treatment_recommendation": "",
    "preliminary_diagnosis": "",
    "doctor_supplementary_info": [],
    "lastObservation": ""
}

result = process_ai_doctor_interaction(interaction_history)
updated_history = result.get("interaction_history", {})
print(f"更新后的交互历史: {updated_history}")
```

### Request
```json
{
  "interaction_history": {
    "cot_entries": [],
    "diagnosis": "string",
    "test_recommendation": [],
    "treatment_recommendation": "string",
    "preliminary_diagnosis": "string",
    "doctor_supplementary_info": [],
    "lastObservation": "string"
  }
}
```

### Response
```json
{
  "interaction_history": {
    "cot_entries": [],
    "diagnosis": "string",
    "test_recommendation": [],
    "treatment_recommendation": "string",
    "preliminary_diagnosis": "string",
    "doctor_supplementary_info": [],
    "lastObservation": "string"
  }
}
```

### Error Response
```json
{
  "error": "Error message"
}
``` 