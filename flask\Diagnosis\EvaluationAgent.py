import json
import httpx
import pandas as pd
from volcenginesdkarkruntime import Ark
from anthropic import Anthropic, DefaultHttpxClient
from dotenv import load_dotenv
import os
import logging

from prompts import (
    _user_prompt_diagnosis, _sys_prompt_diagnosis,
    _user_prompt_treatment_plan, _sys_prompt_treatment_plan,
    _user_prompt_compare, _sys_prompt_compare,
    _user_prompt_score_category, _sys_prompt_score_category,
    _user_prompt_score_category_treatment, _sys_prompt_score_category_treatment
)

load_dotenv('../.env')  # 加载环境变量


# 配置日志记录
logging.basicConfig(
    level=logging.INFO,  # 设置日志级别
    format="%(asctime)s - %(levelname)s - %(message)s",  # 日志格式
    handlers=[
        logging.StreamHandler(),  # 输出到控制台
        logging.FileHandler("evaluation_agent.log", mode="a", encoding="utf-8")  # 输出到文件
    ]
)

def extract_medication_info(json_data):
    print("json_data:", json_data)
    try:
        # 将单引号转换为双引号
        json_data = json_data.replace("'", "\"")
        # 解析 JSON 数据
        json_data = json.loads(json_data)
        # 获取药品信息，如果字段不存在则返回空列表
        medications = json_data.get('药品', [])
        result_str = ""
        for med in medications:
            # 使用 dict.get() 方法获取字段值，如果字段不存在则返回空字符串
            name = med.get('药品名', '')
            dose = med.get('剂量', '')
            method = med.get('服用方法', '')
            schedule = med.get('剂量安排', '')
            purpose = med.get('使用目的', '')
            # 拼接药品信息
            result_str += f"{name} {dose} {method} {schedule} {purpose}。"
        print("result_str:", result_str)
        return result_str
    except json.JSONDecodeError as e:
        print(f"JSON 解析错误: {e}")
        return "JSON 数据格式错误，无法解析。"
    except Exception as e:
        print(f"未知错误: {e}")
        return "提取药品信息时发生错误。"
        


# 创建日志记录器
logger = logging.getLogger()

class BaseEvaluationAgent:
    def __init__(self, model_name):
        self.model = model_name

    def _initialize_client(self):
        raise NotImplementedError("Subclasses must implement this method")

    def _send_request(self, user_prompt, sys_prompt):
        raise NotImplementedError("Subclasses must implement this method")

    def _compute_similarity(self, text1, text2, type="diagnosis"):
        if text1 == '#N/A' or text2 == '#N/A':
            return None

        if type == "diagnosis":
            user_prompt = _user_prompt_diagnosis.format(text1=text1, text2=text2)
            sys_prompt = _sys_prompt_diagnosis
        elif type == "treatment_plan":
            user_prompt = _user_prompt_treatment_plan.format(text1=text1, text2=text2)
            sys_prompt = _sys_prompt_treatment_plan
        else:
            raise ValueError(f"Type {type} not supported.")

        return self._send_request(user_prompt, sys_prompt)

    def _score_treatment_plans(self, disease_name, condition, treatment_plan_1, treatment_plan_2):
        user_prompt = _user_prompt_compare.format(disease_name=disease_name,condition=condition, treatment_plan_1=treatment_plan_1, treatment_plan_2=treatment_plan_2)
        sys_prompt = _sys_prompt_compare

        result = self._send_request(user_prompt, sys_prompt)
        result_dict = eval(result)
        return result_dict['score1'], result_dict['score2']

    def _scoreCategory(self, text0, text1, text2, text3):
        user_prompt = _user_prompt_score_category.format(text0=text0, text1=text1, text2=text2, text3=text3)
        sys_prompt = _sys_prompt_score_category

        result = self._send_request(user_prompt, sys_prompt)
        result_dict = json.loads(result)
        return result_dict['score1'], result_dict['score2'], result_dict['score3']
    
    def _scoreCategoryTreatment(self, condition, diagnosis, treatment_plan1, treatment_plan2):
        user_prompt = _user_prompt_score_category_treatment.format(condition=condition, diagnosis=diagnosis, treatment_plan1=treatment_plan1, treatment_plan2=treatment_plan2)
        sys_prompt = _sys_prompt_score_category_treatment

        logger.info(f"Sending request to {self.model}: {user_prompt}")


        result = self._send_request(user_prompt, sys_prompt)
        logger.info(f"Received response from {self.model}: {result}")
        result_dict = json.loads(result)
        return result_dict['score1'], result_dict['score2']
        
    
    def evaluate(self, csv_file):
        # 读取 CSV 文件
        df = pd.read_csv(csv_file)
        
        # 新增列来存储相似度分数
        df['diagnosis_similarity'] = None
        df['treatment_plan_similarity'] = None
        
        # 遍历每一行进行评估
        for index, row in df.iterrows():
            # if index > 1:
            #     break
            print("evaluate index:", index)
            # 评估 diagnosis 与 诊断标准的相似度
            diagnosis_similarity = self._compute_similarity(row['diagnosis'], row['诊断标准'], type="diagnosis")
            df.at[index, 'diagnosis_similarity'] = diagnosis_similarity
            
            # 评估 treatment_plan 与 诊疗计划的相似度
            treatment_plan_similarity = self._compute_similarity(row['treatment_plan'], row['诊疗计划'], type="treatment_plan")
            df.at[index, 'treatment_plan_similarity'] = treatment_plan_similarity
        
        # 将更新后的 DataFrame 写回到 CSV 文件
        df.to_csv(csv_file, index=False)
        print(f"Evaluation complete. Results written to {csv_file}")

        # 输出结果
        print("诊断相似度:")
        print(df['diagnosis_similarity'])
        print("治疗方案相似度:")
        print(df['treatment_plan_similarity'])


    def score(self, csv_file):
        # # 读取 CSV 文件
        # df = pd.read_csv(csv_file)

        # 读取xlsx文件
        df = pd.read_excel(csv_file)
        
        # 新增列来存储治疗方案的得分
        df['treatment_plan_score_1'] = None
        df['treatment_plan_score_2'] = None
        
        # 遍历每一行进行评估
        for index, row in df.iterrows():

            print("score index:", index)
            
            print("preliminary_exam:", row['preliminary_exam'])

            if row['preliminary_exam'] is None or row['preliminary_exam'] is None or row['preliminary_exam'] == '#N/A' or row['preliminary_exam'] == "" or pd.isna(row['preliminary_exam']):
                disease_name = row['diagnosis']
            else:
                disease_name = row['preliminary_exam']

            # 第一次
            # treatment_plan_1 = row['treatment']
            # treatment_plan_2 = row['treatment_plan']

            # 第二次
            treatment_plan_1 = row['treatment_plan']
            treatment_plan_2 = row['treatment'] 
                

            condition = row['病例合并'] if row['病例合并'] is not None else ""

            # 评估治疗方案的得分
            score_1, score_2 = self._score_treatment_plans(disease_name, condition, treatment_plan_1, treatment_plan_2)
            df.at[index, 'treatment_plan_score100_3'] = score_1
            df.at[index, 'treatment_plan_score100_4'] = score_2
        
        # # 将更新后的 DataFrame 写回到 CSV 文件
        # df.to_csv(csv_file, index=False)
        # print(f"Scoring complete. Results written to {csv_file}")
        
        # 将更新后的 DataFrame 写回到 XLSX 文件
        df.to_excel(csv_file, index=False)
        print(f"Scoring complete. Results written to {csv_file}")

        # 输出结果
        print("治疗方案得分:")
        print(df['treatment_plan_score100_3'])
        print(df['treatment_plan_score100_4'])


    def scoreCategory(self, csv_file):
        # 读取 CSV 文件
        df = pd.read_csv(csv_file)
        
        # 新增列来存储治疗方案的得分
        df['diagnosis_score_claude'] = None
        df['diagnosis_score_r1'] = None
        df['diagnosis_score_v3'] = None
        
        # 遍历每一行进行评估
        for index, row in df.iterrows():

            print("score index:", index)
            
            print("preliminary_exam:", row['preliminary_exam'])
            
            score_claude, score_r1, score_v3 = self._scoreCategory(row['preliminary_exam'], row['diagnosis_claude'], row['diagnosis_r1'], row['diagnosis_v3'])

            df.at[index, 'diagnosis_score_claude'] = score_claude
            df.at[index, 'diagnosis_score_r1'] = score_r1
            df.at[index, 'diagnosis_score_v3'] = score_v3
            
        
        # 将更新后的 DataFrame 写回到 CSV 文件
        df.to_csv(csv_file, index=False)
        print(f"Scoring complete. Results written to {csv_file}")

        # 输出结果
        print("治疗方案得分:")
        print(df['diagnosis_score_claude'])
        print(df['diagnosis_score_r1'])
        print(df['diagnosis_score_v3'])


    def scoreCategoryTreatment(self, xlsx_file):

        #读取xlsx文件
        df = pd.read_excel(xlsx_file)

        # 新增列来存储治疗方案的得分
        df['treatment_plan_score_1'] = None
        df['treatment_plan_score_2'] = None

        # 遍历每一行进行评估
        for index, row in df.iterrows():

            print("score index:", index)

            print("condition:", row['病例合并'])

            model_treatment_plan = row['treatment_plan']

            extract_treatment_plan = extract_medication_info(model_treatment_plan)

            # score_1, score_2 = self._scoreCategoryTreatment(condition=row['病例合并'], diagnosis=row['preliminary_exam'], treatment_plan1=row['treatment'], treatment_plan2=extract_treatment_plan)
            score_1, score_2 = self._scoreCategoryTreatment(condition=row['病例合并'], diagnosis=row['preliminary_exam'], treatment_plan1=extract_treatment_plan, treatment_plan2=row['treatment'])
            df.at[index, 'treatment_plan_score_1'] = score_1
            df.at[index, 'treatment_plan_score_2'] = score_2
        
        # 将更新后的 DataFrame 写回到 XLSX 文件
        df.to_excel(xlsx_file, index=False)
        print(f"Scoring complete. Results written to {xlsx_file}")

            



class ClaudeEvaluationAgent(BaseEvaluationAgent):
    def __init__(self):
        super().__init__("Claude")

    def _initialize_client(self):
        api_key = os.getenv("CLAUDE_API_KEY")
        return Anthropic(
            api_key=api_key,
            http_client=DefaultHttpxClient(
                proxy="http://localhost:7890",
                transport=httpx.HTTPTransport(local_address="0.0.0.0"),
            ),
        )

    def _send_request(self, user_prompt, sys_prompt):
        client = self._initialize_client()

        logger.info(f"Sending request to Claude: {user_prompt}")

        message = client.messages.create(
            model="claude-3-5-sonnet-20241022",
            max_tokens=1024,
            messages=[{"role": "user", "content": user_prompt}],
            system=sys_prompt
        )

        logger.info(f"Received response from Claude: {message.content[0].text}")

        return message.content[0].text

class DeepSeekV3EvaluationAgent(BaseEvaluationAgent):
    def __init__(self):
        super().__init__("deepseek_v3")

    def _initialize_client(self):
        api_key = os.getenv("DEEPSEEK_V3_API_KEY")
        return Ark(
            base_url="https://ark.cn-beijing.volces.com/api/v3",
            api_key=api_key,
        )

    def _send_request(self, user_prompt, sys_prompt):
        client = self._initialize_client()
        logger.info(f"Sending request to DeepSeek V3: {user_prompt}")
        completion = client.chat.completions.create(
            model="ep-20250218172420-mcwk8",
            messages=[
                {"role": "system", "content": sys_prompt},
                {"role": "user", "content": user_prompt},
            ],
        )
        logger.info(f"Received response from DeepSeek V3: {completion.choices[0].message.content}")
        return completion.choices[0].message.content

class DeepSeekR1EvaluationAgent(BaseEvaluationAgent):
    def __init__(self):
        super().__init__("deepseek_r1")

    def _initialize_client(self):
        api_key = os.getenv("DEEPSEEK_R1_API_KEY")
        return Ark(
            api_key=api_key,
            timeout=httpx.Timeout(timeout=1800),
        )

    def _send_request(self, user_prompt, sys_prompt):
        client = self._initialize_client()
        logger.info(f"Sending request to DeepSeek R1: {user_prompt}")
        completion = client.chat.completions.create(
            model="ep-20250218163136-2mnnz",
            messages=[
                {"role": "user", "content": user_prompt},
                {"role": "system", "content": sys_prompt}
            ],
        )
        logger.info(f"Received response from DeepSeek R1: {completion.choices[0].message.content}")
        result = completion.choices[0].message.content
        if not result.startswith('json'):
            result = result[result.find('{'):result.rfind('}')+1]
        return result

# 使用示例
# claude_agent = ClaudeEvaluationAgent()
# similarity = claude_agent._compute_similarity("text1", "text2", type="diagnosis")
# score1, score2 = claude_agent._score_treatment_plans("disease", "plan1", "plan2")
# score1, score2, score3 = claude_agent._scoreCategory("text0", "text1", "text2", "text3")


    



if __name__ == '__main__':
    # file_path = "../data/results_v3_full.csv"
    # file_path = "../data/json_all.csv"
    # file_path = "../data/results_new_treatment.csv"
    file_path = "../data/results_treatment_specification.xlsx"

    # evaluation_agent = EvaluationAgent(model_name="deepseek_r1")
    # evaluation_agent = DeepSeekV3EvaluationAgent()
    evaluation_agent = ClaudeEvaluationAgent()

    # 评估两种诊断的相似度
    # evaluation_agent.evaluate(file_path)

    # 评估两种治疗方案的得分
    evaluation_agent.scoreCategoryTreatment(file_path)

    # evaluation_agent.score(file_path)

    # 评估三种诊断的得分
    # evaluation_agent.scoreCategory(file_path)

