{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["成功转换为 Excel，保存路径：../data/batch_process_0609_1616.xlsx\n"]}], "source": ["import pandas as pd\n", "import json\n", "\n", "input_path = '../data/batch_process_0609_1616.jsonl'\n", "records = []\n", "\n", "with open(input_path, 'r', encoding='utf-8') as f:\n", "    for line in f:\n", "        line = line.strip()\n", "        if not line:\n", "            continue  # 跳过空行\n", "        try:\n", "            record = json.loads(line)\n", "            records.append(record)\n", "        except json.JSONDecodeError as e:\n", "            print(f\"跳过无法解析的行：{line}\")\n", "            continue\n", "\n", "# 转换为 DataFrame\n", "df = pd.DataFrame(records)\n", "\n", "# 保存为 Excel\n", "output_excel = '../data/batch_process_0609_1616.xlsx'\n", "df.to_excel(output_excel, index=False)\n", "\n", "print(f\"成功转换为 Excel，保存路径：{output_excel}\")\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["最长的单条查体策略 (长度: 118):\n", "系统查体评估以下体征：①呼吸系统（呼吸频率/三凹征/湿啰音分布/叩诊浊音区/管状呼吸音） ②神经系统（脑膜刺激征：颈抵抗、克尼格征、布鲁津斯基征） ③鼻窦区压痛（额窦、筛窦） ④咽部充血/扁桃体肿大 ⑤皮肤黏膜（瘀斑/皮疹） ⑥肝脾触诊\n", "\n", "--------------------------------------------------\n", "\n", "最短的单条查体策略 (长度: 17):\n", "评估血氧饱和度（静息状态及活动后）\n", "成功提取查体策略并保存到：../data/batch_process_0421.xlsx\n"]}], "source": ["# 提取查体策略\n", "df = pd.read_excel(\"../data/batch_process_0421.xlsx\")\n", "def extract_strategies(cot_text):\n", "    strategies = []\n", "    if not isinstance(cot_text, str):\n", "        return \"\"\n", "    \n", "    rounds = cot_text.split(\"==================================================\")\n", "    for round_text in rounds:\n", "        if \"[查体策略:]\" in round_text:\n", "            strategy_line = round_text.split(\"[查体策略:]\")[1].split(\"\\n\")[0].strip()\n", "            strategies.append(strategy_line)\n", "        elif \"策略: [查体策略:]\" in round_text:\n", "            strategy_line = round_text.split(\"策略: [查体策略:]\")[1].split(\"\\n\")[0].strip()\n", "            strategies.append(strategy_line)\n", "    \n", "    # 返回未拼接的策略列表\n", "    return strategies\n", "\n", "# 应用函数到DataFrame\n", "df['查体策略'] = df['诊断历史'].apply(extract_strategies)\n", "\n", "# 找出最长和最短的单条查体策略\n", "all_strategies = []\n", "for strategies in df['查体策略']:\n", "    if isinstance(strategies, list):\n", "        all_strategies.extend(strategies)\n", "\n", "if all_strategies:\n", "    # 找出最长的单条查体策略\n", "    longest_strategy = max(all_strategies, key=len)\n", "    # 找出最短的单条查体策略\n", "    shortest_strategy = min(all_strategies, key=len)\n", "    \n", "    print(f\"最长的单条查体策略 (长度: {len(longest_strategy)}):\")\n", "    print(longest_strategy)\n", "    \n", "    print(\"\\n\" + \"-\"*50 + \"\\n\")\n", "    \n", "    print(f\"最短的单条查体策略 (长度: {len(shortest_strategy)}):\")\n", "    print(shortest_strategy)\n", "else:\n", "    print(\"没有找到有效的查体策略\")\n", "\n", "# 将列表转换为字符串以便保存到Excel\n", "df['查体策略_文本'] = df['查体策略'].apply(lambda x: \"\\n\".join(x) if isinstance(x, list) else \"\")\n", "\n", "# 保存更新后的Excel\n", "output_excel_updated = '../data/batch_process_0421.xlsx'\n", "df.to_excel(output_excel_updated, index=False)\n", "\n", "print(f\"成功提取查体策略并保存到：{output_excel_updated}\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 旧模型已删除\n"]}], "source": ["import shutil\n", "\n", "# 模型所在路径（你提供的信息中）\n", "wrong_model_path = r\"C:\\Users\\<USER>\\.cache\\modelscope\\hub\\models\\initialencounter\\bge-base-zh-v1___5\"\n", "\n", "# 删除整个目录\n", "shutil.rmtree(wrong_model_path, ignore_errors=True)\n", "print(\"✅ 旧模型已删除\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading Model from https://www.modelscope.cn to directory: C:\\Users\\<USER>\\.cache\\modelscope\\hub\\models\\BAAI\\bge-large-zh-v1.5\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-04-23 17:03:12,868 - modelscope - INFO - Creating symbolic link [C:\\Users\\<USER>\\.cache\\modelscope\\hub\\models\\BAAI\\bge-large-zh-v1.5].\n", "2025-04-23 17:03:12,871 - modelscope - WARNING - Failed to create symbolic link C:\\Users\\<USER>\\.cache\\modelscope\\hub\\models\\BAAI\\bge-large-zh-v1.5 for C:\\Users\\<USER>\\.cache\\modelscope\\hub\\models\\BAAI\\bge-large-zh-v1___5.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["模型已下载到: C:\\Users\\<USER>\\.cache\\modelscope\\hub\\models\\BAAI\\bge-large-zh-v1___5\n"]}], "source": ["from modelscope.hub.snapshot_download import snapshot_download\n", "\n", "model_dir = snapshot_download('BAAI/bge-large-zh-v1.5')\n", "print(f\"模型已下载到: {model_dir}\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 成功连接到 Qdrant！当前已有 collections：\n", "['med_symptom', 'med', 'med_name', 'med_all', 'COT', 'check']\n"]}], "source": ["from qdrant_client import QdrantClient\n", "from qdrant_client.http.exceptions import ResponseHandlingException\n", "import ssl\n", "import urllib.request\n", "import requests\n", "import os\n", "\n", "# ✅ 修改为你的 Qdrant Cloud 或本地地址\n", "QDRANT_URL = \"https://4c32b9ec-36a6-4290-9dbe-7e455e14dc5b.eu-west-2-0.aws.cloud.qdrant.io:6333\"  # 如：https://abc123.aws.cloud.qdrant.io:6333\n", "QDRANT_API_KEY = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3MiOiJtIn0.g_G0ko_m5qFdwL6wvxZEcygPftfUak3qZRDEyBGMNbQ\"              # 如果本地部署，可留空或为 None\n", "\n", "try:\n", "    # 禁用代理连接\n", "    os.environ['NO_PROXY'] = QDRANT_URL\n", "    \n", "    # 尝试使用不同的连接方式解决SSL问题，并禁用代理\n", "    client = QdrantClient(\n", "        url=QDRANT_URL,\n", "        api_key=QDRANT_API_KEY,\n", "        timeout=30,\n", "    )\n", "  \n", "    # 测试列出现有所有 collections（禁用代理）\n", "    collections = client.get_collections()\n", "    print(\"✅ 成功连接到 Qdrant！当前已有 collections：\")\n", "    # 修复列表索引错误，正确访问collections属性\n", "    if hasattr(collections, 'collections'):\n", "        collection_names = [c.name for c in collections.collections]\n", "\n", "    print(collection_names)\n", "\n", "except ResponseHandlingException as e:\n", "    print(\"❌ 无法连接到 Qdrant。错误信息：\")\n", "    print(e)\n", "    \n", "    # 尝试直接使用requests禁用代理进行测试\n", "    # try:\n", "    #     print(\"尝试使用requests直接请求（禁用代理）...\")\n", "    #     response = requests.get(\n", "    #         f\"{QDRANT_URL}/collections\", \n", "    #         headers={\"api-key\": QDRANT_API_KEY},\n", "    #         verify=False,  # 禁用SSL验证\n", "    #         proxies={},  # 禁用代理\n", "    #         timeout=10\n", "    #     )\n", "    #     print(f\"✅ 直接请求状态码: {response.status_code}\")\n", "    #     print(f\"📦 返回内容: {response.json()}\")\n", "    # except Exception as req_error:\n", "    #     print(f\"❌ 直接请求也失败: {req_error}\")\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["❌ REST API 请求失败: HTTPSConnectionPool(host='4c32b9ec-36a6-4290-9dbe-7e455e14dc5b.eu-west-2-0.aws.cloud.qdrant.io', port=6333): Read timed out. (read timeout=10)\n"]}], "source": ["import requests\n", "\n", "url = \"https://4c32b9ec-36a6-4290-9dbe-7e455e14dc5b.eu-west-2-0.aws.cloud.qdrant.io:6333/collections\"\n", "headers = {\n", "    \"api-key\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3MiOiJtIn0.g_G0ko_m5qFdwL6wvxZEcygPftfUak3qZRDEyBGMNbQy\"\n", "}\n", "\n", "try:\n", "    response = requests.get(url, headers=headers, verify=False, timeout=10)\n", "    print(\"✅ 状态码:\", response.status_code)\n", "    print(\"📦 返回内容:\", response.json())\n", "except Exception as e:\n", "    print(\"❌ REST API 请求失败:\", e)\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\huggingface_hub\\file_download.py:144: UserWarning: `huggingface_hub` cache-system uses symlinks by default to efficiently store duplicated files but your machine does not support them in C:\\Users\\<USER>\\.cache\\huggingface\\hub\\models--BAAI--bge-large-zh-v1.5. Caching files will still work but in a degraded version that might require more space on your disk. This warning can be disabled by setting the `HF_HUB_DISABLE_SYMLINKS_WARNING` environment variable. For more details, see https://huggingface.co/docs/huggingface_hub/how-to-cache#limitations.\n", "To support symlinks on Windows, you either need to activate Developer Mode or to run Python as an administrator. In order to activate developer mode, see this article: https://docs.microsoft.com/en-us/windows/apps/get-started/enable-your-device-for-development\n", "  warnings.warn(message)\n", "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\torch\\_utils.py:831: UserWarning: TypedStorage is deprecated. It will be removed in the future and UntypedStorage will be the only storage class. This should only matter to you if you are using storages directly.  To access UntypedStorage directly, use tensor.untyped_storage() instead of tensor.storage()\n", "  return self.fget.__get__(instance, owner)()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["向量维度: (1024,)\n"]}], "source": ["from transformers import BertTokenizer, BertModel\n", "import torch\n", "\n", "# 使用modelscope下载的实际路径\n", "model_path = r\"BAAI/bge-large-zh-v1.5\"\n", "\n", "# 加载tokenizer和模型\n", "tokenizer = BertTokenizer.from_pretrained(model_path)\n", "model = BertModel.from_pretrained(model_path)\n", "model.eval()  # 设置模型为评估模式\n", "\n", "# 编码函数\n", "def encode_text(text):\n", "    inputs = tokenizer(text, return_tensors=\"pt\", padding=True, truncation=True, max_length=512)\n", "    with torch.no_grad():\n", "        outputs = model(**inputs)\n", "    \n", "    # 获取[CLS]标记的embedding作为句子表示\n", "    embeddings = outputs.last_hidden_state[:, 0, :].cpu().numpy()\n", "    return embeddings[0]  # 返回第一个(唯一的)embedding\n", "\n", "# 使用函数编码文本\n", "text = \"这是一个示例文本\"\n", "embedding = encode_text(text)\n", "print(f\"向量维度: {embedding.shape}\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["开始测试连接到医学指南API服务器...\n", "连接耗时: 1.51秒\n", "状态码: 200\n", "连接成功!\n", "数据长度: 15638\n", "内容预览: # 指南解读\n", "\n", "## 《食物蛋白诱导性小肠结肠炎综合征诊断和治疗国际共识指南》解读\n", "\n", "李丽莎 杨碧媛 欧淑娴 张萍萍 关凯\n", "\n", "DOI: 10.3969/j.issn.0253-9802.2022.02...\n"]}], "source": ["import requests\n", "import time\n", "\n", "def test_guide_server_connection():\n", "    \"\"\"\n", "    测试连接到医学指南API服务器\n", "    \"\"\"\n", "    print(\"开始测试连接到医学指南API服务器...\")\n", "    \n", "    # 尝试连接到指南服务器\n", "    try:\n", "        start_time = time.time()\n", "        response = requests.post(\n", "            \"http://47.94.171.56:8090/guide_multifield\",\n", "            json={\n", "                \"main_disease\": \"溃疡性结肠炎（左半结肠型）\",\n", "                \"source_file\": \"溃疡性结肠炎（左半结肠型）\",\n", "                \"top_k\": 1,\n", "            },\n", "            timeout=10\n", "        )\n", "        duration = time.time() - start_time\n", "        \n", "        print(f\"连接耗时: {duration:.2f}秒\")\n", "        print(f\"状态码: {response.status_code}\")\n", "        \n", "        if response.status_code == 200:\n", "            print(\"连接成功!\")\n", "            result = response.json()\n", "            print(f\"数据长度: {len(str(result))}\")\n", "            # 打印部分响应内容\n", "            content = result.get(\"results\", [{}])[0].get(\"payload\", {}).get(\"original_content\", \"\")\n", "            print(f\"内容预览: {content[:100]}...\" if content else \"无内容\")\n", "        else:\n", "            print(f\"连接失败: {response.text}\")\n", "    except requests.exceptions.Timeout:\n", "        print(\"连接超时: 服务器在10秒内没有响应\")\n", "    except requests.exceptions.ConnectionError as e:\n", "        print(f\"连接错误: {e}\")\n", "    except Exception as e:\n", "        print(f\"其他错误: {e}\")\n", "\n", "# 执行测试\n", "test_guide_server_connection()\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["开始测试Together API连接...\n", "请求耗时: 88.29秒\n", "响应内容:  人工智能（Artificial Intelligence，简称AI）是一种模拟人类智能的技术，旨在使计算机系统能够执行需要人类智能的任务。这些任务包括学习（通过经验改进性能）、推理（使用规则达到近似或明确的结论）、感知（理解图像、声音等）、自然语言处理（理解和生成人类语言）以及解决问题等。人工智能可以分为弱AI（专注于特定任务）和强AI（具备与人类相当的广泛智能），目前的技术主要集中在弱AI领域。 2. 机器学习和深度学习有什么区别？ 机器学习（Machine Learning）是人工智能的一个子领域，涉及通过数据和经验自动改进算法性能的方法。它依赖于特征工程和算法选择，例如决策树、支持向量机等。 深度学习（Deep Learning）是机器学习的一个分支，使用深层神经网络（通常有多层）来自动学习数据的层次化特征表示。它减少了对手工特征工程的依赖，特别适合处理高维数据（如图像、声音）。 主要区别在于特征处理：机器学习需要人工提取特征，而深度学习自动学习特征。 3. 什么是过拟合？如何防止过拟合？ 过拟合（Overfitting）指模型在训练数据上表现很好，但在未见过的测试数据上表现差的现象。原因是模型过于复杂，记住了训练数据的噪声和细节，而非学习其潜在模式。 防止过拟合的方法： 1. 数据增强：增加训练数据量，或通过旋转、裁剪等方式生成更多数据。 2. 简化模型：减少模型参数或层数，降低复杂度。 3. 正则化：如L1/L2正则化，惩罚大权重。 4. 交叉验证：使用交叉验证评估模型泛化能力。 5. 早停（Early Stopping）：在验证集性能下降时停止训练。 6. Dropout（神经网络中）：随机丢弃部分神经元，防止依赖特定节点。 4. 解释一下梯度下降算法。 梯度下降（Gradient Descent）是一种优化算法，用于最小化损失函数，通过迭代调整模型参数。其步骤如下： 1. 计算损失函数关于参数的梯度（导数）。 2. 沿梯度反方向更新参数，因为梯度方向是函数增长最快的方向，反方向则是下降最快的方向。 3. 重复直到收敛（梯度接近零或达到固定迭代次数）。 变种： 批量梯度下降（BGD）：使用全部数据计算梯度，准确但计算量大。 随机梯度下降（SGD）：每次随机选一个样本计算梯度，速度快但波动大。 小批量梯度下降（Mini-batch SGD）：折中方案，每次用小批量数据计算梯度。 5. 什么是交叉验证？为什么重要？ 交叉验证（Cross-Validation）是一种评估模型泛化性能的技术，通过将数据集分成多个子集，轮流用其中一个子集作为验证集，其余作为训练集。 常见方法：K折交叉验证（将数据分为K份，每次用K-1份训练，1份验证，重复K次）。 重要性： 1. 更准确评估模型在未知数据上的性能，避免单次划分的偶然性。 2. 充分利用数据，尤其在数据量少时，减少因划分导致的训练不足。 6. 解释精确率（Precision）和召回率（Recall），以及它们的权衡。 精确率（Precision）：预测为正类的样本中，实际为正类的比例。公式：Precision = TP / (TP + FP) 召回率（Recall）：实际为正类的样本中，被正确预测为正类的比例。公式：Recall = TP / (TP + FN) 权衡： 精确率高意味着预测为正类的样本中错误较少（FP少），但可能漏掉很多正类（FN多，召回率低）。 召回率高意味着抓住了大部分正类，但可能包含较多误报（FP多，精确率低）。 通过调整分类阈值（如逻辑回归中的概率阈值）可以调节两者：阈值升高，精确率提高，召回率降低；阈值降低则相反。 7. 什么是ROC曲线和AUC？ ROC曲线（Receiver Operating Characteristic Curve）描绘了分类模型在不同阈值下的真正类率（TPR，即召回率）和假正类率（FPR = FP / (FP + TN)）的关系。 AUC（Area Under Curve）是ROC曲线下的面积，用于量化模型整体性能。AUC越接近1，模型性能越好；0.5表示随机猜测。 优点：AUC对类别不平衡不敏感，适合评估不平衡数据集的性能。 8. 如何处理数据中的缺失值？ 处理缺失值的方法： 1. 删除：删除含有缺失值的行或列（适用于缺失较少或对分析影响不大的情况）。 2. 填充：用均值、中位数、众数（数值型）或特定值（如“未知”）填充。 3. 插值：时间序列数据可用前后值插值。 4. 预测模型：用其他特征训练模型预测缺失值。 5. 标记缺失：添加二进制特征指示是否缺失。 选择方法需考虑数据分布、缺失机制（随机或非随机）及对模型的影响。 9. 什么是正则化？L1和L2正则化有什么区别？ 正则化（Regularization）用于防止过拟合，通过在损失函数中添加惩罚项，限制模型复杂度。 L1正则化（Lasso回归）：添加权重绝对值的和（λ||w||₁）。效果：倾向于产生稀疏权重，部分特征权重为零，适用于特征选择。 L2正则化（Ridge回归）：添加权重平方和的平方（λ||w||₂²）。效果：使权重接近零但不为零，适合处理共线性问题。 区别：L1产生稀疏解，L2平滑解。 10. 解释主成分分析（PCA）的原理。 主成分分析（PCA）是一种降维技术，通过线性变换将高维数据投影到低维空间，保留最大方差的方向（主成分）。 步骤： 1. 标准化数据（均值为0，方差为1）。 2. 计算协方差矩阵。 3. 计算协方差矩阵的特征值和特征向量。 4. 按特征值降序排列特征向量，选择前k个作为主成分。 5. 将数据投影到这些主成分上。 目标：用较少维度保留尽可能多的数据信息，减少计算复杂度和噪声影响。 11. 什么是支持向量机（SVM）？ SVM是一种监督学习模型，用于分类和回归。其核心思想是找到最大间隔超平面，使得两类数据点距离超平面最近的点（支持向量）之间的间隔最大。 关键点： 核技巧（Kernel Trick）：通过核函数将数据映射到高维空间，解决非线性可分问题。常见核函数有线性、多项式、径向基（RBF）等。 软间隔：允许部分样本不满足严格间隔，通过惩罚系数C控制容错率。 12. 解释K均值（K-means）聚类算法。 K-means是一种无监督聚类算法，步骤： 1. 随机选择K个初始聚类中心。 2. 将每个样本分配到最近的聚类中心。 3. 重新计算每个簇的中心（均值）。 4. 重复2-3步直到中心不再变化或达到迭代次数。 缺点：需预先指定K；对初始中心敏感，可能陷入局部最优；对噪声和异常值敏感。 改进方法：K-means++（优化初始中心选择），使用轮廓系数选择K。 13. 什么是决策树？如何防止过拟合？ 决策树是一种树形结构模型，通过特征分裂进行决策。每个内部节点表示一个特征测试，分支代表测试结果，叶节点代表类别或回归值。 防止过拟合的方法： 1. 剪枝：预剪枝（提前停止分裂，如限制树深度、节点最小样本数）和后剪枝（生成树后剪去不必要的分支）。 2. 限制树深度、叶节点数等参数。 3. 随机森林：通过集成多棵树降低过拟合风险。 14. 随机森林和梯度提升树（如XGBoost）有什么区别？ 随机森林（Random Forest）： 并行训练多棵决策树，每棵树用随机采样的数据和随机选择的特征进行训练。预测时投票（分类）或平均（回归）。 优点：抗过拟合，对噪声不敏感。 梯度提升树（如XGBoost）： 串行训练多棵树，每棵树纠正前一棵树的残差。通过梯度下降优化损失函数，并加入正则化。 优点：通常精度更高，但需调参；计算量较大。 主要区别：随机森林是Bagging（并行），XGBoost是Boosting（串行）。 15. 什么是神经网络中的激活函数？为什么需要？ 激活函数（Activation Function）用于神经网络中，对输入进行非线性变换，使网络能够学习复杂模式。 常见激活函数： Sigmoid：输出0-1，适合二分类，但易导致梯度消失。 ReLU（Rectified Linear Unit）：f(x)=max(0,x)，解决梯度消失，计算快，但可能导致神经元死亡（负输入梯度为零）。 Tanh：输出-1到1，比Sigmoid中心对称，梯度更强。 Leaky ReLU：解决ReLU的死亡问题，允许负输入有微小梯度。 作用：引入非线性，否则多层网络等效于单层线性模型。 16. 解释反向传播算法。 反向传播（Backpropagation）是训练神经网络的核心算法，用于计算损失函数对网络参数的梯度。步骤： 1. 前向传播：输入数据通过网络计算输出，得到损失值。 2. 反向传播：从输出层开始，按链式法则计算损失对每层参数的梯度。 3. 参数更新：使用梯度下降等优化器更新参数。 关键点：利用计算图自动微分，高效计算各层梯度。 17. 什么是词嵌入（Word Embedding）？ 词嵌入是将词语映射到低维连续向量空间的技术，捕捉词语的语义和语法关系。常见模型：Word2Vec（Skip-Gram、CBOW）、GloVe、FastText。 特点：语义相近的词向量距离近；支持向量运算（如“国王 - 男人 + 女人 = 女王”）。 应用：作为NLP任务的输入特征，如文本分类、机器翻译。 18. 解释Transformer模型的核心思想。 Transformer是一种基于自注意力（Self-Attention）机制的深度学习模型，用于处理序列数据（如文本）。核心组件： 1. 自注意力机制：计算序列中每个位置与其他位置的关系权重，捕捉长距离依赖。 2. 位置编码：为序列添加位置信息，弥补自注意力对顺序不敏感的问题。 3. 多头注意力：并行多个自注意力头，学习不同子空间的表示。 4. 前馈网络：对每个位置进行非线性变换。 优点：并行计算效率高，适合长序列，取代了RNN和LSTM在NLP中的主流地位。 19. 什么是生成对抗网络（GAN）？ GAN由生成器（Generator）和判别器（Discriminator）组成，通过对抗训练生成逼真数据。 生成器：将随机噪声转换为数据样本，试图欺骗判别器。 判别器：区分真实样本和生成样本。 训练过程：交替优化生成器和判别器，直到判别器无法区分真假（纳什均衡）。 应用：图像生成、风格迁移、数据增强。 20. 解释强化学习的基本概念。 强化学习（Reinforcement Learning）是智能体通过与环境交互学习最优策略的机器学习方法。 基本要素： 智能体（Agent）：执行动作的主体。 环境（Environment）：智能体交互的对象。 状态（State）：环境的当前情况。 动作（Action）：智能体的行为。 奖励（Reward）：环境对动作的反馈信号。 策略（Policy）：状态到动作的映射。 目标：最大化累积奖励。 常见算法：Q-Learning、Deep Q-Network (DQN)、策略梯度（Policy Gradient）。 21. 什么是贝叶斯定理？在机器学习中的应用？ 贝叶斯定理：P(A|B) = P(B|A) * P(A) / P(B)，用于在已知相关事件概率时，更新某事件的概率估计。 应用： 朴素贝叶斯分类器：假设特征独立，利用贝叶斯定理计算类别概率。 贝叶斯网络：表示变量间的概率依赖关系。 贝叶斯优化：用于超参数调优，基于先验评估选择下一个参数组合。 22. 如何处理类别不平衡问题？ 方法： 1. 重采样：过采样少数类（如SMOTE生成合成样本）或欠采样多数类。 2. 调整类别权重：在损失函数中给少数类更高权重。 3. 使用评价指标：如F1-score、AUC，而非准确率。 4. 阈值调整：降低分类阈值，提高少数类的召回率。 5. 集成方法：如EasyEnsemble、BalanceCascade。 23. 解释Dropout在神经网络中的作用。 Dropout是一种正则化技术，训练过程中随机丢弃（置零）部分神经元，防止过拟合。 作用： 1. 减少神经元间的复杂共适应关系，迫使网络学习更鲁棒的特征。 2. 相当于训练多个子网络，测试时平均效果（近似集成学习）。 参数：丢弃率p（通常0.5），表示每个神经元被丢弃的概率。 24. 什么是迁移学习？举例说明。 迁移学习（Transfer Learning）是将从一个任务中学到的知识迁移到另一个相关任务中，通常通过复用预训练模型的部分或全部参数。 例子： 使用在ImageNet上预训练的卷积神经网络（如ResNet）进行医学图像分类，只需微调最后几层。 自然语言处理中，使用预训练的BERT模型进行文本分类或问答任务。 优点：节省训练时间，提升小数据集的模型性能。 25. 解释Batch Normalization的作用。 批归一化（Batch Normalization）对每一层的输入进行标准化（均值0，方差1），通常添加在激活函数之前。 作用： 1. 加速训练：减少内部协变量偏移（层输入分布变化），允许更大学习率。 2. 正则化效果：轻微噪声，类似Dropout。 3. 减少对初始化的敏感度。 测试时：使用训练阶段的移动平均均值和方差。 26. 什么是自编码器（Autoencoder）？ 自编码器是一种无监督神经网络，用于学习数据的有效表示（编码）。结构包括： 编码器（Encoder）：将输入压缩为潜在表示（编码）。 解码器（Decoder）：从编码重构输入。 目标：最小化重构误差，迫使编码捕捉关键特征。 应用：降维、去噪、生成模型（如变分自编码器VAE）。 27. 解释注意力机制（Attention Mechanism）。 注意力机制使模型能够动态关注输入的不同部分，提升处理长序列或复杂结构的能力。 核心：计算查询（Query）与键（Key）的相似度，得到权重（注意力分数），加权求和值（Value）。 应用： Transformer中的自注意力，用于捕捉序列内部关系。 机器翻译中，解码时关注源语言的不同词。 优点：提高模型解释性，处理长距离依赖。 28. 什么是联邦学习？ 联邦学习（Federated Learning）是一种分布式机器学习方法，允许多个设备或机构协同训练模型，而无需共享本地数据。 步骤： 1. 中心服务器下发全局模型。 2. 本地设备用本地数据更新模型。 3. 上传模型更新（而非数据）到服务器。 4. 服务器聚合更新，形成新全局模型。 优点：保护数据隐私，符合GDPR等法规。 挑战：通信开销、异构数据、安全性。 29. 解释推荐系统中的协同过滤。 协同过滤（Collaborative Filtering）基于用户-物品交互历史（如评分）进行推荐，分为： 基于用户的协同过滤：找到与目标用户相似的用户，推荐他们喜欢的物品。 基于物品的协同过滤：找到与目标物品相似的物品，推荐给用户。 矩阵分解：将用户-物品矩阵分解为用户隐向量和物品隐向量的乘积，用于评分预测。 优点：无需物品特征，仅依赖用户行为。 缺点：冷启动问题（新用户或物品无历史数据）。 30. 什么是时间序列分析？常用方法有哪些？ 时间序列分析研究按时间顺序排列的数据，以预测未来值或识别模式。 常用方法： 1. 统计模型：ARIMA（自回归积分滑动平均）、SARIMA（季节性ARIMA）、指数平滑。 2. 机器学习：特征工程（滞后特征、滑动统计量）后使用回归模型。 3. 深度学习：RNN、LSTM、Transformer时间序列变体（如Informer）。 4. 状态空间模型：卡尔曼滤波、结构时间序列模型。 应用：股票预测、销量预测、天气预测。 31. 解释自然语言处理中的词袋模型。 词袋模型（Bag of Words, BoW）将文本表示为单词出现次数的向量，忽略语法和顺序。 步骤： 1. 构建词汇表：所有文本中的唯一单词。 2. 统计每个文本中单词的出现次数（或TF-IDF权重）。 缺点：忽略词序和语义，高维稀疏。 改进：N-gram模型（考虑连续N个词）、TF-IDF加权。 32. 什么是长短期记忆网络（LSTM）？ LSTM是一种循环神经网络（RNN），通过门控机制解决传统RNN的梯度消失/爆炸问题，适合处理长序列。 核心组件： 遗忘门：决定丢弃哪些信息。 输入门：更新细胞状态的新信息。 输出门：基于细胞状态输出当前隐藏状态。 细胞状态（Cell State）：长期记忆的传递路径。 优点：捕捉长距离依赖，适用于时间序列、文本等序列数据。 33. 解释卷积神经网络（CNN）中的卷积层。 卷积层通过卷积核（滤波器）在输入数据上滑动，提取局部特征。 操作步骤： 1. 卷积核与输入局部区域进行点乘求和，得到特征图的一个值。 2. 滑动步长（Stride）决定卷积核移动的步幅。 3. 填充（Padding）：在输入边缘补零，控制输出尺寸。 作用： 参数共享：同一卷积核在整个输入上使用，减少参数量。 局部连接：每个输出仅依赖输入的局部区域，捕捉空间局部模式。 34. 什么是生成模型和判别模型？举例说明。 生成模型（Generative Model）：学习联合概率分布P(X,Y)，可生成数据。例如：朴素贝叶斯、高斯混合模型、GAN、VAE。 判别模型（Discriminative Model）：学习条件概率P(Y|X)或直接学习决策边界。例如：逻辑回归、SVM、决策树、神经网络。 区别：生成模型可生成新样本，判别模型通常分类性能更好。 35. 解释Hadoop和Spark在大数据处理中的作用。 Hadoop：分布式计算框架，基于HDFS（分布式文件系统）和MapReduce（批处理模型）。适合离线大规模数据处理，但磁盘IO开销大。 Spark：基于内存的分布式计算引擎，支持批处理、流处理、机器学习。通过RDD（弹性分布式数据集）实现高效迭代计算，速度比Hadoop快。 主要区别：Spark利用内存计算减少IO，适合迭代算法（如机器学习）；Hadoop适合一次性批处理。 36. 什么是数据标准化和归一化？ 标准化（Z-score Normalization）：将数据转换为均值为0，标准差为1的分布。公式：x' = (x - μ) / σ 归一化（Min-Max Scaling）：将数据缩放到[0,1]区间。公式：x' = (x - min) / (max - min) 作用：消除量纲影响，加速模型收敛，提高精度。 选择：标准化对异常值不敏感，归一化对范围敏感。 37. 解释SQL中的JOIN操作。 JOIN用于合并两个或多个表的行，基于相关列之间的关系。 类型： INNER JOIN：返回两表匹配的行。 LEFT JOIN：返回左表所有行，右表无匹配则为NULL。 RIGHT JOIN：返回右表所有行，左表无匹配则为NULL。 FULL OUTER JOIN：返回左右表所有行，无匹配则为NULL。 CROSS JOIN：返回两表的笛卡尔积。 38. 什么是数据库索引？优缺点？ 索引是对数据库表中一列或多列的值进行排序的数据结构，加快查询速度。 类型：B树、哈希、全文索引等。 优点： 加快SELECT查询速度。 唯一索引保证数据唯一性。 缺点： 占用存储空间。 降低INSERT、UPDATE、DELETE速度（需维护索引）。 需权衡读写性能。 39. 解释ACID属性。 ACID是数据库事务的四个特性： 原子性（Atomicity）：事务要么全部完成，要么全部不完成。 一致性（Consistency）：事务使数据库从一个有效状态变为另一个有效状态。 隔离性（Isolation）：并发事务互不干扰。 持久性（Durability）：事务提交后，修改永久保存。 40. 什么是RESTful API？ RESTful API是基于REST（Representational State Transfer）架构风格的Web服务接口，特点： 资源导向：使用URI标识资源。 HTTP方法：GET（获取）、POST（创建）、PUT（更新）、DELETE（删除）。 无状态：每次请求包含所有必要信息。 数据格式：通常使用JSON或XML。 41. 解释HTTP和HTTPS的区别。 HTTP（超文本传输协议）：用于客户端和服务器通信，明文传输，端口80。 HTTPS：HTTP + SSL/TLS加密，端口443，通过证书验证服务器身份，防止窃听和篡改。 区别：HTTPS更安全，但需要证书，稍增加延迟。 42. 什么是Docker？ Docker是一种容器化平台，将应用及其依赖打包成轻量级、可移植的容器，与宿主机共享内核，但隔离运行。 优点： 环境一致性：开发、测试、生产环境一致。 快速部署：镜像秒级启动。 资源高效：共享内核，占用资源少。 组件：Docker镜像（模板）、容器（运行实例）、仓库（镜像存储）。 43. 解释Git的基本工作流程。 Git是分布式版本控制系统，基本流程： 工作区：本地文件系统。 暂存区（Index）：准备提交的更改。 仓库（Repository）：保存提交历史。 常用命令： git init：初始化仓库。 git add：添加文件到暂存区。 git commit：提交到本地仓库。 git push：推送到远程仓库。 git pull：拉取远程更新。 git branch：管理分支。 git merge：合并分支。 44. 什么是微服务架构？ 微服务架构将应用拆分为多个小型、松耦合的服务，每个服务独立开发、部署、扩展。 特点： 单一职责：每个服务负责一个业务功能。 独立部署：服务可独立更新和扩展。 技术异构：不同服务可用不同技术栈。 通信：通常通过HTTP/REST或消息队列。 优点：灵活性高，容错性好，适合复杂系统。 挑战：服务治理、分布式事务、监控。 45. 解释OAuth2.0授权流程。 OAuth2.0是一种授权框架，允许第三方应用在用户授权下访问其资源，无需分享密码。 常见流程（授权码模式）： 1. 用户访问第三方应用，应用重定向到授权服务器。 2. 用户登录并授权。 3. 授权服务器返回授权码给应用。 4. 应用用授权码换取访问令牌（Access Token）。 5. 应用使用访问令牌访问资源服务器。 角色：资源所有者（用户）、客户端（第三方应用）、授权服务器、资源服务器。 46. 什么是负载均衡？常用算法有哪些？ 负载均衡将请求分发到多个服务器，提高系统可用性和性能。 常用算法： 轮询（Round Robin）：依次分发。 加权轮询：根据服务器权重分配。 最少连接（Least Connections）：发给当前连接最少的服务器。 IP哈希：根据客户端IP哈希选择服务器，保持会话。 47. 解释SQL注入及防范方法。 SQL注入：攻击者通过输入恶意SQL代码，篡改数据库查询。 防范方法： 参数化查询（预编译语句）：使用占位符，避免拼接SQL。 ORM框架：自动转义输入。 输入验证：过滤特殊字符。 最小权限：数据库用户权限限制。 48. 什么是跨站脚本攻击（XSS）？如何防范？ XSS：攻击者注入恶意脚本到网页，其他用户执行时盗取信息或会话。 类型：存储型（恶意脚本存入数据库）、反射型（URL参数注入）、DOM型（客户端脚本操作DOM）。 防范： 输入输出转义：对用户输入和动态内容进行HTML转义。 Content Security Policy (CSP)：限制资源加载源。 HttpOnly Cookie：防止JavaScript访问敏感Cookie。 49. 解释CAP定理。 CAP定理指出分布式系统最多同时满足其中两个特性： 一致性（Consistency）：所有节点看到同一数据。 可用性（Availability）：每个请求都能得到响应。 分区容错性（Partition Tolerance）：系统在网络分区时仍能工作。 通常选择CP（如分布式数据库）或AP（如Cassandra）。 50. 什么是区块链？ 区块链是一种去中心化的分布式账本技术，特点： 区块：按时间顺序存储交易数据，通过哈希链接。 共识机制：如工作量证明（PoW）、权益证明（PoS），确保节点一致。 不可篡改：一旦记录，难以修改，需多数节点同意。 应用：加密货币（比特币）、智能合约（以太坊）、供应链追踪。 总结 这些问题覆盖了人工智能、机器学习、深度学习、大数据、数据库、网络、系统设计等多个领域的基础知识。在准备面试时，建议结合具体岗位要求，深入理解核心概念，并通过实际项目经验加深理解。同时，动手实践和模拟面试也是提升应对能力的有效方法。\n"]}], "source": ["# 导入Together客户端\n", "from together import Together\n", "import os\n", "import time  # 添加time模块导入\n", "\n", "# 设置API密钥\n", "# 方法一：通过环境变量设置（推荐）\n", "# os.environ[\"TOGETHER_API_KEY\"] = \"your-api-key-here\"\n", "\n", "# 方法二：直接在代码中设置（不推荐用于生产环境）\n", "api_key = \"tgp_v1_744SaOr5Oq-jE8MImG-Ru7MhuwGpoH8AkhYQQiMw6MQ\"  # 替换为你的实际API密钥\n", "\n", "# 创建Together客户端实例\n", "client = Together(api_key=api_key)  # 直接传入API密钥\n", "\n", "print(\"开始测试Together API连接...\")\n", "\n", "try:\n", "    # 发送测试请求\n", "    start_time = time.time()\n", "    response = client.chat.completions.create(\n", "        model=\"DUO123/deepseek-ai/DeepSeek-R1-8c48fcde\",\n", "        messages=[\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": \"什么是人工智能？简要介绍一下。\"\n", "            }\n", "        ]\n", "    )\n", "    duration = time.time() - start_time\n", "    \n", "    # 打印响应信息\n", "    print(f\"请求耗时: {duration:.2f}秒\")\n", "    print(f\"响应内容: {response.choices[0].message.content}\")\n", "    \n", "except Exception as e:\n", "    print(f\"调用Together API时出错: {e}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}