user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log;
pid /run/nginx.pid;

include /usr/share/nginx/modules/*.conf;

events {
    worker_connections 1024;
}

http {
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile            on;
    tcp_nopush          on;
    tcp_nodelay         on;
    keepalive_timeout   65;
    types_hash_max_size 4096;

    include             /etc/nginx/mime.types;
    default_type        application/octet-stream;

    # 启用 gzip 压缩
    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 6;
    gzip_types text/plain text/css application/javascript application/json application/x-javascript text/xml application/xml application/xml+rss text/javascript;
    gzip_vary on;
    gzip_disable "MSIE [1-6]\.";

    # 客户端请求大小限制，适应语音上传
    client_max_body_size 10M;

    include /etc/nginx/conf.d/*.conf;

    # HTTP 服务器 - 重定向到HTTPS
    server {
        listen 80;
        listen [::]:80;
        server_name aiconsult.xiaoerfang.cn ************;  # 同时匹配域名和IP

        # 重定向所有HTTP请求到HTTPS
        return 301 https://$host$request_uri;
    }

    # HTTPS 服务器
    server {
        listen 443 ssl;
        listen [::]:443 ssl;
        server_name aiconsult.xiaoerfang.cn;

        # SSL证书配置
        ssl_certificate /home/<USER>
        ssl_certificate_key /home/<USER>
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_prefer_server_ciphers on;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;

        # Vue 前端服务
        location / {
            root   /usr/share/nginx/html;
            index  index.html;
            try_files $uri $uri/ /index.html;
            
            # 安全响应头
            add_header X-Frame-Options "SAMEORIGIN";
            add_header X-XSS-Protection "1; mode=block";
            add_header X-Content-Type-Options "nosniff";
            add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        }

        # API 代理转发服务
        location ~ ^/(user|message|session|api|guidelines) {
            proxy_pass http://localhost:8080;
            
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket 支持
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            
            # SSE 配置
            proxy_set_header Connection '';
            chunked_transfer_encoding off;
            proxy_buffering off;
            proxy_cache off;
            proxy_read_timeout 300s;
            
            # 跨域配置
            add_header Access-Control-Allow-Origin * always;
            add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS' always;
            add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization' always;
            add_header Access-Control-Allow-Credentials 'true' always;
            
            if ($request_method = 'OPTIONS') {
                add_header Access-Control-Allow-Origin * always;
                add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS' always;
                add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization' always;
                add_header Access-Control-Allow-Credentials 'true' always;
                add_header Content-Length 0;
                add_header Content-Type text/plain;
                return 204;
            }
        }

        # 音频代理转发服务
        location /audio/stream {
            proxy_pass http://localhost:8080;
            
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 音频流设置配置
            proxy_buffering off;
            proxy_cache off;
            proxy_read_timeout 300s;
            
            add_header Access-Control-Allow-Origin * always;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }

        # 语音识别 WebSocket 代理
        location /api/asr {
            proxy_pass http://localhost:8080;
            
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_read_timeout 300s;
            proxy_send_timeout 300s;
            
            add_header Access-Control-Allow-Origin * always;
        }

        # 静态资源缓存控制
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 7d;
            add_header Cache-Control "public, no-transform";
            access_log off;
            
            # 启用 gzip 压缩
            gzip on;
            gzip_types text/plain text/css application/javascript application/json image/svg+xml;
        }

        # 错误页面配置
        error_page 404 /404.html;
        location = /404.html {
            root /usr/share/nginx/html;
        }

        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }

    # 保留原有的IP访问配置
    server {
        listen 80;
        listen [::]:80;
        server_name ************;

        # Vue 前端服务
        location / {
            root   /usr/share/nginx/html;
            index  index.html;
            try_files $uri $uri/ /index.html;
            
            # 安全响应头
            add_header X-Frame-Options "SAMEORIGIN";
            add_header X-XSS-Protection "1; mode=block";
            add_header X-Content-Type-Options "nosniff";
            add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        }

        # API 代理转发服务
        location ~ ^/(user|message|session|api|guidelines) {
            proxy_pass http://************:8080;
            
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket 支持
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            
            # SSE 配置 - 用于语音识别和实时聊天
            proxy_set_header Connection '';
            chunked_transfer_encoding off;
            proxy_buffering off;
            proxy_cache off;
            proxy_read_timeout 300s;
            
            # 跨域配置
            add_header Access-Control-Allow-Origin * always;
            add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS' always;
            add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization' always;
            add_header Access-Control-Allow-Credentials 'true' always;
            
            if ($request_method = 'OPTIONS') {
                add_header Access-Control-Allow-Origin * always;
                add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS' always;
                add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization' always;
                add_header Access-Control-Allow-Credentials 'true' always;
                add_header Content-Length 0;
                add_header Content-Type text/plain;
                return 204;
            }
        }

        # 音频代理转发服务
        location /audio/stream {
            proxy_pass http://************:8080;
            
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            
            # 音频流设置配置
            proxy_buffering off;
            proxy_cache off;
            proxy_read_timeout 300s;
            
            add_header Access-Control-Allow-Origin * always;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }

        # 语音识别 WebSocket 代理
        location /api/asr {
            proxy_pass http://************:8080;
            
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            
            proxy_read_timeout 300s;
            proxy_send_timeout 300s;
            
            add_header Access-Control-Allow-Origin * always;
        }

        # 静态资源缓存控制
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 7d;
            add_header Cache-Control "public, no-transform";
            access_log off;
            
            # 启用 gzip 压缩
            gzip on;
            gzip_types text/plain text/css application/javascript application/json image/svg+xml;
        }

        # 错误页面配置
        error_page 404 /404.html;
        location = /404.html {
            root /usr/share/nginx/html;
        }

        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }
}