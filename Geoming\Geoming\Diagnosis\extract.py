import json
import re
import pandas as pd
import numpy as np
import openpyxl


def format_medical_advice(data):
    # 提取药品信息
    medicine_text = "药品推荐：\n"
    for medicine in data.get('药品', []):  # 确保 '药品' 存在，否则返回空列表
        medicine_text += f"\n药品名: {medicine.get('药品名', '未知')}\n"
        medicine_text += f"规格: {medicine.get('规格', '未知')}\n"
        medicine_text += f"服用方法: {medicine.get('服用方法', '未知')}\n"
        medicine_text += f"剂量安排: {medicine.get('剂量安排', '未知')}\n"
        medicine_text += f"使用目的: {medicine.get('使用目的', '未知')}\n"
        medicine_text += "-" * 30

    # 提取生活建议
    life_advice_text = "\n生活建议：\n"
    for advice in data.get('生活建议', []):  # 确保 '生活建议' 存在，否则返回空列表
        life_advice_text += f"- {advice}\n"

    # 合并药品信息和生活建议
    formatted_text = medicine_text + life_advice_text
    return formatted_text



# 读取日志文件
with open("tmp.txt", "r", encoding="utf-8") as file:
    logs = file.read()
    # 输出file的大小
    print(len(logs))


# 分割不同的诊断块
log_entries = logs.split("--------------------------------------")
results = []

diagnosis_results = []

treatment_results = []


# 解析每个日志块
for entry in log_entries:
    # # 输出index
    print(log_entries.index(entry))
    # print(len(entry))
    
    # 匹配 JSON 块
    json_match = re.findall(r'\{\s*"病名"\s*:\s*".+?"\s*,\s*"种类"\s*:\s*\{[\s\S]+?\}\s*\}', entry)
    if json_match:
        json_str = json_match[1]  # 提取 JSON 部分
        # print(json_str)
        diagnosis = re.search(r'"病名"\s*:\s*"(.+?)"', json_str).group(0)
        print(diagnosis)
        # 对diagnosis进行截断，只保留病名
        diagnosis = diagnosis[7:-1]
        print(diagnosis)
        diagnosis_results.append(diagnosis)
    else:
        print("未找到 JSON 数据")

    json_pattern = r'\{\s*"药品"\s*:\s*\[[\s\S]*?\]\s*,\s*"生活建议"\s*:\s*\[[\s\S]+?\]\s*\}'
    json_match_2 = re.findall(json_pattern, entry)

    json_str_2 = ""
    if json_match_2:
        print("找到 JSON    数据")
        json_str_2 = json_match_2[1]  # 提取 JSON 部分
        # print(json_str_2)
    treatment = format_medical_advice(json.loads(json_str_2))
    treatment_results.append(treatment)
        

# 保存到 XLSX 文件
df = pd.DataFrame(diagnosis_results, columns=["病名"])
df["治疗建议"] = treatment_results  

df.to_excel("extracted_results.xlsx", index=False)
print("提取完成，结果已保存到 extracted_results.xlsx")




