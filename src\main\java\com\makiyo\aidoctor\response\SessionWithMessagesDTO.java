package com.makiyo.aidoctor.response;

import com.makiyo.aidoctor.entity.Message;
import com.makiyo.aidoctor.entity.Session;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 会话与消息组合的数据传输对象
 */
@Data
public class SessionWithMessagesDTO {
    /**
     * 会话ID
     */
    private Integer id;
    
    /**
     * 用户ID
     */
    private Integer userId;
    
    /**
     * 创建时间
     */
    private Date createdAt;
    
    /**
     * 更新时间
     */
    private Date updatedAt;
    
    /**
     * 会话对应的消息列表
     */
    private List<Message> messages;
    
    /**
     * 消息数量
     */
    private long messageCount;
    
    /**
     * 会话状态
     */
    private String sessionStatus;
    
    /**
     * 从Session实体创建DTO对象
     */
    public static SessionWithMessagesDTO fromSession(Session session, List<Message> messages) {
        SessionWithMessagesDTO dto = new SessionWithMessagesDTO();
        dto.setId(session.getId());
        dto.setUserId(session.getUserId());
        dto.setCreatedAt(session.getCreatedAt());
        dto.setUpdatedAt(session.getUpdatedAt());
        dto.setMessages(messages);
        return dto;
    }

    public Integer getId() {
        return id;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public List<Message> getMessages() {
        return messages;
    }

    public void setMessages(List<Message> messages) {
        this.messages = messages;
    }

    public long getMessageCount() {
        return messageCount;
    }

    public void setMessageCount(long messageCount) {
        this.messageCount = messageCount;
    }

    public String getSessionStatus() {
        return sessionStatus;
    }

    public void setSessionStatus(String sessionStatus) {
        this.sessionStatus = sessionStatus;
    }
} 