package com.makiyo.ai_doctor_prod.controller;

import com.makiyo.ai_doctor_prod.entity.OcrResult;
import com.makiyo.ai_doctor_prod.service.ImageService;
import com.makiyo.ai_doctor_prod.service.OcrService;
import com.makiyo.ai_doctor_prod.service.CaseBaseInfoService;
import com.makiyo.ai_doctor_prod.response.PageInfo;
import com.mongodb.client.gridfs.model.GridFSFile;
import io.swagger.v3.oas.annotations.Operation;
import org.bson.Document;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.gridfs.GridFsResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;

/**
 * OCR数据管理控制器 (生产环境)
 * 提供OCR识别结果和图片查询接口
 */
@RestController
@RequestMapping("/images")
@Tag(name = "OCR 数据 API (Prod)", description = "提供OCR结果和图片查询接口")
public class OcrDataController {

    private static final Logger log = LoggerFactory.getLogger(OcrDataController.class);
    
    private final OcrService ocrService;
    private final ImageService imageService;
    private final CaseBaseInfoService baseInfoService;
    
    @Autowired
    public OcrDataController(OcrService ocrService, ImageService imageService, CaseBaseInfoService baseInfoService) {
        this.ocrService = ocrService;
        this.imageService = imageService;
        this.baseInfoService = baseInfoService;
    }
    
    /**
     * 获取案例的所有OCR结果
     * 
     * @param caseId 案例ID
     * @return OCR结果列表
     */
    @Operation(
        summary = "获取案例OCR结果",
        description = "根据案例ID获取所有OCR识别历史记录"
    )
    @GetMapping("/results/{caseId}")
    public ResponseEntity<?> getOcrResultsByCaseId(
            @Parameter(description = "案例ID", required = true) 
            @PathVariable String caseId) {
        try {
            List<OcrResult> results = ocrService.getOcrResultsByCaseId(caseId);
            
            if (results.isEmpty()) {
                log.info("[PROD OCR Data] No OCR results found for case ID: {}", caseId);
                return ResponseEntity.ok(Collections.emptyList());
            }
            
            log.info("[PROD OCR Data] Found {} OCR results for case ID: {}", results.size(), caseId);
            return ResponseEntity.ok(results);
        } catch (Exception e) {
            log.error("[PROD OCR Data] Error retrieving OCR results for case ID {}: {}", caseId, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Collections.singletonMap("error", "获取OCR结果时发生错误: " + e.getMessage()));
        }
    }
    
    /**
     * 获取特定OCR结果
     * 
     * @param resultId OCR结果ID
     * @return OCR结果详情
     */
    @Operation(
        summary = "获取OCR结果详情",
        description = "根据OCR结果ID获取详细信息"
    )
    @GetMapping("/result/{resultId}")
    public ResponseEntity<?> getOcrResultById(
            @Parameter(description = "OCR结果ID", required = true) 
            @PathVariable String resultId) {
        try {
            Optional<OcrResult> result = ocrService.getOcrResultById(resultId);
            
            if (result.isPresent()) {
                log.info("[PROD OCR Data] Found OCR result with ID: {}", resultId);
                return ResponseEntity.ok(result.get());
            } else {
                log.warn("[PROD OCR Data] OCR result not found with ID: {}", resultId);
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(Collections.singletonMap("error", "未找到指定的OCR结果"));
            }
        } catch (Exception e) {
            log.error("[PROD OCR Data] Error retrieving OCR result with ID {}: {}", resultId, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Collections.singletonMap("error", "获取OCR结果时发生错误: " + e.getMessage()));
        }
    }
    
    /**
     * 获取OCR图片
     * 
     * @param imageId 图片ID (GridFS ID)
     * @return 图片资源
     */
    @Operation(
        summary = "获取OCR图片",
        description = "根据图片ID获取原始OCR图片",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "成功返回图片",
                content = @Content(mediaType = "image/*")
            ),
            @ApiResponse(
                responseCode = "404",
                description = "图片未找到",
                content = @Content(schema = @Schema(implementation = Map.class))
            )
        }
    )
    @GetMapping("/file/{imageId:[0-9a-fA-F]{24}}")
    public ResponseEntity<?> getImageById(
            @Parameter(description = "图片ID (GridFS ID)", required = true) 
            @PathVariable String imageId) {
        try {
            Optional<GridFsResource> resource = imageService.getImageById(imageId);
            
            if (resource.isPresent()) {
                GridFsResource gridFsResource = resource.get();
                
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.valueOf(gridFsResource.getContentType()));
                headers.setContentDisposition(
                    org.springframework.http.ContentDisposition.builder("inline")
                        .filename(gridFsResource.getFilename())
                        .build()
                );
                
                InputStreamResource inputStreamResource = new InputStreamResource(gridFsResource.getInputStream());
                
                log.info("[PROD OCR Data] Serving image with ID: {}", imageId);
                return new ResponseEntity<>(inputStreamResource, headers, HttpStatus.OK);
            } else {
                log.warn("[PROD OCR Data] Image not found with ID: {}", imageId);
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(Collections.singletonMap("error", "未找到指定的图片"));
            }
        } catch (IOException e) {
            log.error("[PROD OCR Data] Error retrieving image with ID {}: {}", imageId, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Collections.singletonMap("error", "获取图片时发生错误: " + e.getMessage()));
        }
    }
    
    /**
     * 获取所有OCR结果及其对应的图片信息
     * 
     * @return 所有OCR结果及图片信息的列表
     */
    @Operation(
        summary = "获取所有OCR结果及图片",
        description = "返回数据库中所有OCR识别结果及其对应的图片信息，支持分页"
    )
    @GetMapping("/all-ocr-results")
    public ResponseEntity<?> getAllOcrResultsWithImages(
            @Parameter(description = "页码 (从1开始)") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页数量") @RequestParam(defaultValue = "10") int size
    ) {
        try {
            Pageable pageable = PageRequest.of(page - 1, size, Sort.by("createdAt").descending());
            Page<OcrResult> ocrResultPage = ocrService.getAllOcrResults(pageable);

            if (ocrResultPage.isEmpty()) {
                log.info("[PROD OCR Data] No OCR results found for page {}", page);
                return ResponseEntity.ok(new PageInfo<>(Collections.emptyList(), page, size, 0));
            }

            List<Map<String, Object>> combinedResults = new ArrayList<>();
            for (OcrResult result : ocrResultPage.getContent()) {
                try {
                    Map<String, Object> combinedResult = new HashMap<>();
                    combinedResult.put("ocrResult", result);

                    String imageId = result.getImageId();
                    if (imageId != null) {
                        imageService.getImageMetadata(imageId).ifPresent(file -> {
                            Map<String, Object> imageInfo = new HashMap<>();
                            imageInfo.put("id", file.getObjectId().toString());
                            imageInfo.put("filename", file.getFilename());
                            Document metadata = file.getMetadata();
                            imageInfo.put("contentType", metadata != null ? metadata.get("contentType") : null);
                            imageInfo.put("size", file.getLength());
                            imageInfo.put("uploadDate", file.getUploadDate());
                            imageInfo.put("imageUrl", "/images/file/" + imageId);
                            combinedResult.put("imageInfo", imageInfo);
                        });
                    }
                    combinedResults.add(combinedResult);
                } catch (Exception e) {
                    log.error("[PROD OCR Data] Failed to process OCR result with ID {}: {}", result.getId(), e.getMessage(), e);
                }
            }
            
            PageInfo<Map<String, Object>> pageInfo = new PageInfo<>(
                combinedResults, 
                ocrResultPage.getNumber() + 1, 
                ocrResultPage.getSize(), 
                ocrResultPage.getTotalElements()
            );

            log.info("[PROD OCR Data] Found and successfully processed {} OCR results with images for page {}", combinedResults.size(), page);
            return ResponseEntity.ok(pageInfo);
        } catch (Exception e) {
            log.error("[PROD OCR Data] Error retrieving all OCR results with images: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Collections.singletonMap("error", "获取OCR结果及图片时发生错误: " + e.getMessage()));
        }
    }
    
    /**
     * 根据案例ID获取所有关联图片
     * 
     * @param caseId 案例ID
     * @return 图片元数据列表
     */
    @Operation(
        summary = "获取案例图片列表",
        description = "根据案例ID获取所有关联图片的元数据"
    )
    @GetMapping("/session/{caseId}")
    public ResponseEntity<?> getImagesByCaseId(
            @Parameter(description = "案例ID", required = true) 
            @PathVariable String caseId) {
        try {
            Iterable<GridFSFile> gridFSFiles = imageService.findImagesByCaseId(caseId);
            
            if (gridFSFiles == null) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(Collections.singletonMap("error", "查询图片时发生错误"));
            }
            
            List<Map<String, Object>> imageInfoList = new ArrayList<>();
            for (GridFSFile file : gridFSFiles) {
                Map<String, Object> imageInfo = new HashMap<>();
                imageInfo.put("id", file.getObjectId().toString());
                imageInfo.put("gridFsId", file.getObjectId().toString()); // For frontend compatibility

                if (file.getMetadata() != null) {
                    Object originalName = file.getMetadata().get("originalFilename");
                    imageInfo.put("originalName", originalName != null ? originalName.toString() : file.getFilename());
                    imageInfo.put("category", file.getMetadata().get("category"));
                    imageInfo.put("contentType", file.getMetadata().get("contentType"));
                    imageInfo.put("uploadTime", file.getMetadata().get("uploadTime"));
                } else {
                    imageInfo.put("originalName", file.getFilename());
                }

                imageInfo.put("size", file.getLength());
                
                imageInfoList.add(imageInfo);
            }
            
            log.info("[PROD OCR Data] Found {} images for case ID: {}", imageInfoList.size(), caseId);
            return ResponseEntity.ok(imageInfoList);
        } catch (Exception e) {
            log.error("[PROD OCR Data] Error retrieving images for case ID {}: {}", caseId, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Collections.singletonMap("error", "获取图片列表时发生错误: " + e.getMessage()));
        }
    }

    /**
     * 基础信息 OCR：上传图片，返回姓名/性别/年龄 JSON
     */
    @Operation(summary = "基础信息OCR", description = "识别图片中的姓名、性别、年龄等基础信息")
    @PostMapping(value = "/baseinfo", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> baseInfoOcr(@Parameter(description="图片文件", required=true) 
                                         @RequestParam("file") MultipartFile file) {
        try {
            Map<String, Object> result = ocrService.processBaseInfoProd(file);
            if (result.containsKey("error")) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result);
            }
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("[PROD OCR Data] baseinfo OCR failed: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Collections.singletonMap("error", "服务器处理 OCR 时发生错误: " + e.getMessage()));
        }
    }

    /**
     * 保存病例基础信息 (姓名/性别/年龄)
     */
    @Operation(summary = "保存病例基础信息", description = "根据 caseId 保存 info 字段")
    @PostMapping(value = "/case/info", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> saveCaseInfo(@RequestBody Map<String, Object> payload) {
        try {
            String caseId = (String) payload.get("caseId");
            Object infoObj = payload.get("info");
            if (caseId == null || infoObj == null || !(infoObj instanceof Map)) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(Collections.singletonMap("error", "请求参数缺失或格式不正确"));
            }
            @SuppressWarnings("unchecked")
            Map<String, Object> info = (Map<String, Object>) infoObj;
            baseInfoService.saveOrUpdate(caseId, info);
            return ResponseEntity.ok(Collections.singletonMap("message", "保存成功"));
        } catch (Exception e) {
            log.error("[PROD Case Info] save error: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Collections.singletonMap("error", "保存信息时发生错误: " + e.getMessage()));
        }
    }

    /**
     * 查询病例基础信息
     */
    @Operation(summary = "查询病例基础信息", description = "根据 caseId 获取 info")
    @GetMapping("/case/info/{caseId}")
    public ResponseEntity<?> getCaseInfo(@PathVariable String caseId) {
        try {
            return baseInfoService.getInfo(caseId)
                    .<ResponseEntity<?>>map(ResponseEntity::ok)
                    .orElseGet(() -> ResponseEntity.status(HttpStatus.NOT_FOUND)
                            .body(Collections.singletonMap("error", "未找到信息")));
        } catch (Exception e) {
            log.error("[PROD Case Info] query error: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Collections.singletonMap("error", "查询信息时发生错误: " + e.getMessage()));
        }
    }
}