from flask import Flask, request, jsonify, Response
import logging
from concurrent.futures import Thr<PERSON>PoolExecutor
import uuid
import time
import json
import re

# 项目中的模块
from Doctor import Doctor
from DiagnosisAgent import DiagnosisAgent
from Consultation import AI_check_solution, convert_history, doctor_turn
from prompts import _sys_prompt_doctor

app = Flask(__name__)

# 配置日志记录（支持多进程，输出到文件）
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(process)d - %(levelname)s - %(message)s",
    filename="app.log"
)
logger = logging.getLogger(__name__)

# 创建线程池（可选，视具体需求可移除）
executor = ThreadPoolExecutor(max_workers=10)


def remove_tagged_lines(text: str) -> str:
    """Remove lines that start with '【tag】' from the input string."""
    return "\n".join(line for line in text.splitlines() if not line.startswith("【tag】"))


def add_tags_to_history(messages):
    """为历史消息添加适当的标签，并返回列表格式"""
    tagged_messages = []
    for msg in messages:
        content = msg.get('content', '').strip()
        # 如果内容已经包含标签，直接分割并添加到列表
        if '<text>' in content and '<observation>' in content:
            parts = re.split(r'(</text>|</observation>)', content)
            current_message = []
            for part in parts:
                if not part.strip():
                    continue
                if part.strip() in ['</text>', '</observation>']:
                    if current_message:
                        current_message.append(part)
                        tagged_messages.append(''.join(current_message))
                        current_message = []
                else:
                    current_message.append(part)
        else:
            # 移除可能存在的标签
            content = remove_tags(content)
            # 根据角色或speaker添加适当的标签
            if msg.get('role') == 'assistant' or msg.get('speaker') == 'doctor':
                tagged_messages.append(f"<text>\n{content}\n</text>")
            elif msg.get('role') == 'user' or msg.get('speaker') == 'patient':
                tagged_messages.append(f"<observation>\n{content}\n</observation>")

    return tagged_messages


def remove_tags(text):
    """移除文本中的标签"""
    if not text:
        return text
    text = re.sub(r'<text>\s*', '', text)
    text = re.sub(r'\s*</text>', '', text)
    text = re.sub(r'<observation>\s*', '', text)
    text = re.sub(r'\s*</observation>', '', text)
    text = re.sub(r'\n\s*\n', '\n', text)
    return text.strip()


def clean_conversation_history(history):
    """清理对话历史并确保正确的格式"""
    if isinstance(history, list):
        cleaned_messages = []
        for message in history:
            message = message.strip()
            if '<text>' in message:
                content = re.sub(r'<text>\s*', '', message)
                content = re.sub(r'\s*</text>', '', content)
                cleaned_messages.append(f"<text>\n{content.strip()}\n</text>")
            elif '<observation>' in message:
                content = re.sub(r'<observation>\s*', '', message)
                content = re.sub(r'\s*</observation>', '', content)
                cleaned_messages.append(f"<observation>\n{content.strip()}\n</observation>")
    else:
        messages = []
        current_message = []
        for line in history.split('\n'):
            line = line.strip()
            if not line:
                continue
            if line.startswith('<text>') or line.startswith('<observation>'):
                if current_message:
                    messages.append('\n'.join(current_message))
                current_message = [line]
            elif line.endswith('</text>') or line.endswith('</observation>'):
                current_message.append(line)
                messages.append('\n'.join(current_message))
                current_message = []
            else:
                if current_message:
                    current_message.append(line)
        if current_message:
            messages.append('\n'.join(current_message))

        cleaned_messages = []
        for message in messages:
            if '<text>' in message:
                content = re.sub(r'<text>\s*', '', message)
                content = re.sub(r'\s*</text>', '', content)
                cleaned_messages.append(f"<text>\n{content.strip()}\n</text>")
            elif '<observation>' in message:
                content = re.sub(r'<observation>\s*', '', message)
                content = re.sub(r'\s*</observation>', '', content)
                cleaned_messages.append(f"<observation>\n{content.strip()}\n</observation>")

    logger.info(f"Cleaned messages: {cleaned_messages}")
    return cleaned_messages


def consultation_doctor_reoponse(history_doc):
    """处理医生问诊对话"""
    try:
        sysprompt_doctor = _sys_prompt_doctor
        history_pat = convert_history(history_doc)
        initial_message = "请您开始问诊"
        round_num = sum(1 for msg in history_doc if '<observation>' in msg) + 1
        model_v3 = "deepseek-v3-241226"

        logger.info(f"Round: {round_num}")
        logger.info(f"History pat: {history_pat}")

        response_doc, solution_text = doctor_turn(
            sysprompt_doctor, history_doc, history_pat, initial_message, model_v3, round_num
        )

        logger.info(f"Response doc: {response_doc}")
        logger.info(f"Solution text: {solution_text}")

        return response_doc, solution_text
    except Exception as e:
        logger.error(f"Error in consultation_doctor_reoponse: {str(e)}")
        raise


@app.route('/v1/chat/inquiry', methods=['POST'])
def chat_inquiry():
    try:
        data = request.get_json()
        if data is None:
            raise ValueError("No JSON data received")
        logger.info(f"Received request data: {data}")
        history_doc = data.get('history_doc', [])
        if not history_doc:
            raise ValueError("No history_doc provided in request")

        history_doc = clean_conversation_history(history_doc)
        response_doc, solution_text = consultation_doctor_reoponse(history_doc)

        complete_response = remove_tagged_lines(response_doc)
        return jsonify({
            "id": f"chatcmpl-{int(time.time())}",
            "object": "chat.completion",
            "created": int(time.time()),
            "model": data.get('model'),
            "choices": [{
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": complete_response
                },
                "finish_reason": "stop"
            }]
        })

    except Exception as e:
        logger.error(f"Error processing request: {str(e)}")
        return jsonify({
            "error": {
                "message": str(e),
                "type": "processing_error"
            }
        }), 500


@app.route('/v1/chat/diagnose', methods=['POST'])
def chat_diagnose():
    try:
        data = request.get_json()
        if data is None:
            raise ValueError("No JSON data received")
        logger.info(f"Received request data: {data}")
        solution_text = data.get('solution_text', "")
        if not solution_text:
            raise ValueError("No solution_text provided in request")

        doctors = Doctor.load_from_solution(solution_text)
        AI_ask_AI_check = AI_check_solution(solution_text)

        diagnosis_agent = DiagnosisAgent(doctors)
        results = diagnosis_agent.diagnose_all()
        if results:
            patient_info = remove_tagged_lines(solution_text)
            complete_response = "-----------------【AI检查建议】-------------------\n"
            if AI_ask_AI_check:
                complete_response += f"{AI_ask_AI_check}\n\n"
            complete_response += "-----------------【诊断建议】-------------------\n"
            complete_response += f"{results[0]['diagnosis']}\n\n"
            complete_response += "-----------------【治疗方案】-------------------\n"
            complete_response += f"{results[0]['treatment_plan']}"
            complete_response += "\n\n------【本次问诊结束，继续测试请开始新的会话】------\n"

        return jsonify({
            "id": f"chatcmpl-{int(time.time())}",
            "object": "chat.completion",
            "created": int(time.time()),
            "model": data.get('model'),
            "choices": [{
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": complete_response
                },
                "finish_reason": "stop"
            }]
        })
    except Exception as e:
        logger.error(f"Error processing request: {str(e)}")
        return jsonify({
            "error": {
                "message": str(e),
                "type": "processing_error"
            }
        }), 500


@app.route('/v1/chat/completions', methods=['POST'])
def chat_completions():
    try:
        data = request.get_json()
        if data is None:
            raise ValueError("No JSON data received")

        logger.info(f"Received request data: {data}")

        history_doc = data.get('history_doc', [])
        if not history_doc:
            raise ValueError("No history_doc provided in request")

        history_doc = clean_conversation_history(history_doc)
        response_doc, solution_text = consultation_doctor_reoponse(history_doc)

        if solution_text:
            doctors = Doctor.load_from_solution(solution_text)
            AI_ask_AI_check = AI_check_solution(solution_text)
            diagnosis_agent = DiagnosisAgent(doctors)
            results = diagnosis_agent.diagnose_all()

            if results:
                patient_info = remove_tagged_lines(solution_text)
                complete_response = "-----------------【问诊结果】-------------------\n"
                complete_response += f"{patient_info}\n\n"
                complete_response += "-----------------【AI检查建议】-------------------\n"
                if AI_ask_AI_check:
                    complete_response += f"{AI_ask_AI_check}\n\n"
                complete_response += "-----------------【诊断建议】-------------------\n"
                complete_response += f"{results[0]['diagnosis']}\n\n"
                complete_response += "-----------------【治疗方案】-------------------\n"
                complete_response += f"{results[0]['treatment_plan']}"
                complete_response += "\n\n------【本次问诊结束，继续测试请开始新的会话】------\n"
            else:
                complete_response = response_doc
        else:
            complete_response = response_doc

        return jsonify({
            "id": f"chatcmpl-{int(time.time())}",
            "object": "chat.completion",
            "created": int(time.time()),
            "model": data.get('model'),
            "choices": [{
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": complete_response
                },
                "finish_reason": "stop"
            }]
        })

    except Exception as e:
        logger.error(f"Error processing request: {str(e)}")
        return jsonify({
            "error": {
                "message": str(e),
                "type": "processing_error"
            }
        }), 500


def generate_streaming_response_from_doc(response_doc):
    """生成流式响应，不带标签，优化分块策略"""
    logger.info(f"Generating streaming response: {response_doc}")

    def generate():
        try:
            sentences = re.split(r'([。！？，：；\n])', response_doc)
            current_chunk = ""

            for i in range(0, len(sentences)):
                if i < len(sentences):
                    current_chunk += sentences[i]
                    if (len(current_chunk) >= 20 or
                            i == len(sentences) - 1 or
                            (i < len(sentences) - 1 and sentences[i] in '。！？，：；\n')):
                        if current_chunk.strip():
                            data_chunk = {
                                "id": f"chatcmpl-{int(time.time())}",
                                "object": "chat.completion.chunk",
                                "created": int(time.time()),
                                "model": "deepseek-r1-250120",
                                "choices": [
                                    {
                                        "index": 0,
                                        "delta": {
                                            "role": "assistant",
                                            "content": current_chunk
                                        },
                                        "finish_reason": None if i < len(sentences) - 1 else "stop"
                                    }
                                ]
                            }
                            yield f"data: {json.dumps(data_chunk)}\n\n"
                            current_chunk = ""
                            time.sleep(0.05)
        except Exception as e:
            logger.error(f"Error in generate_streaming_response: {str(e)}")
            yield "data: {\"error\": \"Internal Server Error\"}\n\n"

        yield "data: [DONE]\n\n"

    return Response(generate(), content_type='text/event-stream')


if __name__ == '__main__':
    # 仅用于开发调试，生产环境使用Gunicorn启动
    app.run(host='0.0.0.0', port=5555, debug=False)