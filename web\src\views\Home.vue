<template>
  <div class="home">
    <!-- 中间区域 -->
    <div class="main-section">
      <div class="button-container">
        <div class="instruction-text">
          <h3>本系统为AI儿科问诊内测版</h3>
          <p>请角色扮演一名门诊患者与AI医生对话，您需要在脑海中预设患者的病情、症状、病因等</p>
          <p>问答结束后，AI医生会输出四部分内容：问诊结果、AI检查建议、诊断建议、治疗方案</p>
          <p>请针对以上四分部分内容进行评测，并将结果返回给我们。评测格式为：</p>
          <p>会话编号：评测内容</p>
        </div>
        
        <a-button type="primary" size="large" @click="showLoginModal">
          请输入邀请码
        </a-button>
<!--        <a-button type="default" size="large" @click="showRegisterModal">
          注册
        </a-button> -->
      </div>
    </div>

    <!-- 登录对话框 -->
    <a-modal
      title="登录"
      v-model:open="loginVisible"
      :maskClosable="false"
      :footer="null"
    >
      <div class="form">
        <a-input
          v-model:value="loginForm.username"
          placeholder="请输入用户名"
          size="large"
          style="margin-bottom: 20px"
        />
        <a-input
          v-model:value="loginForm.password"
          placeholder="请输入密码"
          size="large"
          type="password"
          style="margin-bottom: 20px"
        />
        <div class="button-group">
          <a-button type="primary" size="large" @click="handleLogin" :loading="loading" block>
            登录
          </a-button>
        </div>
      </div>
    </a-modal>

    <!-- 注册对话框 -->
    <a-modal
      title="注册"
      v-model:open="registerVisible"
      :maskClosable="false"
      :footer="null"
    >
      <div class="form">
        <a-form :model="registerForm" layout="vertical">
          <a-form-item label="用户ID" required>
            <a-input
              v-model:value="registerForm.userId"
              placeholder="请输入用户ID（数字）"
              size="large"
            />
          </a-form-item>
          <a-form-item label="用户名">
            <a-input
              v-model:value="registerForm.username"
              placeholder="请输入用户名"
              size="large"
            />
          </a-form-item>
          <a-form-item label="简介">
            <a-textarea
              v-model:value="registerForm.bio"
              placeholder="请输入个人简介"
              :rows="3"
            />
          </a-form-item>
        </a-form>
        <div class="button-group">
          <a-button type="primary" size="large" @click="handleRegister" :loading="loading" block>
            注册
          </a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { message } from 'ant-design-vue'
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import request from '@/utils/request'

export default {
  name: 'HomePage',
  setup() {
    const router = useRouter()
    const loginVisible = ref(false)
    const registerVisible = ref(false)
    const loading = ref(false)
    
    const loginForm = reactive({
      username: '',
      password: ''
    })

    const registerForm = reactive({
      userId: '',
      username: '',
      bio: ''
    })

    const showLoginModal = () => {
      loginVisible.value = true
      registerVisible.value = false
    }

    const showRegisterModal = () => {
      registerVisible.value = true
      loginVisible.value = false
    }

    const handleLogin = async () => {
      if (!loginForm.username) {
        message.warning('请输入用户名')
        return
      }

      // 验证密码是否正确
      if (loginForm.password !== 'K7^pDx9@3FrZ$e2L#mVb5!qWtN8*yQaS') {
        message.error('密码错误')
        return
      }

      loading.value = true
      try {
        console.log('发送登录请求，参数:', {
          url: '/user/login',
          method: 'post',
          data: { username: loginForm.username }
        })
        const response = await request({
          url: '/user/login',
          method: 'post',
          data: {
            username: loginForm.username
          }
        })
        console.log('登录响应详情:', {
          status: response.code,
          message: response.message,
          data: response.content
        })
        if (response.code === 200) {
          message.success('登录成功')
          loginVisible.value = false
          
          // 从响应中获取真实的用户ID
          const userId = response.content.userInfo.userId
          
          // 确保userId存在且有效
          if (userId) {
            // 跳转到聊天页面，使用数字用户ID
            router.push({
              name: 'chat',
              params: { 
                userId: userId.toString(), // 确保是字符串
              },
              state: response.content  // 传递完整的登录响应数据
            })
          } else {
            message.error('获取用户ID失败')
          }
        } else {
          message.error(response.message || '登录失败')
        }
      } catch (error) {
        console.error('登录错误:', error)
        message.error(error.message || '登录失败')
      } finally {
        loading.value = false
      }
    }

    const handleRegister = async () => {
      if (!registerForm.userId) {
        message.warning('请输入用户ID')
        return
      }

      // 验证输入是否为数字
      if (!/^\d+$/.test(registerForm.userId)) {
        message.warning('请输入有效的数字ID')
        return
      }

      loading.value = true
      try {
        const userId = parseInt(registerForm.userId)
        const response = await request({
          url: '/user/register',
          method: 'post',
          data: {
            user_id: userId,
            userId: userId,
            username: registerForm.username || `用户${userId}`,
            profile_picture: `https://api.dicebear.com/7.x/avataaars/svg?seed=${userId}`,
            profilePicture: `https://api.dicebear.com/7.x/avataaars/svg?seed=${userId}`,
            bio: registerForm.bio || '这个用户很懒，还没有填写简介'
          }
        })
        if (response.code === 200) {
          message.success(response.content)
          registerVisible.value = false
          // 跳转到聊天页面
          router.push({
            name: 'chat',
            params: { userId: registerForm.userId }
          })
        } else {
          message.error(response.message || '注册失败')
        }
      } catch (error) {
        console.error('注册错误:', error)
        message.error(error.message || '注册失败')
      } finally {
        loading.value = false
      }
    }

    return {
      router,
      loginVisible,
      registerVisible,
      loading,
      loginForm,
      registerForm,
      showLoginModal,
      showRegisterModal,
      handleLogin,
      handleRegister
    }
  }
}
</script>

<style scoped>
.home {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f5f5;
  padding-top: 0;
  overflow-y: auto;
}

.main-section {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.button-container {
  display: flex;
  flex-direction: column;
  gap: 30px;
  align-items: center;
  max-width: 800px;
  width: 90%;
  background-color: #fff;
  padding: 40px;
  border-radius: 16px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

.instruction-text {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
  width: 100%;
  background-color: #f8faff;
  border: 2px solid rgba(77, 107, 254, 0.2);
  border-radius: 12px;
  padding: 30px;
}

.instruction-text h3 {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #4D6BFE;
  position: relative;
  display: inline-block;
  padding-bottom: 10px;
}

.instruction-text h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: #4D6BFE;
  border-radius: 3px;
}

.instruction-text p {
  font-size: 17px;
  line-height: 1.8;
  margin-bottom: 14px;
  max-width: 90%;
  margin-left: auto;
  margin-right: auto;
}

.instruction-text p:last-child {
  font-weight: 600;
  color: #4D6BFE;
  margin-top: 20px;
}

.form {
  padding: 20px 0;
}

.button-group {
  margin-top: 20px;
}

:deep(.ant-btn-primary) {
  background-color: #4D6BFE;
  border-color: #4D6BFE;
  height: 52px;
  font-size: 18px;
  padding: 0 40px;
  border-radius: 26px;
  box-shadow: 0 6px 16px rgba(77, 107, 254, 0.3);
  transition: all 0.3s ease;
}

:deep(.ant-btn-primary:hover) {
  background-color: #3D5BEE;
  border-color: #3D5BEE;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(77, 107, 254, 0.4);
}

:deep(.ant-btn-default:hover) {
  color: #4D6BFE;
  border-color: #4D6BFE;
}

:deep(.ant-input:focus), :deep(.ant-input:hover) {
  border-color: #4D6BFE;
  box-shadow: 0 0 0 2px rgba(77, 107, 254, 0.2);
}

:deep(.ant-form-item-label > label) {
  color: #4D6BFE;
}

:deep(.ant-modal-title) {
  color: #4D6BFE;
}
</style> 