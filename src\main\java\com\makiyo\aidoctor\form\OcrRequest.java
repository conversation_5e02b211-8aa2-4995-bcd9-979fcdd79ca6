package com.makiyo.aidoctor.form;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * DTO for OCR request payload.
 */
public class OcrRequest {

    @NotNull(message = "会话ID不能为空")
    private Integer sessionId;

    @NotEmpty(message = "图片ID不能为空")
    private String imageId;

    @NotEmpty(message = "图片Base64编码不能为空")
    private String imageBase64; // 注意：非常长的Base64字符串可能影响性能，考虑其他传输方式

    private String name; // 姓名，根据需要添加 @NotEmpty

    private String time; // 时间，根据需要添加 @NotEmpty，并考虑日期/时间类型

    // --- Get<PERSON> and Setters ---

    public Integer getSessionId() {
        return sessionId;
    }

    public void setSessionId(Integer sessionId) {
        this.sessionId = sessionId;
    }

    public String getImageId() {
        return imageId;
    }

    public void setImageId(String imageId) {
        this.imageId = imageId;
    }

    public String getImageBase64() {
        return imageBase64;
    }

    public void setImageBase64(String imageBase64) {
        this.imageBase64 = imageBase64;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }
} 