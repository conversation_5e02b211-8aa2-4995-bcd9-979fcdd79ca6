package com.makiyo.aidoctor.controller;

import com.makiyo.aidoctor.service.OcrService;
import com.makiyo.aidoctor.service.OcrQueueService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.core.io.Resource;

import java.util.Collections;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * 处理 OCR 相关请求的控制器。
 */
@RestController
@RequestMapping("/ocr") // OCR 相关端点的基础路径
public class OcrController {

    private static final Logger log = LoggerFactory.getLogger(OcrController.class);

    private final OcrService ocrService;
    private final OcrQueueService ocrQueueService;
    private final OcrQueueTestController ocrQueueTestController;

    @Autowired
    public OcrController(OcrService ocrService, OcrQueueService ocrQueueService, OcrQueueTestController ocrQueueTestController) {
        this.ocrService = ocrService;
        this.ocrQueueService = ocrQueueService;
        this.ocrQueueTestController = ocrQueueTestController;
    }

    /**
     * 处理 OCR 请求的端点。
     * 接收 multipart/form-data 请求，包含图片文件和其他参数。
     *
     * @param image 上传的图片文件 (表单部分名称: "image")。
     * @param name  用户名 (表单参数名称: "name")。
     * @param time  时间字符串 (表单参数名称: "time")。
     * @param sessionId 会话 ID (表单参数名称: "sessionId")。
     * @return 如果成功，返回包含 "content" 数据的 ResponseEntity (HTTP 200)；
     *         如果失败，返回包含错误信息的 ResponseEntity (HTTP 400 或 500)。
     */
    @PostMapping("/process")
    public ResponseEntity<?> processOcrRequest(
            @RequestPart("image") MultipartFile image,
            @RequestParam("name") String name,
            @RequestParam("time") String time,
            @RequestParam("sessionId") Integer sessionId) {

        log.info("收到 OCR 请求，Session ID: {}, 姓名: {}, 时间: {}, 图片: {}",
                 sessionId, name, time, image.getOriginalFilename());

        try {
            // 记录新请求统计
            ocrQueueTestController.recordNewRequest();
            
            // 使用队列服务而不是直接调用OCR服务
            long startTime = System.currentTimeMillis();
            CompletableFuture<Object> future = ocrQueueService.enqueueOcrRequest(image, name, time, sessionId);
            
            // 等待结果，设置合理的超时时间
            Object result = future.get(180, TimeUnit.SECONDS); // 3分钟超时
            long processingTime = System.currentTimeMillis() - startTime;
            
            if (result == null) {
                // 处理服务层返回的意外 null 值 (理想情况下不应发生)
                log.error("OCR 服务返回了意外的 null 结果。");
                ocrQueueTestController.updateStats(sessionId, false, processingTime);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                     .body(Collections.singletonMap("error", "OCR 处理期间发生内部服务器错误。"));
            } else if (result instanceof Map && ((Map<?, ?>) result).containsKey("error")) {
                 // 处理由服务层或 Python API 返回的特定错误
                 log.warn("OCR 处理失败，错误信息: {}", result);
                 ocrQueueTestController.updateStats(sessionId, false, processingTime);
                 // Determine status code based on error content
                 HttpStatus status = determineHttpStatusFromError((Map<String, Object>) result);
                 return ResponseEntity.status(status).body(result); // 返回包含错误信息的响应
            } else {
                // 成功处理，返回提取到的 "content"
                log.info("OCR 请求处理成功，Session ID: {}, 姓名: {}, 处理时间: {}ms", 
                         sessionId, name, processingTime);
                ocrQueueTestController.updateStats(sessionId, true, processingTime);
                // 为了响应体中有一致的 JSON 结构，将 content 包装在一个 Map 中
                return ResponseEntity.ok(Collections.singletonMap("content", result));
            }
        } catch (TimeoutException e) {
            log.error("OCR 处理超时，Session ID: {}: {}", sessionId, e.getMessage());
            return ResponseEntity.status(HttpStatus.REQUEST_TIMEOUT)
                                .body(Collections.singletonMap("error", "OCR 处理超时，请稍后重试。"));
        } catch (InterruptedException e) {
            log.error("OCR 处理被中断，Session ID: {}: {}", sessionId, e.getMessage());
            Thread.currentThread().interrupt(); // 重置中断标志
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                                .body(Collections.singletonMap("error", "OCR 处理被中断，请稍后重试。"));
        } catch (ExecutionException e) {
            log.error("OCR 处理执行异常，Session ID: {}: {}", sessionId, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                .body(Collections.singletonMap("error", "OCR 处理失败: " + e.getCause().getMessage()));
        } catch (Exception e) {
            // 捕获控制器中未处理的异常
            log.error("OcrController 中发生未处理的异常: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                 .body(Collections.singletonMap("error", "发生意外的内部服务器错误。"));
        }
    }

    /**
     * 从后端prod服务代理获取图片
     * @param imageId 图片ID
     * @return 图片资源
     */
    @GetMapping("/image/{imageId}")
    public ResponseEntity<Resource> getImage(@PathVariable String imageId) {
        log.info("收到图片请求，ID: {}，将从后端服务代理。", imageId);
        return ocrService.fetchImageFromProdService(imageId);
    }

    /**
     * 获取所有OCR结果
     * 该接口会代理请求到后端的prod服务
     * @return
     */
    @GetMapping("/all-results")
    public ResponseEntity<?> getAllOcrResults() {
        log.info("收到获取所有OCR结果的请求，将转发到后端服务。");
        try {
            Object result = ocrService.fetchAllOcrResultsFromProdService();
            if (result instanceof Map && ((Map<?, ?>) result).containsKey("error")) {
                log.warn("获取所有OCR结果失败: {}", result);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
            }
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("在 getAllOcrResults 控制器中发生错误: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                 .body(Collections.singletonMap("error", "发生意外错误"));
        }
    }

    /**
     * Helper method to determine HTTP status code based on error map.
     * @param errorResult The map possibly containing an "error" key.
     * @return Appropriate HttpStatus.
     */
    private HttpStatus determineHttpStatusFromError(Map<String, Object> errorResult) {
        String errorMsg = errorResult.getOrDefault("error", "").toString();
        Object content = errorResult.get("content"); // Check for specific content errors too

        if (errorMsg.contains("连接 OCR 服务失败") || errorMsg.contains("Failed to connect")) {
            return HttpStatus.SERVICE_UNAVAILABLE;
        } else if (errorMsg.contains("Internal server error") || errorMsg.contains("内部服务器错误")) {
            return HttpStatus.INTERNAL_SERVER_ERROR;
        } else if (content != null && (content.equals("识别失败") || content.equals("姓名错误"))) {
            return HttpStatus.BAD_REQUEST; // Specific API failure indicates bad input/image
        } else {
            // General client-side errors (missing params, file not found by python, etc.)
            // or other errors reported by the python service
            return HttpStatus.BAD_REQUEST;
        }
    }
}