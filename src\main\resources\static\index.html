<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语音识别测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }
        .btn {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover {
            background-color: #45a049;
        }
        .btn:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            min-height: 100px;
            max-height: 300px;
            overflow-y: auto;
            background-color: #f9f9f9;
        }
        .status {
            margin-top: 10px;
            color: #666;
            font-style: italic;
        }
        .error {
            color: #d9534f;
        }
        .recording {
            color: #d9534f;
            font-weight: bold;
            animation: blink 1s infinite;
        }
        @keyframes blink {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .controls {
            display: flex;
            gap: 10px;
        }
        .audio-player {
            width: 100%;
            margin-top: 10px;
        }
        .toggle-container {
            display: flex;
            margin-bottom: 15px;
            align-items: center;
        }
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
            margin-right: 10px;
        }
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #2196F3;
        }
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        .toggle-label {
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>语音识别测试</h1>
        
        <div class="toggle-container">
            <label class="toggle-switch">
                <input type="checkbox" id="recognitionTypeToggle">
                <span class="slider"></span>
            </label>
            <span class="toggle-label">使用一次性识别模式</span>
        </div>
        
        <div class="form-group">
            <label for="audioFile">选择音频文件：</label>
            <input type="file" id="audioFile" accept="audio/*">
            <div class="audio-player" id="audioPlayerContainer" style="display: none;">
                <audio id="audioPlayer" controls></audio>
            </div>
        </div>
        
        <div class="form-group">
            <div class="controls">
                <button id="recordBtn" class="btn">开始录音</button>
                <button id="stopBtn" class="btn" disabled>停止录音</button>
                <button id="uploadBtn" class="btn">上传并识别</button>
            </div>
            <div id="recordingStatus" class="status"></div>
        </div>
        
        <div class="form-group">
            <label>识别结果：</label>
            <div id="result" class="result"></div>
            <div id="status" class="status"></div>
        </div>
    </div>

    <script>
        // DOM元素
        const audioFileInput = document.getElementById('audioFile');
        const audioPlayer = document.getElementById('audioPlayer');
        const audioPlayerContainer = document.getElementById('audioPlayerContainer');
        const recordBtn = document.getElementById('recordBtn');
        const stopBtn = document.getElementById('stopBtn');
        const uploadBtn = document.getElementById('uploadBtn');
        const resultDiv = document.getElementById('result');
        const statusDiv = document.getElementById('status');
        const recordingStatus = document.getElementById('recordingStatus');
        const recognitionTypeToggle = document.getElementById('recognitionTypeToggle');
        
        // 录音相关变量
        let mediaRecorder;
        let audioChunks = [];
        let recordedBlob;
        
        // 显示选择的音频文件
        audioFileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const url = URL.createObjectURL(file);
                audioPlayer.src = url;
                audioPlayerContainer.style.display = 'block';
            }
        });
        
        // 开始录音
        recordBtn.addEventListener('click', async function() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                
                // 设置录音选项，使用WAV格式
                const options = {
                    mimeType: 'audio/webm',
                    audioBitsPerSecond: 16000
                };
                
                // 尝试创建MediaRecorder，如果不支持指定的MIME类型，则使用默认类型
                try {
                    mediaRecorder = new MediaRecorder(stream, options);
                } catch (e) {
                    console.warn('指定的MIME类型不受支持，使用默认类型', e);
                    mediaRecorder = new MediaRecorder(stream);
                }
                
                mediaRecorder.ondataavailable = function(e) {
                    audioChunks.push(e.data);
                };
                
                mediaRecorder.onstop = function() {
                    // 创建Blob对象
                    recordedBlob = new Blob(audioChunks, { type: mediaRecorder.mimeType });
                    console.log('录音完成，MIME类型:', mediaRecorder.mimeType);
                    
                    const url = URL.createObjectURL(recordedBlob);
                    audioPlayer.src = url;
                    audioPlayerContainer.style.display = 'block';
                    
                    recordingStatus.textContent = '录音已完成，可以上传识别';
                    recordingStatus.classList.remove('recording');
                };
                
                audioChunks = [];
                mediaRecorder.start();
                
                recordBtn.disabled = true;
                stopBtn.disabled = false;
                recordingStatus.textContent = '正在录音...';
                recordingStatus.classList.add('recording');
                
            } catch (err) {
                console.error('录音失败:', err);
                recordingStatus.textContent = '录音失败: ' + err.message;
                recordingStatus.classList.add('error');
            }
        });
        
        // 停止录音
        stopBtn.addEventListener('click', function() {
            if (mediaRecorder && mediaRecorder.state !== 'inactive') {
                mediaRecorder.stop();
                mediaRecorder.stream.getTracks().forEach(track => track.stop());
                
                recordBtn.disabled = false;
                stopBtn.disabled = true;
            }
        });
        
        // 上传并识别
        uploadBtn.addEventListener('click', function() {
            let audioFile;
            
            if (audioFileInput.files.length > 0) {
                audioFile = audioFileInput.files[0];
                console.log('使用选择的文件:', audioFile.name, audioFile.type);
            } else if (recordedBlob) {
                // 使用录制的音频，确保文件扩展名与MIME类型匹配
                let extension = 'webm';
                if (recordedBlob.type.includes('wav')) {
                    extension = 'wav';
                } else if (recordedBlob.type.includes('mp3')) {
                    extension = 'mp3';
                } else if (recordedBlob.type.includes('ogg')) {
                    extension = 'ogg';
                }
                
                audioFile = new File([recordedBlob], `recorded_audio.${extension}`, { type: recordedBlob.type });
                console.log('使用录制的音频:', audioFile.name, audioFile.type);
            } else {
                statusDiv.textContent = '请先选择音频文件或录制音频';
                statusDiv.classList.add('error');
                return;
            }
            
            // 根据选择的识别模式处理音频
            if (recognitionTypeToggle.checked) {
                uploadAudioAll(audioFile);
            } else {
                uploadAudioStream(audioFile);
            }
        });
        
        // 一次性识别音频文件
        function uploadAudioAll(file) {
            resultDiv.textContent = '';
            statusDiv.textContent = '正在识别中...';
            statusDiv.classList.remove('error');
            
            const formData = new FormData();
            formData.append('audio', file);
            
            // 发送音频文件到一次性识别接口
            fetch('/api/asr/recognizeAll', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('上传失败，服务器返回: ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                console.log('识别结果:', data);
                
                if (data.status === 'success') {
                    resultDiv.textContent = data.text;
                    statusDiv.textContent = '识别完成';
                } else {
                    throw new Error(data.message || '识别失败');
                }
            })
            .catch(error => {
                console.error('识别失败:', error);
                statusDiv.textContent = '识别失败: ' + error.message;
                statusDiv.classList.add('error');
            });
        }
        
        // 流式识别音频文件
        function uploadAudioStream(file) {
            resultDiv.textContent = '';
            statusDiv.textContent = '正在识别中...';
            statusDiv.classList.remove('error');
            
            const formData = new FormData();
            formData.append('audio', file);
            
            // 发送音频文件
            fetch('/api/asr/recognize', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('上传失败，服务器返回: ' + response.status);
                }
                return response.text();
            })
            .then(audioId => {
                console.log('获取到音频ID:', audioId);
                
                // 创建SSE连接接收识别结果
                const eventSource = new EventSource(`/api/asr/recognize?audioId=${audioId}`);
                
                // 监听识别结果事件
                eventSource.addEventListener('result', function(e) {
                    const text = e.data;
                    resultDiv.textContent = text;
                });
                
                // 监听连接成功事件
                eventSource.addEventListener('connected', function(e) {
                    statusDiv.textContent = e.data;
                });
                
                // 监听错误事件
                eventSource.addEventListener('error', function(e) {
                    if (e.data) {
                        statusDiv.textContent = e.data;
                    } else {
                        statusDiv.textContent = '识别过程中发生错误';
                    }
                    statusDiv.classList.add('error');
                    eventSource.close();
                });
                
                // 监听完成事件
                eventSource.addEventListener('complete', function(e) {
                    statusDiv.textContent = '识别完成';
                    eventSource.close();
                });
                
                // 监听SSE连接错误
                eventSource.onerror = function(e) {
                    console.error('SSE连接错误:', e);
                    if (eventSource.readyState === EventSource.CLOSED) {
                        statusDiv.textContent = 'SSE连接已关闭';
                    } else {
                        statusDiv.textContent = 'SSE连接错误';
                    }
                    statusDiv.classList.add('error');
                    eventSource.close();
                };
            })
            .catch(error => {
                console.error('上传失败:', error);
                statusDiv.textContent = '上传失败: ' + error.message;
                statusDiv.classList.add('error');
            });
        }
    </script>
</body>
</html> 