package com.makiyo.ai_doctor_prod.utils;

import java.util.HashMap;
import java.util.Map;

/**
 * 用于处理诊断和治疗相关内容的工具类。
 */
public class TreatmentUtils {

    private static final String REASONING_SEPARATOR = "推理过程：";

    /**
     * 解析包含治疗方案和推理过程的字符串。
     * 
     * @param rawRecommendation 从Python服务收到的原始 treatment_recommendation 字符串。
     * @return 一个Map，包含两个键：
     *         "treatment" -> 清理后的治疗方案部分。
     *         "reasoning" -> 分离出的推理过程部分。
     */
    public static Map<String, String> parseTreatmentAndReasoning(String rawRecommendation) {
        Map<String, String> result = new HashMap<>();
        
        if (rawRecommendation == null || rawRecommendation.trim().isEmpty()) {
            result.put("treatment", "");
            result.put("reasoning", "");
            return result;
        }

        int separatorIndex = rawRecommendation.indexOf(REASONING_SEPARATOR);

        if (separatorIndex != -1) {
            // 分隔符存在
            String treatmentPart = rawRecommendation.substring(0, separatorIndex).trim();
            String reasoningPart = rawRecommendation.substring(separatorIndex + REASONING_SEPARATOR.length()).trim();
            result.put("treatment", treatmentPart);
            result.put("reasoning", reasoningPart);
        } else {
            // 不包含推理过程，整个字符串都是治疗方案
            result.put("treatment", rawRecommendation.trim());
            result.put("reasoning", "");
        }

        return result;
    }
} 