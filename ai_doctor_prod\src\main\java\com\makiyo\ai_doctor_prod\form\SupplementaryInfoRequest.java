package com.makiyo.ai_doctor_prod.form;


import io.swagger.v3.oas.annotations.media.Schema;


import javax.validation.constraints.NotBlank;

// public class SupplementaryInfoRequest { // if in a separate file
// @Schema(description = "添加医生补充信息请求体") // if in a separate file
public class SupplementaryInfoRequest { // if as inner class

    @Schema(description = "CaseItem 的唯一标识符", required = true, example = "case_uuid_123")
    @NotBlank(message = "ID 不能为空")
    private String id;

    @Schema(description = "医生补充信息", required = true, example = "患者既往有糖尿病史")
    @NotBlank(message = "补充信息不能为空")
    private String supplementaryInfo;

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSupplementaryInfo() {
        return supplementaryInfo;
    }

    public void setSupplementaryInfo(String supplementaryInfo) {
        this.supplementaryInfo = supplementaryInfo;
    }
}