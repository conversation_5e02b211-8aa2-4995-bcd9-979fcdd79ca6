package com.makiyo.ai_doctor_prod.repository;

import com.makiyo.ai_doctor_prod.entity.CaseBaseInfo;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface CaseBaseInfoRepository extends MongoRepository<CaseBaseInfo, String> {
    Optional<CaseBaseInfo> findByCaseId(String caseId);
} 