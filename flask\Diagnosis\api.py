import logging
from Doctor import Doctor
from prompts import _sys_prompt_doctor
from DiagnosisAgent import Diagnosis<PERSON><PERSON>, process_json
from Consultation import convert_history, doctor_turn, process_xlsx_to_json


# 配置日志记录
logging.basicConfig(
    level=logging.INFO,  # 设置日志级别
    format="%(asctime)s - %(levelname)s - %(message)s",  # 日志格式
    handlers=[
        logging.StreamHandler(),  # 输出到控制台
        logging.FileHandler("main.log", mode="a", encoding="utf-8")  # 输出到文件
    ]
)

logger = logging.getLogger(__name__)




# 问诊接口

def consultation_doctor_reoponse(history_doc):
    sysprompt_doctor = _sys_prompt_doctor
    history_pat = convert_history(history_doc)
    initial_message = "请您开始问诊"
    round_num = len(history_doc) // 2 + 1
    model_v3 = "deepseek-v3-241226"
    logger.info(f"history_pat: {history_pat}")
    logger.info(f"round_num: {round_num}")

    response_doc, solution_text = doctor_turn(
        sysprompt_doctor, history_doc, history_pat, initial_message, model_v3, round_num
    )

    logger.info(f"response_doc: {response_doc}")
    logger.info(f"solution_text: {solution_text}")

    return response_doc, solution_text



# def main():
#     xlsx_file = r"..\data\检查和药品0224_测试.xlsx"
#
#     # 输入文件路径，读取数据， 输出json格式的问诊历史
#     historys_json = process_xlsx_to_json(xlsx_file)
#
#     # 输入json格式的问诊历史，输出诊断结果和治疗方案，保存到XLSX文件,
#     # 默认为"../data/json_r1_full_new_test_json.xlsx"
#     process_json(historys_json)

