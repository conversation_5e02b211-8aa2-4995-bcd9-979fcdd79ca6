package com.makiyo.aidoctor.controller;

import com.makiyo.aidoctor.entity.ImageMetadata;
import com.makiyo.aidoctor.service.ImageService;
import com.mongodb.client.gridfs.model.GridFSFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.mongodb.gridfs.GridFsResource;
import org.springframework.data.mongodb.gridfs.GridFsOperations;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/images")
public class ImageController {
    private static final Logger log = LoggerFactory.getLogger(ImageController.class);

    @Autowired
    private ImageService imageService;

    @Autowired
    private GridFsOperations gridFsOperations;

    /**
     * 根据图片ID获取图片
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getImage(@PathVariable String id) {
        try {
            GridFSFile file = imageService.getImageById(id);
            if (file == null) {
                return ResponseEntity.notFound().build();
            }

            // 获取文件元数据
            String contentType = file.getMetadata().get("contentType", String.class);
            String filename = file.getFilename();

            GridFsResource resource = gridFsOperations.getResource(file);

            // 设置HTTP头
            HttpHeaders headers = new HttpHeaders();
            if (contentType != null) {
                headers.setContentType(MediaType.parseMediaType(contentType));
            } else {
                headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            }

            headers.setContentDispositionFormData("attachment", filename);
            headers.setCacheControl("max-age=86400"); // 缓存1天

            return ResponseEntity
                    .ok()
                    .headers(headers)
                    .contentLength(file.getLength())
                    .body(new InputStreamResource(resource.getInputStream()));

        } catch (Exception e) {
            log.error("获取图片失败: {}", e.getMessage(), e);
            return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "获取图片失败: " + e.getMessage()));
        }
    }

    /**
     * 获取会话的所有图片元数据
     */
    @GetMapping("/session/{sessionId}")
    public ResponseEntity<List<ImageMetadata>> getSessionImages(
            @PathVariable Integer sessionId,
            @RequestParam(required = false) String category) {
        try {
            List<ImageMetadata> images;
            if (category != null && !category.isEmpty()) {
                images = imageService.getImagesBySessionIdAndCategory(sessionId, category);
                log.info("获取会话ID: {}的{}类别图片，共{}张", sessionId, category, images.size());
            } else {
                images = imageService.getImagesBySessionId(sessionId);
                log.info("获取会话ID: {}的所有图片，共{}张", sessionId, images.size());
            }
            return ResponseEntity.ok(images);
        } catch (Exception e) {
            log.error("获取会话图片失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 上传图片（通用接口）
     */
    @PostMapping("/upload")
    public ResponseEntity<?> uploadImage(
            @RequestParam("image") MultipartFile image,
            @RequestParam("sessionId") Integer sessionId,
            @RequestParam(value = "category", defaultValue = "general") String category) {
        try {
            if (image.isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of("error", "请选择要上传的图片"));
            }

            ImageMetadata metadata = imageService.storeImage(image, sessionId, category);
            log.info("图片上传成功: ID={}, 会话ID={}, 类别={}", metadata.getGridFsId(), sessionId, category);

            Map<String, Object> response = new HashMap<>();
            response.put("message", "图片上传成功");
            response.put("imageId", metadata.getGridFsId());
            response.put("metadata", metadata);

            return ResponseEntity.ok(response);
        } catch (IOException e) {
            log.error("上传图片失败: {}", e.getMessage(), e);
            return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "上传图片失败: " + e.getMessage()));
        }
    }

    /**
     * 删除图片
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteImage(@PathVariable String id) {
        try {
            imageService.deleteImage(id);
            return ResponseEntity.ok(Map.of("message", "图片删除成功"));
        } catch (Exception e) {
            log.error("删除图片失败: {}", e.getMessage(), e);
            return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "删除图片失败: " + e.getMessage()));
        }
    }
}