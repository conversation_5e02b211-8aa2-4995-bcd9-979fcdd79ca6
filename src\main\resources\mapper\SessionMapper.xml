<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.makiyo.aidoctor.mapper.SessionMapper">
  <resultMap id="BaseResultMap" type="com.makiyo.aidoctor.entity.Session">
    <id column="id" jdbcType="INTEGER" property="id"/>
    <result column="user_id" jdbcType="INTEGER" property="userId"/>
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
  </resultMap>

  <sql id="Base_Column_List">
    id, user_id, created_at, updated_at
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from session
    where id = #{id,jdbcType=INTEGER}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from session
    where id = #{id,jdbcType=INTEGER}
  </delete>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.makiyo.aidoctor.entity.Session" useGeneratedKeys="true">
    insert into session (user_id)
    values (#{userId,jdbcType=INTEGER})
  </insert>

  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.makiyo.aidoctor.entity.Session" useGeneratedKeys="true">
    insert into session
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.makiyo.aidoctor.entity.Session">
    update session
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.makiyo.aidoctor.entity.Session">
    update session
    set user_id = #{userId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectByUserId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    SELECT 
    <include refid="Base_Column_List" />
    FROM session
    WHERE user_id = #{userId,jdbcType=INTEGER}
    ORDER BY created_at DESC
  </select>
  
  <select id="selectAllSessions" resultMap="BaseResultMap">
    SELECT 
    <include refid="Base_Column_List" />
    FROM session
    ORDER BY created_at DESC
  </select>

  <select id="selectSessionsWithPagination" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM session
    ORDER BY created_at DESC
    LIMIT #{size} OFFSET #{offset}
  </select>

  <select id="countTotalSessions" resultType="java.lang.Long">
    SELECT COUNT(*) FROM session
  </select>
</mapper>