package com.makiyo.aidoctor.mapper;

import com.makiyo.aidoctor.entity.Session;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SessionMapper {
    /**
     * 根据主键删除会话
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 插入会话记录
     */
    int insert(Session record);

    /**
     * 选择性插入会话记录
     */
    int insertSelective(Session record);

    /**
     * 根据主键查询会话
     */
    Session selectByPrimaryKey(Integer id);

    /**
     * 根据主键选择性更新会话
     */
    int updateByPrimaryKeySelective(Session record);

    /**
     * 根据主键更新会话
     */
    int updateByPrimaryKey(Session record);

    /**
     * 根据用户ID查询会话列表
     * @param userId 用户ID
     * @return 会话列表
     */
    List<Session> selectByUserId(Integer userId);

    /**
     * 查询所有会话列表
     * @return 所有会话列表
     */
    List<Session> selectAllSessions();

    List<Session> selectSessionsWithPagination(@Param("offset") int offset, @Param("size") int size);

    long countTotalSessions();
}