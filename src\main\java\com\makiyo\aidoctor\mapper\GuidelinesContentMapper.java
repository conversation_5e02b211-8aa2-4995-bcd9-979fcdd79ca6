package com.makiyo.aidoctor.mapper;

import com.makiyo.aidoctor.entity.GuidelinesContent;
import org.springframework.stereotype.Repository;

/**
 * 指南内容Mapper接口
 */
@Repository
public interface GuidelinesContentMapper {

    /**
     * 保存指南内容
     *
     * @param guidelinesContent 指南内容实体
     * @return 影响的行数
     */
    String insertContent(GuidelinesContent guidelinesContent);

    /**
     * 根据ID查询指南内容
     *
     * @param id 指南内容ID
     * @return 指南内容
     */
    GuidelinesContent selectById(String id);

    /**
     * 根据会话ID查询指南内容
     *
     * @param sessionId 会话ID
     * @return 指南内容
     */
    GuidelinesContent selectBySessionId(Integer sessionId);

    /**
     * 更新指南内容
     *
     * @param guidelinesContent 指南内容
     * @return 影响的行数
     */
    int updateContent(GuidelinesContent guidelinesContent);

    /**
     * 根据ID删除指南内容
     *
     * @param id 指南内容ID
     * @return 影响的行数
     */
    int deleteById(String id);

    /**
     * 根据会话ID删除指南内容
     *
     * @param sessionId 会话ID
     * @return 影响的行数
     */
    int deleteBySessionId(Integer sessionId);
}