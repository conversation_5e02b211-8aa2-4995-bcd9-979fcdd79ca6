package com.makiyo.aidoctor.controller;

import com.makiyo.aidoctor.entity.Message;
import com.makiyo.aidoctor.entity.Session;
import com.makiyo.aidoctor.response.Response;
import com.makiyo.aidoctor.response.SessionWithMessagesDTO;
import com.makiyo.aidoctor.service.MessageService;
import com.makiyo.aidoctor.service.SessionService;
import org.springframework.web.bind.annotation.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.util.List;
import com.makiyo.aidoctor.response.PageInfo;

@Tag(name = "会话管理", description = "诊疗会话相关的 API 接口")
@RestController
@RequestMapping("/session")
public class SessionController {

    @Resource
    private SessionService sessionService;

    @Resource
    private MessageService messageService;

    @Operation(summary = "创建会话 (V1)", description = "创建新的诊疗会话并添加 V1 欢迎消息")
    @PostMapping("/addSession")
    public Response<Session> addSession(
            @Parameter(description = "用户ID", required = true, example = "1") 
            @RequestParam @NotNull(message = "用户ID不能为空") @Positive(message = "用户ID必须为正整数") Integer userId) {
        try {
            Session createdSession = sessionService.createSession(userId);
            if (createdSession == null) {
                return Response.fail("创建会话失败");
            }
            
            // V1 Welcome Message
            Message welcomeMessage = new Message();
            welcomeMessage.setSessionId(createdSession.getId());
            welcomeMessage.setSpeaker("doctor");
            welcomeMessage.setMessage("您好，请问孩子的性别和年龄？");
            messageService.createMessage(welcomeMessage);
            
            return Response.ok(createdSession);
        } catch (Exception e) {
            return Response.fail("创建会话失败 (V1)：" + e.getMessage());
        }
    }

    @Operation(summary = "创建会话 (V2)", description = "创建新的诊疗会话并添加 V2 初始 JSON 消息")
    @PostMapping("/addSessionV2")
    public Response<Session> addSessionV2(
            @Parameter(description = "用户ID", required = true, example = "1") 
            @RequestParam @NotNull(message = "用户ID不能为空") @Positive(message = "用户ID必须为正整数") Integer userId) {
        try {
            Session createdSession = sessionService.createSession(userId);
            if (createdSession == null) {
                return Response.fail("创建 V2 会话失败：无法创建 Session 记录");
            }

            // Define the updated initial V2 JSON structure
            String initialV2Json = "{\"interaction_history\":{\"cot_entries\":[{\"dialogue_history\":[{\"content\":\"您好，我是本次的医生，请问孩子的年龄，性别，主要的症状是什么？\",\"role\":\"doctor\"}],\"feedback\":\"\",\"observation\":\"\",\"reasoning\":\"主诉是问诊的切入点，通过了解患者的基本信息和主要症状，可以快速建立初步印象。进一步询问发病时间、起病情况、症状特点、伴随症状、病情演变和诊疗经过，有助于全面了解现病史，为后续诊断提供关键信息。这种系统性的问诊策略能够帮助我们准确把握患者的病情，避免遗漏重要信息。\",\"strategy\":\"询问患者的基本信息（性别、年龄）和主诉症状，了解发病时间、起病情况、症状特点（性质、程度、持续时间、频率）、伴随症状、病情演变以及之前的诊疗经过。\"}],\"preliminary_diagnosis\":null,\"test_recommendation\":[],\"diagnosis\":\"\",\"treatment_recommendation\":[],\"doctor_supplementary_info\":[]}}";

            // Create the initial message with the V2 JSON
            Message initialMessage = new Message();
            initialMessage.setSessionId(createdSession.getId());
            initialMessage.setSpeaker("doctor");
            initialMessage.setMessage(initialV2Json);
            
            Message createdMsg = messageService.createMessage(initialMessage);
            if (createdMsg == null) {
                 // Optional: Consider if the session should be deleted if message creation fails
                 System.err.println("创建 V2 初始消息失败 for session ID: " + createdSession.getId());
                 return Response.fail("创建 V2 会话失败：无法保存初始消息状态");
            }
            
            System.out.println("成功创建 V2 会话及初始消息, Session ID: " + createdSession.getId());
            
            return Response.ok(createdSession);
        } catch (Exception e) {
            System.err.println("创建 V2 会话异常: " + e.getMessage());
            e.printStackTrace();
            return Response.fail("创建 V2 会话失败：" + e.getMessage());
        }
    }

    @Operation(summary = "获取会话详情", description = "根据会话ID获取会话详细信息")
    @GetMapping("/{id}")
    public Response<Session> getSession(@PathVariable Integer id) {
        try {
            Session session = sessionService.getSession(id);
            if (session == null) {
                return Response.fail("会话不存在");
            }
            return Response.ok(session);
        } catch (Exception e) {
            return Response.fail("获取会话失败：" + e.getMessage());
        }
    }

    @Operation(summary = "获取用户会话列表", description = "获取指定用户的所有诊疗会话记录")
    @GetMapping("/getUserSessions")
    public Response<List<Session>> getUserSessions(@RequestParam Integer userId) {
        try {
            List<Session> sessions = sessionService.getUserSessions(userId);
            return Response.ok(sessions);
        } catch (Exception e) {
            return Response.fail("获取用户会话列表失败：" + e.getMessage());
        }
    }
    
    @Operation(summary = "分页获取所有会话及其消息", description = "分页获取所有会话，消息仅在需要时单独加载")
    @GetMapping("/getAllSessionsWithMessages")
    public Response<PageInfo<SessionWithMessagesDTO>> getAllSessionsWithMessages(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size
    ) {
        try {
            PageInfo<SessionWithMessagesDTO> sessionPage = sessionService.getAllSessionsWithPagination(page, size);
            return Response.ok(sessionPage);
        } catch (Exception e) {
            System.err.println("获取所有会话及消息失败: " + e.getMessage());
            e.printStackTrace();
            return Response.fail("获取所有会话及消息失败：" + e.getMessage());
        }
    }

    @Operation(summary = "获取单个会话的所有消息", description = "根据会话ID获取其包含的所有消息记录")
    @GetMapping("/{id}/messages")
    public Response<List<Message>> getSessionMessages(
            @Parameter(description = "会话ID", required = true) @PathVariable("id") Integer id
    ) {
        try {
            List<Message> messages = messageService.getSessionMessages(id);
            return Response.ok(messages);
        } catch (Exception e) {
            System.err.println("获取会话消息失败 for session ID " + id + ": " + e.getMessage());
            e.printStackTrace();
            return Response.fail("获取会话消息失败：" + e.getMessage());
        }
    }
}
