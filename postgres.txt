--
-- PostgreSQL database dump
--

-- Dumped from database version 13.11 (Debian 13.11-1.pgdg110+1)
-- Dumped by pg_dump version 17.0

-- Started on 2025-04-24 15:48:46 CST

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- TOC entry 10 (class 2615 OID 2200)
-- Name: public; Type: SCHEMA; Schema: -; Owner: postgres
--

-- *not* creating schema, since initdb creates it


ALTER SCHEMA public OWNER TO postgres;

--
-- TOC entry 7 (class 2615 OID 822283)
-- Name: tiger; Type: SCHEMA; Schema: -; Owner: postgres
--

CREATE SCHEMA tiger;


ALTER SCHEMA tiger OWNER TO postgres;

--
-- TOC entry 8 (class 2615 OID 822284)
-- Name: tiger_data; Type: SCHEMA; Schema: -; Owner: postgres
--

CREATE SCHEMA tiger_data;


ALTER SCHEMA tiger_data OWNER TO postgres;

--
-- TOC entry 9 (class 2615 OID 822285)
-- Name: topology; Type: SCHEMA; Schema: -; Owner: postgres
--

CREATE SCHEMA topology;


ALTER SCHEMA topology OWNER TO postgres;

--
-- TOC entry 3304 (class 0 OID 0)
-- Dependencies: 9
-- Name: SCHEMA topology; Type: COMMENT; Schema: -; Owner: postgres
--

COMMENT ON SCHEMA topology IS 'PostGIS Topology schema';


--
-- TOC entry 2 (class 3079 OID 822286)
-- Name: fuzzystrmatch; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS fuzzystrmatch WITH SCHEMA public;


--
-- TOC entry 3305 (class 0 OID 0)
-- Dependencies: 2
-- Name: EXTENSION fuzzystrmatch; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION fuzzystrmatch IS 'determine similarities and distance between strings';


--
-- TOC entry 3 (class 3079 OID 822297)
-- Name: ltree; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS ltree WITH SCHEMA public;


--
-- TOC entry 3306 (class 0 OID 0)
-- Dependencies: 3
-- Name: EXTENSION ltree; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION ltree IS 'data type for hierarchical tree-like structures';


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- TOC entry 205 (class 1259 OID 822482)
-- Name: access_feature; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.access_feature (
    id character varying(32) NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    deleted boolean DEFAULT false,
    valid boolean DEFAULT true,
    profile jsonb DEFAULT '{}'::jsonb,
    path public.ltree,
    index integer,
    logs jsonb DEFAULT '{}'::jsonb,
    deleted_at character varying(32) DEFAULT ''::text
);


ALTER TABLE public.access_feature OWNER TO postgres;

--
-- TOC entry 206 (class 1259 OID 822494)
-- Name: access_role; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.access_role (
    id character varying(32) NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    deleted boolean DEFAULT false,
    profile jsonb DEFAULT '{}'::jsonb
);


ALTER TABLE public.access_role OWNER TO postgres;

--
-- TOC entry 207 (class 1259 OID 822503)
-- Name: captcha__id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.captcha__id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.captcha__id_seq OWNER TO postgres;

--
-- TOC entry 222 (class 1259 OID 823090)
-- Name: case_item; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.case_item (
    id character varying(32) NOT NULL,
    created_at timestamp(6) with time zone DEFAULT now(),
    updated_at timestamp(6) with time zone DEFAULT now(),
    profile jsonb DEFAULT '{}'::jsonb
);


ALTER TABLE public.case_item OWNER TO postgres;

--
-- TOC entry 224 (class 1259 OID 823125)
-- Name: case_message; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.case_message (
    id character varying(32) NOT NULL,
    created_at timestamp(6) with time zone DEFAULT now(),
    updated_at timestamp(6) with time zone DEFAULT now(),
    profile jsonb DEFAULT '{}'::jsonb
);


ALTER TABLE public.case_message OWNER TO postgres;

--
-- TOC entry 208 (class 1259 OID 822505)
-- Name: category; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.category (
    id character varying(32) NOT NULL,
    created_at timestamp(6) with time zone DEFAULT now(),
    deleted boolean DEFAULT false,
    deleted_at character varying(32) DEFAULT ''::text,
    profile jsonb DEFAULT '{}'::jsonb,
    path public.ltree,
    index integer DEFAULT 0,
    valid boolean DEFAULT true,
    logs jsonb DEFAULT '{}'::jsonb,
    type character varying(128),
    clasz public.ltree
);


ALTER TABLE public.category OWNER TO postgres;

--
-- TOC entry 209 (class 1259 OID 822527)
-- Name: error_validation__id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.error_validation__id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.error_validation__id_seq OWNER TO postgres;

--
-- TOC entry 210 (class 1259 OID 822573)
-- Name: label; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.label (
    id character varying(32) NOT NULL,
    created_at timestamp(6) with time zone DEFAULT now(),
    deleted boolean DEFAULT false,
    deleted_at character varying(32) DEFAULT ''::text,
    valid boolean DEFAULT true,
    profile jsonb DEFAULT '{}'::jsonb,
    category_id character varying(32),
    logs jsonb DEFAULT '{}'::jsonb
);


ALTER TABLE public.label OWNER TO postgres;

--
-- TOC entry 211 (class 1259 OID 822585)
-- Name: log__id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.log__id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.log__id_seq OWNER TO postgres;

--
-- TOC entry 223 (class 1259 OID 823114)
-- Name: member; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.member (
    id character varying(32) NOT NULL,
    created_at timestamp(6) with time zone DEFAULT now(),
    updated_at timestamp(6) with time zone DEFAULT now(),
    profile jsonb DEFAULT '{}'::jsonb
);


ALTER TABLE public.member OWNER TO postgres;

--
-- TOC entry 212 (class 1259 OID 822587)
-- Name: no_trouble_log_item; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.no_trouble_log_item (
    id character varying(32) NOT NULL,
    created_at timestamp(6) with time zone DEFAULT now(),
    updated_at timestamp(6) with time zone DEFAULT now(),
    profile jsonb DEFAULT '{}'::jsonb
);


ALTER TABLE public.no_trouble_log_item OWNER TO postgres;

--
-- TOC entry 213 (class 1259 OID 822596)
-- Name: no_trouble_trash_item; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.no_trouble_trash_item (
    id character varying(32) NOT NULL,
    created_at timestamp(6) with time zone DEFAULT now(),
    updated_at timestamp(6) with time zone DEFAULT now(),
    profile jsonb DEFAULT '{}'::jsonb,
    logs jsonb DEFAULT '{}'::jsonb
);


ALTER TABLE public.no_trouble_trash_item OWNER TO postgres;

--
-- TOC entry 214 (class 1259 OID 822606)
-- Name: oss_object; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.oss_object (
    id character varying(32) NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    deleted boolean DEFAULT false,
    profile jsonb DEFAULT '{}'::jsonb,
    hash character varying(256)
);


ALTER TABLE public.oss_object OWNER TO postgres;

--
-- TOC entry 215 (class 1259 OID 822624)
-- Name: settings; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.settings (
    id character varying(64) NOT NULL,
    created_at timestamp(6) with time zone DEFAULT now(),
    private boolean DEFAULT false,
    deleted boolean DEFAULT false,
    profile jsonb DEFAULT '{}'::json,
    content text,
    deleted_at character varying(32) DEFAULT ''::text,
    logs jsonb DEFAULT '{}'::jsonb,
    category_id character varying(32)
);


ALTER TABLE public.settings OWNER TO postgres;

--
-- TOC entry 216 (class 1259 OID 822636)
-- Name: user_account; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_account (
    id character varying(32) NOT NULL,
    created_at timestamp(6) with time zone DEFAULT now() NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT now() NOT NULL,
    nickname character varying(255),
    phone character varying(255),
    avatar text,
    profile jsonb DEFAULT '{}'::json,
    deleted boolean DEFAULT false,
    email text,
    valid boolean DEFAULT true NOT NULL,
    device jsonb DEFAULT '{}'::jsonb,
    role jsonb DEFAULT '{}'::jsonb,
    real_data jsonb DEFAULT '{}'::jsonb,
    pwd jsonb DEFAULT '{}'::jsonb,
    "from" jsonb,
    support_ability jsonb DEFAULT '{}'::jsonb,
    deleted_at character varying(32) DEFAULT ''::text NOT NULL,
    phone_country_calling_code character varying(10) DEFAULT '86'::character varying,
    logs jsonb DEFAULT '{}'::jsonb,
    CONSTRAINT user_email_check CHECK ((email ~ '^\w+@[-\w]+\.\w+$'::text)),
    CONSTRAINT user_phone_check CHECK (((phone)::text ~ '^[0-9-]{7,}$'::text))
);


ALTER TABLE public.user_account OWNER TO postgres;

--
-- TOC entry 3307 (class 0 OID 0)
-- Dependencies: 216
-- Name: COLUMN user_account.support_ability; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.user_account.support_ability IS '权限范围';


--
-- TOC entry 217 (class 1259 OID 822657)
-- Name: user_captcha; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_captcha (
    _id integer DEFAULT nextval('public.captcha__id_seq'::regclass) NOT NULL,
    created_at timestamp(6) with time zone DEFAULT now(),
    expired_at timestamp(6) with time zone,
    code text,
    checked boolean DEFAULT false,
    checked_at timestamp(6) with time zone
);


ALTER TABLE public.user_captcha OWNER TO postgres;

--
-- TOC entry 218 (class 1259 OID 822666)
-- Name: user_error_validation; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_error_validation (
    _id integer DEFAULT nextval('public.error_validation__id_seq'::regclass) NOT NULL,
    created_at timestamp(6) with time zone DEFAULT now(),
    ip character varying(256),
    "user" integer,
    api text,
    data text,
    deleted boolean DEFAULT false
);


ALTER TABLE public.user_error_validation OWNER TO postgres;

--
-- TOC entry 219 (class 1259 OID 822675)
-- Name: user_message; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_message (
    id character varying(32) NOT NULL,
    created_at timestamp(6) with time zone DEFAULT now(),
    updated_at timestamp(6) with time zone DEFAULT now(),
    profile jsonb DEFAULT '{}'::jsonb
);


ALTER TABLE public.user_message OWNER TO postgres;

--
-- TOC entry 220 (class 1259 OID 822684)
-- Name: validate_code__id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.validate_code__id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.validate_code__id_seq OWNER TO postgres;

--
-- TOC entry 221 (class 1259 OID 822686)
-- Name: user_validate_code; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_validate_code (
    _id integer DEFAULT nextval('public.validate_code__id_seq'::regclass) NOT NULL,
    dest character varying(128),
    code character varying(32),
    created_at timestamp(6) with time zone,
    api character varying(64),
    valid boolean DEFAULT true,
    checked_times integer DEFAULT 0
);


ALTER TABLE public.user_validate_code OWNER TO postgres;

--
-- TOC entry 3278 (class 0 OID 822482)
-- Dependencies: 205
-- Data for Name: access_feature; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.access_feature VALUES ('498_I73gh6', '2020-07-20 14:06:47.459761+08', false, true, '{"api": [{"R": "* /support/system/settings/*"}], "name": "系统配置", "api_def": "# 接口备注\n[[api]]\nR = \"* /support/system/settings/*\""}', 'XN0BISn1lO.498_I73gh6', 0, '{"logs": [{"date": "2022-08-11T08:33:08.902Z", "action": "更新", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}]}', '');
INSERT INTO public.access_feature VALUES ('R5v2v2_0G8', '2020-07-20 14:07:09.980584+08', false, true, '{"api": [{"R": "* /support/system/access-control/*"}], "name": "角色权限", "api_def": "# 接口备注\n[[api]]\nR = \"* /support/system/access-control/*\""}', 'XN0BISn1lO.R5v2v2_0G8', 1, '{"logs": [{"date": "2022-08-11T08:33:08.946Z", "action": "更新", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}]}', '');
INSERT INTO public.access_feature VALUES ('uVFBmLAXIQAD6_k2sMab1', '2022-08-19 10:44:19.475687+08', false, true, '{"api": [{"R": "* /support/label/*"}], "name": "标签管理", "api_def": "# 接口备注\n[[api]]\nR = \"* /support/label/*\""}', 'XN0BISn1lO.uVFBmLAXIQAD6_k2sMab1', 2, '{"logs": [{"date": "2022-08-19T02:44:19.330Z", "action": "添加", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}]}', '');
INSERT INTO public.access_feature VALUES ('XN0BISn1lO', '2020-07-20 14:06:29.481457+08', false, true, '{"api": [], "name": "系统配置", "api_def": "# 接口备注\n# [[api]]\n# R = \"GET /support/xxxxx/\""}', 'XN0BISn1lO', 1, '{"logs": [{"date": "2022-08-11T08:36:12.380Z", "action": "更新", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}]}', '');
INSERT INTO public.access_feature VALUES ('hpdgBleP2s', '2020-07-20 14:05:58.319744+08', false, true, '{"api": [{"R": "* /support/user/*"}], "name": "账号管理", "api_def": "# 接口备注\n[[api]]\nR = \"* /support/user/*\"\n\n"}', 'VLFNkpToUkaLbpqUisEJm.hpdgBleP2s', 0, '{"logs": [{"date": "2022-08-11T08:36:12.509Z", "action": "更新", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}]}', '');
INSERT INTO public.access_feature VALUES ('O_KyGJymdJKcMLAM9xpgd', '2022-08-23 10:25:39.818606+08', false, true, '{"api": [{"R": "* /support/category/*"}], "name": "类目管理", "api_def": "# 接口备注\n [[api]]\n R = \"* /support/category/*\""}', 'XN0BISn1lO.O_KyGJymdJKcMLAM9xpgd', 3, '{"logs": [{"date": "2022-08-23T02:25:39.707Z", "action": "添加", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}, {"date": "2022-08-23T03:48:08.708Z", "action": "更新", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}, {"date": "2023-08-31T08:43:03.282Z", "action": "更新", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}]}', '');
INSERT INTO public.access_feature VALUES ('hznDlepCSl4BIs5nUyouU', '2024-04-16 14:13:17.889868+08', false, true, '{"api": [{"R": "* /support/trash_item/*"}], "name": "回收站", "api_def": "# 接口备注\n [[api]]\n R = \"* /support/trash_item/*\""}', 'XN0BISn1lO.hznDlepCSl4BIs5nUyouU', 4, '{"logs": [{"date": "2024-04-16T06:13:17.890Z", "action": "添加", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}]}', '');
INSERT INTO public.access_feature VALUES ('VLFNkpToUkaLbpqUisEJm', '2023-09-01 10:53:56.126249+08', false, true, '{"api": [], "name": "用户管理", "api_def": "# 接口备注\n# [[api]]\n# R = \"GET /support/xxxxx/\""}', 'VLFNkpToUkaLbpqUisEJm', 2, '{"logs": [{"date": "2023-09-01T02:53:56.010Z", "action": "添加", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}]}', '');
INSERT INTO public.access_feature VALUES ('MiHRSrFkDhWEBfxldyA7N', '2024-10-15 15:48:56.997601+08', false, true, '{"api": [{"R": "GET /work/manage/member/*"}, {"R": "GET /work/manage/user/*"}, {"R": "GET /work/manage/access_role/*"}, {"R": "* /work/tool/*"}], "name": "基础功能", "api_def": "# 查看成员 \n[[api]]\nR = \"GET /work/manage/member/*\"\n\n[[api]]\nR = \"GET /work/manage/user/*\"\n\n[[api]]\nR = \"GET /work/manage/access_role/*\"\n\n[[api]]\nR = \"* /work/tool/*\""}', '5hSJ9ZoFZi8JJRJ0oUHOz.MiHRSrFkDhWEBfxldyA7N', 0, '{"logs": [{"date": "2024-10-15T07:48:56.999Z", "action": "添加", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}, {"date": "2024-10-21T08:44:15.071Z", "action": "更新", "operator": {"id": "bgPEw4MBHM", "nickname": "cp"}}, {"date": "2024-10-29T00:56:27.696Z", "action": "更新", "operator": {"id": "bgPEw4MBHM", "nickname": "cp"}}, {"date": "2024-10-29T00:56:57.776Z", "action": "更新", "operator": {"id": "bgPEw4MBHM", "nickname": "cp"}}]}', '');
INSERT INTO public.access_feature VALUES ('QyjncasOAea5CBgOxoeVC', '2024-11-25 16:57:58.957082+08', false, true, '{"api": [{"R": "* /support/device/*"}], "name": "设备", "api_def": "# 接口备注\n[[api]]\nR = \"* /support/device/*\""}', 'QyjncasOAea5CBgOxoeVC', 5, '{"logs": [{"date": "2024-11-25T08:57:58.861Z", "action": "添加", "operator": {"id": "YICXLp0eGQ7TwsekyKm3-", "nickname": "未乘车没"}}]}', '');
INSERT INTO public.access_feature VALUES ('5hSJ9ZoFZi8JJRJ0oUHOz', '2024-10-12 09:33:35.872737+08', false, true, '{"api": [], "name": "医院端", "api_def": "# 接口备注\n# [[api]]\n# R = \"GET /work/work_shop/\""}', '5hSJ9ZoFZi8JJRJ0oUHOz', 6, '{"logs": [{"date": "2024-10-12T01:33:35.874Z", "action": "添加", "operator": {"id": "bgPEw4MBHM", "nickname": "cp"}}, {"date": "2024-10-12T01:34:51.935Z", "action": "更新", "operator": {"id": "bgPEw4MBHM", "nickname": "cp"}}]}', '');
INSERT INTO public.access_feature VALUES ('wECZyZ2ucEAc10qJfxwbW', '2025-03-10 13:25:55.725713+08', false, true, '{"api": [{"R": "* /support/patient_test/*"}], "name": "测评", "api_def": "# 接口备注\n[[api]]\nR = \"* /support/patient_test/*\"\n"}', 'fL5pyZb1YbYiZzwD1TJIQ.wECZyZ2ucEAc10qJfxwbW', 1, '{}', '');
INSERT INTO public.access_feature VALUES ('yGWay4PwcVfrQDrDYaU2r', '2025-03-11 16:28:25.991599+08', false, true, '{"api": [{"R": "GET /support/label/*"}, {"R": "GET /support/category/*"}, {"R": "GET /support/system/access-control/*"}], "name": "基本信息", "api_def": "# 接口备注\n[[api]]\nR = \"GET /support/label/*\"\n\n[[api]]\nR = \"GET /support/category/*\"\n\n[[api]]\nR = \"GET /support/system/access-control/*\""}', 'yGWay4PwcVfrQDrDYaU2r', 0, '{}', '');
INSERT INTO public.access_feature VALUES ('gDPV1rCfZvJAi_xNt1fw4', '2025-02-06 00:26:55.749192+08', false, true, '{"api": [{"R": "* /work/patient_test/*"}], "name": "测评", "api_def": "# 接口备注\n[[api]]\nR = \"* /work/patient_test/*\""}', '5hSJ9ZoFZi8JJRJ0oUHOz.gDPV1rCfZvJAi_xNt1fw4', 1, '{}', '');
INSERT INTO public.access_feature VALUES ('2EVL9YxSiXvYX6ZQLYTub', '2025-02-10 17:52:14.913548+08', false, true, '{"api": [{"R": "* /work/patient/*"}], "name": "患者管理", "api_def": "# 接口备注\n[[api]]\nR = \"* /work/patient/*\""}', '5hSJ9ZoFZi8JJRJ0oUHOz.2EVL9YxSiXvYX6ZQLYTub', 2, '{}', '');
INSERT INTO public.access_feature VALUES ('kOEe9EilTLsJ5FnXLJK7y', '2025-03-17 11:37:22.401868+08', false, true, '{"api": [], "name": "功能", "api_def": "# 接口备注\n# [[api]]\n# R = \"GET /support/xxxxx/\""}', 'XN0BISn1lO.R5v2v2_0G8.kOEe9EilTLsJ5FnXLJK7y', 0, '{}', '');
INSERT INTO public.access_feature VALUES ('fL5pyZb1YbYiZzwD1TJIQ', '2024-08-03 11:14:39.224677+08', false, true, '{"api": [], "name": "诊断", "api_def": "# 接口备注\n\n"}', 'fL5pyZb1YbYiZzwD1TJIQ', 3, '{"logs": [{"date": "2024-08-03T03:14:39.540Z", "action": "添加", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}, {"date": "2024-09-07T07:37:19.169Z", "action": "更新", "operator": {"id": "bgPEw4MBHM", "nickname": "cpx"}}, {"date": "2024-10-11T04:02:07.097Z", "action": "更新", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}, {"date": "2024-10-18T10:57:40.142Z", "action": "更新", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}]}', '');
INSERT INTO public.access_feature VALUES ('ltQCCM__ctmrCdoYUXu_D', '2025-02-28 09:07:19.125974+08', false, true, '{"api": [{"R": "* /support/case/*"}], "name": "问诊管理", "api_def": "# 接口备注\n[[api]]\nR = \"* /support/case/*\"\n"}', 'fL5pyZb1YbYiZzwD1TJIQ.ltQCCM__ctmrCdoYUXu_D', 0, '{}', '');
INSERT INTO public.access_feature VALUES ('rPjQLIWL3zefYQ6WaaFLw', '2025-04-21 16:18:42.269647+08', false, true, '{"api": [{"R": "* /support/member/*"}, {"R": "GET /support/system/access-control/*"}], "name": "医生资格", "api_def": "# 接口备注\n[[api]]\nR = \"* /support/member/*\"\n\n\n# 接口备注\n[[api]]\nR = \"GET /support/system/access-control/*\""}', 'AWB1ddZONrsOYgVkPYXj1.rPjQLIWL3zefYQ6WaaFLw', 0, '{}', '');
INSERT INTO public.access_feature VALUES ('AWB1ddZONrsOYgVkPYXj1', '2024-10-28 09:59:03.52806+08', false, true, '{"api": [], "name": "医生", "api_def": "# 接口备注\n"}', 'AWB1ddZONrsOYgVkPYXj1', 4, '{"logs": [{"date": "2024-10-28T01:59:03.505Z", "action": "添加", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}, {"date": "2024-10-30T06:56:38.537Z", "action": "更新", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}]}', '');


--
-- TOC entry 3279 (class 0 OID 822494)
-- Dependencies: 206
-- Data for Name: access_role; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.access_role VALUES ('AwS0BtcuC_LZOJ3C9La9o', '2025-03-11 09:55:48.598+08', false, '{"name": "管理员", "index": 1, "features": ["yGWay4PwcVfrQDrDYaU2r", "VLFNkpToUkaLbpqUisEJm", "fL5pyZb1YbYiZzwD1TJIQ", "AWB1ddZONrsOYgVkPYXj1", "QyjncasOAea5CBgOxoeVC", "ltQCCM__ctmrCdoYUXu_D", "hpdgBleP2s", "R5v2v2_0G8", "wECZyZ2ucEAc10qJfxwbW"], "category_id": "ullbfhn8IIcXUH9p_vYjX"}');
INSERT INTO public.access_role VALUES ('wRna4LjkmEaZWfyT908p1', '2024-10-12 09:35:22.422123+08', false, '{"name": "医生", "index": 2, "features": ["5hSJ9ZoFZi8JJRJ0oUHOz", "MiHRSrFkDhWEBfxldyA7N", "gDPV1rCfZvJAi_xNt1fw4", "2EVL9YxSiXvYX6ZQLYTub"], "category_id": "qaCMOpOb5uOp_oX9z1jlh"}');
INSERT INTO public.access_role VALUES ('vvtXrXb1PN', '2020-07-20 13:58:17.118997+08', false, '{"name": "超级管理员", "index": 0, "features": ["yGWay4PwcVfrQDrDYaU2r", "XN0BISn1lO", "VLFNkpToUkaLbpqUisEJm", "fL5pyZb1YbYiZzwD1TJIQ", "AWB1ddZONrsOYgVkPYXj1", "QyjncasOAea5CBgOxoeVC", "5hSJ9ZoFZi8JJRJ0oUHOz", "hpdgBleP2s", "rPjQLIWL3zefYQ6WaaFLw", "ltQCCM__ctmrCdoYUXu_D", "498_I73gh6", "MiHRSrFkDhWEBfxldyA7N", "R5v2v2_0G8", "wECZyZ2ucEAc10qJfxwbW", "gDPV1rCfZvJAi_xNt1fw4", "uVFBmLAXIQAD6_k2sMab1", "2EVL9YxSiXvYX6ZQLYTub", "O_KyGJymdJKcMLAM9xpgd", "hznDlepCSl4BIs5nUyouU", "kOEe9EilTLsJ5FnXLJK7y"], "category_id": "ullbfhn8IIcXUH9p_vYjX"}');


--
-- TOC entry 3295 (class 0 OID 823090)
-- Dependencies: 222
-- Data for Name: case_item; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.case_item VALUES ('IHPg54mIIbd1EoBCVehiR', '2025-04-21 16:36:52.296032+08', '2025-04-24 14:13:18.628915+08', '{"state": {"flow": {"code": "inquiring", "name": "问诊中", "clasz": "text-dark"}}, "title": "新问诊", "remark": "hello", "诊断": "病毒性脑炎", "patient": {"name": "吴用", "phone": "***********", "idcard": "11100011000"}, "owner_id": "rJxGvsO1Ir", "creater_id": "rJxGvsO1Ir", "初步诊断": "病毒性脑炎", "患者信息": "【基本信息】男，1岁\n【主诉】发热、哭闹、喷射状呕吐1天\n【现病史】发热1天，体温最高达40°C，使用退烧药无效，持续哭闹，不进食、不睡觉，喷射状呕吐1天，每天3次，呕吐物呈红色，含咖啡渣样物质，伴有腹痛、腹泻、尿少、皮肤干燥等脱水表现。病后未予诊治。患儿自发病以来，精神差，食欲差，睡眠差。\n【既往史】既往体健\n【传染病接触史】无", "检查建议": "检查A\n检查B\n检查C", "治疗方案": "【药物治疗】\n药品A每日1次每次10ml\n药品B每日1次 每次10ml\n药品C每日1次每次10ml【无】，展示链接\n【生活建议】\n1.保持充足水分：确保孩子充分补充水分，可以尝试少量多次饮水，以防脱水。\n2.观察症状变化：密切观察孩子的症状变化，特别是体温、呕吐、腹痛等情况，如有加重应及时就医。\n3.适当休息：让孩子多休息，减少活动量，避免过度疲劳。"}');


--
-- TOC entry 3297 (class 0 OID 823125)
-- Dependencies: 224
-- Data for Name: case_message; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.case_message VALUES ('6scVFUiahgMbZ_Zr1sdeU', '2025-04-24 10:20:01.634066+08', '2025-04-24 10:33:05.390575+08', '{"file": {"file": "APC86OkvhJ4yjH_8SPjt-.jpeg", "name": "db0826c07f2eb493cda5d9cea9790183c03e5ad9.jpeg", "size": 97762, "type": "image/jpeg"}, "from": "患者", "case_id": "IHPg54mIIbd1EoBCVehiR", "label_ids": ["NY7YCXQIGPiOMSzFx1jBM"], "creater_id": "rJxGvsO1Ir"}');
INSERT INTO public.case_message VALUES ('4fbvNd_lzrowxEibH5_V8', '2025-04-24 10:37:36.943135+08', '2025-04-24 10:40:10.575593+08', '{"from": "AI", "text": "您老哪里不舒服", "case_id": "IHPg54mIIbd1EoBCVehiR", "label_ids": ["I7nug0LLzcOBkbnubiR9P"], "creater_id": "rJxGvsO1Ir"}');
INSERT INTO public.case_message VALUES ('_JREnBcKcLymb0c7pJMiR', '2025-04-24 10:41:21.142815+08', '2025-04-24 10:41:21.236049+08', '{"from": "患者", "text": "胃疼", "case_id": "IHPg54mIIbd1EoBCVehiR", "label_ids": ["I7nug0LLzcOBkbnubiR9P"], "creater_id": "rJxGvsO1Ir"}');
INSERT INTO public.case_message VALUES ('pjnR8xpHYCHd6WnOnKNHL', '2025-04-24 10:40:50.776487+08', '2025-04-24 10:40:50.871277+08', '{"from": "医生", "text": "心跳缓慢，呼吸急促", "case_id": "IHPg54mIIbd1EoBCVehiR", "label_ids": ["iZjwb81uWy193r2zDiwIj"], "creater_id": "rJxGvsO1Ir"}');
INSERT INTO public.case_message VALUES ('38rkfuj17xBhOdzYO35ON', '2025-04-24 11:25:01.105447+08', '2025-04-24 11:25:01.214199+08', '{"from": "医生", "text": "患者说得很好", "case_id": "IHPg54mIIbd1EoBCVehiR", "label_ids": ["fpDmnOMR5Tpy4ChSfCH9b"], "creater_id": "rJxGvsO1Ir"}');


--
-- TOC entry 3281 (class 0 OID 822505)
-- Dependencies: 208
-- Data for Name: category; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.category VALUES ('cklK7Hv4jSYyt74VFziAC', '2022-08-18 10:24:50.268426+08', false, '', '{"name": "用户", "type": "user"}', 'cklK7Hv4jSYyt74VFziAC', 0, true, '{"logs": [{"date": "2022-08-18T02:24:50.153Z", "action": "添加", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}, {"date": "2022-08-18T09:38:45.073Z", "action": "更新", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}, {"date": "2022-08-19T02:10:30.707Z", "action": "更新", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}]}', 'label', NULL);
INSERT INTO public.category VALUES ('ullbfhn8IIcXUH9p_vYjX', '2024-10-15 10:15:39.388976+08', false, '', '{"name": "系统"}', 'ullbfhn8IIcXUH9p_vYjX', 0, true, '{"logs": [{"date": "2024-10-15T02:15:39.381Z", "action": "添加", "operator": {"id": "bgPEw4MBHM", "nickname": "cp"}}]}', 'access_role', NULL);
INSERT INTO public.category VALUES ('tj0iS3bjvn3jZWQsIPsLY', '2024-10-15 10:12:05.509653+08', false, '', '{"name": "用户角色", "type_code": "access_role"}', 'tj0iS3bjvn3jZWQsIPsLY', 0, true, '{"logs": [{"date": "2024-10-15T02:12:05.497Z", "action": "添加", "operator": {"id": "bgPEw4MBHM", "nickname": "cp"}}]}', 'category', NULL);
INSERT INTO public.category VALUES ('TioCwI5G88', '2022-08-08 13:12:55.06021+08', false, '', '{"name": "系统", "type": "text/x-markdown", "cache_ttl": 0}', 'TioCwI5G88', 0, true, '{"logs": [{"date": "2022-08-08T05:12:54.934Z", "action": "添加", "detail": {"private": false, "profile": {"name": "系统", "type": "text/x-markdown", "cache_ttl": 0}, "selected": true}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}, {"date": "2022-08-11T08:03:15.744Z", "action": "更新", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}, {"date": "2022-08-11T08:03:37.589Z", "action": "更新", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}]}', 'settings', NULL);
INSERT INTO public.category VALUES ('qaCMOpOb5uOp_oX9z1jlh', '2024-10-15 10:12:31.797394+08', false, '', '{"name": "医院"}', 'qaCMOpOb5uOp_oX9z1jlh', 1, true, '{"logs": [{"date": "2024-10-15T02:12:31.786Z", "action": "添加", "operator": {"id": "bgPEw4MBHM", "nickname": "cp"}}]}', 'access_role', NULL);
INSERT INTO public.category VALUES ('9mfGBoQjQRuLzr_dL_OjU', '2025-03-06 16:42:16.297106+08', false, '', '{"name": "管理员测试"}', '9mfGBoQjQRuLzr_dL_OjU', 1, true, '{}', 'label', NULL);
INSERT INTO public.category VALUES ('akfLbNhFD09P2TAyghnZk', '2025-03-06 17:19:17.682384+08', false, '', '{"name": "问诊会话"}', 'akfLbNhFD09P2TAyghnZk', 2, true, '{}', 'label', NULL);


--
-- TOC entry 3283 (class 0 OID 822573)
-- Dependencies: 210
-- Data for Name: label; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.label VALUES ('GKkwmJXdb_', '2022-07-20 10:25:36.541202+08', false, '', true, '{"name": "示例3", "intro": "", "remark": "", "background_color": "#FF0000"}', 'cklK7Hv4jSYyt74VFziAC', '{"logs": [{"date": "2022-08-19T02:45:21.004Z", "action": "更新", "detail": {"id": "GKkwmJXdb_", "valid": true, "update": true, "profile": {"name": "华北", "intro": "", "remark": "", "background_color": "#FF0000"}, "_line_no": 2, "category": {"path": "cklK7Hv4jSYyt74VFziAC"}, "created_at": "2022-07-20T02:25:36.541Z", "_line_count": 3, "_page_count": 1}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}, {"date": "2024-11-09T01:41:14.667Z", "action": "更新", "detail": {"id": "GKkwmJXdb_", "valid": true, "update": true, "profile": {"name": "示例3", "intro": "", "remark": "", "background_color": "#FF0000"}, "_line_no": 1, "category": {"id": "cklK7Hv4jSYyt74VFziAC", "path": "cklK7Hv4jSYyt74VFziAC", "type": "label", "profile": {"name": "用户", "type": "user"}}, "created_at": "2022-07-20T02:25:36.541Z", "_line_count": 3, "_page_count": 1}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}]}');
INSERT INTO public.label VALUES ('K8Jk_pmCe', '2022-07-14 15:03:00.243653+08', false, '', true, '{"name": "示例1", "intro": "", "remark": "", "background_color": "#D10069"}', 'cklK7Hv4jSYyt74VFziAC', '{"logs": [{"date": "2022-08-19T02:45:13.926Z", "action": "更新", "detail": {"id": "K8Jk_pmCe", "valid": true, "update": true, "profile": {"name": "不花钱的主", "intro": "", "remark": "", "background_color": "#D1D100"}, "_line_no": 1, "category": {"path": "cklK7Hv4jSYyt74VFziAC"}, "created_at": "2022-07-14T07:03:00.244Z", "_line_count": 3, "_page_count": 1}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}, {"date": "2024-11-09T01:40:57.043Z", "action": "更新", "detail": {"id": "K8Jk_pmCe", "valid": true, "update": true, "profile": {"name": "示例1", "intro": "", "remark": "", "background_color": "#D1D100"}, "_line_no": 1, "category": {"id": "cklK7Hv4jSYyt74VFziAC", "path": "cklK7Hv4jSYyt74VFziAC", "type": "label", "profile": {"name": "用户", "type": "user"}}, "created_at": "2022-07-14T07:03:00.243Z", "_line_count": 3, "_page_count": 1}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}]}');
INSERT INTO public.label VALUES ('d2ZYFqt15HcVgAAhbPmj9', '2025-03-06 16:41:24.720723+08', false, '', true, '{"name": "标签1", "remark": "", "background_color": "#69D100"}', 'cklK7Hv4jSYyt74VFziAC', '{}');
INSERT INTO public.label VALUES ('FpuUnIjSOyw4uu3J1yv_D', '2025-03-06 17:19:48.204497+08', false, '', true, '{"name": "河流", "remark": "", "background_color": "#4defd4"}', 'cklK7Hv4jSYyt74VFziAC', '{}');
INSERT INTO public.label VALUES ('iZjwb81uWy193r2zDiwIj', '2025-04-24 10:00:49.928497+08', false, '', true, '{"name": "查体", "remark": "", "background_color": "#004E00"}', 'akfLbNhFD09P2TAyghnZk', '{}');
INSERT INTO public.label VALUES ('fpDmnOMR5Tpy4ChSfCH9b', '2025-04-24 10:01:07.023689+08', false, '', true, '{"name": "补充信息", "remark": "", "background_color": "#5843AD"}', 'akfLbNhFD09P2TAyghnZk', '{}');
INSERT INTO public.label VALUES ('NY7YCXQIGPiOMSzFx1jBM', '2025-04-24 10:02:38.768002+08', false, '', true, '{"name": "检查报告", "remark": "", "background_color": "#F0AD4E"}', 'akfLbNhFD09P2TAyghnZk', '{}');
INSERT INTO public.label VALUES ('I7nug0LLzcOBkbnubiR9P', '2025-04-24 10:39:56.159191+08', false, '', true, '{"name": "对话", "remark": "", "background_color": "#69D100"}', 'akfLbNhFD09P2TAyghnZk', '{}');


--
-- TOC entry 3296 (class 0 OID 823114)
-- Dependencies: 223
-- Data for Name: member; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.member VALUES ('lCD3j4dkMZeQffF4_c13o', '2025-04-21 16:22:45.448876+08', '2025-04-21 16:22:46.00335+08', '{"name": "兽医皇甫端", "image": {"file": "-hY8xB3TIejOoLCqa8UiO.jpeg", "name": "6f08d6b778684f2187bceaf45461f377.jpeg", "size": 33335, "type": "image/jpeg"}, "intro": "", "state": {"flow": {"code": "ready", "name": "发布"}}, "owner_id": "YICXLp0eGQ7TwsekyKm3-", "creater_id": "rJxGvsO1Ir"}');
INSERT INTO public.member VALUES ('eAfwNI49RPdTvS92eEOrt', '2025-04-24 08:58:17.076845+08', '2025-04-24 14:14:23.501292+08', '{"name": "基层医生", "intro": "", "state": {"flow": {"code": "ready", "name": "生效", "clasz": "text-success"}}, "owner_id": "rJxGvsO1Ir", "creater_id": "rJxGvsO1Ir"}');


--
-- TOC entry 3285 (class 0 OID 822587)
-- Dependencies: 212
-- Data for Name: no_trouble_log_item; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.no_trouble_log_item VALUES ('itS3zctE4P7uRu5ZiR34U', '2025-04-21 16:04:12.936758+08', '2025-04-21 16:04:12.936758+08', '{"id": "fL5pyZb1YbYiZzwD1TJIQ", "log": {"date": "2025-04-21T08:04:12.865Z", "action": "更新", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "access_feature"}');
INSERT INTO public.no_trouble_log_item VALUES ('CJ6EhC2qWDLc6wuQnwIoE', '2025-04-21 16:04:12.972457+08', '2025-04-21 16:04:12.972457+08', '{"id": "ltQCCM__ctmrCdoYUXu_D", "log": {"date": "2025-04-21T08:04:12.905Z", "action": "更新", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "access_feature"}');
INSERT INTO public.no_trouble_log_item VALUES ('kdOCQBs9N3KlJocb0QVPN', '2025-04-21 16:12:42.891949+08', '2025-04-21 16:12:42.891949+08', '{"id": "AWB1ddZONrsOYgVkPYXj1", "log": {"date": "2025-04-21T08:12:42.826Z", "action": "更新", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "access_feature"}');
INSERT INTO public.no_trouble_log_item VALUES ('OdcrhKLsfW36jUCqtRL6D', '2025-04-21 09:19:27.250965+08', '2025-04-21 09:19:27.250965+08', '{"id": "u20Ls_lY20isuX57c4tHM", "log": {"date": "2025-04-21T01:19:27.166Z", "action": "更新", "detail": {"id": "u20Ls_lY20isuX57c4tHM", "update": true, "content": "<!-- \n\n<link\n\trel=\"stylesheet\"\n\thref=\"https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css\"\n\tintegrity=\"sha384-xOolHFLEh07PJGoPkLv1IbcEPTNtaed2xpHsD9ESMhqIYd0nLMwNLD69Npy4HI+N\"\n\tcrossorigin=\"anonymous\"\n/>\n\n<style>\n    body {\n        background: #f2f2f2; backdrop-filter: saturate(180%) blur(3px);\n    }\n</style>\n \n上面的样式，预览时拷贝到正文里，保存时删掉\n-->\n\n<div class=\"container-fluid\">\n\t<div class=\"row  \">\n\t\t<div class=\"col-12 py-1 text-center\">\n\t\t\tcopyright AI基层儿科医生\n\t\t</div>\n\t</div>\n</div> ", "private": false, "profile": {"name": "FOOTER-SITE", "type": "text/html", "version": 16, "cache_ttl": 0}, "_line_no": 1, "category": {"id": "TioCwI5G88", "path": "TioCwI5G88", "profile": {"name": "系统", "type": "text/x-markdown", "cache_ttl": 0}}, "created_at": "2024-06-03T02:08:54.267Z", "_line_count": 1, "_page_count": 1, "category_id": "TioCwI5G88", "content_l100": "<!-- \n\n<link\n\trel=\"stylesheet\"\n\thref=\"https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstra"}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "settings"}');
INSERT INTO public.no_trouble_log_item VALUES ('JW4qHwMEC1Su_7KA5ldhZ', '2025-04-21 16:18:42.31813+08', '2025-04-21 16:18:42.31813+08', '{"id": "rPjQLIWL3zefYQ6WaaFLw", "log": {"date": "2025-04-21T08:18:42.255Z", "action": "添加", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "access_feature"}');
INSERT INTO public.no_trouble_log_item VALUES ('lv70-Ok_S_bSs2Rym40eJ', '2025-04-21 16:18:42.576433+08', '2025-04-21 16:18:42.576433+08', '{"id": "AWB1ddZONrsOYgVkPYXj1", "log": {"date": "2025-04-21T08:18:42.513Z", "action": "更新", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "access_feature"}');
INSERT INTO public.no_trouble_log_item VALUES ('E7bD40-PUI7qDR7bol0oy', '2025-04-21 16:22:45.498839+08', '2025-04-21 16:22:45.498839+08', '{"id": "lCD3j4dkMZeQffF4_c13o", "log": {"date": "2025-04-21T08:22:45.435Z", "action": "添加", "detail": {"profile": {"name": "兽医皇甫端", "image": {"file": "-hY8xB3TIejOoLCqa8UiO.jpeg", "name": "6f08d6b778684f2187bceaf45461f377.jpeg", "size": 33335, "type": "image/jpeg"}, "intro": "", "owner_id": "YICXLp0eGQ7TwsekyKm3-"}}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "member"}');
INSERT INTO public.no_trouble_log_item VALUES ('-UvOAYlFOz5Bu5Hf4JG52', '2025-04-21 16:22:45.955621+08', '2025-04-21 16:22:45.955621+08', '{"id": "lCD3j4dkMZeQffF4_c13o", "log": {"date": "2025-04-21T08:22:45.890Z", "action": "owner_id_updated", "detail": {"profile": {"owner_id": "YICXLp0eGQ7TwsekyKm3-"}}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "member"}');
INSERT INTO public.no_trouble_log_item VALUES ('0cmwtHS9ONYFZXyLku5jx', '2025-04-21 16:36:52.349036+08', '2025-04-21 16:36:52.349036+08', '{"id": "IHPg54mIIbd1EoBCVehiR", "log": {"date": "2025-04-21T08:36:52.295Z", "action": "添加", "detail": {"profile": {"title": "新问诊", "remark": "", "patient_name": "勿用", "patient_phone": "13333333333", "patient_idcard": "11100011000"}}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "case_item"}');
INSERT INTO public.no_trouble_log_item VALUES ('S3wOB1lIkKNwM8Qk6i8Mi', '2025-04-24 08:58:17.133061+08', '2025-04-24 08:58:17.133061+08', '{"id": "eAfwNI49RPdTvS92eEOrt", "log": {"date": "2025-04-24T00:58:17.065Z", "action": "添加", "detail": {"profile": {"name": "原始", "intro": "", "owner_id": "rJxGvsO1Ir"}}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "member"}');
INSERT INTO public.no_trouble_log_item VALUES ('jlvR3E2ySJMKmmtuE2c9X', '2025-04-24 08:58:17.713101+08', '2025-04-24 08:58:17.713101+08', '{"id": "eAfwNI49RPdTvS92eEOrt", "log": {"date": "2025-04-24T00:58:17.647Z", "action": "owner_id_updated", "detail": {"profile": {"owner_id": "rJxGvsO1Ir"}}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "member"}');
INSERT INTO public.no_trouble_log_item VALUES ('DFivw8j0mOYnt4WEQKBP5', '2025-04-24 08:58:18.137097+08', '2025-04-24 08:58:18.137097+08', '{"id": "eAfwNI49RPdTvS92eEOrt", "log": {"date": "2025-04-24T00:58:18.070Z", "action": "owner_id_updated", "detail": {"profile": {"owner_id": "rJxGvsO1Ir"}}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "member"}');
INSERT INTO public.no_trouble_log_item VALUES ('MhGZ184NKAlk6TImd95Uz', '2025-04-24 08:58:32.844007+08', '2025-04-24 08:58:32.844007+08', '{"id": "eAfwNI49RPdTvS92eEOrt", "log": {"date": "2025-04-24T00:58:32.777Z", "action": "state_updated", "detail": {"flow": {"code": "closed", "name": "关闭", "clasz": "text-danger"}}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "member"}');
INSERT INTO public.no_trouble_log_item VALUES ('gbiP3fJOV1I5E8Ekye0PR', '2025-04-24 08:58:40.689083+08', '2025-04-24 08:58:40.689083+08', '{"id": "eAfwNI49RPdTvS92eEOrt", "log": {"date": "2025-04-24T00:58:40.622Z", "action": "state_updated", "detail": {"flow": {"code": "ready", "name": "生效", "clasz": "text-success"}}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "member"}');
INSERT INTO public.no_trouble_log_item VALUES ('meb8UHANwq39PXhZhdAjl', '2025-04-24 09:13:42.783896+08', '2025-04-24 09:13:42.783896+08', '{"id": "Gg0xoCC8Acy0o0rBFOZKu", "log": {"date": "2025-04-24T01:13:42.658Z", "action": "添加", "detail": {"profile": {"files": [{"key": "DQExTSWYQJElx02bv6yzt", "file": "2nEAwe2VZch7CRwuOOp3W.jpeg", "name": "db0826c07f2eb493cda5d9cea9790183c03e5ad9.jpeg", "size": 97762, "type": "image/jpeg"}], "title": "新文件"}}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "case_attachment"}');
INSERT INTO public.no_trouble_log_item VALUES ('jE-XiSuLpSpeadLWxs8JO', '2025-04-24 09:30:08.564822+08', '2025-04-24 09:30:08.564822+08', '{"id": "IHPg54mIIbd1EoBCVehiR", "log": {"date": "2025-04-24T01:30:08.452Z", "action": "更新", "detail": {"id": "IHPg54mIIbd1EoBCVehiR", "profile": {"title": "新问诊", "remark": "", "patient_name": "勿用", "患者信息": "【基本信息】男，1岁\n【主诉】发热、哭闹、喷射状呕吐1天\n【现病史】发热1天，体温最高达40°C，使用退烧药无效，持续哭闹，不进食、不睡觉，喷射状呕吐1天，每天3次，呕吐物呈红色，含咖啡渣样物质，伴有腹痛、腹泻、尿少、皮肤干燥等脱水表现。病后未予诊治。患儿自发病以来，精神差，食欲差，睡眠差。\n【既往史】既往体健\n【传染病接触史】无", "patient_phone": "13333333333", "patient_idcard": "11100011000"}, "overview": {}, "created_at": "2025-04-21T08:36:52.296Z", "updated_at": "2025-04-21T08:36:52.403Z"}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "case_item"}');
INSERT INTO public.no_trouble_log_item VALUES ('fhTQ2AMCDDyEy3SMlt3l0', '2025-04-24 09:33:56.893497+08', '2025-04-24 09:33:56.893497+08', '{"id": "IHPg54mIIbd1EoBCVehiR", "log": {"date": "2025-04-24T01:33:56.780Z", "action": "更新", "detail": {"id": "IHPg54mIIbd1EoBCVehiR", "profile": {"title": "新问诊", "remark": "", "诊断": "病毒性脑炎", "patient_name": "勿用", "初步诊断": "病毒性脑炎", "患者信息": "【基本信息】男，1岁\n【主诉】发热、哭闹、喷射状呕吐1天\n【现病史】发热1天，体温最高达40°C，使用退烧药无效，持续哭闹，不进食、不睡觉，喷射状呕吐1天，每天3次，呕吐物呈红色，含咖啡渣样物质，伴有腹痛、腹泻、尿少、皮肤干燥等脱水表现。病后未予诊治。患儿自发病以来，精神差，食欲差，睡眠差。\n【既往史】既往体健\n【传染病接触史】无", "检查建议": "检查A\n检查B\n检查C", "治疗方案": "【药物治疗】\n药品A每日1次每次10ml\n药品B每日1次 每次10ml\n药品C每日1次每次10ml【无】，展示链接\n【生活建议】\n1.保持充足水分：确保孩子充分补充水分，可以尝试少量多次饮水，以防脱水。\n2.观察症状变化：密切观察孩子的症状变化，特别是体温、呕吐、腹痛等情况，如有加重应及时就医。\n3.适当休息：让孩子多休息，减少活动量，避免过度疲劳。", "patient_phone": "13333333333", "patient_idcard": "11100011000"}, "overview": {}, "created_at": "2025-04-21T08:36:52.296Z", "updated_at": "2025-04-24T01:30:08.612Z"}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "case_item"}');
INSERT INTO public.no_trouble_log_item VALUES ('o2Ps82LwW-Z4_pgBr1I2B', '2025-04-24 09:57:56.144194+08', '2025-04-24 09:57:56.144194+08', '{"id": "bQrEHyzMWNLgKN9BKLndO", "log": {"date": "2025-04-24T01:57:56.061Z", "action": "添加", "detail": {"profile": {"from": "患者", "text": "我不健康"}}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "case_message"}');
INSERT INTO public.no_trouble_log_item VALUES ('3DqQRFCKujKf8wHf5Sve6', '2025-04-24 10:00:06.507018+08', '2025-04-24 10:00:06.507018+08', '{"id": "akfLbNhFD09P2TAyghnZk", "log": {"date": "2025-04-24T02:00:06.425Z", "action": "更新", "detail": {"path": "akfLbNhFD09P2TAyghnZk", "type": "label", "clasz": null, "index": 2, "profile": {"name": "问诊会话"}}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "category"}');
INSERT INTO public.no_trouble_log_item VALUES ('_9r-OzB3tIHPhIfnvRIeT', '2025-04-24 10:00:19.148438+08', '2025-04-24 10:00:19.148438+08', '{"id": "RgXcwPzqYKUzMfydltkM9", "log": {"date": "2025-04-24T02:00:19.066Z", "action": "删除", "detail": {"id": "RgXcwPzqYKUzMfydltkM9", "logs": {}, "path": "akfLbNhFD09P2TAyghnZk.RgXcwPzqYKUzMfydltkM9", "type": "label", "clasz": null, "index": 0, "valid": true, "deleted": false, "profile": {"name": "河流下1"}, "created_at": "2025-03-06T09:23:01.658Z", "deleted_at": ""}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "category"}');
INSERT INTO public.no_trouble_log_item VALUES ('9Js8wMcj4m2U61ccY6HII', '2025-04-24 10:00:49.976243+08', '2025-04-24 10:00:49.976243+08', '{"id": "iZjwb81uWy193r2zDiwIj", "log": {"date": "2025-04-24T02:00:49.893Z", "action": "添加", "detail": {"profile": {"name": "查体", "remark": "", "background_color": "#004E00"}, "category": {"path": "akfLbNhFD09P2TAyghnZk"}}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "label"}');
INSERT INTO public.no_trouble_log_item VALUES ('y4rbmIohKEfTidmuevPdy', '2025-04-24 10:00:50.219236+08', '2025-04-24 10:00:50.219236+08', '{"id": "iZjwb81uWy193r2zDiwIj", "log": {"date": "2025-04-24T02:00:50.136Z", "action": "更新", "detail": {"id": "iZjwb81uWy193r2zDiwIj", "profile": {"name": "查体", "remark": "", "background_color": "#004E00"}, "category": {"path": "akfLbNhFD09P2TAyghnZk"}}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "label"}');
INSERT INTO public.no_trouble_log_item VALUES ('0S1ulCa6h5hObev5lbSH3', '2025-04-24 10:01:07.070699+08', '2025-04-24 10:01:07.070699+08', '{"id": "fpDmnOMR5Tpy4ChSfCH9b", "log": {"date": "2025-04-24T02:01:06.987Z", "action": "添加", "detail": {"profile": {"name": "补充信息", "remark": "", "background_color": "#5843AD"}, "category": {"path": "akfLbNhFD09P2TAyghnZk"}}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "label"}');
INSERT INTO public.no_trouble_log_item VALUES ('1oNttzS_MjjRdqMmtuPgJ', '2025-04-24 10:01:07.314467+08', '2025-04-24 10:01:07.314467+08', '{"id": "fpDmnOMR5Tpy4ChSfCH9b", "log": {"date": "2025-04-24T02:01:07.231Z", "action": "更新", "detail": {"id": "fpDmnOMR5Tpy4ChSfCH9b", "profile": {"name": "补充信息", "remark": "", "background_color": "#5843AD"}, "category": {"path": "akfLbNhFD09P2TAyghnZk"}}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "label"}');
INSERT INTO public.no_trouble_log_item VALUES ('iUioF_BC6OlK1OUtqyqII', '2025-04-24 10:02:38.825497+08', '2025-04-24 10:02:38.825497+08', '{"id": "NY7YCXQIGPiOMSzFx1jBM", "log": {"date": "2025-04-24T02:02:38.742Z", "action": "添加", "detail": {"profile": {"name": "新标签", "remark": "检查", "background_color": "#F0AD4E"}, "category": {"path": "akfLbNhFD09P2TAyghnZk"}}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "label"}');
INSERT INTO public.no_trouble_log_item VALUES ('-WTLCJnjfw_Gh31gQpGbA', '2025-04-24 10:02:39.077751+08', '2025-04-24 10:02:39.077751+08', '{"id": "NY7YCXQIGPiOMSzFx1jBM", "log": {"date": "2025-04-24T02:02:38.993Z", "action": "更新", "detail": {"id": "NY7YCXQIGPiOMSzFx1jBM", "profile": {"name": "新标签", "remark": "检查", "background_color": "#F0AD4E"}, "category": {"path": "akfLbNhFD09P2TAyghnZk"}}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "label"}');
INSERT INTO public.no_trouble_log_item VALUES ('VfmViX6go4M2hxtd10kpL', '2025-04-24 10:02:52.970125+08', '2025-04-24 10:02:52.970125+08', '{"id": "NY7YCXQIGPiOMSzFx1jBM", "log": {"date": "2025-04-24T02:02:52.887Z", "action": "更新", "detail": {"id": "NY7YCXQIGPiOMSzFx1jBM", "valid": true, "update": true, "profile": {"name": "检查", "remark": "", "background_color": "#F0AD4E"}, "_line_no": 1, "category": {"id": "akfLbNhFD09P2TAyghnZk", "path": "akfLbNhFD09P2TAyghnZk", "type": "label", "profile": {"name": "问诊会话"}}, "created_at": "2025-04-24T02:02:38.768Z", "_line_count": 3, "_page_count": 1}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "label"}');
INSERT INTO public.no_trouble_log_item VALUES ('SwRmEL_dc7pdU3qQGTUyi', '2025-04-24 10:03:06.105736+08', '2025-04-24 10:03:06.105736+08', '{"id": "NY7YCXQIGPiOMSzFx1jBM", "log": {"date": "2025-04-24T02:03:06.023Z", "action": "更新", "detail": {"id": "NY7YCXQIGPiOMSzFx1jBM", "valid": true, "update": true, "profile": {"name": "检查报告", "remark": "", "background_color": "#F0AD4E"}, "_line_no": 2, "category": {"id": "akfLbNhFD09P2TAyghnZk", "path": "akfLbNhFD09P2TAyghnZk", "type": "label", "profile": {"name": "问诊会话"}}, "created_at": "2025-04-24T02:02:38.768Z", "_line_count": 3, "_page_count": 1}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "label"}');
INSERT INTO public.no_trouble_log_item VALUES ('i-tRsxLQyVweOcX_SB8IG', '2025-04-24 10:41:21.189052+08', '2025-04-24 10:41:21.189052+08', '{"id": "_JREnBcKcLymb0c7pJMiR", "log": {"date": "2025-04-24T02:41:21.027Z", "action": "添加", "detail": {"profile": {"from": "患者", "text": "胃疼", "labels": ["I7nug0LLzcOBkbnubiR9P"]}}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "case_message"}');
INSERT INTO public.no_trouble_log_item VALUES ('UreyYbHAeiAdmXDM5K8qb', '2025-04-24 10:13:10.327741+08', '2025-04-24 10:13:10.327741+08', '{"id": "IHPg54mIIbd1EoBCVehiR", "log": {"date": "2025-04-24T02:13:10.245Z", "action": "更新", "detail": {"id": "IHPg54mIIbd1EoBCVehiR", "profile": {"title": "新问诊", "remark": "", "诊断": "病毒性脑炎", "patient": {"name": "勿用"}, "patient_name": "勿用", "初步诊断": "病毒性脑炎", "患者信息": "【基本信息】男，1岁\n【主诉】发热、哭闹、喷射状呕吐1天\n【现病史】发热1天，体温最高达40°C，使用退烧药无效，持续哭闹，不进食、不睡觉，喷射状呕吐1天，每天3次，呕吐物呈红色，含咖啡渣样物质，伴有腹痛、腹泻、尿少、皮肤干燥等脱水表现。病后未予诊治。患儿自发病以来，精神差，食欲差，睡眠差。\n【既往史】既往体健\n【传染病接触史】无", "检查建议": "检查A\n检查B\n检查C", "治疗方案": "【药物治疗】\n药品A每日1次每次10ml\n药品B每日1次 每次10ml\n药品C每日1次每次10ml【无】，展示链接\n【生活建议】\n1.保持充足水分：确保孩子充分补充水分，可以尝试少量多次饮水，以防脱水。\n2.观察症状变化：密切观察孩子的症状变化，特别是体温、呕吐、腹痛等情况，如有加重应及时就医。\n3.适当休息：让孩子多休息，减少活动量，避免过度疲劳。", "patient_phone": "13333333333", "patient_idcard": "11100011000"}, "overview": {}, "created_at": "2025-04-21T08:36:52.296Z", "updated_at": "2025-04-24T01:33:57.009Z"}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "case_item"}');
INSERT INTO public.no_trouble_log_item VALUES ('ePbVSkQnLlXGeK1voIe8N', '2025-04-24 10:13:23.497073+08', '2025-04-24 10:13:23.497073+08', '{"id": "IHPg54mIIbd1EoBCVehiR", "log": {"date": "2025-04-24T02:13:23.414Z", "action": "更新", "detail": {"id": "IHPg54mIIbd1EoBCVehiR", "profile": {"title": "新问诊", "remark": "hello", "诊断": "病毒性脑炎", "patient": {"name": "勿用", "phone": "***********", "idcard": "11100011000"}, "patient_name": "勿用", "初步诊断": "病毒性脑炎", "患者信息": "【基本信息】男，1岁\n【主诉】发热、哭闹、喷射状呕吐1天\n【现病史】发热1天，体温最高达40°C，使用退烧药无效，持续哭闹，不进食、不睡觉，喷射状呕吐1天，每天3次，呕吐物呈红色，含咖啡渣样物质，伴有腹痛、腹泻、尿少、皮肤干燥等脱水表现。病后未予诊治。患儿自发病以来，精神差，食欲差，睡眠差。\n【既往史】既往体健\n【传染病接触史】无", "检查建议": "检查A\n检查B\n检查C", "治疗方案": "【药物治疗】\n药品A每日1次每次10ml\n药品B每日1次 每次10ml\n药品C每日1次每次10ml【无】，展示链接\n【生活建议】\n1.保持充足水分：确保孩子充分补充水分，可以尝试少量多次饮水，以防脱水。\n2.观察症状变化：密切观察孩子的症状变化，特别是体温、呕吐、腹痛等情况，如有加重应及时就医。\n3.适当休息：让孩子多休息，减少活动量，避免过度疲劳。", "patient_phone": "13333333333", "patient_idcard": "11100011000"}, "overview": {}, "created_at": "2025-04-21T08:36:52.296Z", "updated_at": "2025-04-24T02:13:10.377Z"}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "case_item"}');
INSERT INTO public.no_trouble_log_item VALUES ('IEw7-FPIn-wXxkLQqhj86', '2025-04-24 10:20:01.684859+08', '2025-04-24 10:20:01.684859+08', '{"id": "6scVFUiahgMbZ_Zr1sdeU", "log": {"date": "2025-04-24T02:20:01.605Z", "action": "添加", "detail": {"profile": {"file": {"file": "APC86OkvhJ4yjH_8SPjt-.jpeg", "name": "db0826c07f2eb493cda5d9cea9790183c03e5ad9.jpeg", "size": 97762, "type": "image/jpeg"}, "from": "患者", "text": "新消息"}}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "case_message"}');
INSERT INTO public.no_trouble_log_item VALUES ('TNd-x8JutB9Zi-EKLPg08', '2025-04-24 10:33:05.32581+08', '2025-04-24 10:33:05.32581+08', '{"id": "6scVFUiahgMbZ_Zr1sdeU", "log": {"date": "2025-04-24T02:33:05.166Z", "action": "更新", "detail": {"id": "6scVFUiahgMbZ_Zr1sdeU", "update": true, "profile": {"file": {"file": "APC86OkvhJ4yjH_8SPjt-.jpeg", "name": "db0826c07f2eb493cda5d9cea9790183c03e5ad9.jpeg", "size": 97762, "type": "image/jpeg"}, "from": "患者", "text": "新消息", "labels": ["NY7YCXQIGPiOMSzFx1jBM"]}, "_line_no": 1, "created_at": "2025-04-24T02:20:01.634Z", "updated_at": "2025-04-24T02:20:01.734Z", "_line_count": 2, "_page_count": 1}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "case_message"}');
INSERT INTO public.no_trouble_log_item VALUES ('eEwDXEDEzC9c4XXLEUPMv', '2025-04-24 10:37:36.98985+08', '2025-04-24 10:37:36.98985+08', '{"id": "4fbvNd_lzrowxEibH5_V8", "log": {"date": "2025-04-24T02:37:36.830Z", "action": "添加", "detail": {"profile": {"from": "AI", "text": "您老哪里不舒服"}}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "case_message"}');
INSERT INTO public.no_trouble_log_item VALUES ('JcvkRaejtpmsHrJNRiX1e', '2025-04-24 10:39:56.217192+08', '2025-04-24 10:39:56.217192+08', '{"id": "I7nug0LLzcOBkbnubiR9P", "log": {"date": "2025-04-24T02:39:56.053Z", "action": "添加", "detail": {"profile": {"name": "对话", "remark": "", "background_color": "#69D100"}, "category": {"path": "akfLbNhFD09P2TAyghnZk"}}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "label"}');
INSERT INTO public.no_trouble_log_item VALUES ('YSR_X9Sw7UVpe1MJ-TK5w', '2025-04-24 10:39:56.470434+08', '2025-04-24 10:39:56.470434+08', '{"id": "I7nug0LLzcOBkbnubiR9P", "log": {"date": "2025-04-24T02:39:56.308Z", "action": "更新", "detail": {"id": "I7nug0LLzcOBkbnubiR9P", "profile": {"name": "对话", "remark": "", "background_color": "#69D100"}, "category": {"path": "akfLbNhFD09P2TAyghnZk"}}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "label"}');
INSERT INTO public.no_trouble_log_item VALUES ('Zhrlmntb5Tkot8JeXKyvE', '2025-04-24 10:40:10.525837+08', '2025-04-24 10:40:10.525837+08', '{"id": "4fbvNd_lzrowxEibH5_V8", "log": {"date": "2025-04-24T02:40:10.364Z", "action": "更新", "detail": {"id": "4fbvNd_lzrowxEibH5_V8", "update": true, "profile": {"from": "AI", "text": "您老哪里不舒服", "labels": ["I7nug0LLzcOBkbnubiR9P"]}, "_line_no": 1, "created_at": "2025-04-24T02:37:36.943Z", "updated_at": "2025-04-24T02:37:37.036Z", "_line_count": 3, "_page_count": 1}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "case_message"}');
INSERT INTO public.no_trouble_log_item VALUES ('ZY4K7VV-nrzaKpzJ2pv3T', '2025-04-24 10:40:17.238436+08', '2025-04-24 10:40:17.238436+08', '{"id": "bQrEHyzMWNLgKN9BKLndO", "log": {"date": "2025-04-24T02:40:17.077Z", "action": "更新", "detail": {"id": "bQrEHyzMWNLgKN9BKLndO", "update": true, "profile": {"from": "患者", "text": "我不健康", "labels": ["I7nug0LLzcOBkbnubiR9P"]}, "_line_no": 3, "created_at": "2025-04-24T01:57:56.094Z", "updated_at": "2025-04-24T01:57:56.199Z", "_line_count": 3, "_page_count": 1}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "case_message"}');
INSERT INTO public.no_trouble_log_item VALUES ('E4td-pnV8bbkIfaK31U_b', '2025-04-24 10:40:50.822995+08', '2025-04-24 10:40:50.822995+08', '{"id": "pjnR8xpHYCHd6WnOnKNHL", "log": {"date": "2025-04-24T02:40:50.661Z", "action": "添加", "detail": {"profile": {"from": "医生", "text": "心跳缓慢，呼吸急促", "labels": ["iZjwb81uWy193r2zDiwIj"]}}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "case_message"}');
INSERT INTO public.no_trouble_log_item VALUES ('Id6tM0mbtM2KSfbQEQLbU', '2025-04-24 11:25:01.158653+08', '2025-04-24 11:25:01.158653+08', '{"id": "38rkfuj17xBhOdzYO35ON", "log": {"date": "2025-04-24T03:25:01.022Z", "action": "添加", "detail": {"profile": {"from": "医生", "text": "患者说得很好", "label_ids": ["fpDmnOMR5Tpy4ChSfCH9b"]}}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "case_message"}');
INSERT INTO public.no_trouble_log_item VALUES ('BcGm5KN8VBg0r9-Nf7uZ4', '2025-04-24 13:47:33.028086+08', '2025-04-24 13:47:33.028086+08', '{"id": "bQrEHyzMWNLgKN9BKLndO", "log": {"date": "2025-04-24T05:47:32.917Z", "action": "删除", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "case_message"}');
INSERT INTO public.no_trouble_log_item VALUES ('K6Pp6dRAtID7ye9cI0QdY', '2025-04-24 14:05:54.058496+08', '2025-04-24 14:05:54.058496+08', '{"id": "W0xpWKus9Tm9_mHV0FYXF", "log": {"date": "2025-04-24T06:05:53.969Z", "action": "添加", "detail": {"content": "\"对话\" = \"I7nug0LLzcOBkbnubiR9P\"\n\"查体\" = \"iZjwb81uWy193r2zDiwIj\"\n\"检查报告\" = \"NY7YCXQIGPiOMSzFx1jBM\"\n\"补充信息\" = \"fpDmnOMR5Tpy4ChSfCH9b\"\n", "private": false, "profile": {"name": "问诊会话标签", "type": "text/x-toml", "cache_ttl": 0}, "category": {"path": "TioCwI5G88"}}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "settings"}');
INSERT INTO public.no_trouble_log_item VALUES ('qJPUlcCgTgTIZEh3fgPic', '2025-04-24 14:09:12.088374+08', '2025-04-24 14:09:12.088374+08', '{"id": "W0xpWKus9Tm9_mHV0FYXF", "log": {"date": "2025-04-24T06:09:11.996Z", "action": "更新", "detail": {"id": "W0xpWKus9Tm9_mHV0FYXF", "update": true, "content": "\"对话\" = \"I7nug0LLzcOBkbnubiR9P\"\n\"查体\" = \"iZjwb81uWy193r2zDiwIj\"\n\"检查报告\" = \"NY7YCXQIGPiOMSzFx1jBM\"\n\"补充信息\" = \"fpDmnOMR5Tpy4ChSfCH9b\"\n", "private": false, "profile": {"name": "问诊消息标签", "type": "text/x-toml", "version": 1, "cache_ttl": 0}, "_line_no": 12, "category": {"id": "TioCwI5G88", "path": "TioCwI5G88", "profile": {"name": "系统", "type": "text/x-markdown", "cache_ttl": 0}}, "created_at": "2025-04-24T06:05:54.011Z", "_line_count": 13, "_page_count": 1, "category_id": "TioCwI5G88", "content_l100": "\"对话\" = \"I7nug0LLzcOBkbnubiR9P\"\n\"查体\" = \"iZjwb81uWy193r2zDiwIj\"\n\"检查报告\" = \"NY7YCXQIGPiOMSzFx1jBM\"\n\"补充信息"}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "settings"}');
INSERT INTO public.no_trouble_log_item VALUES ('E1kyTz_UhRD7c70Psqpqa', '2025-04-24 14:13:18.585536+08', '2025-04-24 14:13:18.585536+08', '{"id": "IHPg54mIIbd1EoBCVehiR", "log": {"date": "2025-04-24T06:13:18.495Z", "action": "更新", "detail": {"id": "IHPg54mIIbd1EoBCVehiR", "profile": {"title": "新问诊", "remark": "hello", "诊断": "病毒性脑炎", "patient": {"name": "吴用", "phone": "***********", "idcard": "11100011000"}, "初步诊断": "病毒性脑炎", "患者信息": "【基本信息】男，1岁\n【主诉】发热、哭闹、喷射状呕吐1天\n【现病史】发热1天，体温最高达40°C，使用退烧药无效，持续哭闹，不进食、不睡觉，喷射状呕吐1天，每天3次，呕吐物呈红色，含咖啡渣样物质，伴有腹痛、腹泻、尿少、皮肤干燥等脱水表现。病后未予诊治。患儿自发病以来，精神差，食欲差，睡眠差。\n【既往史】既往体健\n【传染病接触史】无", "检查建议": "检查A\n检查B\n检查C", "治疗方案": "【药物治疗】\n药品A每日1次每次10ml\n药品B每日1次 每次10ml\n药品C每日1次每次10ml【无】，展示链接\n【生活建议】\n1.保持充足水分：确保孩子充分补充水分，可以尝试少量多次饮水，以防脱水。\n2.观察症状变化：密切观察孩子的症状变化，特别是体温、呕吐、腹痛等情况，如有加重应及时就医。\n3.适当休息：让孩子多休息，减少活动量，避免过度疲劳。"}, "overview": {}, "created_at": "2025-04-21T08:36:52.296Z", "updated_at": "2025-04-24T02:13:23.543Z"}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "case_item"}');
INSERT INTO public.no_trouble_log_item VALUES ('qeniywyl0jQcWTjPR8qv6', '2025-04-24 14:14:23.447786+08', '2025-04-24 14:14:23.447786+08', '{"id": "eAfwNI49RPdTvS92eEOrt", "log": {"date": "2025-04-24T06:14:23.357Z", "action": "更新", "detail": {"id": "eAfwNI49RPdTvS92eEOrt", "update": true, "profile": {"name": "基层医生", "intro": ""}, "_line_no": 1, "created_at": "2025-04-24T00:58:17.076Z", "updated_at": "2025-04-24T00:58:40.739Z", "_line_count": 2, "_page_count": 1}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员", "realname": "元始天尊"}}, "table": "member"}');


--
-- TOC entry 3286 (class 0 OID 822596)
-- Dependencies: 213
-- Data for Name: no_trouble_trash_item; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.no_trouble_trash_item VALUES ('oqdyqEKDfVGu9lg8C5xrK', '2025-04-24 13:47:33.17031+08', '2025-04-24 13:47:33.17031+08', '{"desc": "[case_message] [bQrEHyzMWNLgKN9BKLndO] undefined", "items": [{"row": {"id": "bQrEHyzMWNLgKN9BKLndO", "profile": {"from": "患者", "text": "我不健康", "case_id": "IHPg54mIIbd1EoBCVehiR", "label_ids": ["I7nug0LLzcOBkbnubiR9P"], "creater_id": "rJxGvsO1Ir"}, "created_at": "2025-04-24T01:57:56.094Z", "updated_at": "2025-04-24T05:47:33.083Z"}, "table": "case_message"}], "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}', '{}');


--
-- TOC entry 3287 (class 0 OID 822606)
-- Dependencies: 214
-- Data for Name: oss_object; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.oss_object VALUES ('-hY8xB3TIejOoLCqa8UiO', '2025-04-21 16:22:34.212757+08', false, '{"oss": {"path": "file/-hY8xB3TIejOoLCqa8UiO.jpeg", "bucket": "m100"}, "user_id": "rJxGvsO1Ir", "filename": "-hY8xB3TIejOoLCqa8UiO.jpeg", "metadata": {"size": 33335, "filename": "6f08d6b778684f2187bceaf45461f377.jpeg", "mimetype": "image/jpeg", "content_type": "image/jpeg"}}', '3bc8da22f4e19623a204f8ac2f64caf1');
INSERT INTO public.oss_object VALUES ('2nEAwe2VZch7CRwuOOp3W', '2025-04-24 09:13:39.117118+08', false, '{"key": "DQExTSWYQJElx02bv6yzt", "oss": {"path": "material/2nEAwe2VZch7CRwuOOp3W.jpeg", "bucket": "m100"}, "private": true, "user_id": "rJxGvsO1Ir", "filename": "2nEAwe2VZch7CRwuOOp3W.jpeg", "metadata": {"size": 97762, "filename": "db0826c07f2eb493cda5d9cea9790183c03e5ad9.jpeg", "mimetype": "image/jpeg", "content_type": "image/jpeg"}}', 'ea95b3751c38d7407ac00f7de4b76034');
INSERT INTO public.oss_object VALUES ('APC86OkvhJ4yjH_8SPjt-', '2025-04-24 10:19:51.750909+08', false, '{"oss": {"path": "file/APC86OkvhJ4yjH_8SPjt-.jpeg", "bucket": "m100"}, "user_id": "rJxGvsO1Ir", "filename": "APC86OkvhJ4yjH_8SPjt-.jpeg", "metadata": {"size": 97762, "filename": "db0826c07f2eb493cda5d9cea9790183c03e5ad9.jpeg", "mimetype": "image/jpeg", "content_type": "image/jpeg"}}', 'ea95b3751c38d7407ac00f7de4b76034');


--
-- TOC entry 3288 (class 0 OID 822624)
-- Dependencies: 215
-- Data for Name: settings; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.settings VALUES ('l4_kjEdKRH7y3mbsRL9w3', '2024-05-27 16:25:36.44417+08', false, false, '{"name": "文件服务", "type": "text/x-toml", "cache_ttl": 0}', '[upload]
max_filesize=52428800 # 50m', '', '{"logs": [{"date": "2024-05-27T08:25:36.446Z", "action": "添加", "detail": {"content": "[upload]\nmax_filesize=52428800 # 50m", "private": false, "profile": {"name": "文件服务", "type": "text/x-toml", "cache_ttl": 0}, "category": {"path": "TioCwI5G88"}}, "operator": {"id": "bgPEw4MBHM", "nickname": "cpx"}}]}', 'TioCwI5G88');
INSERT INTO public.settings VALUES ('I60qt6nmLhHAGH_9cnD5K', '2024-09-19 14:57:06.441241+08', true, false, '{"name": "PUSH_SERVICE", "type": "text/x-toml", "version": 4, "cache_ttl": 60}', '[yunpian] #jnpinno
apikey = ''e5dec617130c2085f6efef5ddaa858c5''

[sendcloud]
api = "https://api.sendcloud.net/apiv2/mail/send"
apiUser = "esimgptuser"
apiKey = "0d2cc3e841f02e83146ca1e569370127"
', '', '{}', 'TioCwI5G88');
INSERT INTO public.settings VALUES ('_lBwAmZ5Wq3FoGUFCbXEh', '2024-09-19 15:00:46.844329+08', true, false, '{"name": "PUSH_VALIDATE_CODE", "type": "text/x-toml", "version": 11, "cache_ttl": 60}', '[default]
# from = "<EMAIL>"
# alias = "ESIMGPT"
# subject = "Authentication Code"
# html = """
# Your requested authorization one time pass code is:<br><br>
# {{code}}<br><br>
# If you haven''t requested the authorization on esimgpt please just ignore this email and do not share the code above
#     """
sms="【嘉朋创新】您的验证码是{{code}}。如非本人操作，请忽略本短信"
', '', '{}', 'TioCwI5G88');
INSERT INTO public.settings VALUES ('mlwZafdmr0zRiQr6A08vd', '2024-10-15 12:17:51.576608+08', false, false, '{"name": "系统角色", "type": "text/x-toml", "version": 3, "cache_ttl": 3600}', '"类目ID" = ''ullbfhn8IIcXUH9p_vYjX''
', '', '{"logs": [{"date": "2024-10-15T04:17:51.573Z", "action": "添加", "detail": {"id": "a8nn1l4LwIzZ1bFYhZCuJ", "content": "\"类目ID\" = ''qaCMOpOb5uOp_oX9z1jlh''\n", "private": false, "profile": {"name": "门店角色 复制", "type": "text/x-toml", "version": 3, "cache_ttl": 3600}, "category": {"id": "TioCwI5G88", "path": "TioCwI5G88", "profile": {"name": "系统", "type": "text/x-markdown", "cache_ttl": 0}}, "created_at": "2024-10-15T02:38:42.682Z", "category_id": "TioCwI5G88"}, "operator": {"id": "bgPEw4MBHM", "nickname": "cp"}}, {"date": "2024-10-15T04:18:05.587Z", "action": "更新", "detail": {"id": "mlwZafdmr0zRiQr6A08vd", "update": true, "content": "\"类目ID\" = ''qaCMOpOb5uOp_oX9z1jlh''\n", "private": false, "profile": {"name": "系统角色", "type": "text/x-toml", "version": 1, "cache_ttl": 3600}, "_line_no": 19, "category": {"id": "TioCwI5G88", "path": "TioCwI5G88", "profile": {"name": "系统", "type": "text/x-markdown", "cache_ttl": 0}}, "created_at": "2024-10-15T04:17:51.576Z", "_line_count": 22, "_page_count": 2, "category_id": "TioCwI5G88", "content_l100": "\"类目ID\" = ''qaCMOpOb5uOp_oX9z1jlh''\n"}, "operator": {"id": "bgPEw4MBHM", "nickname": "cp"}}, {"date": "2024-10-15T04:19:22.310Z", "action": "更新", "detail": {"id": "mlwZafdmr0zRiQr6A08vd", "update": true, "content": "\"类目ID\" = ''ullbfhn8IIcXUH9p_vYjX''\n", "private": false, "profile": {"name": "系统角色", "type": "text/x-toml", "version": 2, "cache_ttl": 3600}, "_line_no": 17, "category": {"id": "TioCwI5G88", "path": "TioCwI5G88", "profile": {"name": "系统", "type": "text/x-markdown", "cache_ttl": 0}}, "created_at": "2024-10-15T04:17:51.576Z", "_line_count": 22, "_page_count": 2, "category_id": "TioCwI5G88", "content_l100": "\"类目ID\" = ''qaCMOpOb5uOp_oX9z1jlh''\n"}, "operator": {"id": "bgPEw4MBHM", "nickname": "cp"}}]}', 'TioCwI5G88');
INSERT INTO public.settings VALUES ('UK86IF_e00t0ySgLZASQU', '2024-11-22 16:19:01.733257+08', false, false, '{"name": "AMAP", "type": "text/x-toml", "version": 1, "cache_ttl": 0}', '#高德地图 https://lbs.amap.com/api/javascript-api-v2/prerequisites
key="82cf2cb1a69981bdcbaf72245e0c2fe5"', '', '{"logs": [{"date": "2024-11-22T08:19:01.734Z", "action": "添加", "detail": {"content": "#高德地图 https://lbs.amap.com/api/javascript-api-v2/prerequisites\nkey=\"82cf2cb1a69981bdcbaf72245e0c2fe5\"", "private": false, "profile": {"name": "AMAP", "type": "text/x-toml", "cache_ttl": 0}, "category": {"path": "TioCwI5G88"}}, "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}]}', 'TioCwI5G88');
INSERT INTO public.settings VALUES ('iMGl4uluj6_1gHU7MKUMS', '2024-06-19 17:46:16.947865+08', false, false, '{"name": "隐私保护声明", "type": "text/x-markdown", "version": 6, "cache_ttl": 0}', '
**PRIVACY POLICY**



', '', '{"logs": []}', 'TioCwI5G88');
INSERT INTO public.settings VALUES ('KDpfLZscHVZ7cHJ4xM6J2', '2024-06-19 18:18:08.670168+08', false, false, '{"name": "用户协议", "type": "text/x-markdown", "version": 9}', '**TERMS OF USE**


', '', '{"logs": []}', 'TioCwI5G88');
INSERT INTO public.settings VALUES ('fhD1U2oeWYt_9MliUY5MP', '2024-09-23 16:02:55.210915+08', false, false, '{"name": "cookie协议", "type": "text/x-markdown", "version": 4}', '**COOKIE POLICY**


', '', '{"logs": []}', 'TioCwI5G88');
INSERT INTO public.settings VALUES ('bA98lo2V2z', '2021-06-10 10:12:33.919494+08', true, false, '{"name": "系统业务-执行账号", "type": "text/x-toml", "version": 3, "cache_ttl": 60}', '''业务员手机号''=''***********''', '', '{"logs": []}', 'TioCwI5G88');
INSERT INTO public.settings VALUES ('a8nn1l4LwIzZ1bFYhZCuJ', '2024-10-15 10:38:42.682505+08', false, false, '{"name": "医院角色", "type": "text/x-toml", "version": 4, "cache_ttl": 3600}', '"类目ID" = ''qaCMOpOb5uOp_oX9z1jlh''
', '', '{"logs": []}', 'TioCwI5G88');
INSERT INTO public.settings VALUES ('W0xpWKus9Tm9_mHV0FYXF', '2025-04-24 14:05:54.011751+08', false, false, '{"name": "问诊消息标签", "type": "text/x-toml", "version": 2, "cache_ttl": 0}', '"对话" = "I7nug0LLzcOBkbnubiR9P"
"查体" = "iZjwb81uWy193r2zDiwIj"
"检查报告" = "NY7YCXQIGPiOMSzFx1jBM"
"补充信息" = "fpDmnOMR5Tpy4ChSfCH9b"
', '', '{}', 'TioCwI5G88');
INSERT INTO public.settings VALUES ('u20Ls_lY20isuX57c4tHM', '2024-06-03 10:08:54.267197+08', false, false, '{"name": "FOOTER-SITE", "type": "text/html", "version": 17, "cache_ttl": 0}', '<!-- 

<link
	rel="stylesheet"
	href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css"
	integrity="sha384-xOolHFLEh07PJGoPkLv1IbcEPTNtaed2xpHsD9ESMhqIYd0nLMwNLD69Npy4HI+N"
	crossorigin="anonymous"
/>

<style>
    body {
        background: #f2f2f2; backdrop-filter: saturate(180%) blur(3px);
    }
</style>
 
上面的样式，预览时拷贝到正文里，保存时删掉
-->

<div class="container-fluid">
	<div class="row  ">
		<div class="col-12 py-1 text-center">
			copyright AI基层儿科医生
		</div>
	</div>
</div> ', '', '{"logs": []}', 'TioCwI5G88');


--
-- TOC entry 3289 (class 0 OID 822636)
-- Dependencies: 216
-- Data for Name: user_account; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.user_account VALUES ('hHR0SJRmrW93r3b3UcDdf', '2024-10-10 15:15:35.070467+08', '2024-10-10 15:15:35.070467+08', '系统业务账号', '***********', NULL, '{}', false, NULL, true, '{}', '{"support": true}', '{}', '{"md5": "ea3f005c8b3bb511b7d5189731889ecd", "random": "SXSyvV32axMxmxuopxnN0"}', '{"ip": "::ffff:localhost", "events": ["admin"], "referer": "/app/support/"}', '{"items": ["vvtXrXb1PN"]}', '', '86', '{"logs": [{"date": "2024-10-10T07:15:35.085Z", "action": "添加", "detail": {"pwd": "test11223322", "role": {"support": true}, "phone": "***********", "profile": {}, "nickname": "系统账号", "real_data": {}, "support_ability": {"items": ["vvtXrXb1PN"]}}, "operator": {"id": "bgPEw4MBHM", "nickname": "cp"}}]}');
INSERT INTO public.user_account VALUES ('n8tRaKK_4k', '2022-08-12 15:03:58.26557+08', '2022-08-12 15:03:58.26557+08', 'jacky test', '***********', NULL, '{}', false, NULL, true, '{}', '{}', '{"weixin_profile": {"openid": "oyR7e5aHhrIEhSuBJ0x_igc1qzIA"}}', '{"md5": "337984310c5817e59811fa5df66f26e8", "random": "jpuUu8XwNBkvobed4L9uO"}', '{"referer": null}', '{"items": []}', '', '86', '{}');
INSERT INTO public.user_account VALUES ('b0JYkB41jRYqsqQSncXu6', '2024-04-24 10:06:32.159182+08', '2024-04-24 10:06:32.159182+08', 'lh', '***********', '7CNqO6WxYgjzKj5KNoVYM.jpg', '{}', false, NULL, true, '{"work-assistant": {"name": "work-assistant", "platform": "android", "version_code": 27, "version_name": "1.0.0", "aliyun_device_id": ""}, "lawyer-assistant": {"name": "lawyer-assistant", "platform": "android", "version_code": 10, "version_name": "1.0.3", "aliyun_device_id": ""}}', '{"support": true}', '{"name": "Lucia", "weixin_profile": {"openid": "o80LE67BGTDEDpsH8GGpPVaCyPec"}}', '{"md5": "963c4e1dac0fc8e3df5940c03160f918", "random": "T_4c5C_Ryvpe23xXT2RXV"}', '{"ip": "*************", "events": ["admin"], "referer": "/app/support/"}', '{"items": ["vvtXrXb1PN"]}', '', '86', '{}');
INSERT INTO public.user_account VALUES ('KqbYer62qb', '2022-07-20 16:06:25.091732+08', '2022-07-20 16:06:25.091732+08', '凡有所相皆是虚妄', '***********', '6ZMqoeOeL8ZC6ZVl73SP4.jpg', '{"labels": []}', false, NULL, true, '{"work-assistant": {"name": "work-assistant", "platform": "android", "version_code": 48, "version_name": "1.0.0", "aliyun_device_id": ""}, "lawyer-assistant": {"name": "lawyer-assistant", "platform": "web", "version_code": 1000, "version_name": "1.1.0", "aliyun_device_id": ""}}', '{"support": true}', '{"sex": "女", "ugc": {"ugc_id": "uLK1_1LHwO", "finished": false, "lesson_id": "d4_deNrz4P"}, "city": "背景", "name": "时间的", "image": {"file": "S1NFy0BsAmsy0qdKIvycl.jpg", "name": "WechatIMG2882.jpg", "size": 518609, "type": "image/jpeg"}, "intro": "张世一媳妇的狗", "qrcode": "data:image/png;base64,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***************************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", "城市": "北京", "birthday": "2024-04-19", "like_tags": [], "mp_profile": {"openid": "o80LE6_zWMaP20eOrFE9zgR4WDfs"}, "weixin_profile": {"openid": "o80LE6_zWMaP20eOrFE9zgR4WDfs"}, "xweixin_profile": {"openid": "oyR7e5UmhfOiobxTcdWnHanZEev4"}}', '{"md5": "c6ca3854cb075145eb31cd02ab4f1e03", "random": "VUxIZZkHt8CP0gMntR8Uq"}', '{"referer": "/wx294a440c293ee542/devtools/page-frame.html"}', '{"items": ["vvtXrXb1PN", "6ab0ECE6WT", "eYxjRqFt5PlR003QF_hT8"]}', '2022-08-11 15:42:13.949494+08', '86', '{}');
INSERT INTO public.user_account VALUES ('rJxGvsO1Ir', '2019-09-06 14:23:22.410296+08', '2019-09-06 14:23:22.410296+08', '零号管理员', '***********', '40BnqEroOpn1DwzF0cDDm.jpeg', '{"labels": []}', false, NULL, true, '{"work-assistant": {"name": "work-assistant", "platform": "android", "version_code": 4, "version_name": "1.0.0", "aliyun_device_id": ""}}', '{"support": true}', '{"name": "元始天尊", "car_series_id": "KebDMsTb1FZee79LM160R"}', '{"md5": "5de8d28c1cb91e427fe71d6005560d36", "random": "WSkNGklX7geae8EBuL6IZ"}', '{}', '{"items": ["vvtXrXb1PN"]}', '', '86', '{}');
INSERT INTO public.user_account VALUES ('bgPEw4MBHM', '2022-07-05 15:40:41.409449+08', '2022-07-05 15:40:41.409449+08', 'cp', '***********', '1r31085RbBmD_WhzGgT4E.jpeg', '{"labels": []}', true, '<EMAIL>', true, '{"work-assistant": {"name": "work-assistant", "platform": "android", "version_code": 6, "version_name": "1.0.0", "aliyun_device_id": ""}, "lawyer-assistant": {"name": "lawyer-assistant", "platform": "web", "version_code": 1000, "version_name": "1.1.0", "aliyun_device_id": ""}}', '{"support": true}', '{"sex": "男", "ugc": {"fff": 123, "ugc_id": "uLK1_1LHwO", "finished": true, "lesson_id": "d4_deNrz4P"}, "city": "老街", "name": "陈大爷", "intro": "", "birthday": "2024-04-22", "like_tags": [], "car_series_id": "KebDMsTb1FZee79LM160R", "weixin_profile": {"openid": "o80LE6y8z9w2rvhgTojm9P2J5yqQ"}}', '{"md5": "102c737e0efa1f43bd2ae06d14608f9f", "random": "wp53tRH3xrktEjJokNxVm"}', '{"referer": "/wx294a440c293ee542/devtools/page-frame.html"}', '{"items": ["vvtXrXb1PN"]}', '2025-02-10 18:13:53.569245+08', '86', '{"logs": [{"date": "2024-06-21T06:25:54.569Z", "action": "删除", "operator": {"id": "bgPEw4MBHM", "nickname": "cpx"}}, {"date": "2024-06-21T07:12:46.148Z", "action": "删除", "operator": {"id": "bgPEw4MBHM", "nickname": "cpx"}}, {"date": "2025-02-10T10:13:51.378Z", "action": "删除", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}]}');
INSERT INTO public.user_account VALUES ('YICXLp0eGQ7TwsekyKm3-', '2024-11-01 16:10:36.099633+08', '2024-11-01 16:10:36.099633+08', '未乘车没', '***********', 'f-eH-FUsOyuLdfoJ17LI5.jpg', '{}', false, NULL, true, '{"work-assistant": {"name": "work-assistant", "platform": "web", "version_code": 1000, "version_name": "1.1.0", "aliyun_device_id": ""}}', '{"support": true}', '{"name": "未乘车没"}', '{"md5": "f53e434024d0a08ea3b450b55217ff76", "random": "JAkd2esxFRDcBnrW7PvgH"}', '{"ip": "*************", "events": {}, "referer": "/"}', '{"items": ["AwS0BtcuC_LZOJ3C9La9o"]}', '', '86', '{}');


--
-- TOC entry 3290 (class 0 OID 822657)
-- Dependencies: 217
-- Data for Name: user_captcha; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- TOC entry 3291 (class 0 OID 822666)
-- Dependencies: 218
-- Data for Name: user_error_validation; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO public.user_error_validation VALUES (253, '2024-09-23 18:51:20.339294+08', '::ffff:localhost', NULL, '/validateCode', '{"account":"<EMAIL>","api":"user/sms-login","captcha":null}', false);
INSERT INTO public.user_error_validation VALUES (255, '2024-09-23 18:55:21.272785+08', '************', NULL, '/validateCode', '{"account":"<EMAIL>","api":"user/sms-login","captcha":null}', false);
INSERT INTO public.user_error_validation VALUES (257, '2024-09-25 12:15:39.731552+08', '::ffff:localhost', NULL, '/login', '{"account":"<EMAIL>","pwd":"**********","account_properties":null,"email":"<EMAIL>"}', false);
INSERT INTO public.user_error_validation VALUES (258, '2024-09-25 12:15:43.525798+08', '::ffff:localhost', NULL, '/login', '{"account":"<EMAIL>","pwd":"1122","account_properties":null,"email":"<EMAIL>"}', false);
INSERT INTO public.user_error_validation VALUES (262, '2024-09-26 14:48:50.547349+08', '::ffff:localhost', NULL, '/validateCode', '{"phone":"<EMAIL>","api":"user/retrievePwd"}', false);
INSERT INTO public.user_error_validation VALUES (254, '2024-09-23 18:52:51.081116+08', '::ffff:localhost', NULL, '/validateCode', '{"account":"<EMAIL>","api":"user/sms-login","captcha":null}', false);
INSERT INTO public.user_error_validation VALUES (256, '2024-09-25 08:37:58.021003+08', '**************', NULL, '/validateCode', '{"phone":"<EMAIL>","api":"user/retrievePwd"}', false);
INSERT INTO public.user_error_validation VALUES (259, '2024-09-26 14:48:14.150622+08', '::ffff:localhost', NULL, '/login', '{"account":"<EMAIL>","pwd":"**********","account_properties":null,"email":"<EMAIL>"}', false);
INSERT INTO public.user_error_validation VALUES (260, '2024-09-26 14:48:21.996836+08', '::ffff:localhost', NULL, '/login', '{"account":"<EMAIL>","pwd":"**********","account_properties":null,"email":"<EMAIL>"}', false);
INSERT INTO public.user_error_validation VALUES (261, '2024-09-26 14:48:27.681342+08', '::ffff:localhost', NULL, '/login', '{"account":"<EMAIL>","pwd":"123","account_properties":null,"email":"<EMAIL>"}', false);
INSERT INTO public.user_error_validation VALUES (263, '2024-09-27 16:17:32.36051+08', '**************', NULL, '/login', '{"account":"<EMAIL>","pwd":"**********","account_properties":null,"email":"<EMAIL>"}', false);
INSERT INTO public.user_error_validation VALUES (252, '2024-09-23 18:48:20.399712+08', '::ffff:localhost', NULL, '/validateCode', '{"account":"<EMAIL>","api":"user/sms-login","captcha":null}', false);
INSERT INTO public.user_error_validation VALUES (264, '2024-10-08 15:42:45.45423+08', '::ffff:localhost', NULL, '/login', '{"account":"<EMAIL>","pwd":"**********","account_properties":null,"email":"<EMAIL>"}', false);
INSERT INTO public.user_error_validation VALUES (265, '2024-10-08 15:42:50.714293+08', '::ffff:localhost', NULL, '/login', '{"account":"<EMAIL>","pwd":"**********","account_properties":null,"email":"<EMAIL>"}', false);
INSERT INTO public.user_error_validation VALUES (266, '2024-10-11 09:28:59.835583+08', '::ffff:localhost', NULL, '/login', '{"phone":"***********","pwd":"**********","account_properties":null}', false);
INSERT INTO public.user_error_validation VALUES (267, '2024-10-11 09:31:11.340263+08', '::ffff:localhost', NULL, '/login', '{"account":"***********","pwd":"**********","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (268, '2024-10-11 09:31:16.371721+08', '::ffff:localhost', NULL, '/login', '{"account":"***********","pwd":"*********","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (269, '2024-10-16 19:14:42.965029+08', '**************', NULL, '/login', '{"account":"***********","pwd":"111111","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (270, '2024-10-16 19:14:50.241705+08', '**************', NULL, '/login', '{"account":"***********","pwd":"**********","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (271, '2024-10-16 19:14:53.452002+08', '**************', NULL, '/login', '{"account":"***********","pwd":"111222","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (272, '2024-10-17 16:32:10.68912+08', '::ffff:localhost', NULL, '/login', '{"phone":"***********","pwd":"*********","account_properties":null}', false);
INSERT INTO public.user_error_validation VALUES (273, '2024-10-17 17:13:01.108034+08', '*************', NULL, '/login', '{"account":"***********","pwd":"12wq!@WQ","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (274, '2024-10-18 20:54:10.565747+08', '::ffff:localhost', NULL, '/login', '{"account":"***********","pwd":"*********","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (275, '2024-10-18 20:54:13.620271+08', '::ffff:localhost', NULL, '/login', '{"account":"***********","pwd":"111222","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (276, '2024-10-22 09:43:57.302278+08', '**************', NULL, '/login', '{"account":"***********","pwd":"111111","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (277, '2024-10-22 09:44:02.604618+08', '**************', NULL, '/login', '{"account":"***********","pwd":"111111","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (278, '2024-10-22 09:44:02.682573+08', '**************', NULL, '/login', '{"account":"***********","pwd":"111111","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (279, '2024-10-22 09:44:02.92346+08', '**************', NULL, '/login', '{"account":"***********","pwd":"111111","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (280, '2024-10-22 09:44:03.145183+08', '**************', NULL, '/login', '{"account":"***********","pwd":"111111","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (292, '2024-10-30 19:18:03.498871+08', '*************', NULL, '/validateCode', '{"account":"***********","api":"user/sms-login","captcha":null}', true);
INSERT INTO public.user_error_validation VALUES (293, '2024-10-30 19:24:12.534315+08', '*************', NULL, '/validateCode', '{"account":"***********","api":"user/sms-login","captcha":null}', true);
INSERT INTO public.user_error_validation VALUES (294, '2024-10-30 20:49:05.247008+08', '*************', NULL, '/validateCode', '{"phone":"***********","api":"user/register"}', true);
INSERT INTO public.user_error_validation VALUES (295, '2024-10-30 21:24:05.659332+08', '*************', NULL, '/validateCode', '{"phone":"***********","api":"user/retrievePwd"}', true);
INSERT INTO public.user_error_validation VALUES (296, '2024-10-30 21:24:46.447024+08', '*************', NULL, '/login', '{"account":"***********","pwd":"12wq12","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (297, '2024-10-30 21:24:48.089669+08', '*************', NULL, '/login', '{"account":"***********","pwd":"12wq12","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (298, '2024-10-30 21:24:49.07138+08', '*************', NULL, '/login', '{"account":"***********","pwd":"12wq12","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (281, '2024-10-22 09:44:03.343205+08', '**************', NULL, '/login', '{"account":"***********","pwd":"111111","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (282, '2024-10-22 09:44:29.690086+08', '**************', NULL, '/login', '{"account":"***********","pwd":"*********","captcha":"vnvq","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (283, '2024-10-22 09:44:41.677673+08', '**************', NULL, '/login', '{"account":"***********","pwd":"*********","captcha":"rdkp","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (284, '2024-10-22 09:44:57.535711+08', '**************', NULL, '/login', '{"account":"***********","pwd":"**********","captcha":"qhaq","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (285, '2024-10-22 09:45:15.666888+08', '**************', NULL, '/login', '{"account":"***********","pwd":"*********","captcha":"anmc","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (286, '2024-10-22 09:45:34.360821+08', '**************', NULL, '/login', '{"account":"***********","pwd":"111222","captcha":"skli","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (287, '2024-10-22 09:45:51.809958+08', '**************', NULL, '/login', '{"account":"***********","pwd":"**********","captcha":"maym","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (288, '2024-10-28 10:56:25.479211+08', '::ffff:localhost', NULL, '/login', '{"account":"***********","pwd":"**********","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (289, '2024-10-28 17:17:01.952797+08', '::ffff:localhost', NULL, '/login', '{"phone":"***********","pwd":"112211","account_properties":null}', false);
INSERT INTO public.user_error_validation VALUES (290, '2024-10-30 12:42:48.820719+08', '***************', NULL, '/login', '{"account":"***********","pwd":"**********","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (291, '2024-10-30 13:04:07.040339+08', '::ffff:localhost', NULL, '/login', '{"account":"***********","pwd":"**********","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (299, '2024-10-30 21:24:49.556804+08', '*************', NULL, '/login', '{"account":"***********","pwd":"12wq12","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (300, '2024-10-30 21:29:16.682081+08', '*************', NULL, '/login', '{"account":"***********","pwd":"123123","captcha":"dpua","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (301, '2024-10-30 21:29:27.14379+08', '*************', NULL, '/login', '{"account":"***********","pwd":"123123","captcha":"ddwa","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (302, '2024-10-30 21:48:06.451564+08', '*************', NULL, '/validateCode', '{"phone":"***********","api":"user/retrievePwd"}', false);
INSERT INTO public.user_error_validation VALUES (303, '2024-10-30 21:59:01.899116+08', '*************', NULL, '/pwd', '{"pwd":"123123","new_pwd":"123123","new_pwd1":"123123"}', false);
INSERT INTO public.user_error_validation VALUES (304, '2024-10-30 22:56:49.371778+08', '*************', NULL, '/login', '{"account":"***********","pwd":"111","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (315, '2024-10-31 10:28:25.292781+08', '::ffff:localhost', NULL, '/login', '{"account":"***********","pwd":"111","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (316, '2024-10-31 11:13:03.851287+08', '***************', NULL, '/validateCode', '{"phone":"***********","api":"user/register"}', false);
INSERT INTO public.user_error_validation VALUES (317, '2024-10-31 14:04:07.037964+08', '**************', NULL, '/login', '{"account":"***********","pwd":"111","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (320, '2024-10-31 16:14:06.723301+08', '**************', NULL, '/login', '{"account":"***********","pwd":"111","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (321, '2024-10-31 16:14:06.900288+08', '**************', NULL, '/login', '{"account":"***********","pwd":"111","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (322, '2024-10-31 16:14:07.068944+08', '**************', NULL, '/login', '{"account":"***********","pwd":"111","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (323, '2024-10-31 16:14:07.237434+08', '**************', NULL, '/login', '{"account":"***********","pwd":"111","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (324, '2024-10-31 16:14:07.401021+08', '**************', NULL, '/login', '{"account":"***********","pwd":"111","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (325, '2024-10-31 16:14:07.570257+08', '**************', NULL, '/login', '{"account":"***********","pwd":"111","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (326, '2024-10-31 16:15:46.532394+08', '**************', NULL, '/login', '{"account":"***********","pwd":"111","captcha":"jahq","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (327, '2024-10-31 16:25:47.403092+08', '***************', NULL, '/login', '{"account":"***********","pwd":"********","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (328, '2024-10-31 16:25:55.353551+08', '***************', NULL, '/login', '{"account":"***********","pwd":"123456","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (331, '2024-10-31 18:55:08.73011+08', '14.123.253.8', NULL, '/login', '{"account":"***********","pwd":"123123","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (305, '2024-10-31 09:36:14.697639+08', '14.123.253.8', NULL, '/login', '{"account":"***********","pwd":"*********","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (306, '2024-10-31 09:36:14.878488+08', '14.123.253.8', NULL, '/login', '{"account":"***********","pwd":"*********","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (307, '2024-10-31 09:36:15.926828+08', '14.123.253.8', NULL, '/login', '{"account":"***********","pwd":"*********","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (308, '2024-10-31 09:36:16.208624+08', '14.123.253.8', NULL, '/login', '{"account":"***********","pwd":"*********","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (309, '2024-10-31 09:36:17.204552+08', '14.123.253.8', NULL, '/login', '{"account":"***********","pwd":"*********","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (310, '2024-10-31 09:36:17.838087+08', '14.123.253.8', NULL, '/login', '{"account":"***********","pwd":"*********","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (311, '2024-10-31 09:37:01.792246+08', '14.123.253.8', NULL, '/login', '{"account":"***********","pwd":"*********","captcha":"aswh","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (312, '2024-10-31 09:57:15.827605+08', '14.123.253.8', NULL, '/login', '{"account":"***********","pwd":"111111","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (313, '2024-10-31 10:25:51.940719+08', '14.123.253.8', NULL, '/login', '{"account":"***********","pwd":"111111","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (314, '2024-10-31 10:25:52.899339+08', '14.123.253.8', NULL, '/login', '{"account":"***********","pwd":"111111","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (318, '2024-10-31 14:06:28.301354+08', '14.123.253.8', NULL, '/login', '{"account":"***********","pwd":"111111","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (319, '2024-10-31 14:06:38.738277+08', '14.123.253.8', NULL, '/login', '{"account":"***********","pwd":"111111","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (329, '2024-10-31 16:27:57.074548+08', '14.123.253.8', NULL, '/login', '{"account":"***********","pwd":"111111","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (330, '2024-10-31 18:55:08.424333+08', '14.123.253.8', NULL, '/login', '{"account":"***********","pwd":"123123","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (332, '2024-10-31 18:55:09.535579+08', '14.123.253.8', NULL, '/login', '{"account":"***********","pwd":"123123","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (333, '2024-10-31 18:55:09.842045+08', '14.123.253.8', NULL, '/login', '{"account":"***********","pwd":"123123","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (334, '2024-10-31 18:55:10.207755+08', '14.123.253.8', NULL, '/login', '{"account":"***********","pwd":"123123","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (335, '2024-10-31 18:55:10.73337+08', '14.123.253.8', NULL, '/login', '{"account":"***********","pwd":"123123","account_properties":null,"phone":"***********"}', true);
INSERT INTO public.user_error_validation VALUES (336, '2024-10-31 19:02:01.23205+08', '14.123.253.8', NULL, '/validateCode', '{"phone":"***********","api":"user/sms-login","captcha":null}', false);
INSERT INTO public.user_error_validation VALUES (337, '2024-10-31 19:03:27.610267+08', '14.123.253.8', NULL, '/validateCode', '{"phone":"***********","api":"user/sms-login","captcha":null}', false);
INSERT INTO public.user_error_validation VALUES (338, '2024-10-31 19:24:38.156085+08', '14.123.253.8', NULL, '/login', '{"account":"***********","pwd":"111111","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (339, '2024-11-01 13:11:51.344456+08', '***************', NULL, '/login', '{"account":"***********","pwd":"lj10q681","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (340, '2024-11-01 16:05:23.065682+08', '*************', NULL, '/validateCode', '{"phone":"***********","api":"user/sms-login","captcha":null}', false);
INSERT INTO public.user_error_validation VALUES (341, '2024-11-01 16:09:13.691607+08', '*************', NULL, '/validateCode', '{"phone":"***********","api":"user/sms-login","captcha":null}', false);
INSERT INTO public.user_error_validation VALUES (342, '2024-11-01 16:10:16.59986+08', '*************', NULL, '/validateCode', '{"phone":"***********","api":"user/register"}', false);
INSERT INTO public.user_error_validation VALUES (343, '2024-11-04 14:13:04.384731+08', '::ffff:localhost', NULL, '/login', '{"account":"***********","pwd":"111","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (344, '2024-11-04 23:34:10.662008+08', '*************', NULL, '/validateCode', '{"phone":"***********","api":"user/retrievePwd"}', false);
INSERT INTO public.user_error_validation VALUES (345, '2024-11-05 09:18:21.695874+08', '*************', NULL, '/login', '{"account":"***********","pwd":"**********","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (346, '2024-11-05 09:20:19.223876+08', '*************', NULL, '/login', '{"account":"***********","pwd":"**********","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (347, '2024-11-06 21:00:00.540598+08', '***************', NULL, '/validateCode', '{"phone":"***********","api":"user/register"}', false);
INSERT INTO public.user_error_validation VALUES (348, '2024-11-06 21:13:15.228584+08', '***************', NULL, '/login', '{"account":"***********","pwd":"zhang\\","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (349, '2024-11-06 21:19:04.841661+08', '***************', NULL, '/login', '{"account":"***********","pwd":"zhabg","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (350, '2024-11-07 09:07:06.672949+08', '::ffff:localhost', NULL, '/login', '{"account":"***********","pwd":"**********","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (351, '2024-11-07 15:49:10.246033+08', '*************', NULL, '/validateCode', '{"phone":"***********","api":"user/register"}', false);
INSERT INTO public.user_error_validation VALUES (352, '2024-11-08 10:41:23.400416+08', '*************', NULL, '/login', '{"account":"***********","pwd":"**********","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (353, '2024-11-08 18:31:41.481758+08', '*************', NULL, '/login', '{"account":"***********","pwd":"**********","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (354, '2024-11-12 09:16:54.14701+08', '************', NULL, '/login', '{"account":"***********","pwd":"**********","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (355, '2024-11-12 16:05:08.460141+08', '************', NULL, '/login', '{"account":"***********","pwd":"********","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (356, '2024-11-12 16:12:50.164525+08', '::ffff:localhost', NULL, '/login', '{"account":"***********","pwd":"********","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (357, '2024-11-12 16:51:45.230604+08', '**************', NULL, '/login', '{"account":"***********","pwd":"111111","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (358, '2024-11-13 09:59:32.645915+08', '::ffff:localhost', NULL, '/login', '{"account":"***********","pwd":"112211","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (359, '2024-11-13 15:15:10.322361+08', '**************', NULL, '/login', '{"account":"***********","pwd":"********","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (360, '2024-11-13 20:36:42.250315+08', '***************', NULL, '/login', '{"account":"***********","pwd":"zhang","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (361, '2024-11-13 20:36:51.108722+08', '***************', NULL, '/login', '{"account":"***********","pwd":"zhang","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (362, '2024-11-19 13:22:56.467898+08', '**************', NULL, '/login', '{"account":"***********","pwd":"101681","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (363, '2024-11-20 11:30:55.632495+08', '::ffff:localhost', NULL, '/login', '{"account":"***********","pwd":"111","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (364, '2024-11-20 12:26:30.016112+08', '***************', NULL, '/validateCode', '{"phone":"***********","api":"user/register"}', false);
INSERT INTO public.user_error_validation VALUES (365, '2024-11-20 23:22:28.130677+08', '***************', NULL, '/login', '{"account":"***********","pwd":"zhabg","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (366, '2024-11-22 15:03:42.917882+08', '::ffff:localhost', NULL, '/login', '{"phone":"***********","pwd":"111","account_properties":null}', false);
INSERT INTO public.user_error_validation VALUES (367, '2024-12-06 16:51:14.259425+08', '***************', NULL, '/login', '{"account":"***********","pwd":"zhabg","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (368, '2024-12-09 10:30:02.833725+08', '**************', NULL, '/validateCode', '{"phone":"***********","api":"user/register"}', false);
INSERT INTO public.user_error_validation VALUES (369, '2024-12-10 13:34:06.094827+08', '::ffff:localhost', NULL, '/login', '{"account":"***********","pwd":"**********","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (370, '2024-12-11 10:11:55.096057+08', '*************', NULL, '/login', '{"account":"***********","pwd":"12wq!@WQ","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (371, '2024-12-11 15:28:18.67713+08', '***************', NULL, '/validateCode', '{"phone":"***********","api":"user/sms-login","captcha":null}', false);
INSERT INTO public.user_error_validation VALUES (372, '2024-12-11 15:29:38.48318+08', '***************', NULL, '/validateCode', '{"phone":"***********","api":"user/register"}', false);
INSERT INTO public.user_error_validation VALUES (373, '2024-12-11 17:21:38.931065+08', '*************', NULL, '/login', '{"account":"***********","pwd":"111","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (374, '2024-12-11 17:21:41.436619+08', '*************', NULL, '/login', '{"account":"***********","pwd":"111","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (375, '2024-12-11 17:21:41.713732+08', '*************', NULL, '/login', '{"account":"***********","pwd":"111","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (376, '2024-12-11 17:21:42.070852+08', '*************', NULL, '/login', '{"account":"***********","pwd":"111","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (377, '2024-12-11 17:21:42.400726+08', '*************', NULL, '/login', '{"account":"***********","pwd":"111","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (378, '2024-12-11 17:21:42.753826+08', '*************', NULL, '/login', '{"account":"***********","pwd":"111","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (379, '2024-12-12 10:38:26.962463+08', '27.10.115.23', NULL, '/validateCode', '{"phone":"***********","api":"user/sms-login","captcha":null}', true);
INSERT INTO public.user_error_validation VALUES (380, '2024-12-12 10:38:41.663653+08', '27.10.115.23', NULL, '/sms-login', '{"phone":"***********","code":"1122","real_data":{},"captcha":null}', true);
INSERT INTO public.user_error_validation VALUES (381, '2024-12-12 10:38:41.985996+08', '27.10.115.23', NULL, '/sms-login', '{"phone":"***********","code":"1122","real_data":{},"captcha":null}', true);
INSERT INTO public.user_error_validation VALUES (382, '2024-12-12 10:38:42.336535+08', '27.10.115.23', NULL, '/sms-login', '{"phone":"***********","code":"1122","real_data":{},"captcha":null}', true);
INSERT INTO public.user_error_validation VALUES (383, '2024-12-12 10:39:31.925151+08', '27.10.115.23', NULL, '/validateCode', '{"phone":"***********","api":"user/sms-login","captcha":null}', true);
INSERT INTO public.user_error_validation VALUES (384, '2024-12-12 10:39:33.54278+08', '27.10.115.23', NULL, '/sms-login', '{"phone":"***********","code":"1122","real_data":{},"captcha":null}', true);
INSERT INTO public.user_error_validation VALUES (385, '2024-12-16 20:55:24.146503+08', '***************', NULL, '/login', '{"account":"***********","pwd":"zhabg","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (386, '2024-12-24 09:31:14.427709+08', '***************', NULL, '/login', '{"account":"***********","pwd":"zhabg","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (387, '2024-12-24 09:31:17.915798+08', '***************', NULL, '/login', '{"account":"***********","pwd":"zhabg","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (388, '2024-12-24 09:31:21.378428+08', '***************', NULL, '/login', '{"account":"***********","pwd":"zhabg","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (389, '2024-12-24 10:15:03.44733+08', '***************', NULL, '/login', '{"account":"***********","pwd":"zhabg","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (390, '2024-12-24 12:02:11.431831+08', '14.123.254.141', NULL, '/login', '{"account":"***********","pwd":"111111","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (391, '2024-12-24 20:46:08.571678+08', '***************', NULL, '/login', '{"account":"***********","pwd":"zhabg","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (392, '2024-12-24 21:43:46.798651+08', '***************', NULL, '/login', '{"account":"***********","pwd":"zgang","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (393, '2024-12-25 11:04:22.025488+08', '***************', NULL, '/login', '{"account":"***********","pwd":"zhag","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (394, '2024-12-25 14:02:41.079279+08', '***************', NULL, '/login', '{"account":"***********","pwd":"zhabg","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (395, '2024-12-25 16:39:01.974272+08', '***************', NULL, '/login', '{"account":"***********","pwd":"zhabg","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (396, '2024-12-25 19:33:55.147008+08', '***************', NULL, '/login', '{"account":"***********","pwd":"zhag","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (397, '2024-12-25 19:33:57.797653+08', '***************', NULL, '/login', '{"account":"***********","pwd":"zhagg","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (398, '2024-12-25 21:26:28.995091+08', '***************', NULL, '/login', '{"account":"***********","pwd":"zhabg","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (399, '2024-12-25 21:34:00.383425+08', '***************', NULL, '/login', '{"account":"***********","pwd":"zhabg","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (400, '2025-01-16 15:10:41.713647+08', '************', NULL, '/validateCode', '{"phone":"***********","api":"user/sms-login","captcha":null}', false);
INSERT INTO public.user_error_validation VALUES (401, '2025-02-12 16:30:47.929791+08', '::ffff:localhost', NULL, '/login', '{"account":"***********","pwd":"**********","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (402, '2025-02-12 16:30:52.124457+08', '::ffff:localhost', NULL, '/login', '{"account":"***********","pwd":"**********","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (403, '2025-02-19 17:35:32.536451+08', '**************', NULL, '/login', '{"account":"***********","pwd":"111111","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (404, '2025-02-19 17:35:36.538098+08', '**************', NULL, '/login', '{"account":"***********","pwd":"1111","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (405, '2025-03-04 17:38:23.95839+08', '**************', NULL, '/login', '{"account":"***********","pwd":"123","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (406, '2025-03-05 14:25:55.619077+08', '*************', NULL, '/login', '{"account":"***********","pwd":"**********","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (407, '2025-03-06 10:51:21.949379+08', '*************', NULL, '/login', '{"account":"***********","pwd":"111111","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (408, '2025-03-06 11:52:48.82298+08', '**************', NULL, '/login', '{"account":"***********","pwd":"beijing66nao","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (409, '2025-03-06 11:52:58.205991+08', '**************', NULL, '/login', '{"account":"***********","pwd":"beijing66nao","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (410, '2025-03-06 11:53:30.202703+08', '**************', NULL, '/login', '{"account":"***********","pwd":"beijing66nao","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (411, '2025-03-06 11:54:05.152368+08', '**************', NULL, '/login', '{"account":"***********","pwd":"beijing66nao","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (412, '2025-03-06 11:54:10.707473+08', '**************', NULL, '/login', '{"account":"***********","pwd":"beijing66nao","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (413, '2025-03-06 14:55:00.002394+08', '*************', NULL, '/login', '{"account":"***********","pwd":"**********","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (414, '2025-03-06 15:53:14.715001+08', '*************', NULL, '/login', '{"account":"***********","pwd":"**********","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (415, '2025-03-07 18:35:55.377651+08', '::ffff:localhost', NULL, '/validateCode', '{"account":"***********","api":"user/sms-login","captcha":null}', false);
INSERT INTO public.user_error_validation VALUES (416, '2025-03-07 18:38:07.31344+08', '************', NULL, '/validateCode', '{"account":"***********","api":"user/sms-login","captcha":null}', false);
INSERT INTO public.user_error_validation VALUES (417, '2025-03-07 18:45:48.76815+08', '::ffff:localhost', NULL, '/login', '{"account":"***********","pwd":"********","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (418, '2025-03-07 21:35:59.781676+08', '::ffff:localhost', NULL, '/login', '{"account":"***********","pwd":"********s","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (419, '2025-03-07 21:59:43.659376+08', '::ffff:localhost', NULL, '/login', '{"account":"***********","pwd":"********","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (420, '2025-03-11 09:57:34.642578+08', '*************', NULL, '/login', '{"account":"***********","pwd":"**********","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (421, '2025-03-11 10:00:04.309699+08', '::ffff:localhost', NULL, '/login', '{"account":"***********","pwd":"**********","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (422, '2025-03-11 16:21:06.60972+08', '*************', NULL, '/validateCode', '{"account":"***********","api":"user/sms-login","captcha":null}', false);
INSERT INTO public.user_error_validation VALUES (423, '2025-03-11 16:21:28.366108+08', '*************', NULL, '/validateCode', '{"account":"***********","api":"user/sms-login","captcha":null}', false);
INSERT INTO public.user_error_validation VALUES (424, '2025-03-12 17:37:08.48074+08', '**************', NULL, '/validateCode', '{"account":"***********","api":"user/sms-login","captcha":null}', false);
INSERT INTO public.user_error_validation VALUES (425, '2025-03-12 17:38:31.51843+08', '**************', NULL, '/validateCode', '{"phone":"***********","api":"user/retrievePwd"}', false);
INSERT INTO public.user_error_validation VALUES (426, '2025-03-13 13:43:49.128298+08', '**************', NULL, '/login', '{"account":"***********","pwd":"111111","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (427, '2025-03-16 21:27:09.879036+08', '**************', NULL, '/login', '{"account":"***********","pwd":"111222","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (428, '2025-03-16 21:27:12.488928+08', '**************', NULL, '/login', '{"account":"***********","pwd":"*********","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (429, '2025-03-18 09:30:50.503133+08', '*************', NULL, '/login', '{"account":"***********","pwd":"beijing66nao","account_properties":null,"phone":"***********"}', false);
INSERT INTO public.user_error_validation VALUES (430, '2025-03-21 15:11:03.906489+08', '114.243.118.254', NULL, '/validateCode', '{"phone":"***********","api":"user/retrievePwd"}', false);


--
-- TOC entry 3292 (class 0 OID 822675)
-- Dependencies: 219
-- Data for Name: user_message; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- TOC entry 3294 (class 0 OID 822686)
-- Dependencies: 221
-- Data for Name: user_validate_code; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- TOC entry 3308 (class 0 OID 0)
-- Dependencies: 207
-- Name: captcha__id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.captcha__id_seq', 183, true);


--
-- TOC entry 3309 (class 0 OID 0)
-- Dependencies: 209
-- Name: error_validation__id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.error_validation__id_seq', 430, true);


--
-- TOC entry 3310 (class 0 OID 0)
-- Dependencies: 211
-- Name: log__id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.log__id_seq', 1, false);


--
-- TOC entry 3311 (class 0 OID 0)
-- Dependencies: 220
-- Name: validate_code__id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.validate_code__id_seq', 192, true);


--
-- TOC entry 3119 (class 2606 OID 823017)
-- Name: access_feature access_feature_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.access_feature
    ADD CONSTRAINT access_feature_pkey PRIMARY KEY (id);


--
-- TOC entry 3121 (class 2606 OID 823019)
-- Name: access_role access_role_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.access_role
    ADD CONSTRAINT access_role_pkey PRIMARY KEY (id);


--
-- TOC entry 3143 (class 2606 OID 823100)
-- Name: case_item case_item_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_item
    ADD CONSTRAINT case_item_pkey PRIMARY KEY (id);


--
-- TOC entry 3147 (class 2606 OID 823135)
-- Name: case_message case_message_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.case_message
    ADD CONSTRAINT case_message_pkey PRIMARY KEY (id);


--
-- TOC entry 3123 (class 2606 OID 823021)
-- Name: category category_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.category
    ADD CONSTRAINT category_pkey PRIMARY KEY (id);


--
-- TOC entry 3125 (class 2606 OID 823031)
-- Name: label label_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.label
    ADD CONSTRAINT label_pkey PRIMARY KEY (id);


--
-- TOC entry 3145 (class 2606 OID 823124)
-- Name: member member_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.member
    ADD CONSTRAINT member_pkey PRIMARY KEY (id);


--
-- TOC entry 3128 (class 2606 OID 823033)
-- Name: no_trouble_log_item no_trouble_log_item_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.no_trouble_log_item
    ADD CONSTRAINT no_trouble_log_item_pkey PRIMARY KEY (id);


--
-- TOC entry 3130 (class 2606 OID 823035)
-- Name: no_trouble_trash_item no_trouble_trash_item_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.no_trouble_trash_item
    ADD CONSTRAINT no_trouble_trash_item_pkey PRIMARY KEY (id);


--
-- TOC entry 3132 (class 2606 OID 823037)
-- Name: oss_object oss_object_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.oss_object
    ADD CONSTRAINT oss_object_pkey PRIMARY KEY (id);


--
-- TOC entry 3135 (class 2606 OID 823041)
-- Name: settings settings_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.settings
    ADD CONSTRAINT settings_pkey PRIMARY KEY (id);


--
-- TOC entry 3141 (class 2606 OID 823043)
-- Name: user_message user_message_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_message
    ADD CONSTRAINT user_message_pkey PRIMARY KEY (id);


--
-- TOC entry 3139 (class 2606 OID 823045)
-- Name: user_account user_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_account
    ADD CONSTRAINT user_pkey PRIMARY KEY (id);


--
-- TOC entry 3126 (class 1259 OID 823049)
-- Name: no_trouble_log_item_expr_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX no_trouble_log_item_expr_idx ON public.no_trouble_log_item USING btree (((profile -> 'id'::text)));


--
-- TOC entry 3133 (class 1259 OID 823050)
-- Name: settings_deleted_deleted_at_expr_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX settings_deleted_deleted_at_expr_idx ON public.settings USING btree (deleted, deleted_at, ((profile -> 'name'::text)));


--
-- TOC entry 3136 (class 1259 OID 823051)
-- Name: user_account_email_deleted_deleted_at_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX user_account_email_deleted_deleted_at_idx ON public.user_account USING btree (email, deleted, deleted_at);


--
-- TOC entry 3137 (class 1259 OID 823052)
-- Name: user_account_phone_phone_country_calling_code_deleted_deleted_a; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX user_account_phone_phone_country_calling_code_deleted_deleted_a ON public.user_account USING btree (phone, phone_country_calling_code, deleted, deleted_at);


--
-- TOC entry 3303 (class 0 OID 0)
-- Dependencies: 10
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: postgres
--

REVOKE USAGE ON SCHEMA public FROM PUBLIC;
GRANT ALL ON SCHEMA public TO PUBLIC;


-- Completed on 2025-04-24 15:48:53 CST

--
-- PostgreSQL database dump complete
--

