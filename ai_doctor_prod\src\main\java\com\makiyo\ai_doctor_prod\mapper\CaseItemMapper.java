package com.makiyo.ai_doctor_prod.mapper;

import com.makiyo.ai_doctor_prod.entity.CaseItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.ZonedDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface CaseItemMapper {
    int insert(CaseItem record);

    int insertSelective(CaseItem record);

    /**
     * 根据 ID 查询 CaseItem
     * @param id CaseItem 的 ID
     * @return CaseItem 对象，如果未找到则返回 null
     */
    CaseItem findById(@Param("id") String id);

    /**
     * 查询所有 CaseItem 记录
     * @return CaseItem 对象列表
     */
    List<CaseItem> findAll(@Param("startDate") ZonedDateTime startDate, @Param("endDate") ZonedDateTime endDate);

    List<CaseItem> findAllWithFiltersAndPagination(
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("limit") int limit,
            @Param("offset") long offset
    );

    long countAllWithFilters(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 测试数据库连接
     * @return 返回 1 表示连接成功
     */
    int testConnection();

    /**
     * 根据 ID 更新 interaction_history 字段。
     * @param id CaseItem 的 ID
     * @param interactionHistory 新的 interactionHistory 内容 (通常是 Map 或 JSON 字符串，具体取决于 JDBC 驱动和配置)
     * @return 影响的行数
     */
    int updateInteractionHistoryById(@Param("id") String id, @Param("interactionHistory") Object interactionHistory);
}