<template>
    <div class="ocr-image-viewer-page">
      <h1>OCR识别数据总览</h1>
      <a-spin :spinning="loading">
        <div v-if="!loading && allOcrData.length === 0" class="no-results">
          <a-empty description="暂无任何OCR数据" />
        </div>
        <div v-else>
          <a-list
            :data-source="allOcrData"
            item-layout="vertical"
            :grid="{ gutter: 24, xs: 1, sm: 1, md: 2, lg: 2, xl: 2, xxl: 3 }"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-card class="ocr-card" :title="item.ocrResult.originalFilename || '未命名图片'">
                  <template #extra>
                    <a-tag v-if="item.ocrResult.caseId" color="blue">案例ID: {{ item.ocrResult.caseId }}</a-tag>
                    <a-tag v-else color="default">独立识别</a-tag>
                  </template>
                  <div class="ocr-item-content">
                    <div class="image-container" v-if="item.imageInfo && item.imageInfo.imageUrl">
                      <img :src="item.imageInfo.imageUrl" :alt="item.ocrResult.originalFilename" @click="showFullImage(item.imageInfo.imageUrl)" />
                    </div>
                    <div v-else class="no-image">
                      <a-empty description="无图片" />
                    </div>
                    
                    <div class="ocr-results-container" v-if="item.ocrResult">
                      <a-collapse v-model:activeKey="activeKey" accordion>
                        <a-collapse-panel key="1" header="原始识别内容">
                          <div v-if="typeof item.ocrResult.content === 'object'">
                            <pre>{{ JSON.stringify(item.ocrResult.content, null, 2) }}</pre>
                          </div>
                          <div v-else class="raw-content">
                            {{ item.ocrResult.content }}
                          </div>
                        </a-collapse-panel>
                        
                        <a-collapse-panel key="2" header="解析数据" v-if="hasStructuredData(item.ocrResult)">
                          <div class="results-table" v-if="getParsedResults(item.ocrResult) && getParsedResults(item.ocrResult).length > 0">
                            <a-table :columns="resultColumns" :dataSource="getParsedResults(item.ocrResult)" size="small" :pagination="false">
                              <template #bodyCell="{ column, record }">
                                <template v-if="column.key === 'itemName'">
                                  {{ record.项目名称 || record.检查 || '未知项目' }}
                                </template>
                                <template v-else-if="column.key === 'status'">
                                  <a-tag :color="getStatusColor(record.结果状态)">{{ record.结果状态 || '未知' }}</a-tag>
                                </template>
                                <template v-else-if="column.key === 'result'">
                                  <span :class="record.结果状态 === '异常' || record.结果状态 === '偏高' || record.结果状态 === '偏低' ? 'abnormal-result' : ''">
                                    {{ record.结果 }} {{ record.单位 || '' }}
                                  </span>
                                </template>
                              </template>
                            </a-table>
                          </div>
                          <a-empty v-else description="无结构化数据" />
                        </a-collapse-panel>
                        
                        <a-collapse-panel key="3" header="元数据">
                          <div class="metadata">
                            <p><strong>处理时间:</strong> {{ item.ocrResult.processingTimeMs }}ms</p>
                            <p><strong>创建时间:</strong> {{ formatDateTime(item.ocrResult.createdAt) }}</p>
                            <p><strong>图片ID:</strong> {{ item.ocrResult.imageId }}</p>
                            <p><strong>原始文件名:</strong> {{ item.ocrResult.originalFilename }}</p>
                          </div>
                        </a-collapse-panel>
                      </a-collapse>
                    </div>
                  </div>
                </a-card>
              </a-list-item>
            </template>
          </a-list>
          <div class="pagination-container">
            <a-pagination
              v-model:current="pagination.current"
              :total="pagination.total"
              :page-size="pagination.pageSize"
              show-size-changer
              @change="handlePageChange"
              @showSizeChange="handlePageChange"
            />
          </div>
        </div>
      </a-spin>
      
      <!-- 全屏图片模态框 -->
      <a-modal v-model:open="imageModalVisible" :footer="null" :width="1000" 
               title="OCR 图片查看" :destroyOnClose="true">
        <div class="full-image-container">
          <img :src="previewImageUrl" alt="OCR Image Full Size" class="fullscreen-image" />
        </div>
      </a-modal>
    </div>
</template>
  
<script>
import { defineComponent, ref, onMounted, reactive } from 'vue';
import { message } from 'ant-design-vue';
import request from '@/utils/request';
  
export default defineComponent({
  name: 'OcrImageViewer',
  setup() {
    const allOcrData = ref([]);
    const loading = ref(false);
    const activeKey = ref('1');
    const imageModalVisible = ref(false);
    const previewImageUrl = ref('');

    const pagination = reactive({
      current: 1,
      pageSize: 6,
      total: 0,
    });

    const resultColumns = [
      {
        title: '检查项目',
        dataIndex: 'itemName',
        key: 'itemName',
      },
      {
        title: '结果',
        dataIndex: 'result',
        key: 'result'
      },
      {
        title: '参考范围',
        dataIndex: '参考范围',
        key: 'range'
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status'
      }
    ];

    const loadAllOcrData = async (page = 1, size = 6) => {
      loading.value = true;
      try {
        const res = await request({
          url: '/images/all-ocr-results',
          method: 'get',
          params: { page, size }
        });
        if (res && res.list) {
          allOcrData.value = res.list;
          pagination.total = res.total;
          pagination.current = res.pageNum;
          pagination.pageSize = res.pageSize;
        } else {
          message.error('加载OCR数据失败');
          allOcrData.value = [];
          pagination.total = 0;
        }
      } catch (error) {
        console.error('获取全部OCR数据失败：', error);
        message.error('获取全部OCR数据时发生错误');
        allOcrData.value = [];
        pagination.total = 0;
      } finally {
        loading.value = false;
      }
    };

    const handlePageChange = (page, pageSize) => {
      pagination.current = page;
      pagination.pageSize = pageSize;
      loadAllOcrData(page, pageSize);
    };

    onMounted(() => {
      loadAllOcrData(pagination.current, pagination.pageSize);
    });
      
    const formatDateTime = (dateStr) => {
      if (!dateStr) return '-';
      try {
        const date = new Date(dateStr);
        return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())} ${padZero(date.getHours())}:${padZero(date.getMinutes())}:${padZero(date.getSeconds())}`;
      } catch (e) { return dateStr; }
    };
      
    const padZero = (num) => (num < 10 ? `0${num}` : num);
      
    const getStatusColor = (status) => {
      if (!status) return 'default';
      switch(status) {
        case '正常': return 'green';
        case '异常': return 'red';
        case '偏高': return 'orange';
        case '偏低': return 'blue';
        default: return 'default';
      }
    };
      
    const hasStructuredData = (ocrResult) => {
      if (!ocrResult || !ocrResult.content) return false;
      const content = ocrResult.content;
      if (typeof content !== 'object') return false;
      if (content.content && Array.isArray(content.content)) return true;
      if (content.检查结果 && Array.isArray(content.检查结果)) return true;
      return false;
    };
      
    const getParsedResults = (ocrResult) => {
      if (!ocrResult || !ocrResult.content) return [];
      const content = ocrResult.content;
      if (typeof content !== 'object') return [];
      
      if (content.content && Array.isArray(content.content)) {
        for (const item of content.content) {
          if (item && item.检查结果 && Array.isArray(item.检查结果)) {
            return item.检查结果.map((result, index) => ({
              ...result,
              key: `result-${index}`
            }));
          }
        }
      }
      
      if (content.检查结果 && Array.isArray(content.检查结果)) {
        return content.检查结果.map((result, index) => ({
          ...result,
          key: `result-${index}`
        }));
      }
      
      return [];
    };
      
    const showFullImage = (imageUrl) => {
      previewImageUrl.value = imageUrl;
      imageModalVisible.value = true;
    };
      
    return {
      allOcrData,
      loading,
      activeKey,
      resultColumns,
      formatDateTime,
      getStatusColor,
      hasStructuredData,
      getParsedResults,
      imageModalVisible,
      previewImageUrl,
      showFullImage,
      pagination,
      handlePageChange
    };
  }
});
</script>
  
<style scoped>
.ocr-image-viewer-page {
  padding: 24px;
}

.ocr-image-viewer-page h1 {
  font-size: 24px;
  margin-bottom: 24px;
}

.ocr-card {
  transition: box-shadow 0.3s, border-color 0.3s;
}

.ocr-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  border-color: #1890ff;
}

.ocr-item-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 250px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 8px;
  background-color: #f9f9f9;
  overflow: hidden;
}
  
.image-container img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  cursor: pointer;
  transition: transform 0.3s;
}

.image-container img:hover {
  transform: scale(1.05);
}
  
.ocr-results-container {
  margin-top: 10px;
}
  
.raw-content {
  white-space: pre-wrap;
  font-family: monospace;
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
}
  
pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
}
  
.metadata {
  font-size: 14px;
  line-height: 1.6;
}
  
.metadata p {
  margin-bottom: 8px;
}

.metadata p:last-child {
  margin-bottom: 0;
}
  
.no-image, .no-results {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  background-color: #fafafa;
}
  
.loading-results {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200px;
}
  
.loading-results span {
  margin-top: 16px;
  color: #666;
}
  
.abnormal-result {
  color: #f5222d;
  font-weight: 600;
}
  
.full-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: auto;
}
  
.fullscreen-image {
  max-width: 100%;
}
  
.results-table {
  margin-top: 16px;
  max-height: 350px;
  overflow-y: auto;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}
</style>