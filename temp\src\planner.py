"""
Planner module for the AI Doctor system.
"""
import os
import json
import requests
import re  # 添加re模块的全局导入
from src.logger import get_logger
from src.llm_client import LLMClient
from src.config import (
    OPENAI_API_KEY, OPENAI_MODEL, OPENAI_TEMPERATURE,
    VOLCANO_API_KEY, VOLCANO_API_BASE, VOLCANO_MODEL, VOLCANO_TEMPERATURE_PLANNER, VOLCANO_TEMPERATURE_EXECUTOR,
    CLAUDE_API_KEY, CLAUDE_MODEL, CLAUDE_TEMPERATURE
)

import torch
from transformers import BertTokenizer, BertModel
from qdrant_client import QdrantClient

from src.get_guide import get_guide

from src.prompts import (
    _user_prompt_treatment_rag, _sys_prompt_treatment_rag,
    _sys_prompt_strategy, _user_prompt_strategy,
    _sys_prompt_update_observation, _sys_prompt_condition_only,
    _sys_prompt_final_diagnosis,
    _user_prompt_verify_diagnosis, _sys_prompt_verify_diagnosis
)

logger = get_logger("planner")

def clean_r1_response(response):
    """
    清理R1模型的响应，移除推理过程部分
    
    参数:
        response (str): R1模型的原始响应
        
    返回:
        str: 清理后的响应内容（仅包含实际回答部分）
    """
    if not isinstance(response, str):
        return response
        
    # 处理R1模型的特殊格式：推理内容 + </think> + 实际回答
    if '</think>' in response:
        # R1模型格式：推理内容在</think>之前，实际回答在</think>之后
        parts = response.split('</think>', 1)
        if len(parts) == 2:
            cleaned_response = parts[1].strip()
            logger.debug(f"从R1模型响应中移除推理内容，清理后长度：{len(cleaned_response)}")
            return cleaned_response
        else:
            logger.warning("无法正确分割R1模型的推理内容")
            return response
    
    # 如果没有</think>标签，返回原始响应
    return response

class Planner:
    """
    Planner class that generates strategies for the Executor to follow.
    Maintains strategy, reasoning, and feedback histories.
    """

    def __init__(self, max_rounds=10, init_strategy=None, init_reasoning=None, interaction_history=None, enable_cot_retrieval=False):
        """
        初始化 Planner 类。

        参数:
            max_rounds (int): 诊断的最大轮次数。
            init_strategy (str): 初始策略。
            init_reasoning (str): 初始推理。
            interaction_history (InteractionHistory): 用于存储和恢复状态的对象。
            enable_cot_retrieval (bool): 是否启用COT召回功能，默认为False。
        """

        self.interaction_history = interaction_history  # 用于存储和恢复状态的对象
        self.max_rounds = max_rounds  # 诊断的最大轮次数
        self.enable_cot_retrieval = enable_cot_retrieval  # 是否启用COT召回
        self.quick_demo_mode = False  # 添加快速演示模式标志
        logger.info(f"Planner initialized with max_rounds={max_rounds}, enable_cot_retrieval={enable_cot_retrieval}")

        # 初始化COT检索相关组件
        if self.enable_cot_retrieval:
            try:
                self._init_cot_retrieval()
            except Exception as e:
                logger.warning(f"无法初始化COT检索组件: {e}")
                self.cot_retrieval_available = False
            else:
                self.cot_retrieval_available = True
        else:
            self.cot_retrieval_available = False
            logger.info("COT检索功能已禁用")

        if init_strategy and init_reasoning:
            self.interaction_history.add_cot_entry(init_strategy, init_reasoning, "", [])  # 添加初始策略和推理到 InteractionHistory
            logger.debug(f"Initial strategy: {init_strategy[:100]}...")
            logger.debug(f"Initial reasoning: {init_reasoning[:100]}...")
            
    def _init_cot_retrieval(self):
        """初始化用于COT检索的模型和客户端"""
        try:
            # 加载预训练模型
            model_path = r"BAAI/bge-large-zh-v1.5"
            self.tokenizer = BertTokenizer.from_pretrained(model_path)
            self.embedding_model = BertModel.from_pretrained(model_path)
            self.embedding_model.eval()  # 设置模型为评估模式
            
            import os
            QDRANT_URL = "https://4c32b9ec-36a6-4290-9dbe-7e455e14dc5b.eu-west-2-0.aws.cloud.qdrant.io:6333"
            QDRANT_API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3MiOiJtIn0.g_G0ko_m5qFdwL6wvxZEcygPftfUak3qZRDEyBGMNbQ"
            os.environ['NO_PROXY'] = QDRANT_URL

            # 连接Qdrant客户端
            self.qdrant_client = QdrantClient(
                url=QDRANT_URL,
                api_key=QDRANT_API_KEY,
                timeout=600  # 超时时间（秒）
            )
            
            # 设置集合名称
            self.collection_name = "COT"
            
            logger.info("COT检索组件初始化成功")
        except Exception as e:
            logger.error(f"初始化COT检索组件失败: {e}")
            raise

    def _extract_symptoms(self, observation):
        """从观察文本中提取主诉和现病史作为查询"""
        # 尝试提取主诉
        chief_complaint = ""
        chief_pattern = r"【主诉】\s*(.*?)(?:\n|【|$)"
        chief_match = re.search(chief_pattern, observation)
        if chief_match:
            chief_complaint = chief_match.group(1).strip()
        
        # 尝试提取现病史
        present_illness = ""
        illness_pattern = r"【现病史】\s*(.*?)(?:\n|【|$)"
        illness_match = re.search(illness_pattern, observation)
        if illness_match:
            present_illness = illness_match.group(1).strip()
        
        # 组合主诉和现病史作为查询
        query = ""
        if chief_complaint:
            query += chief_complaint
        if present_illness:
            if query:
                query += " "
            query += present_illness
            
        # 如果没有找到主诉或现病史，则返回整个观察文本的前100个字符
        if not query:
            query = observation[:100] if observation else ""
            
        logger.info(f"提取的症状查询: {query}")
        return query

    def _generate_embedding(self, text):
        """将文本转换为向量"""
        if not self.cot_retrieval_available:
            return None
            
        try:
            inputs = self.tokenizer(text, return_tensors='pt', padding=True, truncation=True, max_length=512)
            with torch.no_grad():
                outputs = self.embedding_model(**inputs)
            return outputs.last_hidden_state[:, 0, :].squeeze().numpy()  # 取 [CLS] 作为向量
        except Exception as e:
            logger.error(f"生成向量嵌入失败: {e}")
            return None

    async def retrieve_cot_examples(self, observation, top_k=2):
        """
        基于患者观察信息检索相似的COT案例
        
        参数:
            observation (str): 患者观察信息
            top_k (int): 返回的案例数量
            
        返回:
            list: 包含COT案例的列表
        """
        if not self.cot_retrieval_available:
            logger.warning("COT检索功能不可用")
            return []
            
        try:
            # 提取关键症状作为查询
            query_text = self._extract_symptoms(observation)
            if not query_text:
                logger.warning("无法从观察中提取症状信息")
                return []
                
            # 生成查询向量
            query_embedding = self._generate_embedding(query_text)
            if query_embedding is None:
                logger.warning("无法生成查询向量")
                return []
                
            # 在Qdrant中执行检索
            # 添加超时设置，防止连接超时
            search_result = self.qdrant_client.search(
                collection_name=self.collection_name,
                query_vector=query_embedding.tolist(),  # 查询向量
                limit=top_k,  # 限制返回的数量
                with_payload=True,  # 返回payload
                timeout=10  # 修改为整数值
            )
            
            # 整理检索结果
            examples = []
            for result in search_result:
                if 'COT' in result.payload:
                    examples.append(result.payload['COT'])
                    logger.info(f"检索到COT案例 ID: {result.id}, 相似度: {result.score:.4f}")
                
            return examples
        except Exception as e:
            logger.error(f"检索COT案例时出错: {e}")
            # 捕获到超时错误后，返回空列表但不影响程序继续运行
            return []

    async def _construct_strategy_prompt(self):
        """
        构建用于生成新策略的提示。

        返回:
            str: 构建的提示。
        """
        prompt = "根据以下信息，生成诊断策略和推理：\n\n"

        # 添加观察信息
        observation = self.interaction_history.cot_entries[-1]['observation'] if self.interaction_history.cot_entries else ""
        prompt += f"患者观察：\n{observation}\n\n"

        # 添加检查信息
        if self.interaction_history.test_recommendation:
            if isinstance(self.interaction_history.test_recommendation, list):
                prompt += "医生提供的检查信息：\n"
                prompt += '\n'.join(str(item) for item in self.interaction_history.test_recommendation)
            else:
                prompt += "医生提供的检查信息：\n"
                prompt += str(self.interaction_history.test_recommendation)
            prompt += "\n"
      # 添加历史记录
        if self.interaction_history:
            prompt += "历史记录：\n"
            for i in range(len(self.interaction_history.cot_entries)):
                entry = self.interaction_history.cot_entries[i]
                prompt += f"第 {i+1} 轮：\n"
                prompt += f"策略：{entry['strategy']}\n"
                prompt += f"推理：{entry['reasoning']}\n"
                if entry['feedback']:
                    prompt += f"反馈：{entry['feedback']}\n"
                # if entry['dialogue_history']:
                #     prompt += f"对话历史：{entry['dialogue_history']}\n"
                prompt += "\n"
        
        # 添加COT检索结果作为示例 (直接await，不使用asyncio.run)
        if self.enable_cot_retrieval and self.cot_retrieval_available and observation:
            try:
                examples = await self.retrieve_cot_examples(observation)
                if examples:
                    prompt += "## 参考案例：\n"
                    for i, example in enumerate(examples, 1):
                        prompt += f"案例 {i}:\n{example}\n\n"
                    prompt += "请参考以上案例，针对当前患者情况生成诊断策略\n\n"
                else:
                    logger.info("没有检索到相关COT案例")
            except Exception as e:
                logger.error(f"检索COT案例时出错: {e}")

        # 根据快速演示模式调整prompt
        if hasattr(self, 'quick_demo_mode') and self.quick_demo_mode:
            prompt += """
## 快速演示模式提示！！！
当前处于快速演示模式，请优先考虑以下策略：
1. 如果已有基本症状信息，应直接进入查体策略阶段，不要过度问诊
2. 查体策略应该全面且针对性强，一次性完成重要的体格检查
3. 在获得基本症状和体格检查信息后，应尽快进入诊断策略阶段
4. 避免反复的问诊策略，快速推进诊断流程

优先级顺序：基本信息收集 -> 查体策略 -> 诊断策略
            """

        prompt += _user_prompt_strategy 

        # # 如果COT长度大于2，强制生成诊断
        # if len(self.interaction_history.cot_entries) >= 1:
        #     prompt += "\n注意：强制诊断模式！！！！！直接生成诊断！！！！\n"
        #     prompt += "请确保诊断结果准确、明确，并包含必要的检查建议。\n"
            
        return prompt

    async def generate_strategy(self):
        """
        基于反馈和观察生成下一个诊断策略。

        参数:
            enable_cot_retrieval (bool): 是否启用COT召回功能，如果为None则使用初始化时的设置。

        返回:
            tuple: (new_strategy, reasoning, should_terminate)
        """
        logger.info("Generating new strategy")

        # 在快速演示模式下，检查是否应该快速进入诊断阶段
        if hasattr(self, 'quick_demo_mode') and self.quick_demo_mode:
            # 获取当前观察信息
            current_observation = self.interaction_history.cot_entries[-1]['observation'] if self.interaction_history.cot_entries else ""
            
            # 检查是否已有基本症状信息且已进行过问诊
            has_basic_info = current_observation and len(self.interaction_history.cot_entries) >= 1
            has_dialogue_history = any(entry.get('dialogue_history') for entry in self.interaction_history.cot_entries)
            
            # 如果已有基本信息和对话历史，且轮次大于等于2，强制进入诊断
            if has_basic_info and has_dialogue_history and len(self.interaction_history.cot_entries) >= 2:
                logger.info("快速演示模式：强制进入诊断阶段")
                
                # 生成实际的诊断内容
                diagnosis_content = await self._generate_quick_diagnosis(current_observation)
                
                # 构建包含实际诊断的策略
                new_strategy = f"[诊断策略:] {diagnosis_content}"
                reasoning = "在快速演示模式下，已收集到足够的基本信息，基于患者症状和体征进行快速诊断"
                
                self.interaction_history.add_cot_entry(new_strategy, reasoning, "", [])
                should_terminate = True
                return new_strategy, reasoning, should_terminate

        # 生成新的策略和推理
        new_strategy, reasoning = await self._generate_new_strategy()

        self.interaction_history.add_cot_entry(new_strategy, reasoning, "", [])

        logger.debug(f"Generated strategy: {new_strategy[:100]}...")
        logger.debug(f"Generated reasoning: {reasoning[:100]}...")

        # 检查是否应该终止
        should_terminate = self._check_termination()

        return new_strategy, reasoning, should_terminate

    async def _generate_quick_diagnosis(self, observation):
        """
        在快速演示模式下生成快速诊断内容。
        
        参数:
            observation (str): 患者观察信息
            
        返回:
            str: 诊断内容
        """
        from src.prompts import _sys_prompt_diagnosis
        from src.llm_client import LLMClient
        from src.config import VOLCANO_TEMPERATURE_PLANNER
        
        # 构建快速诊断提示
        prompt = f"""
基于以下患者信息，请给出快速初步诊断：

患者观察信息：
{observation}

对话历史：
{self._format_dialogue_for_diagnosis()}

请直接给出：
1. 初步诊断
2. 建议的检查项目（如有必要）

格式示例：
诊断：上呼吸道感染
检查：血常规、胸部X线（如症状持续）
"""
        
        try:
            # 调用LLM生成诊断
            response = await LLMClient.generate_response(
                provider="jiuzhang", 
                prompt=prompt, 
                system_message="你是一位经验丰富的医生，能够基于患者症状快速做出准确的初步诊断。请给出简洁明确的诊断结果。", 
                temperature=VOLCANO_TEMPERATURE_PLANNER, 
                model="deepseek-reasoner"
            )
            
            # 清理R1模型的响应，移除推理过程
            cleaned_response = clean_r1_response(response)
            
            logger.info(f"Quick diagnosis generated: {cleaned_response}")
            return cleaned_response.strip()
            
        except Exception as e:
            logger.error(f"Error generating quick diagnosis: {e}")
            # 返回基于观察的简单诊断
            if "感冒" in observation or "发热" in observation or "咳嗽" in observation:
                return "诊断：上呼吸道感染，检查：血常规、胸部X线（如症状严重或持续）"
            elif "腹痛" in observation or "腹泻" in observation:
                return "诊断：急性胃肠炎，检查：血常规、大便常规"
            else:
                return "诊断：需要进一步检查以明确诊断，检查：血常规、基础生化检查"

    def _format_dialogue_for_diagnosis(self):
        """
        格式化对话历史用于诊断生成。
        
        返回:
            str: 格式化的对话历史
        """
        dialogue_summary = []
        for entry in self.interaction_history.cot_entries:
            if 'dialogue_history' in entry:
                for dialogue in entry['dialogue_history']:
                    if dialogue['role'] == 'patient':
                        dialogue_summary.append(f"患者：{dialogue['content']}")
                    elif dialogue['role'] == 'doctor':
                        dialogue_summary.append(f"医生：{dialogue['content']}")
        return "\n".join(dialogue_summary)

    async def _generate_new_strategy(self):
        """
        基于反馈和观察生成新的策略和推理。

        参数:
            feedback (str): 来自执行者的反馈。
            observation (str): 患者状况的全局观察。

        返回:
            tuple: (strategy, reasoning)
        """
        # 构建用于生成新策略的提示

        prompt = await self._construct_strategy_prompt()

        # 从 LLM 获取响应
        system_message = _sys_prompt_strategy
        response = await LLMClient.generate_response(provider="jiuzhang", prompt=prompt, system_message=system_message, temperature=VOLCANO_TEMPERATURE_PLANNER, model="deepseek-reasoner")
        # 解析响应
        strategy, reasoning = self._parse_strategy_response(response)

        return strategy, reasoning

    def _parse_strategy_response(self, response):
        """
        解析 LLM 响应以提取策略和推理。

        参数:
            response (str): LLM 的响应。

        返回:
            tuple: (strategy, reasoning)
        """
        strategy = ""
        reasoning = ""

        # 按行分割响应
        lines = response.split('\n')

        # 提取策略和推理
        for line in lines:
            if not strategy and (line.startswith("问诊策略:") or line.startswith("问诊策略：") or
                line.startswith("诊断策略:") or line.startswith("诊断策略：") or
                line.startswith("查体策略:") or line.startswith("查体策略：")):
                split_pos = line.find(":")
                if split_pos == -1:
                    split_pos = line.find("：")
                prefix = line[:split_pos+1].strip()
                content = line[split_pos+1:].strip()
                strategy = f"[{prefix}] {content}"
            elif not reasoning and (line.startswith("推理:") or line.startswith("推理：")):
                reasoning = line[len("推理:"):].strip() if line.startswith("推理:") else line[len("推理："):].strip()
            
            if strategy and reasoning:
                break

        return strategy, reasoning

    def _check_termination(self):
        """
        检查诊断过程是否应该终止。

        参数:
            observation (str): 患者状况的全局观察。

        返回:
            bool: 如果过程应该终止则为 True，否则为 False。
        """

        # 检查是否已达到最大轮次数
        if len(self.interaction_history.cot_entries) >= self.max_rounds:
            logger.info("Reached maximum number of rounds, terminating")
            return True

        # 检查最新策略中是否包含诊断
        if self.interaction_history.cot_entries and ("诊断:" in self.interaction_history.cot_entries[-1]['strategy'] or "诊断：" in self.interaction_history.cot_entries[-1]['strategy']):
            logger.info("Diagnosis found in strategy, terminating")
            return True

        return False

    def get_final_diagnosis(self):
        """
        从策略历史中获取最终诊断。

        返回:
            str: 最终诊断，或指示未做出诊断的消息。
        """
        # 检查策略历史中是否有诊断
        for entry in reversed(self.interaction_history.cot_entries):
            strategy = entry['strategy']
            if "诊断策略:" in strategy or "诊断策略：" in strategy:
                # 提取诊断策略后的内容
                if "诊断策略:" in strategy:
                    diagnosis_content = strategy.split("诊断策略:", 1)[1].strip()
                else:
                    diagnosis_content = strategy.split("诊断策略：", 1)[1].strip()
                
                # 移除可能的方括号标记
                if diagnosis_content.startswith("]"):
                    diagnosis_content = diagnosis_content[1:].strip()
                
                # 如果诊断内容包含具体的诊断信息（不是通用描述）
                if diagnosis_content and not diagnosis_content.startswith("基于收集到的患者信息"):
                    logger.info(f"Final diagnosis: {diagnosis_content}")
                    return diagnosis_content
            elif "诊断:" in strategy or "诊断：" in strategy:
                # 处理直接包含诊断的策略
                diagnosis_index = strategy.find("诊断:") if "诊断:" in strategy else strategy.find("诊断：")
                diagnosis = strategy[diagnosis_index:].strip()
                logger.info(f"Final diagnosis: {diagnosis}")
                return diagnosis

        # 如果未找到诊断，返回消息
        logger.warning("No diagnosis found in strategy history")
        return "未达成明确诊断。可能需要进一步检查。"




    def format_medical_advice(self, data):
        # 如果数据是字符串，尝试解析为JSON
        if isinstance(data, str):
            try:
                reasoning_process = ""
                
                # 检查是否包含"推理过程"和"最终回答"的格式
                if "推理过程:" in data and "最终回答:" in data:
                    # 提取"推理过程"部分
                    reasoning_start = data.find("推理过程:") + len("推理过程:")
                    reasoning_end = data.find("最终回答:")
                    reasoning_process = data[reasoning_start:reasoning_end].strip()
                    
                    # 提取"最终回答"部分
                    final_answer_start = data.find("最终回答:") + len("最终回答:")
                    final_answer = data[final_answer_start:].strip()
                    
                    # 检查最终回答是否包含JSON格式数据
                    if final_answer.startswith("```json"):
                        json_start = final_answer.find("```json") + len("```json")
                        json_end = final_answer.rfind("```")
                        if json_end > json_start:
                            json_str = final_answer[json_start:json_end].strip()
                            data = json.loads(json_str)
                        else:
                            data = json.loads(final_answer.replace("```json", "").replace("```", "").strip())
                    else:
                        data = json.loads(final_answer)
                    
                    # 保存推理过程以便后续使用
                    data['推理过程'] = reasoning_process
                else:
                    # 如果没有推理过程和最终回答的格式，直接解析整个字符串为JSON
                    data = json.loads(data)
                    
            except json.JSONDecodeError:
                # 如果解析失败，返回错误信息
                return f"无法解析诊疗计划: {data}"
                
        # 提取药品信息
        medicine_list = []
        for medicine in data.get('药品', []):  # 如果 '药品' 不存在，则默认空列表，避免 KeyError
            medicine_details = [
                f"药品名: {medicine.get('药品名', '未知')}",
                f"规格: {medicine.get('规格', '未提供')}",
                f"服用方法: {medicine.get('服用方法', '未提供')}",
                f"剂量安排: {medicine.get('剂量安排', '未提供')}",
                f"使用目的: {medicine.get('使用目的', '未提供')}",
                "\n",
                "-" * 30
            ]
            medicine_list.append("\n".join(medicine_details))

        medicine_text = "药品推荐：\n" + "\n\n".join(medicine_list) if medicine_list else "药品推荐：暂无推荐药品\n"

        # 提取生活建议
        life_advice_list = [f"- {advice}" for advice in data.get('生活建议', [])]  # 如果 '生活建议' 不存在，默认为空列表
        life_advice_text = "生活建议：\n" + "\n".join(life_advice_list) if life_advice_list else "\n生活建议：暂无生活建议"

        # 添加推理过程到输出
        reasoning_text = ""
        if data.get('推理过程'):
            reasoning_text = f"\n\n推理过程：\n{data.get('推理过程')}"

        # 读取"是否住院"字段，如果是"是" 则仅输出"住院理由"字段
        if data.get("是否住院") == "是":
            return f"{medicine_text}\n{life_advice_text}\n住院理由：{data.get('住院理由', '未提供')}{reasoning_text}"

        # 合并药品信息和生活建议
        return f"{medicine_text}\n{life_advice_text}{reasoning_text}"

    async def get_treatments(self, diagnosis, symptoms):
        # 获取诊断和症状
        logger.info(f"Symptoms: {symptoms}")

        # 统一的API请求函数
        def fetch_medicines(medicine_type, top_k):
            url = "http://47.94.171.56:8090/search"
            headers = {"Content-Type": "application/json"}
            data = {
                "text": f"{diagnosis}, {symptoms}",
                "medicine_type": medicine_type,
                "availability": "有",
                "top_k": top_k
            }

            logger.info(f"Requesting {medicine_type} medicines with data: {data}")
            try:
                response = requests.post(url, json=data, headers=headers, timeout=10)
                response.raise_for_status()  # 检查 HTTP 响应状态码
                return response.json()
            except requests.exceptions.RequestException as e:
                logger.error(f"Failed to fetch {medicine_type} medicines: {e}")
                return {"results": []}  # 返回空数据，防止后续代码崩溃
            except requests.exceptions.JSONDecodeError:
                logger.error(f"Invalid JSON response for {medicine_type}: {response.text}")
                return {"results": []}


        

        # 获取西药信息
        meds_retriveal_xiyao = fetch_medicines("西药", 15)
        meds_retriveal_payloads_xiyao = [med.get("payload", {}) for med in meds_retriveal_xiyao.get("results", [])]
        meds_retriveal_extracted_xiyao = [
            {
                "通用名称": med.get("通用名称", ""),
                "适应症": med.get("适应症", ""),
                "说明书中的规格": med.get("规格", ""),
                "在本医院的规格": med.get("在本医院的规格", ""),
                "说明书中的用法用量": med.get("用法用量", "")
            }
            for med in meds_retriveal_payloads_xiyao
        ]

        # 获取中药信息
        meds_retriveal_zhongyao = fetch_medicines("中药", 10)
        meds_retriveal_payloads_zhongyao = [med.get("payload", {}) for med in meds_retriveal_zhongyao.get("results", [])]
        meds_retriveal_extracted_zhongyao = [
            {
                "通用名称": med.get("通用名称", ""),
                "功能主治": med.get("功能主治", ""),
                "说明书中的规格": med.get("规格", ""),
                "在本医院的规格": med.get("在本医院的规格", ""),
                "说明书中的用法用量": med.get("用法用量", "")
            }
            for med in meds_retriveal_payloads_zhongyao
        ]

        # 合并西药和中药的信息
        meds_retriveal_extracted = {
            "西药": meds_retriveal_extracted_xiyao,
            "中药": meds_retriveal_extracted_zhongyao
        }

        
        observation_used = self.interaction_history.cot_entries[-1]['observation']
        index_used = -1

        #从self.interaction_history.cot_entries[-1]['observation']中读取observation，如果为空，或者字符个数少于10个就读取上一个，如果仍不符合标准就接着读,直到符合标准，不更改cot
        while not observation_used or len(observation_used) < 10:
            index_used -= 1
            observation_used = self.interaction_history.cot_entries[index_used]['observation']
            if index_used < -len(self.interaction_history.cot_entries):
                break


        # 获取治疗指南建议
        if self.interaction_history.guidance_for_treatment:
            treatment_guide = self.interaction_history.guidance_for_treatment
        else:
            treatment_guide = await get_guide(diagnosis, self.interaction_history.cot_entries[-2]['observation'], top_k_qdrant=12, top_k_llm=1)
            self.interaction_history.guidance_for_treatment = treatment_guide


        # 将治疗指南建议转换为文本格式
        treatment_guide_text = ""
        if treatment_guide:
            for index, guide in enumerate(treatment_guide.get("results", [])):
                logger.info(f"Treatment guide id: {guide.get('id', '')}")
                treatment_guide_text += f"第{index+1}个治疗指南：{guide.get('payload', '').get('source_file', '')}\n"
                treatment_guide_text += f"治疗指南内容：\n"
                for item in guide.get("payload", []).get("relevant_content", []):
                    logger.info(f"Treatment guide item: {item.get('title', '')}")
                    treatment_guide_text += f"{item.get('content', '')}\n\n"

        logger.info(f"Treatment guide text: {treatment_guide_text}")

        

        

        # 生成诊疗计划
        user_prompt_treatment_rag = _user_prompt_treatment_rag.format(
            observation=observation_used, diagnosis=diagnosis, meds_list=meds_retriveal_extracted, treatment_guide=treatment_guide_text
        )
        sys_prompt_treatment_rag = _sys_prompt_treatment_rag
        new_treatment_plan_rag = await LLMClient.generate_response(
            provider="jiuzhang",
            prompt=user_prompt_treatment_rag,
            system_message=sys_prompt_treatment_rag,
            temperature=VOLCANO_TEMPERATURE_EXECUTOR,
            show_reasoning=True,
            model="deepseek-reasoner"
        )
        logger.debug(f"New treatment plan: {new_treatment_plan_rag}")

        return self.format_medical_advice(new_treatment_plan_rag)

    async def update_observation_from_diagnosis(self, response):
        """
        从诊断响应中提取病史并更新最新的观察信息

        参数:
            response (str): 包含诊断、病情和病史的响应

        返回:
            tuple: (诊断, 病情, 病史)
        """
        diagnosis = ""
        condition = ""
        basis = ""

        # 使用正则表达式提取内容
        logger.info(f"Response before cleaning: {response[:100]}...")
        cleaned_response = clean_r1_response(response)
        logger.info(f"Response after cleaning: {cleaned_response[:100]}...")

        # 提取诊断
        diagnosis_match = re.search(r"<诊断>(.*?)</诊断>", cleaned_response, re.DOTALL)
        if diagnosis_match:
            diagnosis = diagnosis_match.group(1).strip()

        # 提取病情
        cleaned_response = clean_r1_response(response)
        condition_match = re.search(r"<病情>(.*?)</病情>", cleaned_response, re.DOTALL)
        condition = condition_match.group(1).strip() if condition_match else ""
        
        # 提取诊断依据
        basis_match = re.search(r"<诊断依据>(.*?)</诊断依据>", cleaned_response, re.DOTALL)
        if basis_match:
            basis = basis_match.group(1).strip()

        return diagnosis, condition, basis

    async def update_observation_only(self):
        """
        根据新信息更新患者病史。
        从交互历史中获取医生补充信息和检查结果。

        返回:
            str: 更新后的观察信息
        """
        # 获取当前观察信息
        current_observation = self.interaction_history.cot_entries[-2]['observation'] if self.interaction_history.cot_entries else ""
        
        # 构建提示
        prompt = f"请根据以下信息，更新患者的病史记录：\n\n"
        prompt += f"## 当前病史：\n{current_observation}\n\n"
        
        # 添加补充信息
        if self.interaction_history.doctor_supplementary_info:
            prompt += "## 医生补充信息：\n"
            for info in self.interaction_history.doctor_supplementary_info:
                prompt += f"- {info}\n\n"
            
        # 添加检查结果
        if self.interaction_history.test_recommendation:
            if isinstance(self.interaction_history.test_recommendation, list):
                prompt += "医生提供的检查信息：\n"
                prompt += '\n'.join(str(item) for item in self.interaction_history.test_recommendation)
            else:
                prompt += "医生提供的检查信息：\n"
                prompt += str(self.interaction_history.test_recommendation)
            prompt += "\n"
        
        system_message = _sys_prompt_update_observation
        
        # 调用LLM更新病史
        response = await LLMClient.generate_response(
            provider="jiuzhang",
            prompt=prompt,
            system_message=system_message,
            temperature=0.3,
            model="deepseek-reasoner"
        )
        
        # 清理R1模型的响应，移除推理过程
        cleaned_response = clean_r1_response(response)
        
        # 提取更新后的病史
        history_match = re.search(r"<病史>(.*?)</病史>", cleaned_response, re.DOTALL)
        if history_match:
            updated_history = history_match.group(1).strip()
            # 更新最新的观察信息
            if self.interaction_history.cot_entries:
                self.interaction_history.cot_entries[-2]['observation'] = updated_history
            return updated_history
        
        return current_observation

    async def _build_condition_prompt(self):
        """
        构建用于生成病情描述的prompt

        参数:
            observation (str): 患者观察信息

        返回:
            tuple: (prompt, system_message) 构建的prompt和系统提示
        """
        # 构建提示
        observation = self.interaction_history.cot_entries[-2]['observation'] if self.interaction_history.cot_entries else ""
        prompt = "请根据以下患者信息，仅给出病情描述：\n\n"
        prompt += f"## 患者病史：\n{observation}\n\n"
        
        # 添加医生的补充信息
        if self.interaction_history.doctor_supplementary_info:
            prompt += "\n## 医生补充信息：\n"
            for info in self.interaction_history.doctor_supplementary_info:
                prompt += f"- {info}\n"

        # 添加检查结果
        if self.interaction_history.test_recommendation:
            if isinstance(self.interaction_history.test_recommendation, list):
                prompt += "医生提供的检查信息：\n"
                prompt += '\n'.join(str(item) for item in self.interaction_history.test_recommendation)
            else:
                prompt += "医生提供的检查信息：\n"
                prompt += str(self.interaction_history.test_recommendation)
            prompt += "\n"
        
        return prompt, _sys_prompt_condition_only

    async def final_diagnosis_and_condition(self, has_input):
        """
        根据是否有新的输入信息，选择不同的诊断模式生成诊断和病情描述

        参数:
            has_input (bool): 是否有新的输入信息（医生补充信息或检查结果）

        返回:
            tuple: (诊断结果, 病情描述, 推理理由) 最终的诊断结果、病情描述和推理理由
        """
        if has_input:
            # 有新的输入信息，需要进行完整的诊断流程
            logger.info("拟诊后医生更新信息，进行确诊以及病情更新")               
            
            # 构建完整的诊断提示
            prompt, system_message = await self.generate_final_diagnosis_prompt()
            
            # 调用 LLM 获取最终诊断
            response = await LLMClient.generate_response(
                provider="jiuzhang",
                prompt=prompt,
                system_message=system_message,
                temperature=0.3,
                model="deepseek-reasoner",
                show_reasoning=True
            )
            
            # 提取诊断、病情和推理理由
            diagnosis, condition, basis = await self.update_observation_from_diagnosis(response)
            
            # 判断诊断是否发生变化
            logger.info(f"二次拟诊: {self.interaction_history.diagnosis}")
            logger.info(f"确诊: {diagnosis}")
            if diagnosis != self.interaction_history.diagnosis:
                # 诊断变化，需要重新召回指南
                logger.info("诊断变化，开药计划需要重新召回指南")
                self.interaction_history.diagnosis = diagnosis
                self.interaction_history.guidance_for_treatment = ""
            else:
                logger.info("确诊与二次拟诊相同，保持指南不变")
            
            # 提取推理过程
            cleaned_response = clean_r1_response(response)
            reasoning_match = re.search(r"推理过程:\n(.*?)\n\n最终回答:", cleaned_response, re.DOTALL)
            reasoning = reasoning_match.group(1).strip() if reasoning_match else ""
            
            return f"{diagnosis}\n{basis}", condition, reasoning
            
        else:
            # 无新的输入信息，仅生成病情描述
            logger.info("拟诊后医生无更新，直接生成病情描述")

            prompt, system_message = await self._build_condition_prompt()
            
            # 调用LLM生成病情描述
            response = await LLMClient.generate_response(
                provider="jiuzhang",
                prompt=prompt,
                system_message=system_message,
                temperature=0.3,
                model="deepseek-reasoner"
            )
            
            # 提取病情描述
            cleaned_response = clean_r1_response(response)
            condition_match = re.search(r"<病情>(.*?)</病情>", cleaned_response, re.DOTALL)
            condition = condition_match.group(1).strip() if condition_match else ""
            
            return self.interaction_history.diagnosis, condition, ""

    async def generate_final_diagnosis_prompt(self):
        """
        构建最终确诊的 prompt，整合所有历史信息。

        返回:
            tuple: (prompt, system_message) 构建的最终确诊 prompt 和系统提示
        """
        prompt = "请根据以下完整的诊断过程信息，给出最终的诊断结果：\n\n"

        # 1. 添加初步拟诊信息
        if self.interaction_history.diagnosis:
            prompt += "## 初步拟诊信息：\n"
            prompt += f"初步诊断：{self.interaction_history.diagnosis}\n\n"

        # 2. 添加指南
        if self.interaction_history.guidance_for_treatment:
            logger.info("二次拟诊相同，使用已召回的医学指南")
            prompt += f"## 相关医学指南：\n{self.interaction_history.guidance_for_treatment}\n\n"
        else:
            logger.info("拟诊已更新，进行指南召回")
            # 使用当前诊断进行指南召回
            guide_response = await get_guide(
                main_disease=self.interaction_history.diagnosis,
                top_k_qdrant=10,
                top_k_llm=1
            )
            
            # 添加对 guide_response 为 None 的处理
            if not guide_response or not guide_response.get("results"):
                logger.error("获取医学指南失败: API请求失败或未返回结果")
                prompt += "## 相关医学指南：\n无\n\n"
                original_content = ""
            else:
                # 记录召回的指南信息
                original_content = guide_response.get("results", [{}])[0].get("payload", {}).get("original_content", "")
                
                # 先把内容写进数据库
                self.interaction_history.guidance_for_treatment = guide_response
                # 添加新的诊断和指南对应关系到列表
                import time
                self.interaction_history.diagnosis_guidelines.append({
                    "diagnosis": self.interaction_history.diagnosis,
                    "guidelines": original_content,
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                })
                prompt += f"## 相关医学指南：\n{original_content}\n\n"

        # 3. 添加患者病史，历史策略、推理和反馈
        prompt += "## 诊断过程历史：\n"
        for i, entry in enumerate(self.interaction_history.cot_entries, 1):
            prompt += f"\n第 {i} 轮：\n"
            prompt += f"策略：{entry['strategy']}\n"
            prompt += f"推理：{entry['reasoning']}\n"
            if entry['feedback']:
                prompt += f"反馈：{entry['feedback']}\n"

        observation_used = self.interaction_history.cot_entries[-1]['observation'] if self.interaction_history.cot_entries else ""
        index_used = -1
        #从self.interaction_history.cot_entries[-1]['observation']中读取observation，如果为空，或者字符个数少于10个就读取上一个，如果仍不符合标准就接着读,直到符合标准，不更改cot
        # while not observation_used or len(observation_used) < 10:
        #     index_used -= 1
        #     observation_used = self.interaction_history.cot_entries[index_used]['observation']
        #     if index_used < -len(self.interaction_history.cot_entries):
        #         break

        observation = observation_used if self.interaction_history.cot_entries else ""
        prompt += f"## 患者当前病史：\n{observation}\n"

        # 4. 添加医生的补充信息
        if self.interaction_history.doctor_supplementary_info:
            prompt += "\n## 医生补充信息：\n"
            for info in self.interaction_history.doctor_supplementary_info:
                prompt += f"- 补充内容{self.interaction_history.doctor_supplementary_info.index(info) + 1}：{info}\n"

        # 5. 添加检查结果
        if self.interaction_history.test_recommendation:
            if isinstance(self.interaction_history.test_recommendation, list):
                prompt += "医生提供的检查信息：\n"
                prompt += '\n'.join(str(item) for item in self.interaction_history.test_recommendation)
            else:
                prompt += "医生提供的检查信息：\n"
                prompt += str(self.interaction_history.test_recommendation)
            prompt += "\n"

        # 6. 系统提示
        system_message = _sys_prompt_final_diagnosis

        return prompt, system_message

    async def generate_preliminary_diagnosis(self, first_diagnosis, examination, observation):
        """
        根据医学指南对诊断进行再次判断
        
        参数:
            first_diagnosis (str): 初步诊断结果
            examination (str): 初步检查建议
            observation (str): 患者观察信息
            
        返回:
            tuple: (verified_diagnosis, additional_tests, reasoning, guidelines_content)
                - verified_diagnosis: 核实后的诊断
                - additional_tests: 建议的额外检查（JSON格式字符串）
                - reasoning: 判断推理过程
                - guidelines_content: 召回的医学指南内容
        """
        try:
            # 使用get_guide函数获取最相关的指南
            # 记录诊断和症状信息
            logger.info(f"第一次拟诊: {first_diagnosis}")
            # logger.info(f"诊断症状: {diagnostic_symptoms}")
            
            guide_response = await get_guide(
                main_disease=first_diagnosis,
                top_k_qdrant=10,
                top_k_llm=1
            )
            
            # 添加对 guide_response 为 None 的处理
            if not guide_response or not guide_response.get("results"):
                logger.error("获取医学指南失败: API请求失败或未返回结果")
                return first_diagnosis, "", "无法获取医学指南进行核实", ""
            
            # 记录召回的指南信息
            for result in guide_response.get("results", []):
                source_file = result.get("payload", {}).get("source_file", "")
                score = result.get("score", 0)
                logger.info(f"召回指南: {source_file}, 相似度分数: {score:.4f}")
                
            original_content = guide_response.get("results", [{}])[0].get("payload", {}).get("original_content", "")
            
            # 打印召回的医学指南内容
            logger.info("召回的医学指南内容:")
            logger.info("-" * 50)
            logger.info(original_content[:100] + "..." if len(original_content) > 100 else original_content)
            logger.debug(original_content)
            logger.info("-" * 50)

            # 构建COT历史
            cot_history = ""
            for i, entry in enumerate(self.interaction_history.cot_entries, 1):
                cot_history += f"\n第 {i} 轮：\n"
                cot_history += f"策略：{entry['strategy']}\n"
                cot_history += f"推理：{entry['reasoning']}\n"
                if entry['feedback']:
                    cot_history += f"反馈：{entry['feedback']}\n"
            
            # 获取医生补充信息
            doctor_supplementary_info = "\n## 医生补充信息：\n" + "\n".join(self.interaction_history.doctor_supplementary_info) + "\n" if self.interaction_history.doctor_supplementary_info else ""

            logger.info(f"医生补充信息888: {doctor_supplementary_info}")
            
            # 获取检查结果
            test_results = ""
            if self.interaction_history.test_recommendation:
                if isinstance(self.interaction_history.test_recommendation, list):
                    test_results += "\n## 检查结果：\n"
                    test_results += '\n'.join(str(item) for item in self.interaction_history.test_recommendation)
                else:
                    test_results += "\n## 检查结果：\n"
                    test_results += str(self.interaction_history.test_recommendation)
                test_results += "\n"

            # 获取本医院可以提供的检查项目，从data/available_test_items.json中读取，如果文件不存在，则说明全部的检查项目都可以提供
            available_test_items = ""
            try:
                with open(os.path.join(os.path.dirname(__file__), "..", "data", "available_test_items.json"), "r", encoding="utf-8") as f:
                    available_test_items = json.load(f)
            except FileNotFoundError:
                available_test_items = "本医院可以提供的检查项目：全部"

            
            logger.info(f"检查结果999: {test_results}")

            
            # 构建提示
            prompt = _user_prompt_verify_diagnosis.format(
                diagnosis=first_diagnosis,
                observation=observation,
                cot_history=cot_history,
                guidelines=original_content,
                doctor_supplementary_info=doctor_supplementary_info,
                test_results=test_results,
                examination=examination,
                available_test_items=available_test_items
            )

            logger.info(f"核实诊断的prompt: {prompt}")
            logger.info(f"长度: {len(prompt)}")
            
            # 调用LLM进行判断
            response = await LLMClient.generate_response(
                provider="jiuzhang",
                prompt=prompt,
                system_message=_sys_prompt_verify_diagnosis,
                temperature=0.3,
                model="deepseek-reasoner",
                show_reasoning=True
            )

            logger.info(f"核实诊断的响应: {response}")
            
            # 解析JSON格式的结果
            verified_diagnosis = ""
            diagnosis_reason = ""
            additional_tests = ""
            reasoning = ""
            
            try:
                # 提取推理过程
                cleaned_response = clean_r1_response(response)
                reasoning_match = re.search(r"推理过程:\n(.*?)\n\n最终回答:", cleaned_response, re.DOTALL)
                reasoning = reasoning_match.group(1).strip() if reasoning_match else ""
                
                # 提取最终回答中的JSON部分
                final_answer_match = re.search(r"最终回答:\s*(.*)", cleaned_response, re.DOTALL)
                if final_answer_match:
                    json_content = final_answer_match.group(1).strip()
                    logger.info(f"提取的最终回答部分: {json_content[:300]}...")
                    
                    # 如果有```json包装，去除包装
                    if json_content.startswith("```json"):
                        json_start = json_content.find("```json") + len("```json")
                        json_end = json_content.rfind("```")
                        if json_end > json_start:
                            json_content = json_content[json_start:json_end].strip()
                        else:
                            json_content = json_content.replace("```json", "").replace("```", "").strip()
                    
                    # 尝试清理可能的额外文本
                    if not json_content.startswith("{"):
                        # 查找第一个 { 字符
                        json_start = json_content.find("{")
                        if json_start != -1:
                            json_content = json_content[json_start:]
                    
                    if not json_content.endswith("}"):
                        # 查找最后一个 } 字符
                        json_end = json_content.rfind("}")
                        if json_end != -1:
                            json_content = json_content[:json_end + 1]
                    
                    logger.info(f"清理后的JSON内容: {json_content}")
                    
                    # 验证JSON内容是否完整
                    if not json_content.strip():
                        raise json.JSONDecodeError("JSON内容为空", json_content, 0)
                    
                    if not (json_content.strip().startswith("{") and json_content.strip().endswith("}")):
                        raise json.JSONDecodeError("JSON格式不完整，缺少开始或结束括号", json_content, 0)
                    
                    # 解析JSON
                    result_data = json.loads(json_content)
                    
                    # 提取各个字段
                    verified_diagnosis = result_data.get("verified_diagnosis", "")
                    diagnosis_reason = result_data.get("diagnosis_reason", "")
                    
                    # 将additional_tests转换为格式化的字符串
                    tests_list = result_data.get("additional_tests", [])
                    if tests_list:
                        formatted_tests = []
                        for test in tests_list:
                            test_info = f"检查项目：{test.get('test_name', '')}\n"
                            test_info += f"推荐理由：{test.get('reason', '')}\n"
                            test_info += f"优先级：{test.get('priority', '')}\n"
                            test_info += f"院内可提供：{'是' if test.get('available_in_hospital', False) else '否'}\n"
                            if test.get('notes', ''):
                                test_info += f"备注：{test.get('notes', '')}\n"
                            formatted_tests.append(test_info)
                        additional_tests = "\n".join(formatted_tests)
                    
                    logger.info("JSON解析成功")
                    
                else:
                    # 如果没有推理过程格式，直接解析整个响应为JSON
                    logger.info("未找到推理过程格式，尝试直接解析整个响应为JSON")
                    logger.info(f"完整响应内容: {response[:500]}...")
                    
                    # 尝试找到JSON内容
                    response_clean = response.strip()
                    if response_clean.startswith("{") and response_clean.endswith("}"):
                        json_content = response_clean
                    else:
                        # 查找JSON块
                        json_start = response_clean.find("{")
                        json_end = response_clean.rfind("}")
                        if json_start != -1 and json_end != -1 and json_end > json_start:
                            json_content = response_clean[json_start:json_end + 1]
                        else:
                            raise json.JSONDecodeError("无法找到有效的JSON内容", response, 0)
                    
                    logger.info(f"提取的JSON内容: {json_content}")
                    result_data = json.loads(json_content)
                    verified_diagnosis = result_data.get("verified_diagnosis", "")
                    diagnosis_reason = result_data.get("diagnosis_reason", "")
                    
                    # 将additional_tests转换为格式化的字符串
                    tests_list = result_data.get("additional_tests", [])
                    if tests_list:
                        formatted_tests = []
                        for test in tests_list:
                            test_info = f"检查项目：{test.get('test_name', '')}\n"
                            test_info += f"推荐理由：{test.get('reason', '')}\n"
                            test_info += f"优先级：{test.get('priority', '')}\n"
                            test_info += f"院内可提供：{'是' if test.get('available_in_hospital', False) else '否'}\n"
                            if test.get('notes', ''):
                                test_info += f"备注：{test.get('notes', '')}\n"
                            formatted_tests.append(test_info)
                        additional_tests = "\n".join(formatted_tests)
                        
            except json.JSONDecodeError as e:
                logger.error(f"解析JSON响应失败: {e}")
                logger.error(f"JSON解析错误位置: 行 {e.lineno}, 列 {e.colno}")
                logger.error(f"错误的JSON内容: {e.doc}")
                logger.error(f"完整响应内容: {response}")
            
            # 存储诊断和指南的对应关系
            import time
            if first_diagnosis and first_diagnosis == verified_diagnosis:
                logger.info("初步拟诊与核实拟诊一致")
                self.interaction_history.diagnosis = first_diagnosis
                self.interaction_history.guidance_for_treatment = guide_response
            else:
                self.interaction_history.diagnosis = verified_diagnosis

            # 添加新的诊断和指南对应关系到列表
            self.interaction_history.diagnosis_guidelines.append({
                "diagnosis": first_diagnosis,
                "guidelines": guide_response,
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            })
            
            return f"{verified_diagnosis}\n{diagnosis_reason}", additional_tests, reasoning, guide_response
            
        except Exception as e:
            logger.error(f"核实诊断时出错: {e}")
            return first_diagnosis, "", f"核实过程出错: {str(e)}", ""


    

