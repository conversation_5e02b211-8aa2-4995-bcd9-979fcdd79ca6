package com.makiyo.ai_doctor_prod.controller;

import com.makiyo.ai_doctor_prod.utils.ConnectionMonitor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * System Monitor Controller
 * Provides various monitoring endpoints including HTTP connection status checks
 */
@RestController
@RequestMapping("/monitor")
public class SystemMonitorController {

    private static final Logger log = LoggerFactory.getLogger(SystemMonitorController.class);

    @Resource
    private ConnectionMonitor connectionMonitor;

    @Autowired(required = false)
    private RestTemplate restTemplate;

    /**
     * Check system connection status
     * @return Map containing various connection states
     */
    @GetMapping("/connections")
    public ResponseEntity<Map<String, Object>> checkConnections() {
        log.info("Received connection status check request");
        Map<String, Object> result = new HashMap<>();
        result.put("timestamp", System.currentTimeMillis());
        
        // Get TCP connection statistics
        try {
            Map<String, Integer> stats = connectionMonitor.getConnectionStats();
            result.put("tcp_stats", stats);
            result.put("success", stats != null);
            
            // Focus on CLOSE_WAIT state
            int closeWaitCount = stats != null ? stats.getOrDefault("CLOSE_WAIT", 0) : -1;
            result.put("close_wait_count", closeWaitCount);
            
            // Check if CLOSE_WAIT count is above warning threshold
            boolean hasIssue = closeWaitCount > 10; // Consider there's an issue if more than 10 CLOSE_WAIT connections
            result.put("has_issue", hasIssue);
            
            if (hasIssue) {
                result.put("recommendations", "Detected a large number of CLOSE_WAIT connections. Check HTTP client configurations and ensure all connections are properly closed.");
            }
        } catch (Exception e) {
            log.error("Error getting connection statistics: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        // If RestTemplate is available, add connection pool status
        if (restTemplate != null) {
            try {
                Map<String, Object> httpClientInfo = new HashMap<>();
                httpClientInfo.put("class", restTemplate.getRequestFactory().getClass().getName());
                
                // Try to get more information about RestTemplate connection pool
                if (restTemplate.getRequestFactory().getClass().getName().contains("HttpComponents")) {
                    httpClientInfo.put("type", "Apache HttpClient");
                    // Cannot directly access connection pool statistics as HttpClient's pool is encapsulated
                    // Consider using JMX or custom monitoring mechanism to get more details
                }
                
                result.put("rest_template_info", httpClientInfo);
            } catch (Exception e) {
                log.warn("Failed to get RestTemplate information: {}", e.getMessage());
                result.put("rest_template_info_error", e.getMessage());
            }
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * Health check endpoint
     * @return Simple health status information
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("timestamp", System.currentTimeMillis());
        
        // Check CLOSE_WAIT state as part of health check
        int closeWaitCount = connectionMonitor.checkCloseWaitCount();
        health.put("close_wait_count", closeWaitCount);
        
        // Mark as warning status if CLOSE_WAIT count exceeds threshold
        if (closeWaitCount > 10) {
            health.put("status", "WARNING");
            health.put("warning", "Large number of CLOSE_WAIT connections detected");
        } else if (closeWaitCount < 0) {
            health.put("warning", "Could not check CLOSE_WAIT connections");
        }
        
        return ResponseEntity.ok(health);
    }
} 