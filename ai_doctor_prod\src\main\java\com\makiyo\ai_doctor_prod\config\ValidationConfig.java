package com.makiyo.ai_doctor_prod.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.beanvalidation.MethodValidationPostProcessor;

/**
 * 参数验证配置
 * 用于启用方法参数验证
 */
@Configuration
public class ValidationConfig {

    /**
     * 启用方法参数验证
     * 这个配置使得可以在@RequestParam、@PathVariable等参数上使用验证注解
     */
    @Bean
    public MethodValidationPostProcessor methodValidationPostProcessor() {
        return new MethodValidationPostProcessor();
    }
} 