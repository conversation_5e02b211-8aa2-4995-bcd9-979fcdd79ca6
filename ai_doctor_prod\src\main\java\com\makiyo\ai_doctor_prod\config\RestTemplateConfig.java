package com.makiyo.ai_doctor_prod.config;

import org.apache.http.HeaderElement;
import org.apache.http.HeaderElementIterator;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.conn.ConnectionKeepAliveStrategy;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicHeaderElementIterator;
import org.apache.http.protocol.HTTP;
import org.apache.http.protocol.HttpContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.TimeUnit;

/**
 * RestTemplate配置类
 * 配置连接池、超时、Keep-Alive策略等
 */
@Configuration
public class RestTemplateConfig {

    private static final Logger log = LoggerFactory.getLogger(RestTemplateConfig.class);

    // 设置更长的超时时间，例如5分钟，并允许从配置文件覆盖
    @Value("${python.prod.http.connect-timeout:300000}")
    private int connectTimeout;

    @Value("${python.prod.http.request-timeout:300000}")
    private int requestTimeout;

    @Value("${python.prod.http.socket-timeout:300000}")
    private int socketTimeout;

    // 连接池设置
    @Value("${python.prod.http.pool.max-total:50}")
    private int maxTotalConnections;

    @Value("${python.prod.http.pool.max-per-route:10}")
    private int maxPerRouteConnections;
    
    @Value("${python.prod.http.pool.keep-alive:30000}")
    private int defaultKeepAliveTimeMillis;

    @Value("${python.prod.http.pool.validate-after-inactivity:2000}")
    private int validateAfterInactivity;

    /**
     * RestTemplate Bean
     * @return RestTemplate实例
     */
    @Bean
    public RestTemplate restTemplate() {
        // 使用配置了连接池的HttpComponentsClientHttpRequestFactory
        RestTemplate restTemplate = new RestTemplate(clientHttpRequestFactory());
        log.info("Initialized RestTemplate with connection pooling.");
        log.info("Timeout settings: connect={}ms, request={}ms, socket={}ms", connectTimeout, requestTimeout, socketTimeout);
        return restTemplate;
    }

    /**
     * ClientHttpRequestFactory配置
     * @return HttpComponentsClientHttpRequestFactory
     */
    @Bean
    public HttpComponentsClientHttpRequestFactory clientHttpRequestFactory() {
        // 使用自定义的HttpClient实例创建RequestFactory，该实例已包含所有配置
        return new HttpComponentsClientHttpRequestFactory(httpClient());
    }
    
    /**
     * 配置底层HttpClient
     */
    @Bean
    public HttpClient httpClient() {
        // 配置请求超时
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectionRequestTimeout(requestTimeout)
                .setConnectTimeout(connectTimeout)
                .setSocketTimeout(socketTimeout)
                .build();

        // 配置连接池
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(maxTotalConnections);
        connectionManager.setDefaultMaxPerRoute(maxPerRouteConnections);
        // 在不活动validateAfterInactivity毫秒后验证连接，以检测并驱逐陈旧或关闭的连接
        connectionManager.setValidateAfterInactivity(validateAfterInactivity);

        return HttpClientBuilder.create()
                .setDefaultRequestConfig(requestConfig)
                .setConnectionManager(connectionManager)
                .setKeepAliveStrategy(connectionKeepAliveStrategy())
                // 定期清理过期和空闲连接
                .evictExpiredConnections()
                .evictIdleConnections(defaultKeepAliveTimeMillis, TimeUnit.MILLISECONDS)
                .build();
    }

    /**
     * Keep-Alive策略
     * @return ConnectionKeepAliveStrategy
     */
    @Bean
    public ConnectionKeepAliveStrategy connectionKeepAliveStrategy() {
        return (response, context) -> {
            // 优先尊重服务器通过'Keep-Alive'头指定的超时时间
            HeaderElementIterator it = new BasicHeaderElementIterator(
                    response.headerIterator(HTTP.CONN_KEEP_ALIVE));
            while (it.hasNext()) {
                HeaderElement he = it.nextElement();
                String param = he.getName();
                String value = he.getValue();
                if (value != null && param.equalsIgnoreCase("timeout")) {
                    try {
                        return Long.parseLong(value) * 1000;
                    } catch (NumberFormatException ignore) {
                        log.warn("Invalid keep-alive timeout value: {}", value);
                    }
                }
            }
            // 如果服务器没有指定，则使用我们的默认值
            return defaultKeepAliveTimeMillis;
        };
    }
}