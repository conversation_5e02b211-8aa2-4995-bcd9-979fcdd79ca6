"""
LLM API clients for generating responses.
"""
from openai import AsyncOpenAI
import anthropic
import httpx
from anthropic import <PERSON><PERSON><PERSON>, De<PERSON>ultHttpxClient
from together import AsyncTogether
from src.config import (
    OPENAI_API_KEY, OPENAI_MODEL, OPENAI_TEMPERATURE,
    VOLCANO_API_KEY, VOLCANO_API_BASE, VOLCANO_MODEL, VOLCANO_TEMPERATURE_PLANNER,VOLCANO_TEMPERATURE_EXECUTOR, VOLCANO_TEMPERATURE,
    CLAUDE_API_KEY, CLAUDE_MODEL, CLAUDE_TEMPERATURE,
    QWEN_API_KEY, QWEN_API_BASE, QWEN_MODEL, QWEN_TEMPERATURE, QWEN_STREAM,
    JIUZHANG_API_KEY, JIUZHANG_API_BASE, JIUZHANG_MODEL,
    TOGETHER_API_KEY, TOGETHER_MODEL, TO<PERSON>TH<PERSON>_TEMPERATURE,
    PPIO_API_KEY, PPIO_API_BASE, PPIO_MODEL, PPIO_TEMPERATURE,
)
from src.logger import get_logger
import asyncio
import re

logger = get_logger("llm_client")

class OpenAIClient:
    """Client for interacting with OpenAI API."""
    
    # Create a client instance
    # client = AsyncOpenAI(api_key=OPENAI_API_KEY)
    
    @classmethod
    async def generate_response(cls, prompt, system_message=None, max_tokens=None, temperature=0.7, show_reasoning=False):
        """
        Generate a response from the OpenAI API.
        
        Args:
            prompt (str): The prompt to send to the API.
            system_message (str, optional): System message to guide the model's behavior.
            max_tokens (int, optional): Maximum number of tokens to generate.
            temperature (float, optional): Temperature for response generation. Default is 0.7.
            show_reasoning (bool, optional): Whether to show the model's reasoning process. Default is False.
            
        Returns:
            str: The generated response.
        """
        client = AsyncOpenAI(api_key=OPENAI_API_KEY)
        try:
            messages = []
            
            # Add system message if provided
            if system_message:
                messages.append({"role": "system", "content": system_message})
                
            # Add user prompt
            messages.append({"role": "user", "content": prompt})
            
            logger.debug(f"Sending prompt to OpenAI: {prompt}...")

            logger.debug(f"System message to OpenAI: {system_message}")
            
            # Call OpenAI API
            # response = await cls.client.chat.completions.create(
            #     model=OPENAI_MODEL,
            #     messages=messages,
            #     temperature=temperature,
            #     max_tokens=max_tokens,
            #     # max_completion_tokens=max_tokens,
            # )
            # OpenAI o1是推理模型，可以直接获取推理过程
            reasoning_params = {"effort": "medium"}
            
            response = await client.responses.create(
                model="o1",
                reasoning=reasoning_params,
                input=messages,
            )
            
            # Extract the response text
            response_text = response.output_text.strip()
            logger.debug(f"Received response from OpenAI: {response_text}...")
            
            # 如果show_reasoning为True且模型返回了推理过程，则包含在响应中
            if show_reasoning and hasattr(response, 'reasoning') and response.reasoning:
                reasoning_text = response.reasoning.get('text', '')
                if reasoning_text:
                    logger.debug(f"Including OpenAI reasoning process")
                    return f"推理过程:\n{reasoning_text}\n\n最终回答:\n{response_text}"
            
            return response_text
            
        except Exception as e:
            logger.error(f"Error generating response from OpenAI: {str(e)}")
            return f"生成响应时出错: {str(e)}"

class VolcanoClient:
    """Client for interacting with Volcano API (Deepseek-r1) using OpenAI interface."""
    
    # Create a client instance
    # client = AsyncOpenAI(
    #     api_key=VOLCANO_API_KEY,
    #     base_url=VOLCANO_API_BASE
    # )
    
    @classmethod
    async def generate_response(cls, prompt, system_message=None, max_tokens=None, temperature=0.7, show_reasoning=False, model = None, max_retries=3, retry_delay=2):
        """
        Generate a response from the Volcano API (Deepseek-r1).
        
        Args:
            prompt (str): The prompt to send to the API.
            system_message (str, optional): System message to guide the model's behavior.
            max_tokens (int, optional): Maximum number of tokens to generate.
            temperature (float, optional): Temperature for response generation. Default is 0.7.
            show_reasoning (bool, optional): Whether to show the model's reasoning process. Default is False.
            max_retries (int, optional): Maximum number of retry attempts. Default is 3.
            retry_delay (int, optional): Delay between retries in seconds. Default is 2.
            
        Returns:
            str: The generated response.
        """
        client = AsyncOpenAI(
            api_key=VOLCANO_API_KEY,
            base_url=VOLCANO_API_BASE
        )
        retry_count = 0
        while retry_count < max_retries:
            try:
                messages = []

                logger.debug(f"llm client name: {cls.__name__}")
                logger.debug(f"llm client model: {VOLCANO_MODEL}")
                logger.debug(f"llm client temperature: {temperature}")

                # Add system message if provided
                if system_message:
                    messages.append({"role": "system", "content": system_message})
                    
                # Add user prompt
                messages.append({"role": "user", "content": prompt})
                
                logger.debug(f"Sending prompt to Volcano (Deepseek-r1): {prompt}")
                logger.debug(f"System message to Volcano (Deepseek-r1): {system_message}")
                
                api_params = {
                    "model": VOLCANO_MODEL,
                    "messages": messages,
                    "temperature": temperature,
                    "max_tokens": max_tokens,
                }
                
                # Call Volcano API
                response = await client.chat.completions.create(**api_params)
                
                # Extract the response text
                response_text = response.choices[0].message.content.strip()
                logger.debug(f"Received response from Volcano (Deepseek-r1): {response}")
                logger.debug(f"Received response from Volcano (Deepseek-r1): {response_text}")
                
                # 如果启用了推理过程显示，从响应中提取reasoning_content字段
                if show_reasoning:
                    try:
                        reasoning_content = response.choices[0].message.reasoning_content
                        if reasoning_content:
                            logger.debug(f"Found reasoning_content in Volcano response")
                            return f"推理过程:\n{reasoning_content}\n\n最终回答:\n{response_text}"
                    except Exception as e:
                        logger.warning(f"Error extracting reasoning_content from Volcano response: {str(e)}")
                        try:
                            import json
                            try:
                                json_response = json.loads(response_text)
                                if "reasoning" in json_response and json_response["reasoning"]:
                                    reasoning = json_response["reasoning"]
                                    answer = json_response.get("answer", "")
                                    return f"推理过程:\n{reasoning}\n\n最终回答:\n{answer}"
                            except json.JSONDecodeError:
                                if "推理过程" in response_text and "最终回答" in response_text:
                                    return response_text
                        except Exception as e:
                            logger.warning(f"Error extracting reasoning from Volcano response: {str(e)}")
                
                return response_text
                
            except Exception as e:
                error_message = str(e)
                logger.error(f"Error generating response from Volcano (Deepseek-r1): {error_message}")
                
                # 检查是否是限流错误
                if "Too many requests" in error_message or "ServiceUnavailable" in error_message:
                    retry_count += 1
                    if retry_count < max_retries:
                        wait_time = retry_delay * (2 ** (retry_count - 1))  # 指数退避
                        logger.warning(f"Rate limit hit, retrying in {wait_time} seconds... (Attempt {retry_count}/{max_retries})")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        logger.error(f"Max retries ({max_retries}) exceeded. Giving up.")
                        return f"服务暂时不可用，请稍后重试。错误信息: {error_message}"
                else:
                    # 非限流错误直接返回
                    return f"生成响应时出错: {error_message}"


class ClaudeClient:
    """Client for interacting with Claude API."""
    
    # Create a client instance
    # client = anthropic.AsyncAnthropic(
    #     api_key=CLAUDE_API_KEY,
    #     http_client=httpx.AsyncClient(
    #         proxy="http://localhost:7890",
    #         transport=httpx.HTTPTransport(local_address="0.0.0.0"), 
    #         )
    #         )
    @classmethod
    async def generate_response(cls, prompt, system_message=None, max_tokens = None, temperature=0.7, show_reasoning=False):
        """
        Generate a response from the Claude API.
        
        Args:
            prompt (str): The prompt to send to the API.
            system_message (str, optional): System message to guide the model's behavior.
            max_tokens (int, optional): Maximum number of tokens to generate.
            temperature (float, optional): Temperature for response generation. Default is 0.7.
            show_reasoning (bool, optional): Whether to show the model's reasoning process. Default is False.
            
        Returns:
            str: The generated response.
        """
        client = anthropic.AsyncAnthropic(
            api_key=CLAUDE_API_KEY,
            http_client=httpx.AsyncClient(
                proxy="http://localhost:7890",
                transport=httpx.HTTPTransport(local_address="0.0.0.0"),
            )
        )
        try:
            logger.debug(f"Sending prompt to Claude: {prompt}...")
            logger.debug(f"System message to Claude: {system_message}")
            if max_tokens is None:
                max_tokens = 1000
            # 非推理模型，通过修改system message和prompt引导模型输出推理过程
            modified_system = system_message
            modified_prompt = prompt
            
            if show_reasoning:
                if modified_system:
                    modified_system += "\n请在回答问题时，先详细说明你的推理过程，然后再给出最终答案。"
                else:
                    modified_system = "请在回答问题时，先详细说明你的推理过程，然后再给出最终答案。"
                
                if "请详细说明你的推理过程" not in modified_prompt:
                    modified_prompt += "\n\n请详细说明你的推理过程，然后再给出最终答案。"
            
            # Call Claude API
            response = await client.messages.create(
                model=CLAUDE_MODEL,
                system=modified_system,
                messages=[
                    {"role": "user", "content": modified_prompt}
                ],
                temperature=temperature,
                max_tokens=max_tokens,
            )
            
            # Extract and return the response text
            response_text = response.content[0].text
            logger.debug(f"Received response from Claude: {response_text}...")
            
            return response_text
            
        except Exception as e:
            logger.error(f"Error generating response from Claude: {str(e)}")
            return f"生成响应时出错: {str(e)}"


class QwenClient:
    """Client for interacting with Qwen API using OpenAI interface."""
    
    # Create a client instance
    # client = AsyncOpenAI(
    #     api_key=QWEN_API_KEY,
    #     base_url=QWEN_API_BASE,
    #     timeout=httpx.Timeout(timeout=180)
    # )
    
    @classmethod
    async def generate_response(cls, prompt, system_message=None, max_tokens=None, temperature=0.7, show_reasoning=False, stream=False, model=None):
        """
        Generate a response from the Qwen API.
        
        Args:
            prompt (str): The prompt to send to the API.
            system_message (str, optional): System message to guide the model's behavior.
            max_tokens (int, optional): Maximum number of tokens to generate.
            temperature (float, optional): Temperature for response generation. Default is 0.7.
            show_reasoning (bool, optional): Whether to show the model's reasoning process. Default is False.
            stream (bool, optional): Whether to use streaming output. Default is False.
            model (str, optional): The specific model to use. If None, uses QWEN_STREAM if stream=True, otherwise QWEN_MODEL.
            
        Returns:
            str: The generated response.
        """
        client = AsyncOpenAI(
            api_key=QWEN_API_KEY,
            base_url=QWEN_API_BASE
        )
        try:
            messages = []
            
            # 根据参数选择模型
            selected_model = model if model else (QWEN_STREAM if stream else QWEN_MODEL)
            logger.debug(f"llm client name: {cls.__name__}")
            logger.info(f"llm client model: {selected_model}")
            logger.debug(f"llm client temperature: {temperature}")
            logger.debug(f"llm client stream: {stream}")
            
            # Add system message if provided
            modified_system_message = system_message
            
            if modified_system_message:
                messages.append({"role": "system", "content": modified_system_message})
                
            # Add user prompt
    
            messages.append({"role": "user", "content": prompt})
            
            logger.debug(f"Sending prompt to Qwen: {prompt}")
            if system_message:
                preview_message = system_message if len(system_message) <= 100 else system_message[:100] + "..."
                logger.debug(f"System message to Qwen: {preview_message}")
            else:
                logger.debug("System message to Qwen: None")
            
            logger.debug(f"System message to Qwen: {system_message[0:100]}...")
            
            # 调用Qwen API
            api_params = {
                "model": selected_model,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
            }
               
            # 如果启用了流式输出，使用流式输出方法
            if stream:
                return await cls.generate_stream_response(prompt, system_message, max_tokens, temperature, show_reasoning, model)
            
            response = await client.chat.completions.create(**api_params)
            
            # 提取并返回响应文本
            response_text = response.choices[0].message.content.strip()
            logger.debug(f"Received response from Qwen: {response_text}...")
            
            # 处理推理过程
            if show_reasoning:
                # 尝试获取推理内容
                reasoning_content = ""
                if hasattr(response.choices[0].message, 'reasoning_content') and response.choices[0].message.reasoning_content:
                    reasoning_content = response.choices[0].message.reasoning_content
                    logger.debug(f"成功捕获到推理过程，长度：{len(reasoning_content)}")
                    return f"推理过程:\n{reasoning_content.strip()}\n\n最终回答:\n{response_text}"
            
            return response_text
            
        except Exception as e:
            logger.error(f"Error generating response from Qwen: {str(e)}")
            return f"生成响应时出错: {str(e)}"
    
    @classmethod
    async def generate_stream_response(cls, prompt, system_message=None, max_tokens=None, temperature=0.7, show_reasoning=False, model=None):
        """
        使用流式输出方式生成响应。
        
        Args:
            prompt (str): 发送给API的提示。
            system_message (str, optional): 引导模型行为的系统消息。
            max_tokens (int, optional): 生成的最大令牌数。
            temperature (float, optional): 响应生成的温度。默认为0.7。
            show_reasoning (bool, optional): 是否显示模型的推理过程。默认为False。
            model (str, optional): 要使用的特定模型。如果为None，则使用QWEN_STREAM。
            
        Returns:
            str: 生成的响应。
        """
        client = AsyncOpenAI(
            api_key=QWEN_API_KEY,
            base_url=QWEN_API_BASE
        )
        try:
            messages = []
            
            # 使用指定模型或默认流式输出模型
            selected_model = model if model else QWEN_STREAM
            
            # 添加系统消息（如果提供）
            if system_message:
                messages.append({"role": "system", "content": system_message})
                
            messages.append({"role": "user", "content": prompt})
                
            # 调用Qwen API（流式）
            api_params = {
                "model": selected_model,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "stream": True,
            }
            
            # 对于qwen3模型，添加enable_thinking参数
            if "qwen3" in selected_model.lower():
                api_params["extra_body"] = {"enable_thinking": True}
                print("开启思考")
                if show_reasoning:
                    print("已启用思考过程捕获")
            
            # 创建流式响应
            stream = await client.chat.completions.create(**api_params)
            
            # 累积响应文本
            full_response = ""
            reasoning_content = ""
            
            # 处理流式响应
            try:
                async for chunk in stream:
                    if chunk.choices and len(chunk.choices) > 0:
                        # 处理推理内容(reasoning_content)
                        if hasattr(chunk.choices[0].delta, 'reasoning_content'):
                            reason = chunk.choices[0].delta.reasoning_content
                            if reason:
                                reasoning_content += reason
                                if show_reasoning:
                                    print(f"\033[33m{reason}\033[0m", end="", flush=True)  # 黄色显示推理过程
                        
                        # 处理常规输出内容
                        if hasattr(chunk.choices[0], 'delta') and hasattr(chunk.choices[0].delta, 'content') and chunk.choices[0].delta.content:
                            content = chunk.choices[0].delta.content
                            print(content, end="", flush=True)
                            full_response += content
            except Exception as e:
                logger.warning(f"Error during stream processing: {str(e)}")
            
            print()  # 在流式输出结束后换行
            
            # 如果启用了推理过程显示并且捕获到了推理内容
            if show_reasoning and reasoning_content:
                logger.debug(f"成功捕获到推理过程，长度：{len(reasoning_content)}")
                return f"推理过程:\n{reasoning_content.strip()}\n\n最终回答:\n{full_response.strip()}"
            
            return full_response
            
        except Exception as e:
            logger.error(f"Error generating stream response from Qwen: {str(e)}")
            return f"生成流式响应时出错: {str(e)}"


class JiuzhangClient:
    """Client for interacting with Jiuzhang API."""
    
    # Create a client instance
    # client = AsyncOpenAI(
    #     api_key=JIUZHANG_API_KEY,
    #     base_url=JIUZHANG_API_BASE
    # )

    @classmethod
    async def generate_response(cls, prompt, system_message=None, max_tokens=None, temperature=0.7, show_reasoning=False, stream=False, model=JIUZHANG_MODEL):
        """
        Generate a response from the Jiuzhang API.
        """
        client = AsyncOpenAI(
            api_key=JIUZHANG_API_KEY,
            base_url=JIUZHANG_API_BASE
        )
        try:
            messages = []

            print(f"llm url: {client.base_url}")

            # Add system message if provided
            if system_message:
                messages.append({"role": "system", "content": system_message})

            # Add user prompt
            messages.append({"role": "user", "content": prompt})

            # 调用Qwen API（流式）
            api_params = {
                "model": model,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "stream": True,
            }

            # 创建流式响应
            stream = await client.chat.completions.create(**api_params)
            
            # 累积响应文本
            full_response = ""
            reasoning_content = ""
            
            # 处理流式响应
            try:
                async for chunk in stream:
                    if chunk.choices and len(chunk.choices) > 0:
                        # 处理推理内容(reasoning_content)
                        if hasattr(chunk.choices[0].delta, 'reasoning_content'):
                            reason = chunk.choices[0].delta.reasoning_content
                            if reason:
                                reasoning_content += reason
                                if show_reasoning:
                                    print(f"\033[33m{reason}\033[0m", end="", flush=True)  # 黄色显示推理过程
                        
                        # 处理常规输出内容
                        if hasattr(chunk.choices[0], 'delta') and hasattr(chunk.choices[0].delta, 'content') and chunk.choices[0].delta.content:
                            content = chunk.choices[0].delta.content
                            print(content, end="", flush=True)
                            full_response += content
            except Exception as e:
                logger.warning(f"Error during stream processing: {str(e)}")
            
            print()  # 在流式输出结束后换行

            # 处理R1模型的特殊格式：推理内容 + </think> + 实际回答
            if '</think>' in full_response:
                # R1模型格式：推理内容在</think>之前，实际回答在</think>之后
                parts = full_response.split('</think>', 1)
                if len(parts) == 2:
                    reasoning_content = parts[0].strip()
                    full_response = parts[1].strip()
                    logger.debug(f"从R1模型响应中提取到推理内容，长度：{len(reasoning_content)}")
                else:
                    # 如果分割失败，保持原始响应
                    logger.warning("无法正确分割R1模型的推理内容")
            else:
                # 尝试处理传统的<think>...</think>格式（向后兼容）
                think_pattern = re.compile(r'<think>(.*?)</think>', re.DOTALL)
                think_matches = think_pattern.findall(full_response)
                if think_matches:
                    reasoning_content = "\n".join(think_matches)
                    full_response = think_pattern.sub('', full_response).strip()
                    logger.debug(f"从传统格式中提取到推理内容，长度：{len(reasoning_content)}")

            
            # 如果启用了推理过程显示并且捕获到了推理内容
            if show_reasoning and reasoning_content:
                logger.debug(f"成功捕获到推理过程，长度：{len(reasoning_content)}")
                return f"推理过程:\n{reasoning_content.strip()}\n\n最终回答:\n{full_response.strip()}"
            
            return full_response


            
        except Exception as e:
            logger.error(f"Error generating response from Jiuzhang: {str(e)}")
            return f"生成响应时出错: {str(e)}"  


class TogetherClient:
    """Client for interacting with Together API."""
    
    # Create a client instance
    # client = AsyncTogether(api_key=TOGETHER_API_KEY)
    
    @classmethod
    async def generate_response(cls, prompt, system_message=None, max_tokens=None, temperature=0.7, show_reasoning=False, model="DUO123/deepseek-ai/DeepSeek-R1-8c48fcde"):
        """
        Generate a response from the Together API.
        
        Args:
            prompt (str): The prompt to send to the API.
            system_message (str, optional): System message to guide the model's behavior.
            max_tokens (int, optional): Maximum number of tokens to generate.
            temperature (float, optional): Temperature for response generation. Default is 0.7.
            show_reasoning (bool, optional): Whether to show the model's reasoning process. Default is False.
            model (str, optional): The specific model to use. If None, uses TOGETHER_MODEL.
            
        Returns:
            str: The generated response.
        """
        client = AsyncTogether(api_key=TOGETHER_API_KEY)
        try:
            messages = []
            
            # 使用指定模型或默认模型
            selected_model = model if model else TOGETHER_MODEL
            logger.debug(f"llm client name: {cls.__name__}")
            logger.info(f"llm client model: {selected_model}")
            logger.debug(f"llm client temperature: {temperature}")
            
            # Add system message if provided
            if system_message:
                messages.append({"role": "system", "content": system_message})
                
            # Add user prompt
            messages.append({"role": "user", "content": prompt})
            
            logger.debug(f"Sending prompt to Together: {prompt}")
            logger.debug(f"System message to Together: {system_message}")
            
            # 调用Together API
            api_params = {
                "model": selected_model,
                "messages": messages,
                "temperature": temperature,
            }
            
            if max_tokens:
                api_params["max_tokens"] = max_tokens
            
            response = await client.chat.completions.create(**api_params)
            
            # 提取响应文本
            response_text = response.choices[0].message.content.strip()
            logger.debug(f"Received response from Together: {response}")
            logger.debug(f"Received response content from Together: {response_text}")
            
            # 处理推理过程 - Together的DeepSeek R1模型支持reasoning_content
            if show_reasoning:
                try:
                    # 尝试获取推理内容
                    reasoning_content = getattr(response.choices[0].message, 'reasoning_content', None)
                    if reasoning_content:
                        logger.debug(f"Found reasoning_content in Together response")
                        return f"推理过程:\n{reasoning_content}\n\n最终回答:\n{response_text}"
                except Exception as e:
                    logger.warning(f"Error extracting reasoning_content from Together response: {str(e)}")
            
            return response_text
            
        except Exception as e:
            logger.error(f"Error generating response from Together: {str(e)}")
            return f"生成响应时出错: {str(e)}"


class PPIOClient:
    """Client for interacting with PPIO API (Deepseek-r1) using OpenAI interface."""
    
    # Create a client instance
    # client = AsyncOpenAI(
    #     api_key=PPIO_API_KEY,
    #     base_url=PPIO_API_BASE,
    # )
    
    @classmethod
    async def generate_response(cls, prompt, system_message=None, max_tokens=None, temperature=0.7, show_reasoning=False, model=None, stream=False):
        """
        Generate a response from the PPIO API (Deepseek-r1).
        
        Args:
            prompt (str): The prompt to send to the API.
            system_message (str, optional): System message to guide the model's behavior.
            max_tokens (int, optional): Maximum number of tokens to generate.
            temperature (float, optional): Temperature for response generation. Default is 0.7.
            show_reasoning (bool, optional): Whether to show the model's reasoning process. Default is False.
            model (str, optional): Model to use. Default is PPIO_MODEL.
            stream (bool, optional): Whether to use streaming. Default is False.
            
        Returns:
            str: The generated response.
        """
        client = AsyncOpenAI(
            api_key=PPIO_API_KEY,
            base_url=PPIO_API_BASE,
        )
        if stream:
            return await cls.generate_stream_response(prompt, system_message, max_tokens, temperature, show_reasoning, model)
        
        retry_count = 0

        try:
            messages = []
            
            # 使用指定模型或默认模型
            selected_model = model if model else PPIO_MODEL
            logger.debug(f"llm client name: {cls.__name__}")
            logger.info(f"llm client model: {selected_model}")
            logger.debug(f"llm client temperature: {temperature}")
            
            # Add system message if provided
            if system_message:
                messages.append({"role": "system", "content": system_message})
                
            # Add user prompt
            messages.append({"role": "user", "content": prompt})
            
            logger.debug(f"Sending prompt to PPIO: {prompt}")
            logger.debug(f"System message to PPIO: {system_message}")
            
            # 构建API参数
            api_params = {
                "model": selected_model,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
            }
            
            # 调用PPIO API
            response = await client.chat.completions.create(**api_params)
            
            # 提取响应文本
            response_text = response.choices[0].message.content.strip()
            logger.debug(f"Received response from PPIO: {response_text}")
            
            # 提取<think>标签中的内容，无论是否需要显示推理过程
            reasoning_content = ""
            cleaned_response = response_text
            
            # 使用正则表达式提取<think>标签中的内容
            think_pattern = re.compile(r'<think>(.*?)</think>', re.DOTALL)
            think_matches = think_pattern.findall(response_text)
            
            if think_matches:
                # 将所有<think>内容合并为推理过程
                reasoning_content = "\n".join(think_matches)
                # 移除响应中的<think>标签及内容
                cleaned_response = think_pattern.sub('', response_text).strip()
                
                logger.debug(f"Found reasoning content in <think> tags, length: {len(reasoning_content)}")
                
                # 根据show_reasoning决定是否显示推理过程
                if show_reasoning:
                    return f"推理过程:\n{reasoning_content}\n\n最终回答:\n{cleaned_response}"
                else:
                    return cleaned_response
            
            return response_text
            
        except Exception as e:
            error_message = str(e)
            logger.error(f"Error generating response from PPIO: {error_message}")
            


class LLMClient:
    """Factory class for interacting with different LLM APIs."""
    
    @staticmethod
    async def generate_response(provider="openai", prompt="", system_message=None, max_tokens=None, temperature=0.7, show_reasoning=False, stream=False, model=None):
        """
        Generate a response from the specified LLM provider.
        
        Args:
            provider (str): The LLM provider to use ('openai', 'volcano', 'claude', 'qwen', 'jiuzhang', 'together', or 'ppio').
            prompt (str): The prompt to send to the API.
            system_message (str, optional): System message to guide the model's behavior.
            max_tokens (int, optional): Maximum number of tokens to generate.
            temperature (float, optional): Temperature for response generation. Default is 0.7.
            show_reasoning (bool, optional): Whether to show the model's reasoning process. Default is False.
            stream (bool, optional): Whether to use streaming output. Default is False.
            model (str, optional): Specific model to use for the provider.
            
        Returns:
            str: The generated response.
        """
        if provider.lower() == "openai":
            return await OpenAIClient.generate_response(prompt, system_message, max_tokens, temperature=temperature, show_reasoning=show_reasoning, stream=stream, model=model)
        elif provider.lower() == "volcano":
            return await VolcanoClient.generate_response(prompt, system_message, max_tokens, temperature=temperature, show_reasoning=show_reasoning, model=model)
        elif provider.lower() == "claude":
            return await ClaudeClient.generate_response(prompt, system_message, max_tokens, temperature=temperature, show_reasoning=show_reasoning)
        elif provider.lower() == "qwen":
            return await QwenClient.generate_response(prompt, system_message, max_tokens, temperature=temperature, show_reasoning=show_reasoning, stream=stream, model=model)
        elif provider.lower() == "jiuzhang":
            return await JiuzhangClient.generate_response(prompt, system_message, max_tokens, temperature=temperature, show_reasoning=show_reasoning, stream=stream, model=model) 
        elif provider.lower() == "together":
            return await TogetherClient.generate_response(prompt, system_message, max_tokens, temperature=temperature, show_reasoning=show_reasoning, model=model)
        elif provider.lower() == "ppio":
            return await PPIOClient.generate_response(prompt, system_message, max_tokens, temperature=temperature, show_reasoning=show_reasoning, stream=stream, model=model)
        else:
            logger.error(f"Unknown provider: {provider}")
            return f"未知的LLM提供商: {provider}"
