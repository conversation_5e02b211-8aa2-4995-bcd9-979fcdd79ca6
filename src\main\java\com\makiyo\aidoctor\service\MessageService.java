package com.makiyo.aidoctor.service;

import com.makiyo.aidoctor.entity.GuidelinesContent;
import com.makiyo.aidoctor.entity.Message;
import com.makiyo.aidoctor.entity.Session; // Added import
import com.makiyo.aidoctor.mapper.MessageMapper;
import com.makiyo.aidoctor.mapper.SessionMapper; // Added import
import com.makiyo.aidoctor.utils.InteractionHistoryUtils;
import com.makiyo.aidoctor.utils.OcrTextToJson;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import java.util.UUID;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException; // Added import
import com.fasterxml.jackson.core.type.TypeReference; // Added import
import com.fasterxml.jackson.databind.ObjectMapper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference; // Added import
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria; // Added import
import org.springframework.data.mongodb.core.query.Query; // Added import
import org.springframework.http.*;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional; // Added import
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap; // Added import
import java.util.List;
import java.util.Map;
import java.util.UUID; // Added import
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;

@Service
public class MessageService {

    private static final Logger log = LoggerFactory.getLogger(MessageService.class);

    // --- Spring Managed Beans ---
    @Resource
    private MessageMapper messageMapper;

    @Resource
    private SessionMapper sessionMapper;
    
    @Resource
    private TtsService ttsService;
    
    @Resource
    private MongoTemplate mongoTemplate;

    @Resource // Assuming ObjectMapper is a Spring-managed bean
    private ObjectMapper objectMapper;

    // 移除注入的RestTemplate，使用构造函数创建的实例
    private RestTemplate restTemplate;

    // --- Other Fields ---
    private final ExecutorService executorService = Executors.newCachedThreadPool();

    // --- Value Injected Fields ---
    @Value("${python.v2.diagnosis.url:http://localhost:5555/ai_doctor_v2_diagnosis}")
    private String pythonV2DiagnosisUrl;

    @Value("${python.v2.inquiry.url:http://localhost:5555/ai_doctor_v2}")
    private String pythonV2InquiryUrl;

    @Value("${python.v2.quick.inquiry.url:http://localhost:5555/ai_doctor_v2_quick_inquiry}")
    private String pythonV2QuickInquiryUrl;

    @Value("${python.v2.preliminary.diagnosis.url:http://localhost:5555/ai_doctor_v2_preliminary_diagnosis}")
    private String pythonV2PreliminaryDiagnosisUrl;

    public MessageService() {
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(100); // Set total max connections
        connectionManager.setDefaultMaxPerRoute(20); // Set max connections per route
        connectionManager.setValidateAfterInactivity(20000); // 20 seconds

        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(600000) // 10 minutes
                .setConnectionRequestTimeout(600000) // 10 minutes
                .setSocketTimeout(600000) // 10 minutes
                .build();

        CloseableHttpClient httpClient = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(requestConfig)
                .evictIdleConnections(30, TimeUnit.SECONDS) // Periodically close idle connections
                .build();

        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory(httpClient);
        this.restTemplate = new RestTemplate(factory);
    }

    /**
     * 获取会话的所有消息
     * @param sessionId 会话ID
     * @return 消息列表
     */
    public List<Message> getSessionMessages(Integer sessionId) {
        return messageMapper.selectBySessionId(sessionId);
    }

    /**
     * 创建新消息
     * @param message 消息内容
     * @return 创建的消息
     */
    public Message createMessage(Message message) {
        message.setCreatedAt(LocalDateTime.now());
        messageMapper.insertSelective(message);
        return message;
    }

    /**
     * 与AI Doctor进行流式对话
     * @param sessionId 会话ID
     * @param userId 用户ID
     * @param userMessage 用户消息
     * @return SSE发射器
     */
    public SseEmitter chatAiDoctor(Integer sessionId, Integer userId, String userMessage) {
        SseEmitter emitter = new SseEmitter(300000L); // 5分钟超时
        
        try {
            // 保存用户消息
            Message userMessageEntity = new Message();
            userMessageEntity.setSessionId(sessionId);
            userMessageEntity.setSpeaker("patient");
            userMessageEntity.setMessage(userMessage);
            createMessage(userMessageEntity);

            // 获取会话的所有历史消息
            List<Message> sessionMessages = getSessionMessages(sessionId);
            List<String> messageHistory = new ArrayList<>();
            String lastMessage = null;

            // 转换所有历史消息为API所需格式（添加适当的标签）
            for (Message msg : sessionMessages) {
                String taggedMessage;
                if ("doctor".equals(msg.getSpeaker())) {
                    taggedMessage = "<text>\n" + msg.getMessage() + "\n</text>";
                } else {
                    taggedMessage = "<observation>\n" + msg.getMessage() + "\n</observation>";
                }
                
                // 避免添加重复的消息
                if (!taggedMessage.equals(lastMessage)) {
                    messageHistory.add(taggedMessage);
                    lastMessage = taggedMessage;
                }
            }

            // 设置 SSE 连接回调
            emitter.onCompletion(() -> {
                System.out.println("SSE completed");
            });

            emitter.onTimeout(() -> {
                System.out.println("SSE timeout");
                emitter.complete();
            });

            emitter.onError((ex) -> {
                System.err.println("SSE error: " + ex.getMessage());
                emitter.complete();
            });

            // 异步处理诊断请求
            executorService.execute(() -> {
                try {
                    // 准备诊断请求
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.APPLICATION_JSON);

                    Map<String, Object> requestBody = new HashMap<>();
                    requestBody.put("model", "deepseek-r1-250120");
                    requestBody.put("history_doc", messageHistory);

                    // 打印请求体日志
                    System.out.println("发送请求体: " + objectMapper.writeValueAsString(requestBody));

                    HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

                    try {
                        // 发送请求到诊断服务
                        ResponseEntity<String> response = restTemplate.exchange(
                            "http://localhost:5555/v1/chat/completions",
                            HttpMethod.POST,
                            requestEntity,
                            String.class
                        );

                        if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                            System.out.println("收到 AI 服务响应: " + response.getBody());
                            
                            // 解析响应数据
                            String responseBody = response.getBody();
                            StringBuilder doctorResponse = new StringBuilder();

                            // 处理响应内容
                            try {
                                Map<String, Object> jsonResponse = objectMapper.readValue(responseBody, Map.class);
                                String content = null;

                                // 提取消息内容
                                if (jsonResponse.containsKey("choices")) {
                                    List<Map<String, Object>> choices = (List<Map<String, Object>>) jsonResponse.get("choices");
                                    if (!choices.isEmpty() && choices.get(0).containsKey("message")) {
                                        Map<String, Object> message = (Map<String, Object>) choices.get(0).get("message");
                                        if (message.containsKey("content")) {
                                            content = (String) message.get("content");
                                            
                                            // 检查是否包含solution标记
                                            if (content.contains("</text solution = 'True'>")) {
                                                // 移除<text>开始标签
                                                content = content.replace("<text>", "");
                                                // 将</text solution = 'True'>替换为 solution = 'True'
                                                content = content.replace("</text solution = 'True'>", " solution = 'True'");
                                            } else {
                                                // 移除所有 <text> 标签
                                                content = content.replaceAll("</?text>", "").trim();
                                            }
                                        }
                                    }
                                }

                                if (content != null && !content.isEmpty()) {
                                    // 生成语音文件
                                    String audioUrl = null;
                                    try {
                                        // 为语音生成准备不含solution标记的内容
                                        String audioContent = content;
                                        if (content.contains("solution = 'True'")) {
                                            audioContent = content.replace(" solution = 'True'", "").trim();
                                        }
                                        audioUrl = ttsService.generateTtsUrl(audioContent);
                                    } catch (Exception e) {
                                        System.err.println("生成语音失败: " + e.getMessage());
                                    }

                                    // 发送数据到客户端
                                    Map<String, Object> responseData = new HashMap<>();
                                    responseData.put("content", content);
                                    if (audioUrl != null) {
                                        responseData.put("audioUrl", audioUrl);
                                    }
                                    String jsonData = objectMapper.writeValueAsString(responseData);
                                    
                                    try {
                                        emitter.send(SseEmitter.event()
                                            .data(jsonData)
                                            .id(String.valueOf(System.currentTimeMillis()))
                                            .name("message"));
                                    } catch (IllegalStateException e) {
                                        System.out.println("Emitter already completed, skipping message send");
                                    }
                                    
                                    // 累积医生响应
                                    doctorResponse.append(content);
                                    
                                    // 保存医生响应
                                    Message doctorMessageEntity = new Message();
                                    doctorMessageEntity.setSessionId(sessionId);
                                    doctorMessageEntity.setSpeaker("doctor");
                                    doctorMessageEntity.setMessage(content);
                                    createMessage(doctorMessageEntity);
                                    System.out.println("保存医生响应: " + content);
                                } else {
                                    System.out.println("未找到有效的响应内容");
                                    try {
                                        emitter.send(SseEmitter.event()
                                            .data("Error: No valid response content")
                                            .name("error"));
                                    } catch (IllegalStateException e) {
                                        System.out.println("Emitter already completed, skipping error message");
                                    }
                                }
                            } catch (Exception e) {
                                System.err.println("处理 AI 响应时出错: " + e.getMessage());
                                e.printStackTrace();
                                try {
                                    emitter.send(SseEmitter.event()
                                        .data("Error processing AI response: " + e.getMessage())
                                        .name("error"));
                                } catch (IllegalStateException ex) {
                                    System.out.println("Emitter already completed, skipping error message");
                                }
                            }
                        } else {
                            System.err.println("AI 服务响应异常: " + response.getStatusCode());
                            try {
                                emitter.send(SseEmitter.event()
                                    .data("Error: AI service returned " + response.getStatusCode())
                                    .name("error"));
                            } catch (IllegalStateException e) {
                                System.out.println("Emitter already completed, skipping error message");
                            }
                        }

                        // 完成 SSE
                        try {
                            emitter.send(SseEmitter.event().data("[DONE]"));
                            emitter.complete();
                        } catch (IllegalStateException e) {
                            System.out.println("Emitter already completed");
                        }

                    } catch (Exception e) {
                        System.err.println("Error processing diagnosis: " + e.getMessage());
                        try {
                            emitter.send(SseEmitter.event()
                                .data("Error: " + e.getMessage())
                                .name("error"));
                            emitter.complete();
                        } catch (IllegalStateException ex) {
                            System.out.println("Emitter already completed, skipping error message");
                        }
                    }

                } catch (Exception e) {
                    System.err.println("Error processing diagnosis: " + e.getMessage());
                    try {
                        emitter.send(SseEmitter.event().data("Error: " + e.getMessage()));
                        emitter.complete();
                    } catch (IOException ex) {
                        System.err.println("Error sending error message: " + ex.getMessage());
                    }
                }
            });

        } catch (Exception e) {
            System.err.println("Error in chatAiDoctor: " + e.getMessage());
            emitter.completeWithError(e);
        }

        return emitter;
    }

    /**
     * 与AI Doctor进行问诊对话
     * @param sessionId 会话ID
     * @param userId 用户ID
     * @param userMessage 用户消息
     * @return SSE发射器
     */
    public SseEmitter inquiryAiDoctor(Integer sessionId, Integer userId, String userMessage) {
        SseEmitter emitter = new SseEmitter(300000L); // 5分钟超时
        
        try {
            // 保存用户消息
            Message userMessageEntity = new Message();
            userMessageEntity.setSessionId(sessionId);
            userMessageEntity.setSpeaker("patient");
            userMessageEntity.setMessage(userMessage);
            createMessage(userMessageEntity);

            // 获取会话的所有历史消息
            List<Message> sessionMessages = getSessionMessages(sessionId);
            List<String> messageHistory = new ArrayList<>();
            String lastMessage = null;

            // 转换所有历史消息为API所需格式（添加适当的标签）
            for (Message msg : sessionMessages) {
                String taggedMessage;
                if ("doctor".equals(msg.getSpeaker())) {
                    taggedMessage = "<text>\n" + msg.getMessage() + "\n</text>";
                } else {
                    taggedMessage = "<observation>\n" + msg.getMessage() + "\n</observation>";
                }
                
                // 避免添加重复的消息
                if (!taggedMessage.equals(lastMessage)) {
                    messageHistory.add(taggedMessage);
                    lastMessage = taggedMessage;
                }
            }

            // 设置 SSE 连接回调
            emitter.onCompletion(() -> {
                System.out.println("SSE completed");
            });

            emitter.onTimeout(() -> {
                System.out.println("SSE timeout");
                emitter.complete();
            });

            emitter.onError((ex) -> {
                System.err.println("SSE error: " + ex.getMessage());
                emitter.complete();
            });

            // 异步处理诊断请求
            executorService.execute(() -> {
                try {
                    // 准备诊断请求
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.APPLICATION_JSON);

                    Map<String, Object> requestBody = new HashMap<>();
                    requestBody.put("model", "deepseek-r1-250120");
                    requestBody.put("history_doc", messageHistory);

                    // 打印请求体日志
                    System.out.println("发送请求体: " + objectMapper.writeValueAsString(requestBody));

                    HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

                    try {
                        // 发送请求到诊断服务
                        ResponseEntity<String> response = restTemplate.exchange(
                            "http://localhost:5555/v1/chat/inquiry",
                            HttpMethod.POST,
                            requestEntity,
                            String.class
                        );

                        if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                            System.out.println("收到 AI 服务响应: " + response.getBody());
                            
                            // 解析响应数据
                            String responseBody = response.getBody();
                            StringBuilder doctorResponse = new StringBuilder();

                            // 处理响应内容
                            try {
                                Map<String, Object> jsonResponse = objectMapper.readValue(responseBody, Map.class);
                                String content = null;

                                // 提取消息内容
                                if (jsonResponse.containsKey("choices")) {
                                    List<Map<String, Object>> choices = (List<Map<String, Object>>) jsonResponse.get("choices");
                                    if (!choices.isEmpty() && choices.get(0).containsKey("message")) {
                                        Map<String, Object> message = (Map<String, Object>) choices.get(0).get("message");
                                        if (message.containsKey("content")) {
                                            content = (String) message.get("content");
                                            
                                            // 检查是否包含solution标记
                                            if (content.contains("</text solution = 'True'>")) {
                                                // 移除<text>开始标签
                                                content = content.replace("<text>", "");
                                                // 将</text solution = 'True'>替换为 solution = 'True'
                                                content = content.replace("</text solution = 'True'>", " solution = 'True'");
                                            } else {
                                                // 移除所有 <text> 标签
                                                content = content.replaceAll("</?text>", "").trim();
                                            }
                                        }
                                    }
                                }

                                if (content != null && !content.isEmpty()) {
                                    // 生成语音文件
                                    String audioUrl = null;
                                    try {
                                        // 为语音生成准备不含solution标记的内容
                                        String audioContent = content;
                                        if (content.contains("solution = 'True'")) {
                                            audioContent = content.replace(" solution = 'True'", "").trim();
                                        }
                                        audioUrl = ttsService.generateTtsUrl(audioContent);
                                    } catch (Exception e) {
                                        System.err.println("生成语音失败: " + e.getMessage());
                                    }

                                    // 发送数据到客户端
                                    Map<String, Object> responseData = new HashMap<>();
                                    responseData.put("content", content);
                                    if (audioUrl != null) {
                                        responseData.put("audioUrl", audioUrl);
                                    }
                                    String jsonData = objectMapper.writeValueAsString(responseData);
                                    
                                    try {
                                        emitter.send(SseEmitter.event()
                                            .data(jsonData)
                                            .id(String.valueOf(System.currentTimeMillis()))
                                            .name("message"));
                                    } catch (IllegalStateException e) {
                                        System.out.println("Emitter already completed, skipping message send");
                                    }
                                    
                                    // 累积医生响应
                                    doctorResponse.append(content);
                                    
                                    // 保存医生响应
                                    Message doctorMessageEntity = new Message();
                                    doctorMessageEntity.setSessionId(sessionId);
                                    doctorMessageEntity.setSpeaker("doctor");
                                    doctorMessageEntity.setMessage(content);
                                    createMessage(doctorMessageEntity);
                                    System.out.println("保存医生响应: " + content);
                                } else {
                                    System.out.println("未找到有效的响应内容");
                                    try {
                                        emitter.send(SseEmitter.event()
                                            .data("Error: No valid response content")
                                            .name("error"));
                                    } catch (IllegalStateException e) {
                                        System.out.println("Emitter already completed, skipping error message");
                                    }
                                }
                            } catch (Exception e) {
                                System.err.println("处理 AI 响应时出错: " + e.getMessage());
                                e.printStackTrace();
                                try {
                                    emitter.send(SseEmitter.event()
                                        .data("Error processing AI response: " + e.getMessage())
                                        .name("error"));
                                } catch (IllegalStateException ex) {
                                    System.out.println("Emitter already completed, skipping error message");
                                }
                            }
                        } else {
                            System.err.println("AI 服务响应异常: " + response.getStatusCode());
                            try {
                                emitter.send(SseEmitter.event()
                                    .data("Error: AI service returned " + response.getStatusCode())
                                    .name("error"));
                            } catch (IllegalStateException e) {
                                System.out.println("Emitter already completed, skipping error message");
                            }
                        }

                        // 完成 SSE
                        try {
                            emitter.send(SseEmitter.event().data("[DONE]"));
                            emitter.complete();
                        } catch (IllegalStateException e) {
                            System.out.println("Emitter already completed");
                        }

                    } catch (Exception e) {
                        System.err.println("Error processing diagnosis: " + e.getMessage());
                        try {
                            emitter.send(SseEmitter.event()
                                .data("Error: " + e.getMessage())
                                .name("error"));
                            emitter.complete();
                        } catch (IllegalStateException ex) {
                            System.out.println("Emitter already completed, skipping error message");
                        }
                    }

                } catch (Exception e) {
                    System.err.println("Error processing diagnosis: " + e.getMessage());
                    try {
                        emitter.send(SseEmitter.event().data("Error: " + e.getMessage()));
                        emitter.complete();
                    } catch (IOException ex) {
                        System.err.println("Error sending error message: " + ex.getMessage());
                    }
                }
            });

        } catch (Exception e) {
            System.err.println("Error in inquiryAiDoctor: " + e.getMessage());
            emitter.completeWithError(e);
        }

        return emitter;
    }

    /**
     * 与AI Doctor进行诊断对话
     * @param sessionId 会话ID
     * @param userId 用户ID
     * @param solutionText 诊断方案文本，可选参数
     * @return SSE发射器
     */
    public SseEmitter diagnoseAiDoctor(Integer sessionId, Integer userId, String solutionText) {
        SseEmitter emitter = new SseEmitter(300000L); // 5分钟超时
        
        try {
            // 设置 SSE 连接回调
            emitter.onCompletion(() -> {
                System.out.println("SSE completed");
            });

            emitter.onTimeout(() -> {
                System.out.println("SSE timeout");
                emitter.complete();
            });

            emitter.onError((ex) -> {
                System.err.println("SSE error: " + ex.getMessage());
                emitter.complete();
            });

            // 异步处理诊断请求
            executorService.execute(() -> {
                try {
                    // 查询包含solution = 'True'的消息
                    List<Message> sessionMessages = getSessionMessages(sessionId);
                    String solutionMessage = null;
                    
                    // 查找包含solution = 'True'的消息
                    for (Message msg : sessionMessages) {
                        if ("doctor".equals(msg.getSpeaker()) && msg.getMessage().contains("solution = 'True'")) {
                            solutionMessage = msg.getMessage();
                            break;
                        }
                    }
                    
                    if (solutionMessage == null) {
                        System.err.println("未找到包含solution = 'True'的消息");
                        try {
                            emitter.send(SseEmitter.event()
                                .data("Error: 未找到包含solution = 'True'的消息")
                                .name("error"));
                            emitter.complete();
                            return;
                        } catch (IllegalStateException e) {
                            System.out.println("Emitter already completed, skipping error message");
                            return;
                        }
                    }
                    
                    // 处理找到的解决方案消息
                    String processedSolution = solutionMessage.replace(" solution = 'True'", "").trim();
                    
                    // 如果提供了solutionText，则将其添加到解决方案消息中
                    if (solutionText != null && !solutionText.isEmpty()) {
                        processedSolution = processedSolution + " " + solutionText;
                    }
                    
                    // 准备诊断请求
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.APPLICATION_JSON);

                    // 构建请求体
                    Map<String, Object> requestBody = new HashMap<>();
                    requestBody.put("model", "deepseek-r1-250120");
                    
                    // 处理文本格式，确保换行符正确
                    String processedText = processedSolution.replace("\\n", "\n");
                    requestBody.put("solution_text", processedText);

                    // 打印请求体日志
                    System.out.println("发送请求体: " + objectMapper.writeValueAsString(requestBody));

                    HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

                    try {
                        // 发送请求到诊断服务
                        ResponseEntity<String> response = restTemplate.exchange(
                            "http://localhost:5555/v1/chat/diagnose",
                            HttpMethod.POST,
                            requestEntity,
                            String.class
                        );

                        if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                            System.out.println("收到 AI 服务响应: " + response.getBody());
                            
                            // 解析响应数据
                            String responseBody = response.getBody();
                            StringBuilder doctorResponse = new StringBuilder();

                            // 处理响应内容
                            try {
                                Map<String, Object> jsonResponse = objectMapper.readValue(responseBody, Map.class);
                                String content = null;

                                // 提取消息内容
                                if (jsonResponse.containsKey("choices")) {
                                    List<Map<String, Object>> choices = (List<Map<String, Object>>) jsonResponse.get("choices");
                                    if (!choices.isEmpty() && choices.get(0).containsKey("message")) {
                                        Map<String, Object> message = (Map<String, Object>) choices.get(0).get("message");
                                        if (message.containsKey("content")) {
                                            content = (String) message.get("content");
                                            
                                            // 检查是否包含solution标记
                                            if (content.contains("</text solution = 'True'>")) {
                                                // 移除<text>开始标签
                                                content = content.replace("<text>", "");
                                                // 将</text solution = 'True'>替换为 solution = 'True'
                                                content = content.replace("</text solution = 'True'>", " solution = 'True'");
                                            } else {
                                                // 移除所有 <text> 标签
                                                content = content.replaceAll("</?text>", "").trim();
                                            }
                                        }
                                    }
                                }

                                if (content != null && !content.isEmpty()) {
                                    // 生成语音文件
                                    String audioUrl = null;
                                    try {
                                        // 为语音生成准备不含solution标记的内容
                                        String audioContent = content;
                                        if (content.contains("solution = 'True'")) {
                                            audioContent = content.replace(" solution = 'True'", "").trim();
                                        }
                                        audioUrl = ttsService.generateTtsUrl(audioContent);
                                    } catch (Exception e) {
                                        System.err.println("生成语音失败: " + e.getMessage());
                                    }

                                    // 发送数据到客户端
                                    Map<String, Object> responseData = new HashMap<>();
                                    responseData.put("content", content);
                                    if (audioUrl != null) {
                                        responseData.put("audioUrl", audioUrl);
                                    }
                                    String jsonData = objectMapper.writeValueAsString(responseData);
                                    
                                    try {
                                        emitter.send(SseEmitter.event()
                                            .data(jsonData)
                                            .id(String.valueOf(System.currentTimeMillis()))
                                            .name("message"));
                                    } catch (IllegalStateException e) {
                                        System.out.println("Emitter already completed, skipping message send");
                                    }
                                    
                                    // 累积医生响应
                                    doctorResponse.append(content);
                                    
                                    // 保存医生响应
                                    Message doctorMessageEntity = new Message();
                                    doctorMessageEntity.setSessionId(sessionId);
                                    doctorMessageEntity.setSpeaker("doctor");
                                    doctorMessageEntity.setMessage(content);
                                    createMessage(doctorMessageEntity);
                                    System.out.println("保存医生响应: " + content);
                                } else {
                                    System.out.println("未找到有效的响应内容");
                                    try {
                                        emitter.send(SseEmitter.event()
                                            .data("Error: No valid response content")
                                            .name("error"));
                                    } catch (IllegalStateException e) {
                                        System.out.println("Emitter already completed, skipping error message");
                                    }
                                }
                            } catch (Exception e) {
                                System.err.println("处理 AI 响应时出错: " + e.getMessage());
                                e.printStackTrace();
                                try {
                                    emitter.send(SseEmitter.event()
                                        .data("Error processing AI response: " + e.getMessage())
                                        .name("error"));
                                } catch (IllegalStateException ex) {
                                    System.out.println("Emitter already completed, skipping error message");
                                }
                            }
                        } else {
                            System.err.println("AI 服务响应异常: " + response.getStatusCode());
                            try {
                                emitter.send(SseEmitter.event()
                                    .data("Error: AI service returned " + response.getStatusCode())
                                    .name("error"));
                            } catch (IllegalStateException e) {
                                System.out.println("Emitter already completed, skipping error message");
                            }
                        }

                        // 完成 SSE
                        try {
                            emitter.send(SseEmitter.event().data("[DONE]"));
                            emitter.complete();
                        } catch (IllegalStateException e) {
                            System.out.println("Emitter already completed");
                        }

                    } catch (Exception e) {
                        System.err.println("Error processing diagnosis: " + e.getMessage());
                        try {
                            emitter.send(SseEmitter.event()
                                .data("Error: " + e.getMessage())
                                .name("error"));
                            emitter.complete();
                        } catch (IllegalStateException ex) {
                            System.out.println("Emitter already completed, skipping error message");
                        }
                    }

                } catch (Exception e) {
                    System.err.println("Error processing diagnosis: " + e.getMessage());
                    try {
                        emitter.send(SseEmitter.event().data("Error: " + e.getMessage()));
                        emitter.complete();
                    } catch (IOException ex) {
                        System.err.println("Error sending error message: " + ex.getMessage());
                    }
                }
            });

        } catch (Exception e) {
            System.err.println("Error in diagnoseAiDoctor: " + e.getMessage());
            emitter.completeWithError(e);
        }

        return emitter;
    }

    /**
     * 与AI Doctor V2进行问诊对话
     * @param sessionId 会话ID
     * @param userMessage 用户消息
     * @return SSE发射器
     */
    public SseEmitter inquiryAiDoctorV2(Integer sessionId, String userMessage) {
        SseEmitter emitter = new SseEmitter(300000L);
        final Message latestMessage; // Declare here to be accessible in finally block

        try {
            // --- Step 1: Read latest message JSON from DB ---
            log.info("[V2 Revised] Starting inquiry for session {}", sessionId);
            List<Message> sessionMessages = getSessionMessages(sessionId);
            String latestMessageJsonStr = null;

            if (sessionMessages == null || sessionMessages.isEmpty()) {
                log.warn("[V2 Revised] Session {} is empty or not found. Creating initial structure.", sessionId);
                Map<String, Object> initialStructure = new HashMap<>();
                initialStructure.put("interaction_history", createInitialInnerInteractionHistory());
                latestMessageJsonStr = objectMapper.writeValueAsString(initialStructure);
                // Create a new message record for the initial structure
                Message newMessage = new Message();
                newMessage.setSessionId(sessionId);
                newMessage.setSpeaker("system");
                newMessage.setMessage(latestMessageJsonStr); // latestMessageJsonStr holds the initial JSON here
                latestMessage = createMessage(newMessage);
                log.info("[V2 Revised] Created initial message record with ID {} for session {}", latestMessage.getId(), sessionId);
            } else {
                latestMessage = sessionMessages.get(sessionMessages.size() - 1); // Get the last message
                latestMessageJsonStr = latestMessage.getMessage();
                log.info("[V2 Revised] Found latest message record with ID {} for session {}", latestMessage.getId(), sessionId);

                // --- Robust JSON Validation ---
                boolean isValidJson = false;
                if (latestMessageJsonStr != null && !latestMessageJsonStr.trim().isEmpty()) {
                    String trimmedJson = latestMessageJsonStr.trim();
                    if (trimmedJson.startsWith("{") && trimmedJson.endsWith("}")) {
                         try {
                             // Try a minimal parse to check basic validity
                             objectMapper.readTree(trimmedJson); // Use readTree for lightweight validation
                             isValidJson = true;
                             log.debug("[V2 Revised] Latest message ID {} content appears to be a valid JSON object.", latestMessage.getId());
                         } catch (IOException e) {
                             log.warn("[V2 Revised] Latest message ID {} content is not valid JSON: {}. Content snippet: {}", latestMessage.getId(), e.getMessage(), trimmedJson.substring(0, Math.min(trimmedJson.length(), 100)));
                             isValidJson = false;
                         }
                    } else {
                         log.warn("[V2 Revised] Latest message ID {} content does not appear to be a JSON object. Content snippet: {}", latestMessage.getId(), trimmedJson.substring(0, Math.min(trimmedJson.length(), 100)));
                         isValidJson = false;
                    }
                } else {
                    log.warn("[V2 Revised] Latest message ID {} content is null or empty.", latestMessage.getId());
                    isValidJson = false;
                }

                if (!isValidJson) {
                    log.warn("[V2 Revised] Invalid or missing JSON in latest message ID {}. Creating initial structure instead.", latestMessage.getId());
                    Map<String, Object> initialStructure = new HashMap<>();
                    initialStructure.put("interaction_history", createInitialInnerInteractionHistory());
                    latestMessageJsonStr = objectMapper.writeValueAsString(initialStructure);
                }
                // --- End JSON Validation ---
            }

            // Now latestMessageJsonStr is guaranteed to be a valid JSON string (either original or initial)

            // --- Step 2: Add Patient Message using Utility ---
            log.debug("[V2 Revised] Adding patient message to JSON for session {}", sessionId);
            String jsonWithUserMessage = InteractionHistoryUtils.addPatientMessage(latestMessageJsonStr, userMessage);

            if (jsonWithUserMessage == null) {
                log.error("[V2 Revised] Failed to add patient message using InteractionHistoryUtils for session {}. Aborting.", sessionId);
                emitter.send(SseEmitter.event().name("error").data("{\"error\":\"Failed to prepare message history\"}"));
                emitter.complete();
                return emitter;
            }
            log.debug("[V2 Revised] JSON with user message prepared (first 200 chars): {}", jsonWithUserMessage.substring(0, Math.min(jsonWithUserMessage.length(), 200)));

            // --- Step 3: Prepare request body for Python ---
            Map<String, Object> requestBodyMap;
            try {
                // Parse the JSON *after* adding the user message to send to Python
                Map<String, Object> parsedJsonWithUser = objectMapper.readValue(jsonWithUserMessage, new TypeReference<Map<String, Object>>() {});
                requestBodyMap = parsedJsonWithUser; // Send the entire structure
                log.debug("[V2 Revised] Parsed JSON with user message for Python request.");
            } catch (IOException e) {
                log.error("[V2 Revised] Failed to parse JSON after adding user message for session {}: {}", sessionId, e.getMessage(), e);
                emitter.send(SseEmitter.event().name("error").data("{\"error\":\"Internal error parsing message history\"}"));
                emitter.complete();
                return emitter;
            }

            // --- Setup SSE Callbacks ---
            emitter.onCompletion(() -> log.info("[V2 Revised] SSE completed for session {}", sessionId));
            emitter.onTimeout(() -> { log.warn("[V2 Revised] SSE timeout for session {}", sessionId); emitter.complete(); });
            emitter.onError((ex) -> { log.error("[V2 Revised] SSE error for session {}: {}", sessionId, ex.getMessage(), ex); emitter.complete(); });

            // --- Step 4-6: Asynchronous Python Call, DB Update, Transform, Send SSE ---
            executorService.execute(() -> {
                String rawPythonResponseStr = null; // To store the original response for COT and DB
                try {
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.APPLICATION_JSON);
                    log.info("[V2 Revised] Sending request to Python V2 Inquiry service for session {}", sessionId);

                    HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBodyMap, headers);
                    ResponseEntity<String> response;

                    try {
                        response = restTemplate.exchange(
                            pythonV2InquiryUrl, HttpMethod.POST, requestEntity, String.class
                        );
                        rawPythonResponseStr = response.getBody();
                    } catch (RestClientException e) {
                        log.error("[V2] Error calling Python service for session {}: {}", sessionId, e.getMessage(), e);
                        emitter.send(SseEmitter.event().name("error").data("{\"error\":\"AI service connection failed: " + e.getMessage() + "\"}"));
                        emitter.complete();
                        return; // Exit current async task
                    }

                    if (response.getStatusCode() == HttpStatus.OK && rawPythonResponseStr != null && !rawPythonResponseStr.trim().isEmpty()) {
                        log.info("[V2] Received OK response from Python for session {}", sessionId);
                        log.debug("[V2] Raw Python Response (first 200 chars): {}", rawPythonResponseStr.substring(0, Math.min(rawPythonResponseStr.length(), 200)));

                        // 检查Python返回的JSON是否缺少"interaction_history"外层包装
                        try {
                            Map<String, Object> responseMap = objectMapper.readValue(rawPythonResponseStr, new TypeReference<Map<String, Object>>() {});
                            
                            // 如果直接返回的是内部数据结构(不包含interaction_history作为顶层key)
                            // 则需要添加外层包装以符合Java端期望的格式
                            if (!responseMap.containsKey("interaction_history") && responseMap.containsKey("cot_entries")) {
                                log.info("[V2] Python response missing 'interaction_history' wrapper, adding it");
                                Map<String, Object> wrappedResponse = new HashMap<>();
                                wrappedResponse.put("interaction_history", responseMap);
                                rawPythonResponseStr = objectMapper.writeValueAsString(wrappedResponse);
                                log.debug("[V2] Wrapped response (first 200 chars): {}", 
                                    rawPythonResponseStr.substring(0, Math.min(rawPythonResponseStr.length(), 200)));
                            }
                        } catch (Exception e) {
                            log.warn("[V2] Error checking/fixing Python response format: {}", e.getMessage());
                            // 继续使用原始响应，错误处理交给下一阶段
                        }

                        try {
                            latestMessage.setMessage(rawPythonResponseStr);
                            latestMessage.setCreatedAt(LocalDateTime.now());
                            messageMapper.updateByPrimaryKeySelective(latestMessage);
                            log.info("[V2] Database updated successfully with raw Python response for message ID {} session {}", latestMessage.getId(), sessionId);
                        } catch (Exception dbEx) {
                            log.error("[V2] Error updating database with raw Python response for message ID {} session {}: {}", latestMessage.getId(), sessionId, dbEx.getMessage(), dbEx);
                        }

                        log.debug("[V2 Revised] Transforming Python response for SSE content extraction...");
                        String transformedJsonStr = InteractionHistoryUtils.transformInteractionHistory(rawPythonResponseStr);

                        if (transformedJsonStr == null) {
                            log.error("[V2 Revised] Failed to transform Python response using InteractionHistoryUtils for session {}. Sending raw response as fallback.", sessionId);
                            String fallbackContent = extractLastDoctorMessage(objectMapper.readValue(rawPythonResponseStr, Map.class));
                            if (fallbackContent == null || fallbackContent.isEmpty()) fallbackContent = "Error processing AI response structure.";

                             Map<String, Object> fallbackData = new HashMap<>();
                             fallbackData.put("content", fallbackContent);
                             fallbackData.put("cot", rawPythonResponseStr);
                             try { emitter.send(SseEmitter.event().data(objectMapper.writeValueAsString(fallbackData)).name("message")); } catch (Exception sendEx) { log.error("Error sending fallback SSE", sendEx); }
                             emitter.complete();
                             return;
                        }
                        log.debug("[V2 Revised] Transformed JSON (first 200 chars): {}", transformedJsonStr.substring(0, Math.min(transformedJsonStr.length(), 200)));

                        Map<String, Object> transformedMap;
                        try {
                            transformedMap = objectMapper.readValue(transformedJsonStr, new TypeReference<Map<String, Object>>() {});
                        } catch (IOException parseEx) {
                             log.error("[V2 Revised] Failed to parse transformed JSON for session {}: {}", sessionId, parseEx.getMessage(), parseEx);
                             emitter.send(SseEmitter.event().name("error").data("{\"error\":\"Internal error parsing transformed response\"}"));
                             emitter.complete();
                             return;
                        }

                        String contentForSSE = null;
                        String latestStrategyFromTransformed = null;

                        try {
                            Map<String, Object> transformedHistory = (Map<String, Object>) transformedMap.get("interaction_history");
                            List<Map<String, Object>> transformedEntries = (List<Map<String, Object>>) transformedHistory.get("cot_entries");
                            if (transformedEntries != null && !transformedEntries.isEmpty()) {
                                latestStrategyFromTransformed = getStringValue(transformedEntries.get(0), "strategy");
                            }
                            log.debug("[V2 Revised] Extracted strategy from transformed JSON: {}", latestStrategyFromTransformed);

                            if (transformedEntries != null && !transformedEntries.isEmpty()) {
                                List<Map<String, Object>> mergedDialogues = (List<Map<String, Object>>) transformedEntries.get(0).get("dialogue_history");
                                if (mergedDialogues != null && !mergedDialogues.isEmpty()){
                                    for (int i = mergedDialogues.size() - 1; i >= 0; i--) {
                                        Map<String, Object> dialogue = mergedDialogues.get(i);
                                        if (dialogue != null && "doctor".equals(dialogue.get("role"))) {
                                             contentForSSE = getStringValue(dialogue, "content");
                                             log.debug("[V2 Revised] Extracted last doctor message from transformed JSON: {}", contentForSSE);
                                             break; 
                                        }
                                    }
                                }
                            }

                            if (latestStrategyFromTransformed != null && latestStrategyFromTransformed.contains("诊断")) {
                                log.info("[V2 Revised] Transformed Strategy contains \'诊断\', using Strategy for SSE content (Session {})", sessionId);
                                contentForSSE = latestStrategyFromTransformed;
                                
                                // 提取诊断信息并存入preliminary_diagnosis字段
                                try {
                                    // 从原始Python响应中获取interaction_history
                                    Map<String, Object> responseRootMap = objectMapper.readValue(rawPythonResponseStr, new TypeReference<Map<String, Object>>() {});
                                    if (responseRootMap.containsKey("interaction_history")) {
                                        Map<String, Object> interactionHistory = (Map<String, Object>) responseRootMap.get("interaction_history");
                                        
                                        // 提取诊断信息，可能在策略中包含"诊断"或"诊断策略:"等字样
                                        String diagnosisInfo = latestStrategyFromTransformed;
                                        int diagnosisIndex = diagnosisInfo.indexOf("诊断");
                                        if (diagnosisIndex >= 0) {
                                            // 提取诊断后面的内容作为初步诊断
                                            String extractedDiagnosis = diagnosisInfo.substring(diagnosisIndex);
                                            log.info("[V2 Revised] Extracted diagnosis info: {}", extractedDiagnosis);
                                            
                                            // 将提取的诊断信息存入preliminary_diagnosis字段
                                            interactionHistory.put("preliminary_diagnosis", extractedDiagnosis);
                                            
                                            // 更新原始响应JSON
                                            rawPythonResponseStr = objectMapper.writeValueAsString(responseRootMap);
                                            
                                            // 更新数据库
                                            latestMessage.setMessage(rawPythonResponseStr);
                                            messageMapper.updateByPrimaryKeySelective(latestMessage);
                                            log.info("[V2 Revised] Updated database with preliminary_diagnosis for session {}", sessionId);
                                        }
                                    }
                                } catch (Exception e) {
                                    log.error("[V2 Revised] Error updating preliminary_diagnosis in interaction_history: {}", e.getMessage(), e);
                                }
                            } else {
                                log.info("[V2 Revised] Transformed Strategy does not contain \'诊断\' or is null, using last doctor message for SSE content (Session {})", sessionId);
                                if (contentForSSE == null) {
                                    log.warn("[V2 Revised] Could not extract a doctor message from transformed history for session {}.", sessionId);
                                    contentForSSE = "";
                                }
                            }

                        } catch (Exception extractEx) {
                            log.error("[V2 Revised] Error extracting content from transformed JSON for session {}: {}", sessionId, extractEx.getMessage(), extractEx);
                            contentForSSE = "Error extracting response content.";
                        }

                        if (contentForSSE != null && !contentForSSE.isEmpty()) {
                            String audioUrl = null;
                            try {
                                log.debug("[V2 Revised] Generating TTS for content: {}", contentForSSE);
                                audioUrl = ttsService.generateTtsUrl(contentForSSE);
                                log.info("[V2 Revised] Generated TTS URL for session {}: {}", sessionId, audioUrl);
                            } catch (Exception e) {
                                log.error("[V2 Revised] Generating TTS failed for session {}: {}", sessionId, e.getMessage());
                            }

                            Map<String, Object> responseData = new HashMap<>();
                            responseData.put("content", contentForSSE);
                            if (audioUrl != null) { responseData.put("audioUrl", audioUrl); }
                            responseData.put("cot", rawPythonResponseStr);
                            String jsonData = objectMapper.writeValueAsString(responseData);

                            try {
                                emitter.send(SseEmitter.event().data(jsonData).id(String.valueOf(System.currentTimeMillis())).name("message"));
                                log.info("[V2 Revised] Sent message event via SSE for session {}", sessionId);
                            } catch (IllegalStateException e) {
                                log.warn("[V2 Revised] Emitter already completed, skipping message send for session {}", sessionId);
                            }
                        } else {
                            log.warn("[V2 Revised] Final content for SSE is empty for session {}", sessionId);
                            Map<String, Object> errorData = new HashMap<>();
                            errorData.put("error", "No valid content generated for SSE");
                            errorData.put("cot", rawPythonResponseStr);
                            try { emitter.send(SseEmitter.event().data(objectMapper.writeValueAsString(errorData)).name("error")); } 
                            catch (Exception e) { log.warn("[V2 Revised] Emitter already completed or IO error sending error message for session {}", sessionId, e); }
                        }

                    } else {
                        log.error("[V2 Revised] Python service returned non-OK status: {} or empty body for session {}", response.getStatusCode(), sessionId);
                        String errorBody = rawPythonResponseStr != null ? rawPythonResponseStr : "Status: " + response.getStatusCode();
                        Map<String, Object> errorData = new HashMap<>();
                        errorData.put("error", "AI service returned " + response.getStatusCode());
                        errorData.put("cot", errorBody);
                        try { emitter.send(SseEmitter.event().data(objectMapper.writeValueAsString(errorData)).name("error")); } 
                        catch (Exception e) { log.warn("[V2 Revised] Emitter already completed or IO error sending error message for session {}", sessionId, e); }
                    }

                    try {
                        emitter.send(SseEmitter.event().data("[DONE]"));
                        emitter.complete();
                        log.info("[V2 Revised] Sent [DONE] and completed emitter for session {}", sessionId);
                    } catch (IllegalStateException e) {
                        log.warn("[V2 Revised] Emitter already completed before [DONE] for session {}", sessionId);
                    } catch (IOException e) {
                        log.error("[V2 Revised] IOException sending [DONE] for session {}: {}", sessionId, e.getMessage(), e);
                        try { emitter.complete(); } catch (Exception ignored) {}
                    }

                } catch (Exception e) {
                    log.error("[V2 Revised] Unexpected error during async processing for session {}: {}", sessionId, e.getMessage(), e);
                    try {
                        Map<String, Object> errorData = new HashMap<>();
                        errorData.put("error", "Internal processing error: " + e.getMessage());
                        if (rawPythonResponseStr != null) errorData.put("cot", rawPythonResponseStr);
                        emitter.send(SseEmitter.event().name("error").data(objectMapper.writeValueAsString(errorData)));
                    } catch (Exception ex) {
                        log.error("[V2 Revised] Error sending final error message for session {}: {}", sessionId, ex.getMessage(), ex);
                    } finally {
                        try { emitter.complete(); } catch (Exception ignored) {}
                    }
                }
            });

        } catch (Exception e) {
            log.error("[V2 Revised] Error during initial setup for inquiryAiDoctorV2 session {}: {}", sessionId, e.getMessage(), e);
            emitter.completeWithError(e);
        }

        return emitter;
    }

    /**
     * 与AI Doctor V2进行快速问诊对话
     * @param sessionId 会话ID
     * @param userMessage 用户消息
     * @return SSE发射器
     */
    public SseEmitter inquiryAiDoctorV2Quick(Integer sessionId, String userMessage) {
        SseEmitter emitter = new SseEmitter(300000L);
        final Message latestMessage; // Declare here to be accessible in finally block

        try {
            // --- Step 1: Read latest message JSON from DB ---
            log.info("[V2 Quick Inquiry] Starting inquiry for session {}", sessionId);
            List<Message> sessionMessages = getSessionMessages(sessionId);
            String latestMessageJsonStr = null;

            if (sessionMessages == null || sessionMessages.isEmpty()) {
                log.warn("[V2 Quick Inquiry] Session {} is empty or not found. Creating initial structure.", sessionId);
                Map<String, Object> initialStructure = new HashMap<>();
                initialStructure.put("interaction_history", createInitialInnerInteractionHistory());
                latestMessageJsonStr = objectMapper.writeValueAsString(initialStructure);
                // Create a new message record for the initial structure
                Message newMessage = new Message();
                newMessage.setSessionId(sessionId);
                newMessage.setSpeaker("system");
                newMessage.setMessage(latestMessageJsonStr); // latestMessageJsonStr holds the initial JSON here
                latestMessage = createMessage(newMessage);
                log.info("[V2 Quick Inquiry] Created initial message record with ID {} for session {}", latestMessage.getId(), sessionId);
            } else {
                latestMessage = sessionMessages.get(sessionMessages.size() - 1); // Get the last message
                latestMessageJsonStr = latestMessage.getMessage();
                log.info("[V2 Quick Inquiry] Found latest message record with ID {} for session {}", latestMessage.getId(), sessionId);

                // --- Robust JSON Validation ---
                boolean isValidJson = false;
                if (latestMessageJsonStr != null && !latestMessageJsonStr.trim().isEmpty()) {
                    String trimmedJson = latestMessageJsonStr.trim();
                    if (trimmedJson.startsWith("{") && trimmedJson.endsWith("}")) {
                         try {
                             // Try a minimal parse to check basic validity
                             objectMapper.readTree(trimmedJson); // Use readTree for lightweight validation
                             isValidJson = true;
                             log.debug("[V2 Quick Inquiry] Latest message ID {} content appears to be a valid JSON object.", latestMessage.getId());
                         } catch (IOException e) {
                             log.warn("[V2 Quick Inquiry] Latest message ID {} content is not valid JSON: {}. Content snippet: {}", latestMessage.getId(), e.getMessage(), trimmedJson.substring(0, Math.min(trimmedJson.length(), 100)));
                             isValidJson = false;
                         }
                    } else {
                         log.warn("[V2 Quick Inquiry] Latest message ID {} content does not appear to be a JSON object. Content snippet: {}", latestMessage.getId(), trimmedJson.substring(0, Math.min(trimmedJson.length(), 100)));
                         isValidJson = false;
                    }
                } else {
                    log.warn("[V2 Quick Inquiry] Latest message ID {} content is null or empty.", latestMessage.getId());
                    isValidJson = false;
                }

                if (!isValidJson) {
                    log.warn("[V2 Quick Inquiry] Invalid or missing JSON in latest message ID {}. Creating initial structure instead.", latestMessage.getId());
                    Map<String, Object> initialStructure = new HashMap<>();
                    initialStructure.put("interaction_history", createInitialInnerInteractionHistory());
                    latestMessageJsonStr = objectMapper.writeValueAsString(initialStructure);
                }
                // --- End JSON Validation ---
            }

            // Now latestMessageJsonStr is guaranteed to be a valid JSON string (either original or initial)

            // --- Step 2: Add Patient Message using Utility ---
            log.debug("[V2 Quick Inquiry] Adding patient message to JSON for session {}", sessionId);
            String jsonWithUserMessage = InteractionHistoryUtils.addPatientMessage(latestMessageJsonStr, userMessage);

            if (jsonWithUserMessage == null) {
                log.error("[V2 Quick Inquiry] Failed to add patient message using InteractionHistoryUtils for session {}. Aborting.", sessionId);
                emitter.send(SseEmitter.event().name("error").data("{\"error\":\"Failed to prepare message history\"}"));
                emitter.complete();
                return emitter;
            }
            log.debug("[V2 Quick Inquiry] JSON with user message prepared (first 200 chars): {}", jsonWithUserMessage.substring(0, Math.min(jsonWithUserMessage.length(), 200)));

            // --- Step 3: Prepare request body for Python ---
            Map<String, Object> requestBodyMap;
            try {
                // Parse the JSON *after* adding the user message to send to Python
                Map<String, Object> parsedJsonWithUser = objectMapper.readValue(jsonWithUserMessage, new TypeReference<Map<String, Object>>() {});
                requestBodyMap = parsedJsonWithUser; // Send the entire structure
                log.debug("[V2 Quick Inquiry] Parsed JSON with user message for Python request.");
            } catch (IOException e) {
                log.error("[V2 Quick Inquiry] Failed to parse JSON after adding user message for session {}: {}", sessionId, e.getMessage(), e);
                emitter.send(SseEmitter.event().name("error").data("{\"error\":\"Internal error parsing message history\"}"));
                emitter.complete();
                return emitter;
            }

            // --- Setup SSE Callbacks ---
            emitter.onCompletion(() -> log.info("[V2 Quick Inquiry] SSE completed for session {}", sessionId));
            emitter.onTimeout(() -> { log.warn("[V2 Quick Inquiry] SSE timeout for session {}", sessionId); emitter.complete(); });
            emitter.onError((ex) -> { log.error("[V2 Quick Inquiry] SSE error for session {}: {}", sessionId, ex.getMessage(), ex); emitter.complete(); });

            // --- Step 4-6: Asynchronous Python Call, DB Update, Transform, Send SSE ---
            executorService.execute(() -> {
                String rawPythonResponseStr = null; // To store the original response for COT and DB
                try {
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.APPLICATION_JSON);
                    log.info("[V2 Quick Inquiry] Sending request to Python V2 Quick Inquiry service for session {}", sessionId);

                    HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBodyMap, headers);
                    ResponseEntity<String> response;

                    try {
                        response = restTemplate.exchange(
                            pythonV2QuickInquiryUrl, HttpMethod.POST, requestEntity, String.class // Use new URL
                        );
                        rawPythonResponseStr = response.getBody(); // Store the raw response
                    } catch (RestClientException e) {
                        log.error("[V2 Quick Inquiry] Error calling Python service for session {}: {}", sessionId, e.getMessage(), e);
                        emitter.send(SseEmitter.event().name("error").data("{\"error\":\"AI service connection failed: " + e.getMessage() + "\"}"));
                        emitter.complete();
                        return; // Exit async task
                    }

                    if (response.getStatusCode() == HttpStatus.OK && rawPythonResponseStr != null && !rawPythonResponseStr.trim().isEmpty()) {
                        log.info("[V2 Quick Inquiry] Received OK response from Python for session {}", sessionId);
                        log.debug("[V2 Quick Inquiry] Raw Python Response (first 200 chars): {}", rawPythonResponseStr.substring(0, Math.min(rawPythonResponseStr.length(), 200)));

                        // 检查Python返回的JSON是否缺少"interaction_history"外层包装
                        try {
                            Map<String, Object> responseMap = objectMapper.readValue(rawPythonResponseStr, new TypeReference<Map<String, Object>>() {});
                            
                            // 如果直接返回的是内部数据结构(不包含interaction_history作为顶层key)
                            // 则需要添加外层包装以符合Java端期望的格式
                            if (!responseMap.containsKey("interaction_history") && responseMap.containsKey("cot_entries")) {
                                log.info("[V2 Quick Inquiry] Python response missing 'interaction_history' wrapper, adding it");
                                Map<String, Object> wrappedResponse = new HashMap<>();
                                wrappedResponse.put("interaction_history", responseMap);
                                rawPythonResponseStr = objectMapper.writeValueAsString(wrappedResponse);
                                log.debug("[V2 Quick Inquiry] Wrapped response (first 200 chars): {}", 
                                    rawPythonResponseStr.substring(0, Math.min(rawPythonResponseStr.length(), 200)));
                            }
                        } catch (Exception e) {
                            log.warn("[V2 Quick Inquiry] Error checking/fixing Python response format: {}", e.getMessage());
                            // 继续使用原始响应，错误处理交给下一阶段
                        }

                        try {
                            latestMessage.setMessage(rawPythonResponseStr);
                            latestMessage.setCreatedAt(LocalDateTime.now());
                            messageMapper.updateByPrimaryKeySelective(latestMessage);
                            log.info("[V2 Quick Inquiry] Database updated successfully with raw Python response for message ID {} session {}", latestMessage.getId(), sessionId);
                        } catch (Exception dbEx) {
                            log.error("[V2 Quick Inquiry] Error updating database with raw Python response for message ID {} session {}: {}", latestMessage.getId(), sessionId, dbEx.getMessage(), dbEx);
                        }

                        log.debug("[V2 Quick Inquiry] Transforming Python response for SSE content extraction...");
                        String transformedJsonStr = InteractionHistoryUtils.transformInteractionHistory(rawPythonResponseStr);

                        if (transformedJsonStr == null) {
                            log.error("[V2 Quick Inquiry] Failed to transform Python response using InteractionHistoryUtils for session {}. Sending raw response as fallback.", sessionId);
                            String fallbackContent = extractLastDoctorMessage(objectMapper.readValue(rawPythonResponseStr, Map.class));
                            if (fallbackContent == null || fallbackContent.isEmpty()) fallbackContent = "Error processing AI response structure.";

                             Map<String, Object> fallbackData = new HashMap<>();
                             fallbackData.put("content", fallbackContent);
                             fallbackData.put("cot", rawPythonResponseStr);
                             try { emitter.send(SseEmitter.event().data(objectMapper.writeValueAsString(fallbackData)).name("message")); } catch (Exception sendEx) { log.error("Error sending fallback SSE", sendEx); }
                             emitter.complete();
                             return;
                        }
                        log.debug("[V2 Quick Inquiry] Transformed JSON (first 200 chars): {}", transformedJsonStr.substring(0, Math.min(transformedJsonStr.length(), 200)));

                        Map<String, Object> transformedMap;
                        try {
                            transformedMap = objectMapper.readValue(transformedJsonStr, new TypeReference<Map<String, Object>>() {});
                        } catch (IOException parseEx) {
                             log.error("[V2 Quick Inquiry] Failed to parse transformed JSON for session {}: {}", sessionId, parseEx.getMessage(), parseEx);
                             emitter.send(SseEmitter.event().name("error").data("{\"error\":\"Internal error parsing transformed response\"}"));
                             emitter.complete();
                             return;
                        }

                        String contentForSSE = null;
                        String latestStrategyFromTransformed = null;

                        try {
                            Map<String, Object> transformedHistory = (Map<String, Object>) transformedMap.get("interaction_history");
                            List<Map<String, Object>> transformedEntries = (List<Map<String, Object>>) transformedHistory.get("cot_entries");
                            if (transformedEntries != null && !transformedEntries.isEmpty()) {
                                latestStrategyFromTransformed = getStringValue(transformedEntries.get(0), "strategy");
                            }
                            log.debug("[V2 Quick Inquiry] Extracted strategy from transformed JSON: {}", latestStrategyFromTransformed);

                            if (transformedEntries != null && !transformedEntries.isEmpty()) {
                                List<Map<String, Object>> mergedDialogues = (List<Map<String, Object>>) transformedEntries.get(0).get("dialogue_history");
                                if (mergedDialogues != null && !mergedDialogues.isEmpty()){
                                    for (int i = mergedDialogues.size() - 1; i >= 0; i--) {
                                        Map<String, Object> dialogue = mergedDialogues.get(i);
                                        if (dialogue != null && "doctor".equals(dialogue.get("role"))) {
                                             contentForSSE = getStringValue(dialogue, "content");
                                             log.debug("[V2 Quick Inquiry] Extracted last doctor message from transformed JSON: {}", contentForSSE);
                                             break; 
                                        }
                                    }
                                }
                            }

                            if (latestStrategyFromTransformed != null && latestStrategyFromTransformed.contains("诊断")) {
                                log.info("[V2 Quick Inquiry] Transformed Strategy contains \'诊断\', using Strategy for SSE content (Session {})", sessionId);
                                contentForSSE = latestStrategyFromTransformed;
                            } else {
                                log.info("[V2 Quick Inquiry] Transformed Strategy does not contain \'诊断\' or is null, using last doctor message for SSE content (Session {})", sessionId);
                                if (contentForSSE == null) {
                                    log.warn("[V2 Quick Inquiry] Could not extract a doctor message from transformed history for session {}.", sessionId);
                                    contentForSSE = "";
                                }
                            }

                        } catch (Exception extractEx) {
                            log.error("[V2 Quick Inquiry] Error extracting content from transformed JSON for session {}: {}", sessionId, extractEx.getMessage(), extractEx);
                            contentForSSE = "Error extracting response content.";
                        }

                        if (contentForSSE != null && !contentForSSE.isEmpty()) {
                            String audioUrl = null;
                            try {
                                log.debug("[V2 Quick Inquiry] Generating TTS for content: {}", contentForSSE);
                                audioUrl = ttsService.generateTtsUrl(contentForSSE);
                                log.info("[V2 Quick Inquiry] Generated TTS URL for session {}: {}", sessionId, audioUrl);
                            } catch (Exception e) {
                                log.error("[V2 Quick Inquiry] Generating TTS failed for session {}: {}", sessionId, e.getMessage());
                            }

                            Map<String, Object> responseData = new HashMap<>();
                            responseData.put("content", contentForSSE);
                            if (audioUrl != null) { responseData.put("audioUrl", audioUrl); }
                            responseData.put("cot", rawPythonResponseStr);
                            String jsonData = objectMapper.writeValueAsString(responseData);

                            try {
                                emitter.send(SseEmitter.event().data(jsonData).id(String.valueOf(System.currentTimeMillis())).name("message"));
                                log.info("[V2 Quick Inquiry] Sent message event via SSE for session {}", sessionId);
                            } catch (IllegalStateException e) {
                                log.warn("[V2 Quick Inquiry] Emitter already completed, skipping message send for session {}", sessionId);
                            }
                        } else {
                            log.warn("[V2 Quick Inquiry] Final content for SSE is empty for session {}", sessionId);
                            Map<String, Object> errorData = new HashMap<>();
                            errorData.put("error", "No valid content generated for SSE");
                            errorData.put("cot", rawPythonResponseStr);
                            try { emitter.send(SseEmitter.event().data(objectMapper.writeValueAsString(errorData)).name("error")); } 
                            catch (Exception e) { log.warn("[V2 Quick Inquiry] Emitter already completed or IO error sending error message for session {}", sessionId, e); }
                        }

                    } else {
                        log.error("[V2 Quick Inquiry] Python service returned non-OK status: {} or empty body for session {}", response.getStatusCode(), sessionId);
                        String errorBody = rawPythonResponseStr != null ? rawPythonResponseStr : "Status: " + response.getStatusCode();
                        Map<String, Object> errorData = new HashMap<>();
                        errorData.put("error", "AI service returned " + response.getStatusCode());
                        errorData.put("cot", errorBody);
                        try { emitter.send(SseEmitter.event().data(objectMapper.writeValueAsString(errorData)).name("error")); } 
                        catch (Exception e) { log.warn("[V2 Quick Inquiry] Emitter already completed or IO error sending error message for session {}", sessionId, e); }
                    }

                    try {
                        emitter.send(SseEmitter.event().data("[DONE]"));
                        emitter.complete();
                        log.info("[V2 Quick Inquiry] Sent [DONE] and completed emitter for session {}", sessionId);
                    } catch (IllegalStateException e) {
                        log.warn("[V2 Quick Inquiry] Emitter already completed before [DONE] for session {}", sessionId);
                    } catch (IOException e) {
                        log.error("[V2 Quick Inquiry] IOException sending [DONE] for session {}: {}", sessionId, e.getMessage(), e);
                        try { emitter.complete(); } catch (Exception ignored) {}
                    }

                } catch (Exception e) {
                    log.error("[V2 Quick Inquiry] Unexpected error during async processing for session {}: {}", sessionId, e.getMessage(), e);
                    try {
                        Map<String, Object> errorData = new HashMap<>();
                        errorData.put("error", "Internal processing error: " + e.getMessage());
                        if (rawPythonResponseStr != null) errorData.put("cot", rawPythonResponseStr);
                        emitter.send(SseEmitter.event().name("error").data(objectMapper.writeValueAsString(errorData)));
                    } catch (Exception ex) {
                        log.error("[V2 Quick Inquiry] Error sending final error message for session {}: {}", sessionId, ex.getMessage(), ex);
                    } finally {
                        try { emitter.complete(); } catch (Exception ignored) {}
                    }
                }
            });

        } catch (Exception e) {
            log.error("[V2 Quick Inquiry] Error during initial setup for inquiryAiDoctorV2Quick session {}: {}", sessionId, e.getMessage(), e);
            emitter.completeWithError(e);
        }

        return emitter;
    }

    /**
     * 创建初始的 *内部* Interaction History 结构 (不含顶层 interaction_history key)
     * 使用 LinkedHashMap 保持顺序
     * @return 包含 cot_entries 的 Map
     */
    private Map<String, Object> createInitialInnerInteractionHistory() {
        // Use LinkedHashMap to preserve field order, maybe meaningful for Python side
        Map<String, Object> innerHistory = new LinkedHashMap<>();
        List<Map<String, Object>> cotEntries = new ArrayList<>();
        Map<String, Object> cotEntry = new LinkedHashMap<>();
        List<Map<String, Object>> dialogueHistory = new ArrayList<>();

        // Add initial doctor greeting
        Map<String, Object> initialDoctorMessage = new LinkedHashMap<>();
        initialDoctorMessage.put("content", "您好，我是AI医生，请问有什么可以帮您？");
        initialDoctorMessage.put("role", "doctor");
        dialogueHistory.add(initialDoctorMessage);

        cotEntry.put("dialogue_history", dialogueHistory);
        cotEntry.put("feedback", "");
        cotEntry.put("observation", "");
        // Keep old initial reasoning and strategy
        cotEntry.put("reasoning", "这个策略可以帮助我们了解患者的基本情况和症状的严重程度");
        cotEntry.put("strategy", "询问患者的性别年龄，症状，持续时间和严重程度");
        cotEntries.add(cotEntry);
        innerHistory.put("cot_entries", cotEntries);
        return innerHistory;
    }

    /**
     * 从响应 Map 中提取最后一个医生消息内容
     * @param jsonResponseMap 解析后的响应 Map
     * @return 医生消息内容, 或 null
     */
    private String extractLastDoctorMessage(Map<String, Object> jsonResponseMap) {
        if (jsonResponseMap == null) return null;
        Object historyObj = jsonResponseMap.get("interaction_history");
        if (!(historyObj instanceof Map)) return null;

        Map<String, Object> history = (Map<String, Object>) historyObj;
        Object entriesObj = history.get("cot_entries");
        if (!(entriesObj instanceof List)) return null;

        List<Map<String, Object>> entries = (List<Map<String, Object>>) entriesObj;
        if (entries.isEmpty()) return null;

        Map<String, Object> lastEntry = entries.get(entries.size() - 1);
        if (lastEntry == null) return null;

        Object dialoguesObj = lastEntry.get("dialogue_history");
        if (!(dialoguesObj instanceof List)) return null;

        List<Map<String, Object>> dialogues = (List<Map<String, Object>>) dialoguesObj;
        if (dialogues.isEmpty()) return null;

        for (int i = dialogues.size() - 1; i >= 0; i--) {
            Map<String, Object> dialogue = dialogues.get(i);
            if (dialogue != null && "doctor".equals(dialogue.get("role"))) {
                return getStringValue(dialogue, "content"); // Use helper
            }
        }
        return null; // No doctor message found in last entry's dialogue
    }

    /**
     * 从 AI 响应 Map 中提取最后一个 cot_entry Map
     * @param aiResponseMap 解析后的 AI 响应 Map
     * @return 最后一个 cot_entry Map, 或 null
     */
    private Map<String, Object> extractLastAiCotEntry(Map<String, Object> aiResponseMap) {
         if (aiResponseMap == null) return null;
         Object historyObj = aiResponseMap.get("interaction_history");
         if (!(historyObj instanceof Map)) return null;

         Map<String, Object> history = (Map<String, Object>) historyObj;
         Object entriesObj = history.get("cot_entries");
         if (!(entriesObj instanceof List)) return null;

         List<Map<String, Object>> entries = (List<Map<String, Object>>) entriesObj;
         if (entries.isEmpty()) return null;

         // Assume the relevant entry returned by AI is the last one
         Map<String, Object> lastEntry = entries.get(entries.size() - 1);
         // Basic check if it's a map, return null otherwise
         return (lastEntry instanceof Map) ? lastEntry : null;
     }

    /**
     * Safely retrieves a String value from a map.
     */
    private static String getStringValue(Map<String, Object> map, String key) {
        if (map == null || key == null) {
            return "";
        }
        Object value = map.get(key);
        if (value instanceof String) {
            return (String) value;
        }
        return "";
    }

    /**
     * 添加医生补充信息到指定会话的最新消息记录中。
     * (Refined version with Chinese char DB storage)
     * @param sessionId 会话ID。
     * @param supplementaryInfo 要添加的补充信息字符串。
     * @return 如果成功更新则返回 true，否则返回 false。
     */
    @Transactional
    public boolean addDoctorSupplementaryInfo(Integer sessionId, String supplementaryInfo) {
         log.info("尝试为 Session ID: {} 添加医生补充信息。", sessionId);
         if (supplementaryInfo == null || supplementaryInfo.trim().isEmpty()) {
             log.warn("补充信息为空，不执行更新操作 (Session ID: {})。", sessionId);
             return false;
         }
         try {
             List<Message> messages = messageMapper.selectBySessionId(sessionId);
             if (messages == null || messages.isEmpty()) { log.error("未找到 Session ID: {} 的任何消息记录。", sessionId); return false; }
             Message latestMessage = messages.get(messages.size() - 1); // Get LAST message
             Map<String, Object> topLevelMessageMap = OcrTextToJson.parseJsonToMap(latestMessage.getMessage());
             if (topLevelMessageMap.isEmpty()) { log.error("解析消息 ID: {} 的顶层 JSON 结构失败。", latestMessage.getId()); return false; }

             Object historyObj = topLevelMessageMap.computeIfAbsent("interaction_history", k -> new HashMap<>());
             if (!(historyObj instanceof Map)) { log.error("无法获取或创建有效的 'interaction_history' Map for message ID: {}", latestMessage.getId()); return false; }
             Map<String, Object> interactionHistoryMap = (Map<String, Object>) historyObj;

             Object existingInfoListObj = interactionHistoryMap.computeIfAbsent("doctor_supplementary_info", k -> new ArrayList<String>());
             if (!(existingInfoListObj instanceof List)) {
                  log.warn("消息 ID: {} 的 interaction_history.doctor_supplementary_info 字段不是列表类型，将创建新列表。", latestMessage.getId());
                  existingInfoListObj = new ArrayList<String>();
                  interactionHistoryMap.put("doctor_supplementary_info", existingInfoListObj);
             }
             @SuppressWarnings("unchecked")
             List<String> targetInfoList = (List<String>) existingInfoListObj;

             targetInfoList.add(supplementaryInfo);
             log.info("已将补充信息添加到列表 (Session ID: {})。", sessionId);

             // Re-serialize with Chinese characters for DB storage (ObjectMapper configured globally)
             String updatedMessageJson = objectMapper.writeValueAsString(topLevelMessageMap);

             if (updatedMessageJson == null) { log.error("将更新后的顶层消息 Map (消息 ID: {}) 转换回 JSON 失败。", latestMessage.getId()); return false; }
             int updateCount = messageMapper.updateMessageById(latestMessage.getId(), updatedMessageJson);
             if (updateCount > 0) log.info("成功更新消息 ID: {}。", latestMessage.getId());
             else log.error("更新消息 ID: {} 失败。", latestMessage.getId());
             return true;
         } catch (Exception e) { log.error("添加医生补充信息过程中发生意外错误 (Session ID: {}): {}", sessionId, e.getMessage(), e); return false; }
     }

    /**
     * 调用 Python V2 诊断接口获取诊断、病情和治疗计划。
     * (Refined version with Chinese char DB storage)
     * @param sessionId 会话ID。
     * @return 包含诊断、病情、治疗计划的 Map，或包含错误的 Map。
     */
    public Map<String, Object> diagnoseAiDoctorV2(Integer sessionId) {
        log.info("开始 V2 诊断流程，Session ID: {}", sessionId);
        Map<String, Object> result = new HashMap<>();
        try {
            List<Message> messages = messageMapper.selectBySessionId(sessionId);
            if (messages == null || messages.isEmpty()) {
                log.error("未找到 Session ID: {} 的任何消息记录。", sessionId);
                result.put("error", "未找到会话消息记录");
                return result;
            }
            Message latestMessage = messages.get(messages.size() - 1);
            Map<String, Object> topLevelMessageMap = OcrTextToJson.parseJsonToMap(latestMessage.getMessage());
            if (topLevelMessageMap.isEmpty()) {
                log.error("解析消息 ID: {} 的顶层 JSON 结构失败。", latestMessage.getId());
                result.put("error", "解析消息历史失败");
                return result;
            }
            Object historyObj = topLevelMessageMap.get("interaction_history");
            if (!(historyObj instanceof Map)) {
                log.error("消息 ID: {} 的 JSON 结构中未找到有效的 'interaction_history' Map。", latestMessage.getId());
                result.put("error", "无效的消息历史结构");
                return result;
            }
            Map<String, Object> interactionHistoryMap = (Map<String, Object>) historyObj;

            // FIX: Ensure 'guidance_for_treatment' is a non-null object before sending.
            if (!interactionHistoryMap.containsKey("guidance_for_treatment") || interactionHistoryMap.get("guidance_for_treatment") == null) {
                interactionHistoryMap.put("guidance_for_treatment", new java.util.HashMap<String, Object>());
                log.info("Patched null 'guidance_for_treatment' for Session ID: {}", sessionId);
            }

            // --- Extract Second Last Observation ---
            String secondLastObservation = InteractionHistoryUtils.extractSecondLastObservation(latestMessage.getMessage());
            log.info("Extracted second last observation value: '{}'", secondLastObservation);
            log.debug("Extracted second last observation for session {}: {}", sessionId, secondLastObservation);
            // --- End Observation Extraction ---

            Map<String, Object> requestPayload = new HashMap<>();
            requestPayload.put("interaction_history", interactionHistoryMap);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestPayload, headers);
            log.info("调用 Python V2 诊断服务 URL: {} (Session ID: {})", pythonV2DiagnosisUrl, sessionId);
            ResponseEntity<String> responseEntity;
            try {
                responseEntity = restTemplate.exchange(pythonV2DiagnosisUrl, HttpMethod.POST, requestEntity, String.class);
            }
            catch (RestClientException e) {
                log.error("调用 Python V2 诊断服务出错 (Session ID: {}): {}", sessionId, e.getMessage(), e);
                result.put("error", "连接 V2 诊断服务失败: " + e.getMessage());
                return result;
            }
            String responseBody = responseEntity.getBody();
            if (responseEntity.getStatusCode() == HttpStatus.OK && responseBody != null) {
                log.info("收到 Python V2 诊断服务响应 (Session ID: {}): {}", sessionId,
                        responseBody.substring(0, Math.min(responseBody.length(), 500)));
                 String jsonToStoreDiagnosis = null;
                 String rawDiagnosisResponseForCot = responseBody;
                Map<String, Object> diagnosisResultMap = OcrTextToJson.parseJsonToMap(responseBody);
                if (diagnosisResultMap.isEmpty()) {
                    log.error("解析 Python V2 诊断响应失败或响应为空 (Session ID: {}).", sessionId);
                    result.put("error", "解析 V2 诊断响应失败。");
                    return result;
                }
                if (diagnosisResultMap.containsKey("error")) {
                    log.error("Python V2 诊断服务返回错误 (Session ID: {}): {}", sessionId, diagnosisResultMap.get("error"));
                    result.put("error", "诊断服务内部错误: " + diagnosisResultMap.get("error"));
                    return result;
                }

                // 处理 guidelines_content - 添加到MongoDB
                if (diagnosisResultMap.containsKey("guidelines_content")) {
                    try {
                        Object guidelinesApiContent = diagnosisResultMap.get("guidelines_content");
                        log.debug("从V2诊断API接收到指南内容，会话ID: {}", sessionId);

                        try {
                            // 使用完全限定名称以避免导入问题
                            org.springframework.data.mongodb.core.query.Query query =
                                    new org.springframework.data.mongodb.core.query.Query();
                            query.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("sessionId").is(sessionId));

                            GuidelinesContent existingContent = mongoTemplate.findOne(query, GuidelinesContent.class, "guidelines_content");

                            String savedContentId;
                            if (existingContent != null) {
                                log.info("找到会话 {} 已存在的指南内容记录 ID: {}，准备更新。", sessionId, existingContent.getId());
                                existingContent.setContent(guidelinesApiContent);
                                existingContent.setCreatedAt(LocalDateTime.now());
                                mongoTemplate.save(existingContent, "guidelines_content");
                                savedContentId = existingContent.getId();
                                log.info("已更新MongoDB中的诊断指南内容，会话ID: {}, 引用ID: {}", sessionId, savedContentId);
                            } else {
                                log.info("未找到会话 {} 的指南内容记录，创建新记录。", sessionId);
                                GuidelinesContent newContent = new GuidelinesContent();
                                newContent.setId(java.util.UUID.randomUUID().toString());
                                newContent.setSessionId(sessionId);
                                newContent.setContent(guidelinesApiContent);
                                mongoTemplate.insert(newContent, "guidelines_content");
                                savedContentId = newContent.getId();
                                log.info("已在MongoDB中创建新的诊断指南内容，会话ID: {}, 引用ID: {}", sessionId, savedContentId);
                            }

                            // 用引用ID替换原始内容
                            diagnosisResultMap.put("guidelines_content_ref", savedContentId);
                            diagnosisResultMap.remove("guidelines_content");
                            log.debug("已将诊断中的 guidelines_content 替换为引用 ID: {}", savedContentId);
                        } catch (Exception mongoEx) {
                            log.error("MongoDB操作失败，无法存储诊断指南内容 (Session ID: {}): {}", sessionId, mongoEx.getMessage(), mongoEx);
                            log.info("由于MongoDB操作失败，诊断指南内容将保留在响应中。");
                            // 保留guidelines_content，不从响应中删除
                        }
                    } catch (Exception e) {
                        log.error("处理诊断指南内容时发生异常 (Session ID: {}): {}", sessionId, e.getMessage(), e);
                    }
                }

                String diagnosis = (String) diagnosisResultMap.getOrDefault("diagnosis", "");
                String condition = (String) diagnosisResultMap.getOrDefault("condition", "");
                Object treatmentRecommendation = diagnosisResultMap.getOrDefault("treatment_recommendation", "");
                Object treatmentGuide = diagnosisResultMap.getOrDefault("treatment_guide", new java.util.HashMap<>());

                if (diagnosis.isEmpty() || condition.isEmpty()) {
                    log.error("Python V2 诊断响应中 diagnosis 或 condition 字段为空 (Session ID: {}). Response: {}", sessionId, responseBody);
                    result.put("error", "V2 诊断响应格式不完整: 缺少诊断或病情描述");
                    return result;
                }

                // --- Extract Last Observation from the history sent to Python --- 
                String lastObservation = null;
                try {
                    Object cotEntriesObj = interactionHistoryMap.get("cot_entries");
                    if (cotEntriesObj instanceof List) {
                        List<Map<String, Object>> cotEntries = (List<Map<String, Object>>) cotEntriesObj;
                        if (!cotEntries.isEmpty()) {
                            Map<String, Object> lastEntry = cotEntries.get(cotEntries.size() - 1);
                            lastObservation = getStringValue(lastEntry, "observation"); // Use helper
                            log.debug("Extracted last observation for session {}", sessionId);
                        }
                    }
                } catch (Exception obsEx) {
                    log.warn("Could not extract last observation for session {}: {}", sessionId, obsEx.getMessage());
                    // Continue without observation if extraction fails
                }
                // --- End Observation Extraction ---

                try {
                    interactionHistoryMap.put("diagnosis", diagnosis);
                    interactionHistoryMap.put("condition", condition);
                    interactionHistoryMap.put("treatment_recommendation", treatmentRecommendation);
                    interactionHistoryMap.put("treatment_guide", treatmentGuide);

                    // --- Add observation before saving to DB ---
                    if (secondLastObservation != null && !secondLastObservation.isEmpty()) {
                        interactionHistoryMap.put("lastObservation", secondLastObservation); // Use key expected by frontend parser
                    }
                    // --- End Add observation before saving ---
                    
                    // 记录更新前的数据结构
                    log.info("准备序列化到数据库的数据结构 (Session ID: {}): {}", sessionId, 
                             "topLevelKeys=" + topLevelMessageMap.keySet() + ", " +
                             "interactionHistoryKeys=" + interactionHistoryMap.keySet());
                    
                    // 检查treatment_guide的结构
                    if (treatmentGuide != null) {
                        log.info("Treatment guide 类型: {}", treatmentGuide.getClass().getName());
                        if (treatmentGuide instanceof Map) {
                            log.info("Treatment guide 包含的键: {}", ((Map<?,?>)treatmentGuide).keySet());
                        }
                    }
                    
                    try {
                        // ObjectMapper configured globally to handle non-ASCII
                        jsonToStoreDiagnosis = objectMapper.writeValueAsString(topLevelMessageMap);
                        log.debug("序列化后的JSON长度: {} 字符", jsonToStoreDiagnosis != null ? jsonToStoreDiagnosis.length() : 0);
                        
                        if (jsonToStoreDiagnosis != null) {
                            // 检查是否超出数据库TEXT类型的限制 (约65,535字节)
                            if (jsonToStoreDiagnosis.getBytes("UTF-8").length > 65555) {
                                log.warn("序列化后的JSON大小超过65KB，可能超出TEXT类型限制 (Session ID: {}). 尝试移除treatment_guide后重试", sessionId);
                                // 移除可能体积较大的字段后重试
                                interactionHistoryMap.remove("treatment_guide");
                                String reducedJson = objectMapper.writeValueAsString(topLevelMessageMap);
                                
                                if (reducedJson != null && reducedJson.getBytes("UTF-8").length <= 65555) {
                                    log.info("移除treatment_guide后，JSON大小变为: {} 字节, 继续更新", reducedJson.getBytes("UTF-8").length);
                                    jsonToStoreDiagnosis = reducedJson;
                                } else {
                                    log.error("即使移除treatment_guide后，JSON仍然过大或无法序列化 (Session ID: {})", sessionId);
                                }
                            }
                             
                            int updateCount = messageMapper.updateMessageById(latestMessage.getId(), jsonToStoreDiagnosis);
                            if (updateCount > 0) {
                                log.info("成功将 V2 诊断结果更新回数据库 (Session ID: {})", sessionId);
                                
                                // 验证更新是否成功写入
                                Message updatedMessage = messageMapper.selectByPrimaryKey(latestMessage.getId());
                                if (updatedMessage != null && updatedMessage.getMessage() != null) {
                                    log.info("数据库验证: 更新后的消息长度为 {} 字符 (Session ID: {})", 
                                           updatedMessage.getMessage().length(), sessionId);
                                } else {
                                    log.warn("数据库验证: 无法读取已更新的消息 (Session ID: {})", sessionId);
                                }
                            } else {
                                log.error("更新数据库 V2 诊断结果失败，影响行数为 0 (Session ID: {})", sessionId);
                            }
                        } else {
                            log.error("无法将最终诊断消息结构序列化为JSON，跳过数据库更新 (Session ID: {})", sessionId);
                        }
                    } catch (Exception jsonEx) {
                        log.error("序列化或更新数据库时发生错误 (Session ID: {}): {}", sessionId, jsonEx.getMessage(), jsonEx);
                        // 尝试简化结构后重试
                        try {
                            log.info("尝试简化数据结构后重试 (Session ID: {})", sessionId);
                            // 简化treatment_recommendation和treatment_guide为String
                            if (treatmentRecommendation != null) {
                                interactionHistoryMap.put("treatment_recommendation", objectMapper.writeValueAsString(treatmentRecommendation));
                            }
                            if (treatmentGuide != null) {
                                interactionHistoryMap.put("treatment_guide", objectMapper.writeValueAsString(treatmentGuide));
                            }
                            
                            String simplifiedJson = objectMapper.writeValueAsString(topLevelMessageMap);
                            if (simplifiedJson != null) {
                                int updateCount = messageMapper.updateMessageById(latestMessage.getId(), simplifiedJson);
                                if (updateCount > 0) {
                                    log.info("使用简化结构成功更新数据库 (Session ID: {})", sessionId);
                                } else {
                                    log.error("使用简化结构更新数据库失败 (Session ID: {})", sessionId);
                                }
                            }
                        } catch (Exception retryEx) {
                            log.error("简化后重试仍然失败 (Session ID: {}): {}", sessionId, retryEx.getMessage());
                        }
                    }
                } catch (Exception dbEx) { 
                    log.error("更新数据库 V2 诊断结果时出错 (Session ID: {}): {}", sessionId, dbEx.getMessage(), dbEx); 
                }

                result.put("diagnosis", diagnosis);
                result.put("condition", condition);
                result.put("treatment_recommendation", treatmentRecommendation);
                result.put("treatment_guide", treatmentGuide);
                // --- Add observation to the result map ---
                if (lastObservation != null) { // Keep existing last observation if needed
                   result.put("last_observation", lastObservation); // Rename existing if needed
                }
                if (secondLastObservation != null && !secondLastObservation.isEmpty()) {
                   result.put("observation", secondLastObservation);
                }
                // --- End Add Observation ---
                result.put("cot", rawDiagnosisResponseForCot);
                log.info("成功获取 V2 诊断结果 (Session ID: {})", sessionId);
                log.info("准备返回给前端的 V2 诊断结果 (Session ID: {}): {}", sessionId, result);

                return result;
            } else {
                 log.error("Python V2 诊断服务返回非 OK 状态: {} 或响应体为空 (Session ID: {}).", responseEntity.getStatusCode(), sessionId);
                 String errorBodyLog = responseBody != null ? responseBody.substring(0, Math.min(responseBody.length(), 500)) : "[null]";
                 log.error("Python V2 诊断服务错误响应体（部分）(Session ID: {}): {}", sessionId, errorBodyLog);
                result.put("error", "V2 诊断服务调用失败");
                result.put("status", responseEntity.getStatusCodeValue());
                result.put("cot", responseBody);
                 return result;
            }
        } catch (Exception e) {
            log.error("V2 诊断过程中发生意外错误 (Session ID: {}): {}", sessionId, e.getMessage(), e);
            result.put("error", "V2 诊断发生内部错误: " + e.getMessage());
            return result;
    }
    }
    /**
     * 调用 Python V2 快速诊断接口获取诊断、病情和治疗计划。
     * @param sessionId 会话ID。
     * @return 包含诊断、病情、治疗计划的 Map，或包含错误的 Map。
     */
    public Map<String, Object> diagnoseAiDoctorV2Quick(Integer sessionId) {
        log.info("开始 V2 快速诊断流程，Session ID: {}", sessionId);
        Map<String, Object> result = new HashMap<>();
        try {
            List<Message> messages = messageMapper.selectBySessionId(sessionId);
            if (messages == null || messages.isEmpty()) { log.error("未找到 Session ID: {} 的任何消息记录。", sessionId); result.put("error", "未找到会话消息记录"); return result; }
            Message latestMessage = messages.get(messages.size() - 1);
            Map<String, Object> topLevelMessageMap = OcrTextToJson.parseJsonToMap(latestMessage.getMessage());
            if (topLevelMessageMap.isEmpty()) { log.error("解析消息 ID: {} 的顶层 JSON 结构失败。", latestMessage.getId()); result.put("error", "解析消息历史失败"); return result; }
            Object historyObj = topLevelMessageMap.get("interaction_history");
            if (!(historyObj instanceof Map)) { log.error("消息 ID: {} 的 JSON 结构中未找到有效的 'interaction_history' Map。", latestMessage.getId()); result.put("error", "无效的消息历史结构"); return result; }
            Map<String, Object> interactionHistoryMap = (Map<String, Object>) historyObj;

            // FIX: Ensure 'guidance_for_treatment' is a non-null object before sending.
            if (!interactionHistoryMap.containsKey("guidance_for_treatment") || interactionHistoryMap.get("guidance_for_treatment") == null) {
                interactionHistoryMap.put("guidance_for_treatment", new java.util.HashMap<String, Object>());
                log.info("Patched null 'guidance_for_treatment' for Session ID: {} (Quick)", sessionId);
            }

            // --- Extract Second Last Observation ---
            String secondLastObservation = InteractionHistoryUtils.extractSecondLastObservation(latestMessage.getMessage());
            log.info("Extracted second last observation value: '{}'", secondLastObservation);

            log.debug("Extracted second last observation for session {}: {}", sessionId, secondLastObservation);
            // --- End Observation Extraction ---

            Map<String, Object> requestPayload = new HashMap<>(); requestPayload.put("interaction_history", interactionHistoryMap);
            HttpHeaders headers = new HttpHeaders(); headers.setContentType(MediaType.APPLICATION_JSON); HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestPayload, headers);
            
            // 使用与V2相同的诊断URL，但后续可以根据需要调整
            String diagnosticUrl = pythonV2DiagnosisUrl;
            log.info("调用 Python V2 快速诊断服务 URL: {} (Session ID: {})", diagnosticUrl, sessionId);
            
            ResponseEntity<String> responseEntity;
            try { responseEntity = restTemplate.exchange(diagnosticUrl, HttpMethod.POST, requestEntity, String.class); }
            catch (RestClientException e) { log.error("调用 Python V2 快速诊断服务出错 (Session ID: {}): {}", sessionId, e.getMessage(), e); result.put("error", "连接 V2 快速诊断服务失败: " + e.getMessage()); return result; }
            String responseBody = responseEntity.getBody();
            if (responseEntity.getStatusCode() == HttpStatus.OK && responseBody != null) {
                log.info("收到 Python V2 快速诊断服务响应 (Session ID: {}): {}", sessionId, responseBody.substring(0, Math.min(responseBody.length(), 500)));
                 String jsonToStoreDiagnosis = null;
                 String rawDiagnosisResponseForCot = responseBody;
                Map<String, Object> diagnosisResultMap = OcrTextToJson.parseJsonToMap(responseBody);
                if (diagnosisResultMap.isEmpty()) { log.error("解析 Python V2 快速诊断响应失败或响应为空 (Session ID: {}).", sessionId); result.put("error", "解析 V2 快速诊断响应失败。"); return result; }
                if (diagnosisResultMap.containsKey("error")) { log.error("Python V2 快速诊断服务返回错误 (Session ID: {}): {}", sessionId, diagnosisResultMap.get("error")); result.put("error", "快速诊断服务内部错误: " + diagnosisResultMap.get("error")); return result; }
                String diagnosis = (String) diagnosisResultMap.getOrDefault("diagnosis", "");
                String condition = (String) diagnosisResultMap.getOrDefault("condition", "");
                Object treatmentRecommendation = diagnosisResultMap.getOrDefault("treatment_recommendation", "");
                Object treatmentGuide = diagnosisResultMap.getOrDefault("treatment_guide", new java.util.HashMap<>());

                if (diagnosis.isEmpty() || condition.isEmpty()) {
                    log.error("Python V2 快速诊断响应中 diagnosis 或 condition 字段为空 (Session ID: {}). Response: {}", sessionId, responseBody);
                    result.put("error", "V2 快速诊断响应格式不完整: 缺少诊断或病情描述");
                    return result;
                }

                // --- Extract Last Observation from the history sent to Python --- 
                String lastObservation = null;
                try {
                    Object cotEntriesObj = interactionHistoryMap.get("cot_entries");
                    if (cotEntriesObj instanceof List) {
                        List<Map<String, Object>> cotEntries = (List<Map<String, Object>>) cotEntriesObj;
                        if (!cotEntries.isEmpty()) {
                            Map<String, Object> lastEntry = cotEntries.get(cotEntries.size() - 1);
                            lastObservation = getStringValue(lastEntry, "observation"); // Use helper
                            log.debug("Extracted last observation for session {}", sessionId);
                        }
                    }
                } catch (Exception obsEx) {
                    log.warn("Could not extract last observation for session {}: {}", sessionId, obsEx.getMessage());
                    // Continue without observation if extraction fails
                }
                // --- End Observation Extraction ---

                try {
                    interactionHistoryMap.put("diagnosis", diagnosis);
                    interactionHistoryMap.put("condition", condition);
                    interactionHistoryMap.put("treatment_recommendation", treatmentRecommendation);
                    interactionHistoryMap.put("treatment_guide", treatmentGuide);
                    // --- Add observation before saving to DB ---
                    if (secondLastObservation != null && !secondLastObservation.isEmpty()) {
                        interactionHistoryMap.put("lastObservation", secondLastObservation); // Use key expected by frontend parser
                    }
                    // --- End Add observation before saving ---
                    
                    // 记录更新前的数据结构
                    log.info("准备序列化到数据库的数据结构 (Session ID: {}): {}", sessionId, 
                             "topLevelKeys=" + topLevelMessageMap.keySet() + ", " +
                             "interactionHistoryKeys=" + interactionHistoryMap.keySet());
                    
                    // 检查treatment_guide的结构
                    if (treatmentGuide != null) {
                        log.info("Treatment guide 类型: {}", treatmentGuide.getClass().getName());
                        if (treatmentGuide instanceof Map) {
                            log.info("Treatment guide 包含的键: {}", ((Map<?,?>)treatmentGuide).keySet());
                        }
                    }
                    
                    try {
                        // ObjectMapper configured globally to handle non-ASCII
                        jsonToStoreDiagnosis = objectMapper.writeValueAsString(topLevelMessageMap);
                        log.debug("序列化后的JSON长度: {} 字符", jsonToStoreDiagnosis != null ? jsonToStoreDiagnosis.length() : 0);
                        
                        if (jsonToStoreDiagnosis != null) {
                            // 检查是否超出数据库TEXT类型的限制 (约65,535字节)
                            if (jsonToStoreDiagnosis.getBytes("UTF-8").length > 65555) {
                                log.warn("序列化后的JSON大小超过65KB，可能超出TEXT类型限制 (Session ID: {}). 尝试移除treatment_guide后重试", sessionId);
                                // 移除可能体积较大的字段后重试
                                interactionHistoryMap.remove("treatment_guide");
                                String reducedJson = objectMapper.writeValueAsString(topLevelMessageMap);
                                
                                if (reducedJson != null && reducedJson.getBytes("UTF-8").length <= 65555) {
                                    log.info("移除treatment_guide后，JSON大小变为: {} 字节, 继续更新", reducedJson.getBytes("UTF-8").length);
                                    jsonToStoreDiagnosis = reducedJson;
                                } else {
                                    log.error("即使移除treatment_guide后，JSON仍然过大或无法序列化 (Session ID: {})", sessionId);
                                }
                            }
                             
                            int updateCount = messageMapper.updateMessageById(latestMessage.getId(), jsonToStoreDiagnosis);
                            if (updateCount > 0) {
                                log.info("成功将 V2 快速诊断结果更新回数据库 (Session ID: {})", sessionId);
                                
                                // 验证更新是否成功写入
                                Message updatedMessage = messageMapper.selectByPrimaryKey(latestMessage.getId());
                                if (updatedMessage != null && updatedMessage.getMessage() != null) {
                                    log.info("数据库验证: 更新后的消息长度为 {} 字符 (Session ID: {})", 
                                           updatedMessage.getMessage().length(), sessionId);
                                } else {
                                    log.warn("数据库验证: 无法读取已更新的消息 (Session ID: {})", sessionId);
                                }
                            } else {
                                log.error("更新数据库 V2 快速诊断结果失败，影响行数为 0 (Session ID: {})", sessionId);
                            }
                        } else {
                            log.error("无法将最终诊断消息结构序列化为JSON，跳过数据库更新 (Session ID: {})", sessionId);
                        }
                    } catch (Exception jsonEx) {
                        log.error("序列化或更新数据库时发生错误 (Session ID: {}): {}", sessionId, jsonEx.getMessage(), jsonEx);
                        // 尝试简化结构后重试
                        try {
                            log.info("尝试简化数据结构后重试 (Session ID: {})", sessionId);
                            // 简化treatment_recommendation和treatment_guide为String
                            if (treatmentRecommendation != null) {
                                interactionHistoryMap.put("treatment_recommendation", objectMapper.writeValueAsString(treatmentRecommendation));
                            }
                            if (treatmentGuide != null) {
                                interactionHistoryMap.put("treatment_guide", objectMapper.writeValueAsString(treatmentGuide));
                            }
                            
                            String simplifiedJson = objectMapper.writeValueAsString(topLevelMessageMap);
                            if (simplifiedJson != null) {
                                int updateCount = messageMapper.updateMessageById(latestMessage.getId(), simplifiedJson);
                                if (updateCount > 0) {
                                    log.info("使用简化结构成功更新数据库 (Session ID: {})", sessionId);
                                } else {
                                    log.error("使用简化结构更新数据库失败 (Session ID: {})", sessionId);
                                }
                            }
                        } catch (Exception retryEx) {
                            log.error("简化后重试仍然失败 (Session ID: {}): {}", sessionId, retryEx.getMessage());
                        }
                    }
                } catch (Exception dbEx) { 
                    log.error("更新数据库 V2 快速诊断结果时出错 (Session ID: {}): {}", sessionId, dbEx.getMessage(), dbEx); 
                }
                result.put("diagnosis", diagnosis);
                result.put("condition", condition);
                result.put("treatment_recommendation", treatmentRecommendation);
                result.put("treatment_guide", treatmentGuide);
                // --- Add observation to the result map ---
                if (lastObservation != null) { // Keep existing last observation if needed
                   result.put("last_observation", lastObservation); // Rename existing if needed
                }
                if (secondLastObservation != null && !secondLastObservation.isEmpty()) {
                   result.put("observation", secondLastObservation);
                }
                // --- End Add Observation ---
                result.put("cot", rawDiagnosisResponseForCot);
                log.info("成功获取 V2 快速诊断结果 (Session ID: {})", sessionId);
                log.info("准备返回给前端的 V2 快速诊断结果 (Session ID: {}): {}", sessionId, result);

                return result;
            } else {
                 log.error("Python V2 快速诊断服务返回非 OK 状态: {} 或响应体为空 (Session ID: {}).", responseEntity.getStatusCode(), sessionId);
                 String errorBodyLog = responseBody != null ? responseBody.substring(0, Math.min(responseBody.length(), 500)) : "[null]";
                 log.error("Python V2 快速诊断服务错误响应体（部分）(Session ID: {}): {}", sessionId, errorBodyLog);
                 result.put("error", "V2 快速诊断服务调用失败"); result.put("status", responseEntity.getStatusCodeValue()); result.put("cot", responseBody);
                 return result;
            }
        } catch (Exception e) { log.error("V2 快速诊断过程中发生意外错误 (Session ID: {}): {}", sessionId, e.getMessage(), e); result.put("error", "V2 快速诊断发生内部错误: " + e.getMessage()); return result; }
    }
    @Transactional
    public Map<String, Object> preliminaryDiagnoseAiDoctorV2(Integer sessionId) {
        log.info("开始 V2 初步诊断流程，会话 ID: {}", sessionId);
        Map<String, Object> result = new HashMap<>();

        try {
            // Get messages directly instead of using Session
            List<Message> messages = messageMapper.selectBySessionId(sessionId);
            if (messages == null || messages.isEmpty()) {
                log.error("未找到会话ID: {} 的消息记录", sessionId);
                result.put("error", "未找到会话消息记录");
                return result;
            }

            Message latestMessage = messages.get(messages.size() - 1);
            Map<String, Object> topLevelMessageMap;

            try {
                topLevelMessageMap = objectMapper.readValue(
                        latestMessage.getMessage(),
                        new TypeReference<Map<String, Object>>() {}
                );
            } catch (Exception e) {
                log.error("解析最新消息JSON失败, 会话ID: {}, 错误: {}", sessionId, e.getMessage(), e);
                topLevelMessageMap = new HashMap<>();
                Map<String, Object> initialHistory = createInitialInnerInteractionHistory();
                topLevelMessageMap.put("interaction_history", initialHistory);
            }

            Map<String, Object> interactionHistory;
            if (topLevelMessageMap.containsKey("interaction_history")) {
                interactionHistory = (Map<String, Object>) topLevelMessageMap.get("interaction_history");
            } else {
                interactionHistory = createInitialInnerInteractionHistory();
                topLevelMessageMap.put("interaction_history", interactionHistory);
            }

            // Call Python service with interaction history
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("interaction_history", interactionHistory);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(requestBody, headers);

            log.info("调用Python初步诊断V2接口，会话ID: {}, URL: {}", sessionId, pythonV2PreliminaryDiagnosisUrl);

            ResponseEntity<String> response = restTemplate.exchange(
                    pythonV2PreliminaryDiagnosisUrl,
                    HttpMethod.POST,
                    httpEntity,
                    String.class
            );

            // Parse response
            Map<String, Object> responseData = objectMapper.readValue(
                    response.getBody(),
                    new TypeReference<Map<String, Object>>() {}
            );
            
            log.info("Python初步诊断V2接口返回数据，会话ID: {}, 响应数据键集: {}", 
                     sessionId, responseData != null ? responseData.keySet() : "null");

            // Handle guidelines_content with MongoDB
            if (responseData != null && responseData.containsKey("guidelines_content")) {
                try {
                    Object guidelinesApiContent = responseData.get("guidelines_content");
                    log.debug("从API接收到指南内容，会话ID: {}", sessionId);

                    GuidelinesContent existingContent = null;
                    try {
                        // Create query for finding existing content
                        org.springframework.data.mongodb.core.query.Query query =
                                new org.springframework.data.mongodb.core.query.Query();
                        query.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("sessionId").is(sessionId));
                        existingContent = mongoTemplate.findOne(query, GuidelinesContent.class, "guidelines_content");
                    } catch (Exception findEx) {
                        log.error("查询MongoDB失败，会话ID: {}, 错误: {}", sessionId, findEx.getMessage(), findEx);
                    }

                    String savedContentId;
                    // Try to update or insert content in MongoDB
                    try {
                        if (existingContent != null) {
                            log.info("找到会话 {} 已存在的指南内容记录 ID: {}，准备更新。", sessionId, existingContent.getId());
                            existingContent.setContent(guidelinesApiContent);
                            existingContent.setCreatedAt(LocalDateTime.now());
                            mongoTemplate.save(existingContent, "guidelines_content");
                            savedContentId = existingContent.getId();
                            log.info("已更新MongoDB中的指南内容，会话ID: {}, 引用ID: {}", sessionId, savedContentId);
                                } else {
                            log.info("未找到会话 {} 的指南内容记录，创建新记录。", sessionId);
                            GuidelinesContent newContent = new GuidelinesContent();
                            newContent.setId(java.util.UUID.randomUUID().toString());
                            newContent.setSessionId(sessionId);
                            newContent.setContent(guidelinesApiContent);
                            mongoTemplate.insert(newContent, "guidelines_content");
                            savedContentId = newContent.getId();
                            log.info("已在MongoDB中创建新的指南内容，会话ID: {}, 引用ID: {}", sessionId, savedContentId);
                        }
                        responseData.put("guidelines_content_ref", savedContentId);
                        responseData.remove("guidelines_content");
                        log.debug("已将 guidelines_content 替换为引用 ID: {}", savedContentId);
                    } catch (Exception saveEx) {
                        log.error("保存/更新指南内容到MongoDB失败，会话ID: {}，错误: {}", sessionId, saveEx.getMessage(), saveEx);
                        log.info("MongoDB操作失败，guidelines_content将保留在响应中。");
                        // Don't remove guidelines_content if we couldn't save it to MongoDB
                    }
                } catch (Exception e) {
                    log.error("处理指南内容时发生异常，会话ID: {}，错误: {}", sessionId, e.getMessage(), e);
                }
            }

            // 处理preliminary_diagnosis和inspection_suggestions字段
            boolean hasUpdates = false;
            
            // 处理preliminary_diagnosis字段
            if (responseData != null && responseData.containsKey("preliminary_diagnosis")) {
                Object preliminaryDiagnosis = responseData.get("preliminary_diagnosis");
                log.info("从Python响应中获取到preliminary_diagnosis内容");
                interactionHistory.put("preliminary_diagnosis", preliminaryDiagnosis);
                log.info("已将preliminary_diagnosis内容存入interaction_history");
                hasUpdates = true;
            } else {
                log.warn("Python响应中不包含preliminary_diagnosis字段");
            }
            
            // 处理inspection_suggestions字段
            if (responseData != null && responseData.containsKey("inspection_suggestions")) {
                Object inspectionSuggestions = responseData.get("inspection_suggestions");
                log.info("从Python响应中获取到inspection_suggestions内容");
                interactionHistory.put("inspection_suggestions", inspectionSuggestions);
                log.info("已将inspection_suggestions内容存入interaction_history");
                hasUpdates = true;
            } else {
                log.warn("Python响应中不包含inspection_suggestions字段");
            }

            // FIX: Persist 'guidance_for_treatment' from preliminary diagnosis response
            if (responseData != null && responseData.containsKey("guidance_for_treatment")) {
                Object guidanceForTreatment = responseData.get("guidance_for_treatment");
                log.info("从Python响应中获取到guidance_for_treatment内容");
                interactionHistory.put("guidance_for_treatment", guidanceForTreatment);
                log.info("已将guidance_for_treatment内容存入interaction_history");
                hasUpdates = true;
            } else {
                log.warn("Python响应中不包含guidance_for_treatment字段");
            }

            // 如果有更新，则保存到数据库
            if (hasUpdates) {
                try {
                    // 确保更新后的interactionHistory被保存到topLevelMessageMap
                    topLevelMessageMap.put("interaction_history", interactionHistory);
                    String updatedMessageJson = objectMapper.writeValueAsString(topLevelMessageMap);
                    
                    latestMessage.setMessage(updatedMessageJson);
                    int updateCount = messageMapper.updateByPrimaryKeySelective(latestMessage);
                    log.info("更新消息记录中的交互历史，影响行数: {}, 会话ID: {}", updateCount, sessionId);
                } catch (Exception e) {
                    log.error("更新交互历史失败，会话ID: {}, 错误: {}", sessionId, e.getMessage(), e);
                }
            }

            return responseData;
        } catch (Exception e) {
            log.error("V2 初步诊断处理过程中发生异常，会话ID: {}, 错误: {}", sessionId, e.getMessage(), e);
            result.put("error", "处理异常: " + e.getMessage());
                 return result;
            }
    }
}