import pandas as pd

from logger import logger


# 配置日志记录
# logging.basicConfig(
#     level=logging.INFO,  # 设置日志级别
#     format="%(asctime)s - %(levelname)s - %(message)s",  # 日志格式
#     handlers=[
#         logging.StreamHandler(),  # 输出到控制台
#         logging.FileHandler("format_json.log", mode="a", encoding="utf-8")  # 输出到文件
#     ]
# )

# # 创建日志记录器
# logger = logging.getLogger()

def format_medical_advice(data):
    # 提取药品信息
    medicine_text = "药品推荐：\n"
    for medicine in data['药品']:
        medicine_text += f"\n药品名: {medicine['药品名']}\n"
        medicine_text += f"规格: {medicine['规格']}\n"
        medicine_text += f"服用方法: {medicine['服用方法']}\n"
        medicine_text += f"剂量安排: {medicine['剂量安排']}\n"
        medicine_text += f"使用目的: {medicine['使用目的']}\n"
        medicine_text += "-" * 30

    # 提取生活建议
    life_advice_text = "\n生活建议：\n"
    for advice in data['生活建议']:
        life_advice_text += f"- {advice}\n"

    # 合并药品信息和生活建议
    formatted_text = medicine_text + life_advice_text
    return formatted_text

# 加载XLSX文件
def load_xlsx_file(file_path):
    try:
        data = pd.read_excel(file_path, sheet_name=None)
        return data
    except FileNotFoundError as e:
        logger.error(f"文件不存在: {e}")
        raise
    except Exception as e:
        logger.error(f"加载文件失败: {e}")
        raise

def main():
    xlsx_file = r"..\data\json_r1_full_new_test_json.xlsx"
    # 对XLSX文件中的"treatment_plan"进行格式化
    data = load_xlsx_file(xlsx_file)
    for sheet_name, sheet_data in data.items():
        #把treatment_plan列的数据转化成json
        sheet_data['treatment_plan'] = sheet_data['treatment_plan'].apply(eval)
        #格式化treatment_plan
        sheet_data['treatment_plan'] = sheet_data['treatment_plan'].apply(format_medical_advice)
        #保存到新的XLSX文件
        sheet_data.to_excel(f"..\data\{sheet_name}_formatted.xlsx", index=False)
        

        

if __name__ == "__main__":
    main()