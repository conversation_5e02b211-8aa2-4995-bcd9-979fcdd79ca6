package com.makiyo.ai_doctor_prod.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * TCP Connection Status Monitor
 * Periodically checks and records system TCP connection states, especially CLOSE_WAIT connections
 */
@Component
public class ConnectionMonitor {

    private static final Logger log = LoggerFactory.getLogger(ConnectionMonitor.class);
    
    // Store recent connection statistics
    private final Map<String, AtomicInteger> lastConnectionStats = new ConcurrentHashMap<>();
    
    // Warning threshold: if CLOSE_WAIT connections exceed this value, log a warning
    private static final int CLOSE_WAIT_WARNING_THRESHOLD = 10;
    
    /**
     * Check connection status every 5 minutes
     */
    @Scheduled(fixedRate = 300000)
    public void monitorConnections() {
        Map<String, Integer> stats = getConnectionStats();
        
        // Update internal state and log information
        if (stats != null) {
            for (Map.Entry<String, Integer> entry : stats.entrySet()) {
                String state = entry.getKey();
                int count = entry.getValue();
                
                // Get previous count (create if doesn't exist)
                AtomicInteger lastCount = lastConnectionStats.computeIfAbsent(state, k -> new AtomicInteger(0));
                int prevCount = lastCount.getAndSet(count);
                
                // Check if there's a significant change (more than 20%)
                boolean significant = Math.abs(count - prevCount) > Math.max(1, prevCount * 0.2);
                
                // Log appropriate messages
                if ("CLOSE_WAIT".equals(state)) {
                    if (count > CLOSE_WAIT_WARNING_THRESHOLD) {
                        log.warn("Detected large number of CLOSE_WAIT connections: {} (change: {})", count, count - prevCount);
                    } else if (significant) {
                        log.info("CLOSE_WAIT connection count: {} (change: {})", count, count - prevCount);
                    } else {
                        log.debug("CLOSE_WAIT connection count: {} (stable)", count);
                    }
                } else if (significant) {
                    log.info("TCP connection state {}: {} (change: {})", state, count, count - prevCount);
                } else {
                    log.debug("TCP connection state {}: {} (stable)", state, count);
                }
            }
        }
    }
    
    /**
     * Get current TCP connection statistics
     * @return Map containing connection counts by state, or null if execution fails
     */
    public Map<String, Integer> getConnectionStats() {
        Map<String, Integer> stats = new HashMap<>();
        
        try {
            String[] command;
            if (System.getProperty("os.name").toLowerCase().contains("win")) {
                // Windows system - use cmd /c to execute command through cmd shell
                command = new String[]{"cmd", "/c", "netstat -ano | findstr TCP"};
            } else {
                // Unix-like systems
                command = new String[]{"sh", "-c", "netstat -ant | grep -v LISTEN"};
            }
            
            Process process = Runtime.getRuntime().exec(command);
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                Pattern statePattern = Pattern.compile("\\b(ESTABLISHED|TIME_WAIT|CLOSE_WAIT|FIN_WAIT[_-][12]|CLOSING|LAST_ACK)\\b");
                
                while ((line = reader.readLine()) != null) {
                    Matcher matcher = statePattern.matcher(line);
                    if (matcher.find()) {
                        String state = matcher.group(1);
                        // Increment count for this state
                        stats.put(state, stats.getOrDefault(state, 0) + 1);
                    }
                }
                
                // Also read error stream to prevent potential blocking
                try (BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {
                    while (errorReader.readLine() != null) {
                        // Just read and discard error output
                    }
                }
                
                int exitCode = process.waitFor();
                if (exitCode != 0) {
                    log.warn("Command execution returned non-zero status: {}", exitCode);
                    // Continue despite error, returning partial results if any
                }
            }
            
            // Ensure CLOSE_WAIT state is included (even if zero)
            stats.putIfAbsent("CLOSE_WAIT", 0);
            
            return stats;
        } catch (IOException | InterruptedException e) {
            log.error("Error getting connection statistics: {}", e.getMessage(), e);
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt(); // Reset interrupt status
            }
            return null;
        }
    }
    
    /**
     * Immediately check CLOSE_WAIT connection count and return
     * @return CLOSE_WAIT connection count, or -1 if execution fails
     */
    public int checkCloseWaitCount() {
        Map<String, Integer> stats = getConnectionStats();
        if (stats != null) {
            return stats.getOrDefault("CLOSE_WAIT", 0);
        }
        return -1; // Indicates execution failure
    }
} 