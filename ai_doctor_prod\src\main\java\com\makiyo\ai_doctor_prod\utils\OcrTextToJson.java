package com.makiyo.ai_doctor_prod.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
// import io.swagger.v3.core.util.Json; // 移除未使用的 swagger import
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.Map;

/**
 * 工具类，用于将 OCR 识别结果的文本（JSON 格式）转换为 Java 对象。
 */
public class OcrTextToJson {

    // 日志记录器
    private static final Logger log = LoggerFactory.getLogger(OcrTextToJson.class);
    // Jackson ObjectMapper 实例，用于 JSON 操作 (线程安全，可重用)
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 将 JSON 格式的字符串解析成 Map<String, Object>。
     * Map 可以很好地表示嵌套的 JSON 结构。
     *
     * @param jsonText 需要解析的 JSON 字符串。
     * @return 一个表示 JSON 结构的 Map 对象。
     *         如果解析失败或输入为 null/空，则返回一个空的 Map (Collections.emptyMap())，以确保安全。
     */
    public static Map<String, Object> parseJsonToMap(String jsonText) {
        // 检查输入是否为 null 或空字符串
        if (jsonText == null || jsonText.trim().isEmpty()) {
            log.warn("输入的 JSON 文本为 null 或空，返回空 Map。");
            return Collections.emptyMap();
        }

        try {
            // 使用 TypeReference 来帮助 Jackson 正确地将 JSON 解析为嵌套的 Map<String, Object> 结构。
            Map<String, Object> resultMap = objectMapper.readValue(jsonText, new TypeReference<Map<String, Object>>() {});
            log.debug("成功将 JSON 字符串解析为 Map。");
            return resultMap; // 返回解析结果
        } catch (JsonProcessingException e) {
            // 处理 JSON 解析过程中发生的错误
            log.error("解析 JSON 字符串失败: [{}]. 错误: {}",
                      jsonText.substring(0, Math.min(jsonText.length(), 200)), // 只记录可能很长的字符串的开头部分
                      e.getMessage());
            // 可以选择性地记录完整的异常堆栈信息用于调试
            // log.error("JSON 解析错误详情:", e);
            return Collections.emptyMap(); // 解析出错时返回空 Map
        } catch (Exception e) {
            // 捕获其他在解析过程中可能发生的意外异常
             log.error("JSON 解析过程中发生意外错误: {}", e.getMessage(), e);
             return Collections.emptyMap(); // 出现意外错误时也返回空 Map
        }
    }

    /**
     * (可选功能) 将 Map<String, Object> 转换回 JSON 格式的字符串。
     *
     * @param map 需要转换的 Map 对象。
     * @return 表示 Map 内容的 JSON 字符串；如果转换失败或输入为 null/空，则返回 null。
     */
    public static String convertMapToJson(Map<String, Object> map) {
        // 检查输入 Map 是否为 null 或空
        if (map == null || map.isEmpty()) {
             log.warn("输入的 Map 为 null 或空，返回 null。");
            return null;
        }
        try {
            // 将 Map 转换为格式化（带缩进换行）的 JSON 字符串
            String jsonString = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(map);
            log.debug("成功将 Map 转换为 JSON 字符串。");
            return jsonString;
        } catch (JsonProcessingException e) {
            // 处理 Map 转 JSON 过程中发生的错误
            log.error("将 Map 转换为 JSON 字符串失败: {}", e.getMessage(), e);
            return null; // 转换失败时返回 null
        }
    }

    // --- 测试用的 main 方法 保持不变 ---
    /**
     * 主方法，用于测试 parseJsonToMap 方法的功能。
     * @param args 命令行参数（未使用）
     */
    public static void main(String[] args) {
        // 示例 JSON 字符串 (注意 Java 字符串中的引号和换行需要转义)
        String testJson = "{\n" +
                "    \"content\": [\n" +
                "        {\n" +
                "            \"基础信息\": {\n" +
                "                \"姓名\": \"高润泽\",\n" +
                "                \"年龄\": \"2月\",\n" +
                "                \"性别\": \"男\",\n" +
                "                \"检查部位\": \"咽拭子\",\n" +
                "                \"科室\": \"重症医学二科\"\n" +
                "            }\n" +
                "        },\n" +
                "        {\n" +
                "            \"检查结果\": [\n" +
                "                {\n" +
                "                    \"其他\": \"\",\n" +
                "                    \"单位\": \"阴性\",\n" +
                "                    \"参考范围\": \"阴性\",\n" +
                "                    \"处理建议\": \"\",\n" +
                "                    \"异常标记\": \"\",\n" +
                "                    \"样本采集时间\": \"2025-03-15 18:25\",\n" +
                "                    \"检查描述\": \"\",\n" +
                "                    \"检测方法\": \"实时荧光PCR法\",\n" +
                "                    \"结果\": \"阴性\",\n" +
                "                    \"结果状态\": \"正常\",\n" +
                "                    \"诊断\": \"\",\n" +
                "                    \"项目名称\": \"肺炎支原体(MP)\"\n" +
                "                },\n" +
                // ... (省略其他检查结果) ...
                "                {\n" +
                "                    \"其他\": \"\",\n" +
                "                    \"单位\": \"阴性\",\n" +
                "                    \"参考范围\": \"阴性\",\n" +
                "                    \"处理建议\": \"\",\n" +
                "                    \"异常标记\": \"\",\n" +
                "                    \"样本采集时间\": \"2025-03-15 18:25\",\n" +
                "                    \"检查描述\": \"\",\n" +
                "                    \"检测方法\": \"实时荧光PCR法\",\n" +
                "                    \"结果\": \"阴性\",\n" +
                "                    \"结果状态\": \"正常\",\n" +
                "                    \"诊断\": \"\",\n" +
                "                    \"项目名称\": \"腺病毒(ADV)\"\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ],\n" +
                "    \"检查名称\": \"呼吸道病毒五项及肺炎支原体核酸检测\",\n" +
                "    \"检查时间\": \"2025-03-15 18:25\"\n" +
                "}";

        System.out.println("--- 输入的 JSON 字符串 ---");
        System.out.println(testJson);

        // 调用解析方法
        Map<String, Object> resultMap = parseJsonToMap(testJson);

        System.out.println("\n--- 解析后的 Map ---");
        // 直接打印 Map 对象，其 toString() 方法通常能展示其内容
        System.out.println(resultMap);

        System.out.println("\n--- 解析后的Json ---");
        // 直接打印Json
        String JsonText = convertMapToJson(resultMap);

        System.out.println(JsonText);

        // 从 Map 中访问数据的示例
        if (!resultMap.isEmpty()) {
            System.out.println("\n--- 示例数据访问 ---");
            System.out.println("检查时间: " + resultMap.get("检查时间"));
            try {
                // 注意：访问嵌套数据时需要进行类型转换，这里假设结构符合预期
                @SuppressWarnings("unchecked") // 抑制类型转换警告，实际项目中应更谨慎处理
                java.util.List<Map<String, Object>> content = (java.util.List<Map<String, Object>>) resultMap.get("content");
                if (content != null && content.size() > 0) {
                    Map<String, Object> basicInfoSection = content.get(0); // 获取第一个元素（基础信息部分）
                    if (basicInfoSection != null && basicInfoSection.containsKey("基础信息")) {
                         @SuppressWarnings("unchecked")
                        Map<String, Object> basicInfo = (Map<String, Object>) basicInfoSection.get("基础信息");
                         System.out.println("姓名: " + basicInfo.get("姓名"));
                    }
                     // 访问检查结果部分
                     if (content.size() > 1) {
                         Map<String, Object> resultsSection = content.get(1); // 获取第二个元素（检查结果部分）
                         if (resultsSection != null && resultsSection.containsKey("检查结果")) {
                             @SuppressWarnings("unchecked")
                             java.util.List<Map<String, Object>> results = (java.util.List<Map<String, Object>>) resultsSection.get("检查结果");
                             if (results != null && !results.isEmpty()) {
                                 // 访问第一个检查结果的名称
                                 System.out.println("第一个检查结果项目名称: " + results.get(0).get("项目名称"));
                             }
                         }
                     }
                }
            } catch (ClassCastException | NullPointerException e) {
                // 处理访问嵌套数据时可能发生的类型转换错误或空指针异常
                System.err.println("访问嵌套数据时出错: " + e.getMessage());
            }
        }
    }
} 