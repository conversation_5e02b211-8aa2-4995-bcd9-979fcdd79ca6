package com.makiyo.ai_doctor_prod.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.UncheckedIOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributes;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Stream;

@Service
public class AudioCleanupService {

    private static final Logger log = LoggerFactory.getLogger(AudioCleanupService.class);

    // 确保这个路径与TtsService中实际保存文件的基础路径配置一致
    @Value("${tts.audio.basePath:/home/<USER>/audio}")
    private String ttsAudioBasePath;

    private static final long EXPIRATION_MINUTES = 10;

    // Cron表达式: "秒 分 时 日 月 周"
    // "0 */5 * * * *" 表示每隔5分钟的第0秒执行一次
    @Scheduled(cron = "0 */5 * * * *")
    public void cleanupExpiredTtsAudioFiles() {
        log.info("Scheduled Task: Starting cleanup of expired TTS audio files from directory: {}", ttsAudioBasePath);
        Path audioDir = Paths.get(ttsAudioBasePath);

        if (!Files.exists(audioDir) || !Files.isDirectory(audioDir)) {
            log.warn("Scheduled Task: TTS audio base path {} does not exist or is not a directory. Cleanup task skipped.", ttsAudioBasePath);
            return;
        }

        Instant cutoffTime = Instant.now().minus(EXPIRATION_MINUTES, ChronoUnit.MINUTES);
        // 使用AtomicInteger来安全地计数，即使在多线程环境中也是线程安全的
        final AtomicInteger filesDeletedCount = new AtomicInteger(0);
        final AtomicInteger filesFailedCount = new AtomicInteger(0);
        final AtomicInteger totalFilesCount = new AtomicInteger(0);

        // 使用try-with-resources确保Stream被正确关闭
        try (Stream<Path> paths = Files.walk(audioDir)) {
            // 收集符合条件的文件路径，而不是在流中直接处理
            // 这样可以确保流被及时关闭，避免资源泄漏
            paths.filter(Files::isRegularFile)
                 // 可根据实际文件扩展名进行过滤，例如 .mp3, .wav 等
                 .filter(path -> path.toString().toLowerCase().endsWith(".mp3"))
                 .forEach(path -> {
                     totalFilesCount.incrementAndGet();
                     processAndDeleteFile(path, cutoffTime, filesDeletedCount, filesFailedCount);
                 });
        } catch (IOException | UncheckedIOException e) {
            log.error("Scheduled Task: Error walking through TTS audio directory {}: {}", ttsAudioBasePath, e.getMessage(), e);
        }

        log.info("Scheduled Task: TTS audio cleanup finished. Total files: {}. Files deleted: {}. Files failed: {}.", 
                totalFilesCount.get(), filesDeletedCount.get(), filesFailedCount.get());
    }
    
    /**
     * 处理并删除单个文件的方法，提取为独立方法以改善可读性和测试性
     */
    private void processAndDeleteFile(Path path, Instant cutoffTime, AtomicInteger filesDeletedCount, AtomicInteger filesFailedCount) {
        try {
            // 使用try-with-resources确保文件属性读取资源被正确关闭
            BasicFileAttributes attrs = Files.readAttributes(path, BasicFileAttributes.class);
            // 使用文件的创建时间判断是否过期
            Instant fileTime = attrs.creationTime().toInstant();

            if (fileTime.isBefore(cutoffTime)) {
                log.debug("Scheduled Task: Attempting to delete expired TTS audio file: {}", path);
                
                try {
                    // 尝试删除文件，并记录结果
                    if (Files.deleteIfExists(path)) {
                        log.info("Scheduled Task: Successfully deleted expired TTS audio file: {}", path);
                        filesDeletedCount.incrementAndGet();
                    } else {
                        log.warn("Scheduled Task: Could not delete expired TTS audio file (it may have been deleted by another process or an issue exists): {}", path);
                        filesFailedCount.incrementAndGet();
                    }
                } catch (IOException deleteEx) {
                    log.error("Scheduled Task: Error deleting TTS audio file {}: {}", path, deleteEx.getMessage());
                    filesFailedCount.incrementAndGet();
                }
            }
        } catch (IOException e) {
            log.error("Scheduled Task: Error processing TTS audio file {}: {}", path, e.getMessage());
            filesFailedCount.incrementAndGet();
        }
    }
} 