import json
import pandas as pd
import httpx
from volcenginesdkarkruntime import Ark
from anthropic import Anthropic, DefaultHttpxClient


from dotenv import load_dotenv
import os
import logging
import re

from prompts import _user_prompt, _sys_prompt, _user_prompt_category, _sys_prompt_category, _user_prompt_category_treatment, _sys_prompt_category_treatment, _user_prompt_diagnosis_category, _sys_prompt_diagnosis_category, _user_prompt_treatment_specification, _sys_prompt_treatment_specification

from Doctor import Doctor



pattern = r'(\{.*?\})'


# 配置日志记录
logging.basicConfig(
    level=logging.INFO,  # 设置日志级别
    format="%(asctime)s - %(levelname)s - %(message)s",  # 日志格式
    handlers=[
        logging.StreamHandler(),  # 输出到控制台
        logging.FileHandler("diagnosis_agent.log", mode="a", encoding="utf-8")  # 输出到文件
    ]
)

# 创建日志记录器
logger = logging.getLogger()


load_dotenv('../.env')  # 加载环境变量

class DiagnosisAgent:
    def __init__(self, doctors, model="deepseek_r1"):
        self.doctors = doctors  # 患者信息列表
        self.model = model  # 诊断模型
        self.meds = self.load_meds()  # 药品分类信息
        logger.info(f"DiagnosisAgent initialized with model: {model}")

    def load_meds(self):
        """
        读取药品分类信息
        """
        # 读取药品分类信息
        with open("D:\\aiDoctor\\flask\Diagnosis\medications.json", "r", encoding="utf-8") as json_file:
            meds = json.load(json_file)
        logger.info("Medications loaded successfully.")
        return meds
    
    # 提取信息的函数
    def extract_medications(self, input_data, data):
        result = {}
        
        # 遍历输入的分类数据
        for category_group, categories in input_data.items():
            medications = []
            for category_name in categories.values():
                # 查找西药类别
                if category_name in data.get(category_group, {}):
                    medications.extend(data[category_group].get(category_name, []))
            result[category_group] = medications
        
        return result
    
    # 提取药品名称的函数
    def extract_medication_names(self, data):
        names = []
        for category, medications in data.items():
            for medication in medications:
                names.append(medication["名称"])
        return names

    def _create_api_client(self, model):
        """
        根据不同模型创建相应的API客户端
        """
        logger.info(f"Creating API client for model: {model}")

        if model == "deepseek_r1" or model == "deepseek_v3":
            api_key = os.getenv(f"{model.upper()}_API_KEY")
            logger.debug(f"Using API key for model {model}: {api_key}")
            return Ark(
                base_url="https://ark.cn-beijing.volces.com/api/v3",
                api_key=api_key,
                timeout=httpx.Timeout(timeout=1800)
            )
        elif model == "claude":
            api_key = os.getenv("CLAUDE_API_KEY")
            logger.debug(f"Using API key for Claude model: {api_key}")
            return Anthropic(
                api_key=api_key,
                http_client=DefaultHttpxClient(
                    proxy="http://localhost:7890",  # Clash HTTP 代理地址
                    transport=httpx.HTTPTransport(local_address="0.0.0.0"),
                ),
            )
        else:
            logger.error(f"Model {model} not supported")
            raise ValueError(f"Model {model} not supported")

    def process_result(self, result, type):
        if type == "diagnosis":
            # 处理结果 加入异常处理
            try :
                result_dict = json.loads(result)
                logger.info("API response successfully parsed.")
                logger.info(f"API response: {result_dict}")
                return result_dict["病名"], result_dict["诊疗计划"], result_dict["种类"]
            except json.JSONDecodeError as e:
                logger.error(f"JSON Decode Error: {e} in diagnosis")
                match = re.search(pattern, result)
                if match:
                    json_data = json.loads(match.group(1))
                    return json_data["病名"], json_data["诊疗计划"], json_data["种类"]
                if result.startswith("```"):
                    result_new = result.replace("```", "")
                    result_dict = json.loads(result_new)
                    logger.info("API response parsed by ``` ``` .")
                    return result_dict["病名"], result_dict["诊疗计划"], result_dict["种类"]
                
                return None, None, None
        elif type == "category":
            # 处理结果 加入异常处理
            try :
                result_dict = json.loads(result)
                logger.info("API response successfully parsed.")
                logger.info(f"API response: {result_dict}")
                return result_dict
            except json.JSONDecodeError as e:
                logger.error(f"JSON Decode Error: {e}")
                match = re.search(pattern, result)
                if match:
                    json_data = json.loads(match.group(1))
                    return json_data
                return None
        elif type == "new_treatment":
            # 处理结果 加入异常处理
            try :
                result_dict = json.loads(result)
                logger.info("API response successfully parsed.")
                logger.info(f"API response: {result_dict}")
                return result_dict["诊疗计划"]
            except json.JSONDecodeError as e:
                logger.error(f"JSON Decode Error: {e}  in new treatment")
                match = re.search(pattern, result)
                if match:
                    json_data = json.loads(match.group(1))
                    return json_data["诊疗计划"]
                return None
        elif type == "diagnosis_category":
            try:
                result_dict = json.loads(result)
                logger.info("API response successfully parsed.")
                logger.info(f"API response: {result_dict}")
                return result_dict["病名"], result_dict["种类"]
            except json.JSONDecodeError as e:
                logger.error(f"JSON Decode Error: {e}  in diagnosis category")
                match = re.search(pattern, result)
                if match:
                    json_data = json.loads(match.group(1))
                    return json_data["病名"], json_data["种类"]
                return None, None
        elif type == "treatment_specification":
            try:
                result_dict = json.loads(result)
                logger.info("API response successfully parsed.")
                logger.info(f"API response: {result_dict}")
                return result_dict
            except json.JSONDecodeError as e:
                logger.error(f"JSON Decode Error: {e}  in treatment specification")
                match = re.search(pattern, result)
                if match:
                    json_data = json.loads(match.group(1))
                    return json_data
                return None    
        
        else:
            logger.error(f"Type {type} not supported")
            return result

    def _get_result_from_api(self, client, user_prompt, sys_prompt, model, type= "diagnosis"):
        """
        通用API调用逻辑
        """
        logger.info(f"Making API call with model: {model}")
        logger.info(f"User prompt: {user_prompt}")
        logger.info(f"System prompt: {sys_prompt}")
        try:
            if model == "claude":
                message = client.messages.create(
                    model="claude-3-5-sonnet-20241022",
                    max_tokens=1024,
                    messages=[{"role": "user", "content": user_prompt}],
                    system=sys_prompt
                )
                result = message.content[0].text
            else:
                completion = client.chat.completions.create(
                    model="ep-20250218172420-mcwk8" if model == "deepseek_v3" else "ep-20250218163136-2mnnz",
                    messages=[{"role": "system", "content": sys_prompt}, {"role": "user", "content": user_prompt}]
                )
            result = completion.choices[0].message.content
            logger.info(f"API response: {result}")

            return self.process_result(result, type)

        except (json.JSONDecodeError, Exception) as e:
            print(f"Error during API call or result parsing: {e}")
            
            return None, None
        
    def diagnose_refactor(self, doctor):
        """
        诊断方法，根据患者信息做出简单推断或调用外部 API 获取诊断结果。
        """

        logger.info(f"Diagnosing patient with information: {doctor.age}")

        user_prompt = _user_prompt.format(doctor=doctor, meds_xiyao=list(self.meds['西药'].keys()), meds_zhongyao=list(self.meds['中药'].keys()))
        sys_prompt = _sys_prompt

        user_prompt_diagnosis_category = _user_prompt_diagnosis_category.format(doctor=doctor, meds_xiyao=list(self.meds['西药'].keys()), meds_zhongyao=list(self.meds['中药'].keys()))
        sys_prompt_diagnosis_category = _sys_prompt_diagnosis_category

        


        # 创建API客户端
        client = self._create_api_client(self.model)

        # 获取诊断和治疗计划
        diagnosis, meds_category = self._get_result_from_api(client, user_prompt_diagnosis_category, sys_prompt_diagnosis_category, self.model, type = "diagnosis_category")

        # logger.info(f"西药种类: {list(self.meds['西药'].keys())}")
        # logger.info(f"西药种类: {self.meds["西药"]}")

        # user_prompt_category = _user_prompt_category.format(diagnosis=diagnosis, meds_xiyao=list(self.meds['西药'].keys()), meds_zhongyao=list(self.meds['中药'].keys()))
        # sys_prompt_category = _sys_prompt_category

        # meds_category = self._get_result_from_api(client, user_prompt_category, sys_prompt_category, self.model, type="category")

        meds_extracted = self.extract_medications(meds_category, self.meds)
        # print("meds_extracted: ", meds_extracted)

        medications_names = self.extract_medication_names(meds_extracted)
        # print("medications_names: ", medications_names)

        user_prompt_treatment_specification = _user_prompt_treatment_specification.format(doctor=doctor, diagnosis=diagnosis, meds_list=meds_extracted)
        sys_prompt_treatment_specification = _sys_prompt_treatment_specification    

        new_treatment_plan_specification = self._get_result_from_api(client, user_prompt_treatment_specification, sys_prompt_treatment_specification, self.model, type="treatment_specification")

        # 输出药品分类结果
        logger.info(f"Medications category: {meds_category}")

        # return diagnosis, treatment_plan
        return diagnosis, new_treatment_plan_specification


    def diagnose_all(self):
        """
        对所有患者进行诊断
        """
        results = []
        logger.info(f"Diagnosing {len(self.doctors)} patients...")

        print("doctors length: ", len(self.doctors))
        for index, doctor in enumerate(self.doctors, start=1):
            # if index > 9:
            #     break
            # 使用换行符分隔不同患者的诊断结果
            logger.info("\n" + "\n" + "\n"  + "-" * 40)
            logger.info(f"Diagnosing patient {index}/{len(self.doctors)}...")

            logger.info(f"Diagnosing patient {index}/{len(self.doctors)}...")
            diagnosis, treatment_plan = self.diagnose_refactor(doctor)
            results.append({
                "diagnosis": diagnosis,
                "treatment_plan": treatment_plan
            })

        logger.info("Diagnosis completed for all patients.")
        return results
    


def main_csv():
    file_path = "../data/cases.csv"
    doctors = Doctor.load_from_csv(file_path)
    
    
    diagnosis_agent = DiagnosisAgent(doctors, model="deepseek_r1")


    # # # 诊断单个患者
    # doctor = doctors[2]
    # diagnosis, treatment_plan = diagnosis_agent.diagnose_refactor(doctor)

    # print(f"Diagnosis: {diagnosis}")
    # print(f"Treatment Plan: {treatment_plan}")

    # 诊断所有患者
    results = diagnosis_agent.diagnose_all()
    # 打印诊断结果
    for result in results:
        # print(f"Patient: {result['patient']}")
        print(f"Diagnosis: {result['diagnosis']}")
        print(f"Treatment Plan: {result['treatment_plan']}")
        print("-" * 40)

    # 将诊断结果保存到CSV文件
    df = pd.DataFrame(results)

    # 将诊断结果保存到xlsx文件
    df = pd.DataFrame(results)

    # df新加两列，分别是doctor.preliminary_exam 和 doctor.treatment
    for i in range(len(doctors)):
        df.loc[i, 'preliminary_exam'] = doctors[i].preliminary_exam
        df.loc[i, 'treatment'] = doctors[i].treatment

    # 将诊断结果写入xlsx文件
    file_path_result = "../data/results_treatment_specification.xlsx"
    df.to_excel(file_path_result, index=False, engine='openpyxl')
    print(f"Diagnosis complete. Results written to {file_path_result}")


def main_json():
    file_path = "../data/cases_full.json"
    doctors = Doctor.load_from_json_filepath(file_path)
    
    
    diagnosis_agent = DiagnosisAgent(doctors, model="deepseek_r1")

    # 诊断所有患者
    results = diagnosis_agent.diagnose_all()
    # 打印诊断结果
    for result in results:
        # print(f"Patient: {result['patient']}")
        print(f"Diagnosis: {result['diagnosis']}")
        print(f"Treatment Plan: {result['treatment_plan']}")
        print("-" * 40)


    # 将诊断结果保存到xlsx文件
    df = pd.DataFrame(results)
    
    # df新加两列，分别是doctor.preliminary_exam 和 doctor.treatment
    for i in range(len(doctors)):
        df.loc[i, 'preliminary_exam'] = doctors[i].preliminary_exam
        df.loc[i, 'treatment'] = doctors[i].treatment

    # 将诊断结果写入xlsx文件
    file_path_result = "../data/json_r1_full.xlsx"
    df.to_excel(file_path_result, index=False)
    print(f"Diagnosis complete. Results written to {file_path_result}")

def process_json(historys_json : dict , output_file_path = "../data/json_r1_full_new_test_json.xlsx"):
    # file_path = "../data/cases_full.json"
    doctors = Doctor.load_from_json(historys_json)
    
    
    diagnosis_agent = DiagnosisAgent(doctors, model="deepseek_r1")

    # 诊断所有患者
    results = diagnosis_agent.diagnose_all()
    # 打印诊断结果
    for result in results:
        # print(f"Patient: {result['patient']}")
        print(f"Diagnosis: {result['diagnosis']}")
        print(f"Treatment Plan: {result['treatment_plan']}")
        print("-" * 40)


    # 将诊断结果保存到xlsx文件
    df = pd.DataFrame(results)
    
    # df新加两列，分别是doctor.preliminary_exam 和 doctor.treatment
    for i in range(len(doctors)):
        df.loc[i, 'preliminary_exam'] = doctors[i].preliminary_exam
        df.loc[i, 'treatment'] = doctors[i].treatment
        df.loc[i, 'doctor_examination'] = doctors[i].doctor_examination
        df.loc[i, 'has_examination'] = doctors[i].has_examination
        df.loc[i, 'index'] = doctors[i].index

    # 将诊断结果写入xlsx文件
    file_path_result = output_file_path
    df.to_excel(file_path_result, index=False)
    print(f"Diagnosis complete. Results written to {file_path_result}")


if __name__ == '__main__':
    main_csv()