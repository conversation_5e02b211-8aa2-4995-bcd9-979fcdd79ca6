# Druid数据库连接池监控配置说明

## 概述

Druid是阿里巴巴开源的数据库连接池，除了提供高效的连接池功能外，还内置了强大的监控功能。在生产环境中，配置Druid监控对于以下方面至关重要：

1. **性能监控**：实时监控SQL执行情况，发现慢查询
2. **连接泄露检测**：识别未正确关闭的数据库连接
3. **资源使用分析**：监控连接池使用情况，合理配置池大小
4. **SQL注入防御**：通过SQL防火墙功能，增强安全性

## 配置说明

本项目已添加以下配置以支持Druid监控：

1. **配置文件**：`application-env.yml`中已添加Druid监控相关配置
2. **环境变量**：`env-template`中已添加监控所需的环境变量
3. **配置类**：`DruidMonitorConfig.java`提供了监控Servlet和Filter的配置

## 使用方法

### 1. 配置环境变量

确保在`.env`文件中设置以下环境变量：

```properties
DRUID_ADMIN_USERNAME=your_secure_username
DRUID_ADMIN_PASSWORD=your_secure_password
DRUID_ALLOW_IP=your_allowed_ips
```

⚠️ **安全提示**：在生产环境中，请设置复杂的用户名和密码，并限制只有特定IP地址能访问监控页面。

### 2. 访问监控页面

应用启动后，访问以下URL查看Druid监控控制台：

```
http://your-application-host:port/druid
```

使用配置的用户名和密码登录。

### 3. 监控功能说明

Druid监控控制台提供以下主要功能：

- **数据源**：监控数据源配置、连接池状态
- **SQL监控**：查看所有执行的SQL语句、执行时间、执行次数等
- **SQL防火墙**：监控SQL注入攻击
- **Web应用**：监控Web应用中的请求
- **URI监控**：监控接口调用情况
- **Session监控**：监控会话情况

## 生产环境注意事项

1. **限制访问IP**：生产环境中应严格限制能访问监控页面的IP地址
2. **设置强密码**：使用复杂密码保护监控页面
3. **调整慢SQL阈值**：根据实际情况调整慢SQL定义阈值（默认1000ms）
4. **定期查看监控**：定期检查监控数据，及时发现并解决问题
5. **考虑性能影响**：监控会有轻微性能开销，根据需要调整配置

## 常见问题排查

### 无法访问监控页面

1. 检查配置中`stat-view-servlet.enabled`是否设为true
2. 确认访问IP是否在允许列表中
3. 检查用户名密码是否正确

### 监控数据不全面

1. 确认`web-stat-filter`配置是否正确
2. 检查不同filter（stat、wall、slf4j）是否启用

### 性能问题

如果监控导致性能下降，可以：
1. 关闭不需要的监控项
2. 调整合并相似SQL参数
3. 提高慢SQL阈值

## 与其他监控工具的集成

Druid监控可以与以下工具集成：

1. **Prometheus + Grafana**：通过JMX导出Druid指标
2. **Spring Boot Actuator**：提供健康检查端点
3. **ELK Stack**：将监控日志发送到日志分析平台

## 更多资源

- [Druid GitHub仓库](https://github.com/alibaba/druid)
- [Druid Wiki](https://github.com/alibaba/druid/wiki) 