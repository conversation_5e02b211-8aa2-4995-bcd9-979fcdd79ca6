package com.makiyo.ai_doctor_prod.controller;

import com.makiyo.ai_doctor_prod.service.OcrQueueService;
import com.makiyo.ai_doctor_prod.service.OcrService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * 处理 OCR 相关请求的控制器 (生产环境)。
 */
@RestController
@RequestMapping("/ocr") // OCR 相关端点的基础路径 (Prod)
@Tag(name = "OCR 接口 (Prod)", description = "提供生产环境的 OCR 处理接口")
public class OcrController {

    private static final Logger log = LoggerFactory.getLogger(OcrController.class);

    private final OcrService ocrService;
    private final OcrQueueService ocrQueueService;
    private final OcrQueueStatsController statsController;

    @Autowired
    public OcrController(OcrService ocrService, OcrQueueService ocrQueueService, OcrQueueStatsController statsController) {
        this.ocrService = ocrService;
        this.ocrQueueService = ocrQueueService;
        this.statsController = statsController;
    }

    /**
     * 处理 OCR 请求的端点 (Prod)。
     * 接收 multipart/form-data 请求，包含图片文件。
     *
     * @param image 上传的图片文件 (表单部分名称: "image")。
     * @return 如果成功，返回包含 OCR 结果的 ResponseEntity (HTTP 200)；
     *         如果失败，返回包含错误信息的 ResponseEntity (HTTP 400 或 500)。
     */
    @Operation(
        summary = "处理 OCR 请求 (Prod)",
        description = "上传图片进行 OCR 识别，并直接返回 Python OCR 服务的原始响应。此接口不与数据库交互。请求会进入队列顺序处理。"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "OCR 处理成功，直接返回 Python OCR 服务响应", content = @io.swagger.v3.oas.annotations.media.Content(mediaType = "application/json", schema = @io.swagger.v3.oas.annotations.media.Schema(example = "{\"检查名称\": \"血常规\", \"content\": [...], ...}"))),
        @ApiResponse(responseCode = "400", description = "请求参数无效（如缺少图片）、OCR 识别失败"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误（如文件保存失败、Python服务调用问题）"),
        @ApiResponse(responseCode = "503", description = "无法连接到 OCR 服务"),
        @ApiResponse(responseCode = "408", description = "处理超时")
    })
    @PostMapping(value = "/process", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> processOcrRequestProd(
            @Parameter(description = "上传的图片文件", required = true,
                       content = @io.swagger.v3.oas.annotations.media.Content(mediaType = MediaType.MULTIPART_FORM_DATA_VALUE,
                               schema = @io.swagger.v3.oas.annotations.media.Schema(type = "string", format = "binary")))
            @RequestPart("image") MultipartFile image) {

        log.info("[PROD OCR Controller] Received OCR request. Image: {}", image.getOriginalFilename());
        
        // 验证图片文件
        if (image == null || image.isEmpty()) {
            log.warn("[PROD OCR Controller] Image file is empty or null");
            return ResponseEntity.badRequest()
                    .body(Collections.singletonMap("error", "图片文件为空或无效"));
        }
        
        
        // 记录新请求
        statsController.recordNewRequest();
        long startTime = System.currentTimeMillis();

        try {
            // 使用队列服务而非直接调用OcrService
            CompletableFuture<Object> future = ocrQueueService.enqueueOcrRequest(image);
            
            // 等待结果，设置超时时间为180秒
            Object result = future.get(180, TimeUnit.SECONDS);
            
            // 计算处理时间
            long processingTime = System.currentTimeMillis() - startTime;

            if (result == null) {
                log.error("[PROD OCR Controller] OCR service returned unexpected null result.");
                statsController.updateStats(false, processingTime);
                statsController.recordError("NULL_RESULT");
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                     .body(Collections.singletonMap("error", "OCR 处理期间发生内部服务器错误。"));
            } else if (result instanceof Map && ((Map<?, ?>) result).containsKey("error")) {
                 log.warn("[PROD OCR Controller] OCR processing failed: {}", result);
                 HttpStatus status = determineHttpStatusFromErrorProd((Map<String, Object>) result);
                 statsController.updateStats(false, processingTime);
                 
                 // 记录错误类型
                 String errorMsg = ((Map<String, Object>) result).get("error").toString();
                 if (errorMsg.contains("连接")) {
                     statsController.recordError("CONNECTION_ERROR");
                 } else if (errorMsg.contains("超时")) {
                     statsController.recordError("TIMEOUT_ERROR");
                 } else {
                     statsController.recordError("OCR_API_ERROR");
                 }
                 
                 return ResponseEntity.status(status).body(result);
            } else {
                log.info("[PROD OCR Controller] OCR request processed successfully. Processing time: {}ms", processingTime);
                statsController.updateStats(true, processingTime);
                
                // 添加处理时间到结果
                if (result instanceof Map) {
                    Map<String, Object> resultMap = new HashMap<>((Map<String, Object>) result);
                    resultMap.put("processingTimeMs", processingTime);
                    return ResponseEntity.ok(resultMap);
                } else {
                    return ResponseEntity.ok(result);
                }
            }
        } catch (ExecutionException e) {
            // 捕获执行时异常
            log.error("[PROD OCR Controller] OCR processing execution exception: {}", e.getMessage(), e.getCause());
            statsController.updateStats(false, System.currentTimeMillis() - startTime);
            statsController.recordError("EXECUTION_ERROR");
            
            Throwable cause = e.getCause();
            String errorMsg = cause != null ? cause.getMessage() : e.getMessage();
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                .body(Collections.singletonMap("error", "OCR处理执行失败: " + errorMsg));
        } catch (TimeoutException e) {
            // 捕获超时异常
            log.error("[PROD OCR Controller] OCR processing timeout: {}", e.getMessage(), e);
            statsController.updateStats(false, System.currentTimeMillis() - startTime);
            statsController.recordError("TIMEOUT_ERROR");
            
            return ResponseEntity.status(HttpStatus.REQUEST_TIMEOUT)
                                .body(Collections.singletonMap("error", "OCR处理超时，请稍后重试"));
        } catch (InterruptedException e) {
            // 捕获中断异常
            log.error("[PROD OCR Controller] OCR processing interrupted: {}", e.getMessage(), e);
            Thread.currentThread().interrupt(); // 重置中断状态
            statsController.updateStats(false, System.currentTimeMillis() - startTime);
            statsController.recordError("INTERRUPTED_ERROR");
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                .body(Collections.singletonMap("error", "OCR处理被意外中断"));
        } catch (Exception e) {
            log.error("[PROD OCR Controller] Unhandled exception occurred: {}", e.getMessage(), e);
            statsController.updateStats(false, System.currentTimeMillis() - startTime);
            statsController.recordError("UNHANDLED_ERROR");
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                 .body(Collections.singletonMap("error", "发生意外的内部服务器错误: " + e.getMessage()));
        }
    }

    /**
     * 处理带ID参数的OCR请求端点（与数据库交互）
     *
     * @param image 上传的图片文件
     * @param id 案例ID
     * @param name 用户名（可选）
     * @return OCR处理结果
     */
    @Operation(
        summary = "处理带ID的OCR请求（与数据库交互）",
        description = "上传图片进行OCR识别，并将结果保存到数据库中。请求会进入队列顺序处理。"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "OCR处理成功，返回结果并已更新数据库"),
        @ApiResponse(responseCode = "400", description = "请求参数无效或OCR识别失败"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误"),
        @ApiResponse(responseCode = "503", description = "无法连接到OCR服务"),
        @ApiResponse(responseCode = "408", description = "处理超时")
    })
    @PostMapping(value = "/process-with-id", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> processOcrWithIdRequestProd(
            @RequestPart("image") MultipartFile image,
            @RequestParam("id") String id,
            @RequestParam(value = "name", required = false) String name) {

        log.info("[PROD OCR Controller] Received OCR request with ID: {}, Image: {}", id, image.getOriginalFilename());
        
        // 验证图片文件
        if (image == null || image.isEmpty()) {
            log.warn("[PROD OCR Controller] Image file is empty or null for case ID: {}", id);
            return ResponseEntity.badRequest()
                    .body(Collections.singletonMap("error", "图片文件为空或无效"));
        }
        
        // 校验文件类型
        String contentType = image.getContentType();
        if (contentType == null || !contentType.startsWith("image/")) {
            log.warn("[PROD OCR Controller] Invalid file type: {} for case ID: {}", contentType, id);
            return ResponseEntity.badRequest()
                    .body(Collections.singletonMap("error", "仅支持图片文件格式"));
        }
        
        // 校验ID参数
        if (id == null || id.trim().isEmpty()) {
            log.warn("[PROD OCR Controller] Missing or empty ID parameter");
            return ResponseEntity.badRequest()
                    .body(Collections.singletonMap("error", "请提供有效的案例ID"));
        }
        
        // 记录新请求
        statsController.recordNewRequest();
        long startTime = System.currentTimeMillis();

        try {
            // 使用队列服务处理带ID的请求
            CompletableFuture<Object> future = ocrQueueService.enqueueOcrRequestWithId(image, id, name);
            
            // 等待结果，设置超时时间为180秒
            Object result = future.get(180, TimeUnit.SECONDS);
            
            // 计算处理时间
            long processingTime = System.currentTimeMillis() - startTime;

            if (result == null) {
                log.error("[PROD OCR Controller] OCR service returned unexpected null result for case ID: {}", id);
                statsController.updateStats(id, false, processingTime);
                statsController.recordError("NULL_RESULT");
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                     .body(Collections.singletonMap("error", "OCR 处理期间发生内部服务器错误。"));
            } else if (result instanceof Map && ((Map<?, ?>) result).containsKey("error")) {
                 log.warn("[PROD OCR Controller] OCR processing failed for case ID {}: {}", id, result);
                 HttpStatus status = determineHttpStatusFromErrorProd((Map<String, Object>) result);
                 statsController.updateStats(id, false, processingTime);
                 
                 // 记录错误类型
                 String errorMsg = ((Map<String, Object>) result).get("error").toString();
                 if (errorMsg.contains("连接")) {
                     statsController.recordError("CONNECTION_ERROR");
                 } else if (errorMsg.contains("超时")) {
                     statsController.recordError("TIMEOUT_ERROR");
                 } else if (errorMsg.contains("数据库")) {
                     statsController.recordError("DATABASE_ERROR");
                 } else {
                     statsController.recordError("OCR_API_ERROR");
                 }
                 
                 return ResponseEntity.status(status).body(result);
            } else {
                log.info("[PROD OCR Controller] OCR request processed successfully for case ID: {}. Processing time: {}ms", 
                        id, processingTime);
                statsController.updateStats(id, true, processingTime);
                
                // 添加处理时间到结果
                if (result instanceof Map) {
                    Map<String, Object> resultMap = new HashMap<>((Map<String, Object>) result);
                    resultMap.put("processingTimeMs", processingTime);
                    resultMap.put("caseId", id);
                    return ResponseEntity.ok(resultMap);
                } else {
                    return ResponseEntity.ok(result);
                }
            }
        } catch (ExecutionException e) {
            log.error("[PROD OCR Controller] OCR processing execution exception for case ID {}: {}", id, e.getMessage(), e.getCause());
            statsController.updateStats(id, false, System.currentTimeMillis() - startTime);
            statsController.recordError("EXECUTION_ERROR");
            
            Throwable cause = e.getCause();
            String errorMsg = cause != null ? cause.getMessage() : e.getMessage();
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                .body(Collections.singletonMap("error", "OCR处理执行失败: " + errorMsg));
        } catch (TimeoutException e) {
            log.error("[PROD OCR Controller] OCR processing timeout for case ID {}: {}", id, e.getMessage(), e);
            statsController.updateStats(id, false, System.currentTimeMillis() - startTime);
            statsController.recordError("TIMEOUT_ERROR");
            
            return ResponseEntity.status(HttpStatus.REQUEST_TIMEOUT)
                                .body(Collections.singletonMap("error", "OCR处理超时，请稍后重试"));
        } catch (InterruptedException e) {
            log.error("[PROD OCR Controller] OCR processing interrupted for case ID {}: {}", id, e.getMessage(), e);
            Thread.currentThread().interrupt();
            statsController.updateStats(id, false, System.currentTimeMillis() - startTime);
            statsController.recordError("INTERRUPTED_ERROR");
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                .body(Collections.singletonMap("error", "OCR处理被意外中断"));
        } catch (Exception e) {
            log.error("[PROD OCR Controller] Unhandled exception occurred for case ID {}: {}", id, e.getMessage(), e);
            statsController.updateStats(id, false, System.currentTimeMillis() - startTime);
            statsController.recordError("UNHANDLED_ERROR");
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                 .body(Collections.singletonMap("error", "发生意外的内部服务器错误: " + e.getMessage()));
        }
    }

    /**
     * 获取OCR队列状态
     * 
     * @return 当前队列长度
     */
    @Operation(
        summary = "获取OCR队列状态",
        description = "返回当前OCR处理队列中待处理的请求数量"
    )
    @GetMapping("/queue-status")
    public ResponseEntity<Map<String, Object>> getQueueStatus() {
        int queueSize = ocrQueueService.getQueueSize();
        
        // 获取更详细的队列统计
        Map<String, Object> queueStats = new HashMap<>(ocrQueueService.getQueueStats());
        queueStats.put("queueSize", queueSize);
        
        // 添加队列健康状态
        String healthStatus;
        if (queueSize == 0) {
            healthStatus = "IDLE"; // 空闲
        } else if (queueSize < 5) {
            healthStatus = "NORMAL"; // 正常
        } else if (queueSize < 10) {
            healthStatus = "BUSY"; // 繁忙
        } else {
            healthStatus = "OVERLOADED"; // 过载
        }
        queueStats.put("healthStatus", healthStatus);
        
        log.info("[PROD OCR Controller] Queue status requested. Current queue size: {}, Status: {}", queueSize, healthStatus);
        return ResponseEntity.ok(queueStats);
    }

    /**
     * Helper method to determine HTTP status code based on error map (Prod version).
     * @param errorResult The map possibly containing an "error" key.
     * @return Appropriate HttpStatus.
     */
    private HttpStatus determineHttpStatusFromErrorProd(Map<String, Object> errorResult) {
        String errorMsg = errorResult.getOrDefault("error", "").toString().toLowerCase();
        
        if (errorResult.containsKey("content") && errorResult.get("content").equals("识别失败")) {
            return HttpStatus.BAD_REQUEST;
        }
        
        if (errorMsg.contains("连接") || errorMsg.contains("connection failed")) {
            return HttpStatus.SERVICE_UNAVAILABLE;
        }
        
        if (errorMsg.contains("超时") || errorMsg.contains("timeout")) {
            return HttpStatus.REQUEST_TIMEOUT;
        }
        
        if (errorMsg.contains("不支持") || errorMsg.contains("无效") || errorMsg.contains("需要提供")) {
            return HttpStatus.BAD_REQUEST;
        }
        
        if (errorMsg.contains("数据库") || errorMsg.contains("database")) {
            return HttpStatus.INTERNAL_SERVER_ERROR;
        }
        
        return HttpStatus.INTERNAL_SERVER_ERROR;
    }
}
