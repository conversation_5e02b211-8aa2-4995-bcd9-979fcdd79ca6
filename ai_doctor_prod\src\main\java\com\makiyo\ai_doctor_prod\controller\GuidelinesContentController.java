package com.makiyo.ai_doctor_prod.controller;


import com.makiyo.ai_doctor_prod.response.Response;
import com.makiyo.ai_doctor_prod.service.GuidelinesContentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.NotBlank;

/**
 * 指南内容控制器
 * 提供检索指南内容相关的API接口
 */
@Tag(name = "指南内容", description = "提供检索指南内容相关的API接口")
@RestController
@RequestMapping("/guidelines")
public class GuidelinesContentController {

    private static final Logger log = LoggerFactory.getLogger(GuidelinesContentController.class);

    @Resource
    private GuidelinesContentService guidelinesContentService;

    @Operation(
        summary = "根据案例ID获取指南内容",
        description = "通过案例ID从MongoDB检索对应的指南内容"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "成功获取指南内容",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = Response.class))
        ),
        @ApiResponse(
            responseCode = "400",
            description = "请求参数无效"
        ),
        @ApiResponse(
            responseCode = "404",
            description = "未找到指定案例ID的指南内容"
        ),
        @ApiResponse(
            responseCode = "500",
            description = "服务器内部错误"
        )
    })
    @GetMapping("/getByCaseId")
    public Response<?> getGuidelinesContentByCaseId(
        @Parameter(description = "案例ID", required = true, example = "abc123")
        @RequestParam @NotNull(message = "案例ID不能为空")
        @NotBlank(message = "案例ID不能为空字符串") String caseId
    ) {
        log.info("收到根据案例ID获取指南内容请求，Case ID: {}", caseId);
        try {
            Object content = guidelinesContentService.getContentByCaseId(caseId);
            
            if (content == null) {
                log.warn("未找到案例ID为{}的指南内容", caseId);
                return Response.ok("无指南");
            } else {
                log.info("成功获取案例ID为{}的指南内容", caseId);
                return Response.ok(content);
            }
        } catch (Exception e) {
            log.error("获取指南内容时发生错误，案例ID: {}, 错误: {}", caseId, e.getMessage(), e);
            return Response.error(500, "获取指南内容时发生服务器内部错误: " + e.getMessage());
        }
    }

    @Operation(
        summary = "根据引用ID获取指南内容",
        description = "通过引用ID从MongoDB检索对应的指南内容"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "成功获取指南内容",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = Response.class))
        ),
        @ApiResponse(
            responseCode = "400",
            description = "请求参数无效"
        ),
        @ApiResponse(
            responseCode = "404",
            description = "未找到指定引用ID的指南内容"
        ),
        @ApiResponse(
            responseCode = "500",
            description = "服务器内部错误"
        )
    })
    @GetMapping("/getByRefId")
    public Response<?> getGuidelinesContentByRefId(
        @Parameter(description = "引用ID", required = true, example = "d290f1ee-6c54-4b01-90e6-d701748f0851")
        @RequestParam @NotNull(message = "引用ID不能为空") String contentRefId
    ) {
        log.info("收到根据引用ID获取指南内容请求，引用ID: {}", contentRefId);
        try {
            Object content = guidelinesContentService.getContentById(contentRefId);
            
            if (content == null) {
                log.warn("未找到引用ID为{}的指南内容", contentRefId);
                return Response.error(404, "未找到指定引用ID的指南内容");
            } else {
                log.info("成功获取引用ID为{}的指南内容", contentRefId);
                return Response.ok(content);
            }
        } catch (Exception e) {
            log.error("获取指南内容时发生错误，引用ID: {}, 错误: {}", contentRefId, e.getMessage(), e);
            return Response.error(500, "获取指南内容时发生服务器内部错误: " + e.getMessage());
        }
    }

    @Operation(
        summary = "根据案例ID获取完整指南内容对象",
        description = "通过案例ID从MongoDB检索对应的完整指南内容对象，包含元数据"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "成功获取完整指南内容对象",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = Response.class))
        ),
        @ApiResponse(
            responseCode = "400",
            description = "请求参数无效"
        ),
        @ApiResponse(
            responseCode = "404",
            description = "未找到指定案例ID的指南内容"
        ),
        @ApiResponse(
            responseCode = "500",
            description = "服务器内部错误"
        )
    })
    @GetMapping("/getFullByCaseId")
    public Response<?> getFullGuidelinesContentByCaseId(
        @Parameter(description = "案例ID", required = true, example = "abc123")
        @RequestParam @NotNull(message = "案例ID不能为空")
        @NotBlank(message = "案例ID不能为空字符串") String caseId
    ) {
        log.info("收到根据案例ID获取完整指南内容对象请求，Case ID: {}", caseId);
        try {
            Object content = guidelinesContentService.getFullContentByCaseId(caseId);
            
            if (content == null) {
                log.warn("未找到案例ID为{}的完整指南内容对象", caseId);
                return Response.ok("无指南");
            } else {
                log.info("成功获取案例ID为{}的完整指南内容对象", caseId);
                return Response.ok(content);
            }
        } catch (Exception e) {
            log.error("获取完整指南内容对象时发生错误，案例ID: {}, 错误: {}", caseId, e.getMessage(), e);
            return Response.error(500, "获取完整指南内容对象时发生服务器内部错误: " + e.getMessage());
        }
    }
} 