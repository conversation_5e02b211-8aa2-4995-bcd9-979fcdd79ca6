# --- Configuration ---
$endpoint = "http://localhost:5555/ai_doctor_v2_inquiry"
$numRequests = 5 # Let's start with a smaller number for this definitive test
$payloadFile = "payload.json"

# --- Request Body ---
# Define the JSON payload.
$jsonBody = @"
{
    "interaction_history": {
        "diagnosis": "",
        "cot_entries": [
            {
                "feedback": "",
                "strategy": "询问患者的基本信息（性别、年龄）和主诉症状，了解发病时间、起病情况、症状特点（性质、程度、持续时间、频率）、伴随症状、病情演变以及之前的诊疗经过。",
                "reasoning": "主诉是问诊的切入点，通过了解患者的基本信息和主要症状，可以快速建立初步印象。进一步询问发病时间、起病情况、症状特点、伴随症状、病情演变和诊疗经过，有助于全面了解现病史，为后续诊断提供关键信息。这种系统性的问诊策略能够帮助我们准确把握患者的病情，避免遗漏重要信息。",
                "observation": "",
                "dialogue_history": [
                    { "role": "doctor", "content": "您好，我是本次的医生，请问孩子的年龄，性别，主要的症状是什么？" },
                    { "role": "patient", "content": "男，三岁，有点发烧" },
                    { "role": "doctor", "content": "孩子是从什么时候开始发烧的？" },
                    { "role": "patient", "content": "三天前开始发烧" },
                    { "role": "doctor", "content": "孩子发烧前有没有什么特别的事情，比如着凉、接触生病的人或者剧烈活动？" }
                ]
            }
        ],
        "test_recommendation": [],
        "preliminary_diagnosis": null,
        "treatment_recommendation": [],
        "doctor_supplementary_info": []
    }
}
"@

# --- File Creation ---
# Save the JSON payload to a file with UTF-8 encoding. This is crucial.
Write-Host "Creating payload file: $payloadFile" -ForegroundColor Yellow
Set-Content -Path $payloadFile -Value $jsonBody -Encoding UTF8

# --- Test Execution (using curl) ---
Write-Host "Starting sequential test with curl: Will send $numRequests requests, 1 per second..." -ForegroundColor Green

for ($i = 1; $i -le $numRequests; $i++) {
    Write-Host "-------------------------------------------"
    Write-Host "Sending request #$i..." -ForegroundColor Cyan
    
    try {
        # Use curl to send the request. It's more reliable for complex JSON and encoding.
        # The '@' tells curl to read the request body from the specified file.
        # -v provides verbose output to see exactly what's happening.
        $curl_output = curl.exe -v -X POST -H "Content-Type: application/json" --data-binary "@$payloadFile" $endpoint 2>&1
        
        Write-Host "Curl command finished. Analyzing output..."
        
        # Check if the output indicates success (e.g., HTTP/1.1 200 OK)
        if ($curl_output | Select-String -Pattern "HTTP/[0-9\.]+ 200") {
             Write-Host "Request successful! (HTTP 200 OK)" -ForegroundColor Green
             $curl_output[-5..-1] # Display last 5 lines of output, which usually contains the body
        } else {
             Write-Host "Request failed. Full curl output below:" -ForegroundColor Red
             Write-Host $curl_output
        }
    } catch {
        # This catch block might not be hit if curl itself doesn't throw a terminating error.
        Write-Host "A script-level error occurred: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # Wait for 1 second
    Start-Sleep -Seconds 1
}

# --- Cleanup ---
Write-Host "-------------------------------------------"
Write-Host "Test complete. Removing temporary payload file." -ForegroundColor Green
Remove-Item -Path $payloadFile -ErrorAction SilentlyContinue