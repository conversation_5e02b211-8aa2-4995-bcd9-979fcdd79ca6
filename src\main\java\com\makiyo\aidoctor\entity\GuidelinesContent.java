package com.makiyo.aidoctor.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.index.Indexed;
import java.time.LocalDateTime;

/**
 * 指南内容 - MongoDB文档
 */
@Data
@Document(collection = "guidelines_content")
public class GuidelinesContent {

    @Id
    private String id;

    /**
     * 会话ID
     */
    @Indexed
    private Integer sessionId;

    /**
     * 指南内容
     */
    private Object content;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    public GuidelinesContent() {
        this.createdAt = LocalDateTime.now();
    }

    public GuidelinesContent(Integer sessionId, Object content) {
        this.sessionId = sessionId;
        this.content = content;
        this.createdAt = LocalDateTime.now();
    }
}