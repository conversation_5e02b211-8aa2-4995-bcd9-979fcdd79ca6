<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>WebSocket 音频传输测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 0 20px;
        }
        #status {
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 4px;
            background-color: #f8f9fa;
        }
        #messages {
            border: 1px solid #ccc;
            height: 200px;
            overflow-y: scroll;
            padding: 10px;
            border-radius: 4px;
            background-color: #fff;
        }
        #controls {
            margin-top: 20px;
        }
        #controls button {
            margin-right: 10px;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
            transition: background-color 0.3s;
        }
        #controls button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        #controls button:hover:not(:disabled) {
            background-color: #0056b3;
        }
        #latency {
            margin-top: 20px;
            font-weight: bold;
            color: #28a745;
        }
        .message {
            margin: 5px 0;
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
    </style>
</head>
<body>

<h1>WebSocket 音频传输测试</h1>

<div id="status">状态：未连接</div>
<div id="messages"></div>

<div id="controls">
    <button id="startButton">开始录音并发送</button>
    <button id="stopButton" disabled>停止录音</button>
</div>

<div id="latency"></div>

<script>
    const wsUrl = 'ws://localhost:8080/ws/audio';
    let socket = null;
    const messagesDiv = document.getElementById('messages');
    const statusDiv = document.getElementById('status');
    const startButton = document.getElementById('startButton');
    const stopButton = document.getElementById('stopButton');
    let recordingAudioContext;
    let audioInput;
    let processor;
    let playbackAudioContext;
    let playbackQueue = [];
    let playbackTime = 0;
    let isPlaying = false;
    let overSentTime = null;
    let latencyMeasured = false;

    function logMessage(message) {
        const p = document.createElement('p');
        p.textContent = new Date().toLocaleTimeString() + ': ' + message;
        p.className = 'message';
        messagesDiv.appendChild(p);
        messagesDiv.scrollTop = messagesDiv.scrollHeight;
    }

    function initializePlayback() {
        playbackAudioContext = new (window.AudioContext || window.webkitAudioContext)();
        logMessage('音频播放上下文已创建');
    }

    function appendToPlaybackQueue(data) {
        playbackAudioContext.decodeAudioData(data, (audioBuffer) => {
            playbackQueue.push(audioBuffer);
            schedulePlayback();
        }, (error) => {
            logMessage('解码音频数据时出错：' + error);
        });
    }

    function schedulePlayback() {
        if (isPlaying || playbackQueue.length === 0) return;

        const buffer = playbackQueue.shift();
        const source = playbackAudioContext.createBufferSource();
        source.buffer = buffer;
        source.connect(playbackAudioContext.destination);

        if (playbackTime < playbackAudioContext.currentTime) {
            playbackTime = playbackAudioContext.currentTime;
        }

        source.start(playbackTime);
        logMessage(`计划在 ${playbackTime.toFixed(2)}s 播放音频`);

        playbackTime += buffer.duration;
        isPlaying = true;

        source.onended = () => {
            isPlaying = false;
            schedulePlayback();
        };
    }

    function createWebSocket() {
        if (socket !== null && (socket.readyState === WebSocket.OPEN || socket.readyState === WebSocket.CONNECTING)) {
            logMessage('WebSocket 已经连接或正在连接中');
            return;
        }

        socket = new WebSocket(wsUrl);
        socket.binaryType = 'arraybuffer';

        socket.onopen = function () {
            statusDiv.textContent = '状态：已连接';
            statusDiv.style.color = '#28a745';
            logMessage('WebSocket 连接已打开');
            startButton.disabled = false;
        };

        socket.onmessage = function (event) {
            if (typeof event.data === 'string' && event.data === 'over') {
                logMessage('收到结束信号: over');
                return;
            }

            if (event.data instanceof ArrayBuffer) {
                logMessage('接收到音频数据');

                if (overSentTime !== null && !latencyMeasured) {
                    const receiveTime = performance.now();
                    const latency = receiveTime - overSentTime;
                    logMessage(`延迟时间：${latency.toFixed(2)} 毫秒`);
                    document.getElementById('latency').textContent = `延迟时间：${latency.toFixed(2)} 毫秒`;
                    latencyMeasured = true;
                }

                appendToPlaybackQueue(event.data);
            }
        };

        socket.onerror = function (error) {
            statusDiv.textContent = '状态：连接错误';
            statusDiv.style.color = '#dc3545';
            logMessage('WebSocket 发生错误：' + error.message);
        };

        socket.onclose = function (event) {
            if (event.code === 1000) {
                statusDiv.textContent = '状态：已断开连接';
                statusDiv.style.color = '#6c757d';
                logMessage('WebSocket 正常关闭');
            } else {
                statusDiv.textContent = '状态：连接错误';
                statusDiv.style.color = '#dc3545';
                logMessage(`WebSocket 关闭，代码：${event.code}, 原因：${event.reason}`);
            }
            startButton.disabled = false;
            stopButton.disabled = true;
        };
    }

    function startRecording() {
        createWebSocket();

        navigator.mediaDevices.getUserMedia({ audio: true })
            .then(function (stream) {
                recordingAudioContext = new (window.AudioContext || window.webkitAudioContext)({ sampleRate: 16000 });
                audioInput = recordingAudioContext.createMediaStreamSource(stream);
                processor = recordingAudioContext.createScriptProcessor(4096, 1, 1);

                audioInput.connect(processor);
                processor.connect(recordingAudioContext.destination);

                processor.onaudioprocess = function (e) {
                    const audioData = e.inputBuffer.getChannelData(0);
                    const int16Data = floatTo16BitPCM(audioData);
                    const wavBuffer = encodeWAV(int16Data, recordingAudioContext.sampleRate);

                    if (socket && socket.readyState === WebSocket.OPEN) {
                        socket.send(wavBuffer);
                    }
                };

                logMessage('开始录音并发送音频数据');
                startButton.disabled = true;
                stopButton.disabled = false;
                initializeAudioPlayback();
            })
            .catch(function (err) {
                logMessage('无法访问麦克风：' + err.message);
            });
    }

    function stopRecording() {
        if (processor) {
            processor.disconnect();
            processor = null;
        }

        if (audioInput) {
            audioInput.disconnect();
            audioInput = null;
        }

        if (recordingAudioContext) {
            recordingAudioContext.close();
            recordingAudioContext = null;
        }

        logMessage('停止录音');
        startButton.disabled = false;
        stopButton.disabled = true;

        if (socket && socket.readyState === WebSocket.OPEN) {
            socket.send("over");
            overSentTime = performance.now();
            latencyMeasured = false;
            logMessage('发送结束信号 "over"');
        }
    }

    function floatTo16BitPCM(float32Array) {
        const int16Array = new Int16Array(float32Array.length);
        for (let i = 0; i < float32Array.length; i++) {
            const s = Math.max(-1, Math.min(1, float32Array[i]));
            int16Array[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
        }
        return int16Array;
    }

    function encodeWAV(samples, sampleRate) {
        const buffer = new ArrayBuffer(44 + samples.length * 2);
        const view = new DataView(buffer);

        writeString(view, 0, 'RIFF');
        view.setUint32(4, 36 + samples.length * 2, true);
        writeString(view, 8, 'WAVE');
        writeString(view, 12, 'fmt ');
        view.setUint32(16, 16, true);
        view.setUint16(20, 1, true);
        view.setUint16(22, 1, true);
        view.setUint32(24, sampleRate, true);
        view.setUint32(28, sampleRate * 2, true);
        view.setUint16(32, 2, true);
        view.setUint16(34, 16, true);
        writeString(view, 36, 'data');
        view.setUint32(40, samples.length * 2, true);

        let offset = 44;
        for (let i = 0; i < samples.length; i++, offset += 2) {
            view.setInt16(offset, samples[i], true);
        }

        return buffer;
    }

    function writeString(view, offset, string) {
        for (let i = 0; i < string.length; i++) {
            view.setUint8(offset + i, string.charCodeAt(i));
        }
    }

    startButton.addEventListener('click', startRecording);
    stopButton.addEventListener('click', stopRecording);

    window.onload = function () {
        logMessage('请点击 "开始录音并发送" 按钮以开始录音');
    };
</script>

</body>
</html> 