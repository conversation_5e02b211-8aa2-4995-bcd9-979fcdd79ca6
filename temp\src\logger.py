"""
Logging configuration for the AI Doctor system.
"""
import sys
from loguru import logger
from datetime import datetime

# 生成启动时间戳
startup_time = datetime.now().strftime("%Y%m%d_%H%M%S")

# Configure loguru logger
logger.remove()  # Remove default handler
logger.add(
    sys.stderr,
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
    level="INFO"
)
logger.add(
    f"logs/{startup_time}.log",  # 使用启动时间戳作为文件名
    retention="30 days",  # 保留30天
    format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
    level="DEBUG",
    enqueue=True,
    rotation="500 MB"  # 防止单文件过大，可根据需要调整
)

# Create a function to get a logger for a specific module
def get_logger(name):
    return logger.bind(name=name)

