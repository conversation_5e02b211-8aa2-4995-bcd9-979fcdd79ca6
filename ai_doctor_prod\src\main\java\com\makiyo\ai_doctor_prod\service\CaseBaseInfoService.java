package com.makiyo.ai_doctor_prod.service;

import com.makiyo.ai_doctor_prod.entity.CaseBaseInfo;
import com.makiyo.ai_doctor_prod.repository.CaseBaseInfoRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;
import java.util.Optional;

@Service
public class CaseBaseInfoService {

    private final CaseBaseInfoRepository repository;

    @Autowired
    public CaseBaseInfoService(CaseBaseInfoRepository repository) {
        this.repository = repository;
    }

    public CaseBaseInfo saveOrUpdate(String caseId, Map<String, Object> info) {
        Optional<CaseBaseInfo> existing = repository.findByCaseId(caseId);
        CaseBaseInfo entity = existing.orElseGet(CaseBaseInfo::new);
        entity.setCaseId(caseId);
        entity.setInfo(info);
        entity.setCreatedAt(new Date());
        return repository.save(entity);
    }

    public Optional<Map<String, Object>> getInfo(String caseId) {
        return repository.findByCaseId(caseId).map(CaseBaseInfo::getInfo);
    }
} 