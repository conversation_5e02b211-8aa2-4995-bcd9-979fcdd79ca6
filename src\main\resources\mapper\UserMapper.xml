<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.makiyo.aidoctor.mapper.UserMapper">
  <resultMap id="BaseResultMap" type="com.makiyo.aidoctor.entity.User">
    <id column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="phone_number" jdbcType="VARCHAR" property="phoneNumber" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="last_login" jdbcType="TIMESTAMP" property="lastLogin" />
    <result column="profile_picture" jdbcType="LONGVARCHAR" property="profilePicture" />
    <result column="bio" jdbcType="VARCHAR" property="bio" />
    <result column="role_id" jdbcType="INTEGER" property="roleId" />
  </resultMap>
  <resultMap id="SessionResultMap" type="com.makiyo.aidoctor.entity.Session">
    <id column="id" jdbcType="INTEGER" property="id"/>
    <result column="user_id" jdbcType="INTEGER" property="userId"/>
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt"/>
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt"/>
  </resultMap>
  <sql id="Base_Column_List">
    user_id, username, email, phone_number, created_at, updated_at, last_login, 
    profile_picture, bio, role_id
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user
    where user_id = #{userId}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user
  </select>
  <select id="selectByUserId" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user
    where user_id = #{userId}
  </select>
  <select id="selectByUsername" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user
    where username = #{username}
  </select>
  <select id="selectBasicInfoByUserId" resultMap="BaseResultMap">
    select 
      username, 
      profile_picture, 
      bio
    from user
    where user_id = #{userId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from user
    where user_id = #{userId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="user_id" keyProperty="userId" parameterType="com.makiyo.aidoctor.entity.User" useGeneratedKeys="true">
    insert into user (username, email, phone_number, 
      created_at, updated_at, last_login, 
      profile_picture, bio, role_id)
    values (#{username,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, #{phoneNumber,jdbcType=VARCHAR}, 
      CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, #{lastLogin,jdbcType=TIMESTAMP}, 
      #{profilePicture,jdbcType=LONGVARCHAR}, #{bio,jdbcType=VARCHAR}, #{roleId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="user_id" keyProperty="userId" parameterType="com.makiyo.aidoctor.entity.User" useGeneratedKeys="true">
    insert into user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="username != null">
        username,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="phoneNumber != null">
        phone_number,
      </if>
      created_at,
      updated_at,
      <if test="lastLogin != null">
        last_login,
      </if>
      <if test="profilePicture != null">
        profile_picture,
      </if>
      <if test="bio != null">
        bio,
      </if>
      <if test="roleId != null">
        role_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="phoneNumber != null">
        #{phoneNumber,jdbcType=VARCHAR},
      </if>
      NOW(),
      NOW(),
      <if test="lastLogin != null">
        #{lastLogin,jdbcType=TIMESTAMP},
      </if>
      <if test="profilePicture != null">
        #{profilePicture,jdbcType=LONGVARCHAR},
      </if>
      <if test="bio != null">
        #{bio,jdbcType=VARCHAR},
      </if>
      <if test="roleId != null">
        #{roleId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.makiyo.aidoctor.entity.User">
    update user
    <set>
      <if test="username != null">
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="phoneNumber != null">
        phone_number = #{phoneNumber,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="lastLogin != null">
        last_login = #{lastLogin,jdbcType=TIMESTAMP},
      </if>
      <if test="profilePicture != null">
        profile_picture = #{profilePicture,jdbcType=LONGVARCHAR},
      </if>
      <if test="bio != null">
        bio = #{bio,jdbcType=VARCHAR},
      </if>
      <if test="roleId != null">
        role_id = #{roleId,jdbcType=INTEGER},
      </if>
    </set>
    where user_id = #{userId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.makiyo.aidoctor.entity.User">
    update user
    set username = #{username,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      phone_number = #{phoneNumber,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      last_login = #{lastLogin,jdbcType=TIMESTAMP},
      profile_picture = #{profilePicture,jdbcType=LONGVARCHAR},
      bio = #{bio,jdbcType=VARCHAR},
      role_id = #{roleId,jdbcType=INTEGER}
    where user_id = #{userId,jdbcType=INTEGER}
  </update>
  <select id="selectSessionsByUserId" resultMap="SessionResultMap">
    SELECT 
        id,
        user_id,
        created_at,
        updated_at
    FROM session
    WHERE user_id = #{userId}
    ORDER BY id DESC
  </select>
</mapper>