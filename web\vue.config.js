const { defineConfig } = require('@vue/cli-service')

// 根据环境确定代理目标
const target = process.env.NODE_ENV === 'development'
  ? 'http://localhost:8080'      // 本地开发环境后端地址
  : 'http://localhost:8080';    // 本地生产环境后端地址

module.exports = defineConfig({
  transpileDependencies: true,
  lintOnSave: false,
  publicPath: '/',
  productionSourceMap: false,
  
  devServer: {
    port: 8888,
    proxy: {
      '/api': {
        target,
        changeOrigin: true,
        pathRewrite: {
          '^/api': '/api'
        }
      },
      '/message': {
        target,
        changeOrigin: true,
        ws: true,
      },
      '/session': {
        target,
        changeOrigin: true,
      },
      '/user': {
        target,
        changeOrigin: true,
      },
      '/audio': {
        target,
        changeOrigin: true,
      },
      '/ocr': {
        target,
        changeOrigin: true,
      },
      '/guidelines': {
        target,
        changeOrigin: true,
      },
      '/images': {
        target,
        changeOrigin: true,
      }
    }
  }
})
