package com.makiyo.ai_doctor_prod.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

/**
 * 全局 Jackson 配置。
 * 统一提供 ObjectMapper Bean，关闭中文转义。
 */
@Configuration
public class JacksonConfig {

    @Bean
    @Primary // 优先作为默认 ObjectMapper
    public ObjectMapper objectMapper(Jackson2ObjectMapperBuilder builder) {
        ObjectMapper mapper = builder.build();
        // 保持中文等非 ASCII 字符，避免转义
        mapper.configure(JsonGenerator.Feature.ESCAPE_NON_ASCII, false);
        return mapper;
    }
} 