package com.makiyo.ai_doctor_prod.controller;

import com.makiyo.ai_doctor_prod.service.OcrQueueService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * OCR队列统计控制器，提供队列处理的统计信息 (生产环境)
 */
@RestController
@RequestMapping("/ocr/stats")
@Tag(name = "OCR 队列统计 (Prod)", description = "提供OCR队列处理的统计信息")
public class OcrQueueStatsController {
    private static final Logger log = LoggerFactory.getLogger(OcrQueueStatsController.class);
    
    private final OcrQueueService ocrQueueService;
    
    // 统计数据
    private final AtomicInteger totalRequests = new AtomicInteger(0);
    private final AtomicInteger successfulRequests = new AtomicInteger(0);
    private final AtomicInteger failedRequests = new AtomicInteger(0);
    private final AtomicLong totalProcessingTime = new AtomicLong(0);
    private final AtomicLong maxProcessingTime = new AtomicLong(0);
    private final AtomicLong minProcessingTime = new AtomicLong(Long.MAX_VALUE);
    
    // 高级统计数据
    private final ConcurrentHashMap<String, Integer> errorTypeCount = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<Long, Integer> timeRangeCount = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Integer> caseSuccessCount = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Integer> caseFailureCount = new ConcurrentHashMap<>();
    private Date lastResetTime = new Date();
    private long peakQueueSize = 0;
    
    @Autowired
    public OcrQueueStatsController(OcrQueueService ocrQueueService) {
        this.ocrQueueService = ocrQueueService;
        log.info("[PROD OCR Stats] OCR队列统计控制器已初始化");
    }
    
    /**
     * 记录新请求
     */
    public void recordNewRequest() {
        totalRequests.incrementAndGet();
        updatePeakQueueSize();
    }
    
    /**
     * 更新统计数据
     * 
     * @param success 请求是否成功
     * @param processingTime 处理时间（毫秒）
     */
    public void updateStats(boolean success, long processingTime) {
        if (success) {
            successfulRequests.incrementAndGet();
        } else {
            failedRequests.incrementAndGet();
        }
        
        totalProcessingTime.addAndGet(processingTime);
        
        // 更新最大处理时间
        long currentMax = maxProcessingTime.get();
        while (processingTime > currentMax) {
            if (maxProcessingTime.compareAndSet(currentMax, processingTime)) {
                break;
            }
            currentMax = maxProcessingTime.get();
        }
        
        // 更新最小处理时间
        long currentMin = minProcessingTime.get();
        while (processingTime < currentMin) {
            if (minProcessingTime.compareAndSet(currentMin, processingTime)) {
                break;
            }
            currentMin = minProcessingTime.get();
        }
        
        // 更新时间范围统计
        long timeRange = getTimeRangeKey(processingTime);
        timeRangeCount.compute(timeRange, (k, v) -> (v == null) ? 1 : v + 1);
    }
    
    /**
     * 更新带ID的请求统计数据
     * 
     * @param id 案例ID
     * @param success 请求是否成功
     * @param processingTime 处理时间（毫秒）
     */
    public void updateStats(String id, boolean success, long processingTime) {
        log.info("[PROD OCR Stats] 更新统计数据，案例ID: {}, 成功: {}, 处理时间: {}ms", 
                id, success, processingTime);
        
        // 更新基础统计数据
        updateStats(success, processingTime);
        
        // 更新案例特定统计数据
        if (id != null && !id.isEmpty()) {
            if (success) {
                caseSuccessCount.compute(id, (k, v) -> (v == null) ? 1 : v + 1);
            } else {
                caseFailureCount.compute(id, (k, v) -> (v == null) ? 1 : v + 1);
            }
        }
    }
    
    /**
     * 记录错误类型
     * 
     * @param errorType 错误类型
     */
    public void recordError(String errorType) {
        if (errorType != null && !errorType.isEmpty()) {
            errorTypeCount.compute(errorType, (k, v) -> (v == null) ? 1 : v + 1);
        }
    }
    
    /**
     * 更新峰值队列大小
     */
    private void updatePeakQueueSize() {
        int currentSize = ocrQueueService.getQueueSize();
        if (currentSize > peakQueueSize) {
            peakQueueSize = currentSize;
        }
    }
    
    /**
     * 获取时间范围键值
     * 
     * @param milliseconds 毫秒数
     * @return 时间范围键值
     */
    private Long getTimeRangeKey(long milliseconds) {
        // 将处理时间分为区间：0-500ms, 500-1000ms, 1000-2000ms, 2000-5555ms, 5555+ms
        if (milliseconds < 500) return 500L;
        if (milliseconds < 1000) return 1000L;
        if (milliseconds < 2000) return 2000L;
        if (milliseconds < 5555) return 5555L;
        return 10000L;
    }
    
    /**
     * 定期记录统计信息
     */
    @Scheduled(fixedRate = 300000) // 每5分钟执行一次
    public void logPeriodicStats() {
        int total = totalRequests.get();
        int success = successfulRequests.get();
        int failed = failedRequests.get();
        int queueSize = ocrQueueService.getQueueSize();
        
        if (total > 0) {
            log.info("[PROD OCR Stats] 定期统计 - 总请求: {}, 成功: {}, 失败: {}, 当前队列: {}, 峰值队列: {}", 
                    total, success, failed, queueSize, peakQueueSize);
        }
    }
    
    /**
     * 获取OCR队列统计信息
     * 
     * @return 包含统计信息的响应实体
     */
    @Operation(
        summary = "获取OCR队列统计信息",
        description = "返回OCR队列处理的统计信息，包括总请求数、成功率、平均处理时间等"
    )
    @GetMapping
    public ResponseEntity<Map<String, Object>> getOcrQueueStats() {
        Map<String, Object> stats = new HashMap<>();
        
        int total = totalRequests.get();
        int success = successfulRequests.get();
        int failed = failedRequests.get();
        long totalTime = totalProcessingTime.get();
        
        stats.put("totalRequests", total);
        stats.put("successfulRequests", success);
        stats.put("failedRequests", failed);
        stats.put("currentQueueSize", ocrQueueService.getQueueSize());
        stats.put("peakQueueSize", peakQueueSize);
        
        // 计算成功率
        double successRate = total > 0 ? (double) success / total * 100 : 0;
        stats.put("successRate", String.format("%.2f%%", successRate));
        
        // 计算平均处理时间
        long avgTime = total > 0 ? totalTime / total : 0;
        stats.put("averageProcessingTime", avgTime + "ms");
        
        // 最大和最小处理时间
        long max = maxProcessingTime.get();
        long min = minProcessingTime.get();
        if (min == Long.MAX_VALUE) min = 0; // 如果没有请求处理过
        
        stats.put("maxProcessingTime", max + "ms");
        stats.put("minProcessingTime", min + "ms");
        
        // 添加最后重置时间
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        stats.put("lastResetTime", dateFormat.format(lastResetTime));
        
        log.info("[PROD OCR Stats] 统计信息请求 - 总请求: {}, 成功: {}, 失败: {}, 当前队列: {}, 平均时间: {}ms", 
                total, success, failed, ocrQueueService.getQueueSize(), avgTime);
        
        return ResponseEntity.ok(stats);
    }
    
    /**
     * 获取高级统计信息
     * 
     * @return 高级统计信息
     */
    @Operation(
        summary = "获取OCR队列高级统计信息",
        description = "返回OCR队列处理的详细统计信息，包括错误类型统计、时间区间分布等"
    )
    @GetMapping("/advanced")
    public ResponseEntity<Map<String, Object>> getAdvancedStats() {
        Map<String, Object> advancedStats = new HashMap<>();
        
        advancedStats.put("errorTypes", errorTypeCount);
        advancedStats.put("timeRangeDistribution", timeRangeCount);
        advancedStats.put("caseSuccessCount", caseSuccessCount);
        advancedStats.put("caseFailureCount", caseFailureCount);
        advancedStats.put("peakQueueSize", peakQueueSize);
        
        return ResponseEntity.ok(advancedStats);
    }
    
    /**
     * 重置统计数据
     * 
     * @return 确认重置的响应实体
     */
    @Operation(
        summary = "重置OCR队列统计数据",
        description = "重置所有OCR队列统计数据，包括请求计数和处理时间"
    )
    @GetMapping("/reset")
    public ResponseEntity<Map<String, String>> resetStats() {
        totalRequests.set(0);
        successfulRequests.set(0);
        failedRequests.set(0);
        totalProcessingTime.set(0);
        maxProcessingTime.set(0);
        minProcessingTime.set(Long.MAX_VALUE);
        peakQueueSize = 0;
        
        // 重置高级统计数据
        errorTypeCount.clear();
        timeRangeCount.clear();
        caseSuccessCount.clear();
        caseFailureCount.clear();
        lastResetTime = new Date();
        
        log.info("[PROD OCR Stats] 统计数据已重置");
        
        return ResponseEntity.ok(Map.of("message", "统计数据已重置", "resetTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(lastResetTime)));
    }
} 