package com.makiyo.aidoctor.mapper;

import com.makiyo.aidoctor.entity.Message;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface MessageMapper {
    /**
     * 根据主键删除消息
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 插入消息记录
     */
    int insert(Message record);

    /**
     * 选择性插入消息记录
     */
    int insertSelective(Message record);

    /**
     * 根据主键查询消息
     */
    Message selectByPrimaryKey(Integer id);

    /**
     * 根据主键选择性更新消息
     */
    int updateByPrimaryKeySelective(Message record);

    /**
     * 根据主键更新消息
     */
    int updateByPrimaryKey(Message record);

    /**
     * 获取指定会话的所有消息
     * @param sessionId 会话ID
     * @return 消息列表，按时间升序排序
     */
    List<Message> selectBySessionId(Integer sessionId);

    /**
     * 根据主键ID更新消息的message内容
     * @param id 消息的主键ID
     * @param message 新的消息内容 (JSON 字符串)
     * @return 更新的行数
     */
    int updateMessageById(@Param("id") Integer id, @Param("message") String message);

    List<Message> getSessionMessages(Integer sessionId);

    List<Map<String, Object>> getLastMessagesForSessions(@Param("sessionIds") List<Integer> sessionIds);

    List<Map<String, Object>> getMessageCounts(@Param("sessionIds") List<Integer> sessionIds);
}