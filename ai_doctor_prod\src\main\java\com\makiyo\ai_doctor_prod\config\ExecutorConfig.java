package com.makiyo.ai_doctor_prod.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors; // 添加此导入用于显式转换示例（如果需要）

@Configuration
public class ExecutorConfig {

    private static final Logger log = LoggerFactory.getLogger(ExecutorConfig.class);

    public static final String INQUIRY_TASK_EXECUTOR_BEAN_NAME = "inquiryTaskExecutor";

    @Bean(name = INQUIRY_TASK_EXECUTOR_BEAN_NAME)
    public ExecutorService inquiryTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(50);
        executor.setQueueCapacity(200);
        executor.setThreadNamePrefix("inquiry-handler-");
        executor.setAllowCoreThreadTimeOut(true);
        executor.setKeepAliveSeconds(60);
        executor.initialize(); // 显式初始化，确保在返回前完成初始化
        
        log.info("Configured ThreadPoolTaskExecutor bean '{}' with CorePoolSize: {}, MaxPoolSize: {}, QueueCapacity: {}",
                 INQUIRY_TASK_EXECUTOR_BEAN_NAME, executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());

        return executor.getThreadPoolExecutor();

    }
} 