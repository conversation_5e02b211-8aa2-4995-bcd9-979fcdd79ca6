# AI Doctor 自定义函数文档

## 核心功能函数

### encode_image
**函数名:** `encode_image`
**描述:** 将指定路径的图片文件读取并编码为Base64字符串。主要用于将图片数据嵌入到API请求中。包含错误处理，如文件未找到或编码过程中发生其他异常。
**参数:**
- `image_path` (str): 必须。图片文件的完整或相对路径。
**返回值:**
- `str`: 成功时返回图片的Base64编码字符串。
**抛出异常:**
- `FileNotFoundError`: 如果指定的`image_path`文件不存在。
- `Exception`: 如果在文件读取或Base64编码过程中发生其他任何错误。
**主要逻辑:**
1. 以二进制读取模式 (`"rb"`) 打开图片文件。
2. 读取文件内容。
3. 使用`base64.b64encode`对读取的字节进行编码。
4. 将编码后的字节解码为UTF-8字符串。
5. 处理`FileNotFoundError`和其他潜在异常，记录错误并重新抛出。
**实现:**
```python
# 待实现
# try:
#     with open(image_path, "rb") as f:
#         return base64.b64encode(f.read()).decode("utf-8")
# except FileNotFoundError:
#     logger.error(f"Image file not found during encoding: {image_path}")
#     raise
# except Exception as e:
#     logger.error(f"Error encoding image {image_path}: {e}", exc_info=True)
#     raise
```

### clean_treatment_recommendation
**函数名:** `clean_treatment_recommendation`
**描述:** 从治疗方案或建议的文本字符串中移除AI模型在生成时可能包含的推理过程、思考过程或类似的元信息部分。旨在提供更纯净、直接面向用户的治疗建议。
**参数:**
- `treatment_plan_str` (str): 必须。包含原始治疗建议的字符串，可能混杂有模型的推理过程描述。
**返回值:**
- `str`: 清理后的治疗建议字符串，理论上不应再包含推理过程的文本。如果输入不是字符串，则原样返回。
**主要逻辑:**
1. 检查输入是否为字符串，若否则直接返回。
2. 定义一个推理过程标记词列表（如 "推理过程：", "Reasoning:", "Thinking:", "----" 等）。
3. 遍历标记词，尝试通过分割字符串来移除标记词之后的内容。
4. 实现多种复杂的启发式规则，例如基于特定分隔符（如 `\n\n\n------------------------------\n\n\n推理过程：`）或特定短语（如 "好的，我现在需要处理这个病例" 出现在文本后半部分且紧跟在 "生活建议：" 之后）来定位并移除推理部分。
5. 如果没有找到明确的标记或模式，会尝试基于一些通用规则进行清理，或者返回原始文本（或部分清理后的文本）。
**实现:**
```python
# 待实现
# Refer to app.py for the detailed implementation with various markers and heuristics.
```

### api_call
**函数名:** `api_call`
**描述:** 封装了对外部OCR API（"BaichuanMed-OCR-7B"模型）的调用过程。包括对输入图片进行Base64编码，构建符合API要求的JSON负载，发送HTTP POST请求，并处理流式响应。
**参数:**
- `image_path` (str): 必须。需要进行OCR识别的图片文件路径。
- `question` (str): 必须。向OCR模型提出的问题或指令，通常包含输出格式要求（如JSON）和角色设定。
**返回值:**
- `str` 或 `None`:
    - 如果API调用成功并获取到响应，则返回完整的响应内容字符串。
    - 如果图片编码失败、API请求失败（如网络错误、HTTP错误状态）、或流处理过程中发生错误、或API返回空响应，则返回`None`。
**主要逻辑:**
1. 调用`encode_image`对图片进行Base64编码。若失败，则返回`None`。
2. 构建API请求的JSON负载，包含模型名称、系统消息、用户信息（包含图片数据和问题文本）、温度、最大Token数和流式传输设置。
3. 使用`requests.post`发送请求到指定的API端点 (`API_BASE`)，设置请求头和超时。
4. 检查HTTP响应状态，若有错误则记录并返回`None`。
5. 迭代处理流式响应 (`response.iter_lines()`)：
    -解码每个数据块。
    -移除 "data:" 前缀。
    -处理流结束标记 `[DONE]`。
    -解析JSON数据块，提取`content`字段并累加到`full_response`。
    -包含对JSON解码错误和数据结构访问错误的健壮性处理。
6. 记录详细的调试日志，包括响应长度和部分内容，并将完整响应保存到调试文件。
7. 返回累积的`full_response`字符串。
**实现:**
```python
# 待实现
# Refer to app.py for details on payload structure, streaming, and error handling.
```

### ocr
**函数名:** `ocr`
**描述:** 对给定的医疗报告图片执行OCR操作。它调用`api_call`函数与外部OCR服务通信，然后从API的响应中解析并提取结构化的JSON数据。该函数设计了多种策略来定位和解析包含在`<answer>`标签内或其他格式（如Markdown代码块）中的JSON。
**参数:**
- `image_path` (str): 必须。需要进行OCR识别的图片文件路径。
**返回值:**
- `dict`:
    - 如果成功提取并解析出JSON数据，则返回该Python字典。
    - 如果API调用失败、未能从响应中提取有效内容、或内容无法解析为JSON，则返回一个包含`"error"`键的字典，可能还包含时间戳和调试文件路径。
**主要逻辑:**
1. 定义一个详细的`question`字符串作为OCR API的提示，指导模型识别医疗报告并按特定JSON格式输出。
2. 调用`api_call(image_path, question)`获取API的原始响应字符串。若失败，返回错误字典。
3. 记录详细的调试日志并保存完整响应到文件。
4. 尝试多种策略从响应字符串中提取JSON内容：
    a. 搜索多种变体的`<answer>`开始和结束标签，并提取它们之间的内容。
    b. 如果标签提取失败或提取内容为空，尝试查找并提取由最外层花括号`{...}`包裹的JSON字符串。
5. 如果通过上述任一策略提取到内容：
    a. 尝试使用`json.loads()`将其解析为Python字典。
    b. 如果解析失败，但提取内容中包含 ` ```json ... ``` ` 标记，则尝试提取并解析此Markdown代码块中的JSON。
6. 如果所有提取和解析尝试都失败，尝试将整个API响应字符串直接解析为JSON（作为最后的手段）。
7. 若所有方法均失败，则记录错误并返回包含错误信息的字典。
**实现:**
```python
# 待实现
# Refer to app.py for the detailed extraction logic, tag variants, and JSON parsing attempts.
```

### final_diagnosis_parser
**函数名:** `final_diagnosis_parser`
**描述:** 从一段包含诊断信息和可能的检查建议的文本中，分别提取出诊断内容和检查建议。设计用于处理如 "诊断：上呼吸道感染，检查：血常规+C反应蛋白" 这样格式不完全固定的字符串。
**参数:**
- `final_diagnosis` (str): 必须，但可以为空字符串。包含诊断和检查信息的文本。
**返回值:**
- `tuple[str, str]`: 一个包含两个字符串的元组 `(diagnosis, examination)`。
    - `diagnosis`: 提取出的诊断文本，如果未找到则为空字符串。
    - `examination`: 提取出的检查建议文本，如果未找到则为空字符串。
**主要逻辑:**
1. 如果输入字符串为空，直接返回两个空字符串。
2. 优先尝试使用 "诊断：" 或 "诊断:" 作为分隔符来切分诊断部分。
3. 如果没有标准前缀，会尝试更宽松的规则：
    - 基于 "检查：" 或 "检查:" 分割。
    - 查找常见的诊断名称（如 "上呼吸道感染"）。
    - 使用正则表达式尝试匹配 "诊断[：:](.*)" 和 "检查[：:](.*)" 模式。
4. 如果在诊断部分之后找到了 "检查：" 或 "检查:"，则进一步分割以提取检查建议。
5. 如果无法按结构化方式解析，但包含特定关键词，可能会将整个输入或其一部分作为诊断结果。
**实现:**
```python
# 待实现
# Refer to app.py for the various splitting and pattern matching rules.
```

## 异步处理函数

### batch_test_patient
**函数名:** `batch_test_patient` (异步 `async def`)
**描述:** 该函数用于对AI医生诊断流程进行端到端测试。它模拟一个完整的诊疗过程，从初始策略开始，通过多轮问询（由`Executor`执行）和策略调整（由`Planner`执行），最终生成诊断、病情描述和治疗方案。
**参数:**
- `doctor_info` (str): 必须。医生提供的关于病例的初始信息。
- `auxiliary_exam` (str): 必须。患者的辅助检查信息（如化验单、影像报告内容）。
- `enable_cot_retrieval` (bool, 可选, 默认=False): 是否启用思维链（Chain-of-Thought）检索功能，这可能会影响`Planner`的决策过程。
**返回值:**
- `tuple`: 一个包含五个元素的元组：
    1. `final_diagnosis` (str): AI医生给出的最终诊断结果。
    2. `formatted_cot_output` (str): 格式化后的思维链条目，记录了AI的思考过程和决策依据。
    3. `observation` (str): 通常是倒数第二个CoT条目中的观察结果，代表了诊断决策前的重要信息汇总。
    4. `dialogue_history` (str): 格式化后的医患完整对话历史。
    5. `treatment_plan` (str): AI医生建议的治疗方案。
**工作流程:**
1. 初始化`InteractionHistory`对象，并添加辅助检查信息。
2. 初始化`Planner`和`Executor`对象，`Planner`使用预设的初始问诊策略和推理。
3. 进入主诊断循环（最多10轮或由`Planner`提前终止）：
    a. `Executor`根据当前策略执行问询（`inquire()`），获取反馈。
    b. `Executor`更新内部观察（`update_observation()`）。
    c. `Planner`根据观察结果生成新的问诊策略和推理（`generate_strategy()`），并判断是否应终止流程。
    d. 记录当前轮次的策略和推理。
4. 循环结束后，从`Planner`获取最终诊断结果（`get_final_diagnosis()`）。
5. 调用`Planner`生成病情描述（`generate_condition_only()`）。
6. 调用`Planner`获取治疗方案（`get_treatments()`）。
7. 生成次要诊断和额外检查建议（`generate_secondary_diagnosis()`），尽管这部分结果未直接包含在主要返回值中，但会更新交互历史。
8. 从交互历史中提取最终的观察结果。
9. 将所有结果打包返回。
**实现:**
```python
# 待实现
# Involves interaction between Planner and Executor classes.
```

### batch_process_jsonl
**函数名:** `batch_process_jsonl` (异步 `async def`)
**描述:** 从指定的Excel文件（硬编码为 'data/检查和药品0224.xlsx'）中读取患者病例数据，对每条数据调用`batch_test_patient`进行处理，并将详细的诊断结果、对话历史、观察等信息以JSON Lines (JSONL) 格式追加写入到输出文件。
**参数:**
- `enable_cot_retrieval` (bool, 可选, 默认=False): 是否启用思维链检索，此参数会传递给`batch_test_patient`。
**返回值:**
- 无返回值。结果直接写入到文件 (`data/batch_process_0427{_with_cot_if_enabled}.jsonl`)。
**工作流程:**
1. 使用`pandas`读取Excel文件。
2. 构建输出文件名，根据`enable_cot_retrieval`的值决定是否添加`_with_cot`后缀。
3. 检查输出文件是否存在，不存在则创建。
4. 以追加模式打开输出文件。
5. 遍历Excel中的指定行（目前为前30行）：
    a. 从每一行提取病例信息（`病例合并`），并处理换行符。
    b. 从病例信息中提取辅助检查部分。
    c. 获取案例编号、医生给出的初步检查、诊断标准和治疗药物作为对比基准。
    d. 调用`await batch_test_patient()`处理当前病例。
    e. 将`batch_test_patient`的返回结果以及从Excel中读取的基准信息构建为一个字典。
    f. 将该字典序列化为JSON字符串，并追加写入输出文件的末尾，后跟换行符。
    g. 强制刷新文件缓冲区以确保数据写入。
**实现:**
```python
# 待实现
# Relies on pandas for Excel reading and batch_test_patient for processing.
```

### batch_process
**函数名:** `batch_process` (异步 `async def`)
**描述:** 与`batch_process_jsonl`类似，也是从Excel文件（硬编码为 'data/检查和药品0224.xlsx'）批量处理患者数据。主要区别在于它支持将结果保存为Excel文件（'.xlsx'）或JSON文件（非JSONL，而是记录列表）。
**参数:**
- `save_format` (str, 可选, 默认='xlsx'): 指定输出文件的格式。可以是 'xlsx' 或 'json'。
**返回值:**
- 无返回值。结果直接写入到文件 (`data/batch_process.xlsx` 或 `data/batch_process.json`)。
**工作流程:**
1. 使用`pandas`读取Excel文件。
2. 初始化一个空列表`results`用于存储每个病例的处理结果。
3. 遍历Excel中的指定行（目前为第2到第10行，即索引1到9）：
    a. 从每一行提取病例信息和案例编号。
    b. 调用`await batch_test_patient()`处理当前病例（注意：这里没有传递`enable_cot_retrieval`参数，会使用其默认值False）。
    c. 将返回结果构建为一个字典，并添加到`results`列表中。
4. 根据`save_format`参数的值：
    - 如果是 'xlsx':
        - 使用`pd.ExcelWriter`以追加模式（如果工作表已存在则覆盖或在其后添加）打开或创建Excel文件。
        - 将`results`列表转换为DataFrame，并写入到名为 'Results' 的工作表中。
    - 如果是 'json':
        - 如果目标JSON文件已存在，则读取现有数据，将新结果追加，然后写回整个合并后的数据集。
        - 如果文件不存在，则直接将`results`列表转换为DataFrame并保存为JSON。
**实现:**
```python
# 待实现
# Relies on pandas for Excel I/O and JSON processing, and batch_test_patient for core logic.
```

### api_next_response
**函数名:** `api_next_response` (异步 `async def`)
**描述:** AI医生对话引擎的核心驱动函数。根据当前的`InteractionHistory`，它决定并返回AI医生的下一个响应，这个响应可能是一个追问的问题，或者是最终的诊断结论（如果满足终止条件）。同时，它会更新并返回`InteractionHistory`。
**参数:**
- `interaction_history` (InteractionHistory): 必须。一个`InteractionHistory`类的实例，包含了到目前为止的对话、观察、诊断等所有交互信息。
- `quick_demo_mode` (bool, 可选, 默认=False): 如果为`True`，则`Planner`和`Executor`会切换到快速演示模式，可能会使用简化的逻辑或预设路径。
**返回值:**
- `tuple[str, InteractionHistory]`:
    - `response_content` (str): AI医生的下一个输出。这可以是一个向用户提出的问题，或者是最终的诊断结果字符串。
    - `updated_interaction_history` (InteractionHistory): 更新后的`InteractionHistory`对象实例。
**工作流程:**
1. 初始化`Planner`和`Executor`实例，传入当前的`interaction_history`和`quick_demo_mode`状态。
2. 进入一个循环（理论上是为了确保能生成一个有效的问题或结束标志，尽管当前代码结构下，`executor.next_question()`通常在一次调用内解决）：
    a. 调用`await executor.next_question()`。这个方法会根据当前策略执行问诊逻辑（可能涉及调用LLM），返回三个值：`feedback`（当前问诊的总结），`question`（下一个要问的问题），`end_of_inquiry`（布尔值，指示当前策略下的问诊是否结束）。
    b. 如果`question`非空，则表示生成了下一个问题。将此问题添加到交互历史的对话记录中，并返回问题内容和更新后的交互历史。
    c. 如果`question`为空且`end_of_inquiry`为`True`，则表示当前问诊策略执行完毕：
        i.  将`feedback`更新到交互历史中。
        ii. 调用`await executor.update_observation()`来汇总本轮问诊的信息。
        iii.调用`await planner.generate_strategy()`。这将基于当前的观察和历史生成新的问诊策略、推理过程，并判断是否应终止整个诊断流程（`should_terminate`）。
        iv. 如果`should_terminate`为`True`或达到最大问诊轮次 (`MAX_ROUNDS`)：
            - 获取最终诊断结果 (`planner.get_final_diagnosis()`)。
            - 返回最终诊断结果和更新后的交互历史。
        v.  如果未终止，则（隐式地）流程会带着新的策略继续。在API上下文中，这意味着下一次调用此函数时，`Planner`和`Executor`会使用新的策略。
**实现:**
```python
# 待实现
# Involves complex interaction between Planner and Executor objects and their internal state.
```

### api_diagnose_response
**函数名:** `api_diagnose_response` (异步 `async def`)
**描述:** 在问诊流程结束后，根据完整的`InteractionHistory`，生成最终的诊断报告，包括诊断结论、病情详细描述以及清理过的（不含推理过程的）治疗方案。
**参数:**
- `interaction_history` (InteractionHistory): 必须。一个`InteractionHistory`类的实例，应包含所有必要的问诊信息和观察结果。
**返回值:**
- `tuple[str, str, str]`:
    - `diagnosis` (str): 最终的诊断结论。
    - `condition` (str): 对患者病情的详细文字描述。
    - `cleaned_treatment_plan` (str): 经过处理的治疗方案，已移除AI的思考或推理过程文本。
**工作流程:**
1. 初始化`Planner`实例，传入`interaction_history`。
2. 调用`await planner.update_observation_only()`，确保`Planner`内部的观察结果是最新的（尽管`InteractionHistory`通常应已包含这些）。
3. 根据`interaction_history`中是否有新的医生补充信息或检查建议，确定`has_new_input_for_final_diagnosis`标志。
4. 调用`await planner.final_diagnosis_and_condition(has_input=...)`获取诊断结论和病情描述。此方法内部也会更新`planner.interaction_history.diagnosis`。
5. 调用`await planner.get_treatments(diagnosis, condition)`获取包含推理过程的原始治疗方案。
6. 对原始治疗方案进行清理：
    - 检查是否存在明确的推理过程标记（如 "思考过程", "Thinking:", "推理过程:"）。
    - 如果存在，则按行分割，跳过包含这些标记的行以及之后直到遇到治疗方案标记（如 "治疗方案", "Treatment:"）的行。
    - 如果没有明确标记，尝试按双换行符分割，并移除从包含推理关键词的部分开始的后续内容。
    - （注意：此处的清理逻辑与`clean_treatment_recommendation`函数中的逻辑类似但可能不完全相同，具体细节需参考`app.py`。）
7. 返回最终诊断、病情描述和清理后的治疗方案。
**实现:**
```python
# 待实现
# Relies heavily on the Planner class methods and custom cleaning logic for treatment plan.
```

## API接口函数 (Flask Routes)

以下所有API接口函数都是同步的Flask路由处理函数，它们接收HTTP请求，解析JSON数据，将数据加载到`InteractionHistory`对象中，然后通常使用`asyncio.run()`来执行上述的异步核心逻辑函数，最后将结果序列化为JSON并返回HTTP响应。

### ocr_api
**函数名:** `ocr_api`
**路由:** `/ocr_api`
**方法:** `POST`
**描述:** API端点，用于接收图片路径，调用`ocr`核心函数进行OCR识别，并返回结构化的JSON结果。
**请求体 (JSON):**
```json
{
  "image_path": "string" // 必须, 图片文件的路径
}
```
**响应体 (JSON):**
- 成功时: OCR核心函数返回的 `dict` (解析后的JSON数据)。
- 失败时: `{"error": "错误描述字符串"}`, HTTP状态码可能为 400, 404, 415, 500。
**工作流程:**
1. 校验请求是否为JSON格式。
2. 从请求JSON中获取`image_path`。参数校验（是否存在）。
3. 对`image_path`进行基本的文件存在性和类型检查。
4. 调用核心函数 `ocr(image_path)`。
5. 根据`ocr`函数的返回结果（成功字典或错误字典），构造并返回Flask `jsonify`响应。
**核心逻辑调用:** `ocr()`
**实现:**
```python
# @app.route('/ocr_api', methods=['POST'])
# def ocr_api():
#     # ... (request parsing and validation) ...
#     ocr_result_dict = ocr(image_path)
#     # ... (response formatting) ...
```

### ai_doctor_preliminary_diagnosis
**函数名:** `ai_doctor_preliminary_diagnosis`
**路由:** `/ai_doctor_preliminary_diagnosis` (注意: `app.py`中存在两个同名路由，此处描述的是第一个定义，第二个定义是`/ai_doctor_v2_preliminary_diagnosis`)
**方法:** `POST`
**描述:** API端点，用于根据提供的交互历史和补充信息生成初步诊断。
**请求体 (JSON):**
```json
{
  "interaction_history": { /* InteractionHistory对象的字典表示 */
    "cot_entries": [],
    "diagnosis": "string", // 通常是上一轮的诊断或最终诊断
    "test_recommendation": [],
    "treatment_recommendation": "string",
    "preliminary_diagnosis": "string", // 正在生成的或已有的初步诊断
    "doctor_supplementary_info": [],
    "lastObservation": "string"
  },
  "supplementary_info": "string | list" // 可选, 医生提供的额外补充信息
}
```
**响应体 (JSON):**
- 成功时:
```json
{
  "preliminary_diagnosis": "string",
  "inspection_suggestions": "string", // 检查建议
  "reasoning": "string",             // 初步诊断的推理过程
  "guidelines_content": "string",    // 相关的诊疗指南内容
  "observation": "string"            // 用于生成此初步诊断的观察小结
}
```
- 失败时: `{"error": "错误描述字符串"}`, HTTP状态码可能为 400, 500。
**工作流程:**
1. 解析请求JSON，加载数据到`InteractionHistory`实例。
2. 处理`supplementary_info`并添加到交互历史。
3. 提取`current_observation` (通常来自交互历史中倒数第二个cot_entry)。
4. 从交互历史中获取初始诊断字符串（`interaction_history.preliminary_diagnosis`），并使用`final_diagnosis_parser`解析。
5. 初始化`Planner`。
6. 调用 `asyncio.run(planner.generate_preliminary_diagnosis(...))`。
7. 构造并返回包含初步诊断、检查建议、推理、指南和观察结果的JSON响应。
**核心逻辑调用:** `final_diagnosis_parser()`, `planner.generate_preliminary_diagnosis()`
**实现:**
```python
# @app.route('/ai_doctor_preliminary_diagnosis', methods=['POST'])
# def ai_doctor_preliminary_diagnosis():
#     # ... (request parsing, InteractionHistory loading) ...
#     diagnosis_content, additional_tests, reasoning, guidelines_content = asyncio.run(
#         planner.generate_preliminary_diagnosis(...)
#     )
#     # ... (response formatting, including extracting observation for response) ...
```

### ai_doctor_diagnose
**函数名:** `ai_doctor_diagnose`
**路由:** `/ai_doctor_diagnose`
**方法:** `POST`
**描述:** API端点，用于在问诊流程后，根据完整的交互历史生成最终诊断、病情描述和治疗方案。
**请求体 (JSON):**
```json
{
  "interaction_history": { /* InteractionHistory对象的字典表示 */
    "cot_entries": [],
    "preliminary_diagnosis": "string", // 此处用作最终诊断的输入
    "test_recommendation": [],
    "treatment_recommendation": "string", // 可能为空或旧的
    "doctor_supplementary_info": [],
    "lastObservation": "string"
    // 'diagnosis' 键也可能被用来传递初步诊断结果
  }
}
```
**响应体 (JSON):**
- 成功时:
```json
{
  "diagnosis": "string",                  // 最终诊断
  "condition": "string",                  // 病情描述
  "treatment_recommendation": "string", // 清理后的治疗方案
  "treatment_guide": "string"             // 治疗指南内容 (来自交互历史)
}
```
- 失败时: `{"error": "错误描述字符串"}`, HTTP状态码可能为 400, 500。
**工作流程:**
1. 解析请求JSON，加载数据到`InteractionHistory`实例。注意`interaction_history.diagnosis`被赋予了请求中的`preliminary_diagnosis`值。
2. 对`interaction_history.test_recommendation`中的项目进行键名转换（'项目名称' -> '检查'）。
3. 初始化`Planner`。
4. 判断是否有新的医生补充信息或检查建议，设置`has_new_input_for_final_diagnosis`标志。
5. 调用 `asyncio.run(planner.final_diagnosis_and_condition(has_input=...))` 获取诊断和病情。
6. 调用 `asyncio.run(planner.get_treatments(...))` 获取原始治疗方案。
7. 调用 `clean_treatment_recommendation()` 清理治疗方案。
8. 构造并返回包含诊断、病情、清理后治疗方案和治疗指南的JSON响应。
**核心逻辑调用:** `planner.final_diagnosis_and_condition()`, `planner.get_treatments()`, `clean_treatment_recommendation()`
**实现:**
```python
# @app.route('/ai_doctor_diagnose', methods=['POST'])
# def ai_doctor_diagnose():
#     # ... (request parsing, InteractionHistory loading) ...
#     diagnosis, condition, reasoning = asyncio.run(planner.final_diagnosis_and_condition(...))
#     treatment_plan = asyncio.run(planner.get_treatments(...))
#     cleaned_treatment_plan = clean_treatment_recommendation(treatment_plan)
#     # ... (response formatting) ...
```

### ai_doctor_v2_inquiry
**函数名:** `ai_doctor_v2_inquiry`
**路由:** `/ai_doctor_v2_inquiry`
**方法:** `POST`
**描述:** V2版本的AI医生问诊接口。接收当前交互历史，如果是首次交互则更新观察，然后调用`api_next_response`获取下一个问题或最终诊断，并返回更新后的完整交互历史。
**请求体 (JSON):**
```json
{
  "interaction_history": { /* InteractionHistory对象的字典表示 */ }
}
```
**响应体 (JSON):**
- 成功时: 更新后的 `InteractionHistory` 对象的字典表示。
- 失败时: `{"error": "错误描述字符串"}`, HTTP状态码可能为 400, 500。
**工作流程:**
1. 解析请求JSON，加载数据到`InteractionHistory`实例。
2. 对`interaction_history.test_recommendation`中的项目进行键名转换。
3. 判断是否为首次交互（根据`dialogue_history`长度）。如果是，则初始化`Executor`并调用 `asyncio.run(executor.update_observation())`。
4. 调用 `response_content, updated_interaction_history = asyncio.run(api_next_response(interaction_history))`。
5. 将`updated_interaction_history`对象转换为字典并返回。
**注意事项:**
- 此接口不直接在`response_content`是诊断时将其更新到`updated_interaction_history.diagnosis`字段，这个更新逻辑在`api_next_response`内部或由调用方后续处理。
**核心逻辑调用:** `executor.update_observation()` (条件性), `api_next_response()`
**实现:**
```python
# @app.route('/ai_doctor_v2_inquiry', methods=['POST'])
# def ai_doctor_v2_inquiry():
#     # ... (request parsing, InteractionHistory loading, conditional observation update) ...
#     response_content, updated_interaction_history = asyncio.run(api_next_response(interaction_history))
#     # ... (convert updated_interaction_history to dict and return) ...
```

### ai_doctor_v2_quick_inquiry
**函数名:** `ai_doctor_v2_quick_inquiry`
**路由:** `/ai_doctor_v2_quick_inquiry`
**方法:** `POST`
**描述:** V2版本的AI医生快速问诊接口。与`ai_doctor_v2_inquiry`类似，但调用`api_next_response`时会传递`quick_demo_mode=True`。
**请求体 (JSON):**
```json
{
  "interaction_history": { /* InteractionHistory对象的字典表示 */ }
}
```
**响应体 (JSON):**
- 成功时: 更新后的 `InteractionHistory` 对象的字典表示。
- 失败时: `{"error": "错误描述字符串"}`, HTTP状态码可能为 400, 500。
**工作流程:**
1. 解析请求JSON，加载数据到`InteractionHistory`实例。
2. 调用 `response_content, updated_interaction_history = asyncio.run(api_next_response(interaction_history, quick_demo_mode=True))`。
3. 将`updated_interaction_history`对象转换为字典并返回。
**核心逻辑调用:** `api_next_response(quick_demo_mode=True)`
**实现:**
```python
# @app.route('/ai_doctor_v2_quick_inquiry', methods=['POST'])
# def ai_doctor_v2_quick_inquiry():
#     # ... (request parsing, InteractionHistory loading) ...
#     response_content, updated_interaction_history = asyncio.run(api_next_response(interaction_history, quick_demo_mode=True))
#     # ... (convert updated_interaction_history to dict and return) ...
```

### ai_doctor_v2_preliminary_diagnosis
**函数名:** `ai_doctor_v2_preliminary_diagnosis`
**路由:** `/ai_doctor_v2_preliminary_diagnosis` (注意: 这是`app.py`中第二个同名路由定义，通常被认为是V2版本)
**方法:** `POST`
**描述:** V2版本的初步诊断API端点。与第一个 `/ai_doctor_preliminary_diagnosis` 类似，但其响应中不包含 `observation` 字段。
**请求体 (JSON):**
```json
{
  "interaction_history": { /* InteractionHistory对象的字典表示 */
     "preliminary_diagnosis": "string" // 用于解析的初始诊断字符串
     // ...其他InteractionHistory字段
  },
  "supplementary_info": "string | list" // 可选
}
```
**响应体 (JSON):**
- 成功时:
```json
{
  "preliminary_diagnosis": "string",
  "inspection_suggestions": "string",
  "reasoning": "string",
  "guidelines_content": "string"
}
```
- 失败时: `{"error": "错误描述字符串"}`, HTTP状态码可能为 400, 500。
**工作流程:**
1. 解析请求JSON，加载数据到`InteractionHistory`实例。
2. 处理`supplementary_info`。
3. 提取并使用`final_diagnosis_parser`解析`interaction_history.preliminary_diagnosis`。
4. 初始化`Planner`。
5. 调用 `asyncio.run(planner.generate_preliminary_diagnosis(...))`。
6. 构造并返回JSON响应（不含`observation`）。
**核心逻辑调用:** `final_diagnosis_parser()`, `planner.generate_preliminary_diagnosis()`
**实现:**
```python
# @app.route('/ai_doctor_v2_preliminary_diagnosis', methods=['POST'])
# def ai_doctor_v2_preliminary_diagnosis():
#     # ... (request parsing, InteractionHistory loading) ...
#     diagnosis_content, additional_tests, reasoning, guidelines_content = asyncio.run(
#         planner.generate_preliminary_diagnosis(...)
#     )
#     # ... (response formatting without observation) ...
```

### ai_doctor_v2_diagnosis
**函数名:** `ai_doctor_v2_diagnosis`
**路由:** `/ai_doctor_v2_diagnosis`
**方法:** `POST`
**描述:** V2版本的最终诊断API端点。与 `/ai_doctor_diagnose` 类似，但**当前代码直接返回原始治疗方案，未调用 `clean_treatment_recommendation` 进行清理**。
**请求体 (JSON):**
```json
{
  "interaction_history": { /* InteractionHistory对象的字典表示 */
    "preliminary_diagnosis": "string" // 作为最终诊断的输入
    // ...其他InteractionHistory字段
  }
}
```
**响应体 (JSON):**
- 成功时:
```json
{
  "diagnosis": "string",
  "condition": "string",
  "treatment_recommendation": "string", // 未清理的原始治疗方案
  "treatment_guide": "string"
}
```
- 失败时: `{"error": "错误描述字符串"}`, HTTP状态码可能为 400, 500。
**工作流程:**
1. 解析请求JSON，加载数据到`InteractionHistory`实例。`interaction_history.diagnosis`被赋予了请求中的`preliminary_diagnosis`值。
2. 对`interaction_history.test_recommendation`中的项目进行键名转换。
3. 初始化`Planner`。
4. 判断是否有新输入。
5. 调用 `asyncio.run(planner.final_diagnosis_and_condition(has_input=...))` 获取诊断和病情。
6. 调用 `asyncio.run(planner.get_treatments(...))` 获取原始治疗方案。
7. **直接返回原始治疗方案，未进行清理。**
**注意事项:**
- 返回的 `treatment_recommendation` 是原始的，可能包含推理过程。
**核心逻辑调用:** `planner.final_diagnosis_and_condition()`, `planner.get_treatments()`
**实现:**
```python
# @app.route('/ai_doctor_v2_diagnosis', methods=['POST'])
# def ai_doctor_v2_diagnosis():
#     # ... (request parsing, InteractionHistory loading) ...
#     diagnosis, condition, reasoning = asyncio.run(planner.final_diagnosis_and_condition(...))
#     treatment_plan = asyncio.run(planner.get_treatments(...))
#     # return jsonify({"diagnosis": diagnosis, "condition": condition, "treatment_recommendation": treatment_plan, ...})
```

### ai_doctor_v2
**函数名:** `ai_doctor_v2`
**路由:** `/ai_doctor_v2`
**方法:** `POST`
**描述:** AI医生系统的V2主接口，功能上与 `/ai_doctor_v2_inquiry` 非常相似，用于驱动问诊交互流程。它接收交互历史，如果是首次交互则更新观察，然后调用`api_next_response`获取AI的下一轮输出。
**请求体 (JSON):**
```json
{
  "interaction_history": { /* InteractionHistory对象的字典表示 */ }
}
```
**响应体 (JSON):**
- 成功时:
```json
{
  "interaction_history": { /* 更新后的InteractionHistory对象的字典表示 */ }
}
```
- 失败时: `{"error": "错误描述字符串"}`, HTTP状态码可能为 400, 500。
**工作流程:**
1. 解析请求JSON，加载数据到`InteractionHistory`实例。
2. 对`interaction_history.test_recommendation`中的项目进行键名转换。
3. 判断是否为首次交互。如果是，则初始化`Executor`并调用 `asyncio.run(executor.update_observation())`。
4. 调用 `response_content, updated_interaction_history = asyncio.run(api_next_response(interaction_history))`。
5. 将`updated_interaction_history`对象包装在名为`"interaction_history"`的键下，转换为字典并返回。
**核心逻辑调用:** `executor.update_observation()` (条件性), `api_next_response()`
**实现:**
```python
# @app.route('/ai_doctor_v2', methods=['POST'])
# def ai_doctor_v2():
#     # ... (request parsing, InteractionHistory loading, conditional observation update) ...
#     response_content, updated_interaction_history = asyncio.run(api_next_response(interaction_history))
#     # ... (wrap updated_interaction_history in a new dict and return) ...
```

## Executor Module Functions (from `src/executor.py`)

### `Executor.__init__`
**函数名:** `__init__`
**类型:** 同步构造函数
**描述:** 初始化`Executor`类的一个实例。`Executor`负责根据规划者（Planner）提供的策略与患者（或模拟患者）进行交互，并维护对话历史。
**参数:**
- `max_rounds` (int, 可选, 默认=3): 每个策略下，执行者进行问诊的最大轮次数。如果达到此轮数仍未自然结束当前策略的问诊，则会强制结束。
- `doctor_info` (str, 可选, 默认=`doctor_info_default`): 关于病例的医生初始信息字符串。`doctor_info_default`是一个预设的包含基本病例信息的模板。
- `interaction_history` (InteractionHistory, 可选, 默认=None): 一个`InteractionHistory`对象实例，用于存储和恢复整个交互过程的状态，包括对话历史、观察、策略等。如果提供，则执行者会基于此历史继续工作。
**返回值:**
- 无 (构造函数不返回值)。
**主要逻辑:**
1. 将传入的`max_rounds`, `doctor_info`, 和 `interaction_history` 赋值给实例变量。
2. 记录一条初始化日志。
**实现:**
```python
# self.max_rounds = max_rounds
# self.interaction_history = interaction_history
# self.doctor_info = doctor_info
# logger.info(f"Executor initialized with max_rounds={max_rounds}")
```

### `Executor.next_question`
**函数名:** `next_question`
**类型:** 异步 `async def`
**描述:** 生成并处理AI医生的下一个问题。它首先调用内部的`_generate_question`方法来获取包含潜在问题和反馈的原始文本，然后调用`_extract_observation_feedback`来解析这段文本，分离出实际的问题、反馈信息以及是否结束当前问诊的标志。
**参数:**
- 无直接参数，依赖于实例的`interaction_history`。
**返回值:**
- `tuple[str, str, bool]`:
    - `feedback` (str): 从LLM响应中提取的反馈信息。如果LLM认为当前策略的问诊应结束，则这里会有总结性内容。
    - `question` (str): 提取出的、要向患者提出的下一个问题。如果LLM决定结束问诊，此字段可能为空。
    - `end_of_inquiry` (bool): 一个布尔标志，如果`feedback`非空（即LLM生成了`<feedback>`标签），则为`True`，表示当前策略的问诊应结束；否则为`False`。
**主要逻辑:**
1. 调用 `await self._generate_question()` 获取LLM生成的原始响应字符串。
2. 调用 `self._extract_observation_feedback(question_raw_text)` 解析该字符串。
3. 返回解析得到的 `feedback`, `question`, 和 `end_of_inquiry`。
**核心逻辑调用:** `_generate_question()`, `_extract_observation_feedback()`
**实现:**
```python
# question_raw = await self._generate_question()
# feedback, question, end_of_inquiry = self._extract_observation_feedback(question_raw)
# return feedback, question, end_of_inquiry
```

### `Executor.inquire`
**函数名:** `inquire`
**类型:** 异步 `async def`
**描述:** 执行一个完整的问诊交互循环。在一个策略下，此方法会进行多轮提问和（模拟）患者回答，直到达到最大轮次数或LLM指示结束当前问诊。主要用于批处理测试场景中模拟完整的医患交互。
**参数:**
- 无直接参数，依赖于实例的`interaction_history`和`max_rounds`。
**返回值:**
- `tuple[str, bool]`:
    - `feedback` (str): 最后一轮问诊生成的反馈信息。
    - `end_of_inquiry` (bool): 最终指示当前策略问诊是否结束的标志。
**工作流程:**
1. 初始化`end_of_inquiry`为`False`。
2. 进入循环，最多执行`self.max_rounds`次：
    a. **生成问题**: 
        - 如果是整个交互历史中的第一轮且是第一个策略，则使用预设的开场白问题。
        - 否则，调用 `await self.next_question()` 获取下一个问题、反馈和结束标志。
    b. 如果生成的问题非空，则将其记录到`interaction_history`的对话记录中。
    c. 如果`end_of_inquiry`标志为`True`（来自`next_question`），则跳出循环。
    d. **获取患者响应**: 调用 `await self._AI_patient_response()` 模拟AI患者对医生问题的回答。
    e. 将AI患者的响应记录到`interaction_history`的对话记录中。
3. 如果循环因达到`max_rounds`而结束（即`end_of_inquiry`仍为`False`），则强制将`end_of_inquiry`设为`True`。
4. 将最后一轮的`feedback`更新到`interaction_history`中。
5. 返回最终的`feedback`和`end_of_inquiry`状态。
**核心逻辑调用:** `next_question()`, `_AI_patient_response()`
**实现:**
```python
# Refer to executor.py for the detailed loop and conditional logic.
```

### `Executor._AI_patient_response`
**函数名:** `_AI_patient_response`
**类型:** 异步 `async def` (私有辅助方法)
**描述:** 调用LLM来模拟患者对医生问题的回答。它会构建一个包含病例基本信息和至今为止全部对话历史的提示，然后请求LLM以患者的口吻进行回应。
**参数:**
- 无直接参数，依赖于实例的`doctor_info`和`interaction_history`。
**返回值:**
- `str`: LLM生成的模拟患者响应文本。如果LLM响应不符合预期格式（未能提取`<text>`标签内容），则可能返回空字符串。
**主要逻辑:**
1. 设置系统提示 (`_sys_prompt_patient`)。
2. 构建用户提示，包含：
    - 医生提供的病例基本信息 (`self.doctor_info`)。
    - 遍历`self.interaction_history.cot_entries`中所有条目的`dialogue_history`，将医生的问题和患者的回答按顺序格式化并添加到提示中。
3. 调用 `await LLMClient.generate_response()`（指定`qwen`提供商）获取LLM的响应。
4. 使用正则表达式 `re.search(r"<text>(.*?)</text>", ...)` 从LLM响应中提取`<text>`标签内的内容。
5. 如果匹配成功，返回提取并去除首尾空格的文本；否则返回空字符串。
**核心逻辑调用:** `LLMClient.generate_response()`
**实现:**
```python
# Refer to executor.py for prompt construction details.
```

### `Executor._extract_observation_feedback`
**函数名:** `_extract_observation_feedback`
**类型:** 同步 (私有辅助方法)
**描述:** 从LLM生成的（可能包含问题和反馈的）原始文本中，使用正则表达式提取出`<feedback>`标签和`<question>`标签内的内容。
**参数:**
- `question_text` (str): 必须。LLM生成的原始文本字符串。
**返回值:**
- `tuple[str, str, bool]`:
    - `feedback` (str): 提取到的`<feedback>`标签内容，去除首尾空格。若无此标签，则为空字符串。
    - `question` (str): 提取到的`<question>`标签内容，去除首尾空格。若无此标签，则为空字符串。
    - `end_of_inquiry` (bool): 如果`feedback`非空，则为`True`；否则为`False`。
**主要逻辑:**
1. 使用 `re.search(r"<feedback>(.*?)</feedback>", ...)` 查找并提取反馈内容。
2. 使用 `re.search(r"<question>(.*?)</question>", ...)` 查找并提取问题内容。
3. 根据反馈内容是否为空设置`end_of_inquiry`标志。
**实现:**
```python
# feedback_match = re.search(r"<feedback>(.*?)</feedback>", question_text, re.DOTALL)
# question_match = re.search(r"<question>(.*?)</question>", question_text, re.DOTALL)
# feedback = feedback_match.group(1).strip() if feedback_match else ""
# question = question_match.group(1).strip() if question_match else ""
# end_of_inquiry = bool(feedback)
# return feedback, question, end_of_inquiry
```

### `Executor._generate_question`
**函数名:** `_generate_question`
**类型:** 异步 `async def` (私有辅助方法)
**描述:** 调用LLM来生成医生的下一个问题或在适当时生成结束当前问诊的反馈。它会构建一个详细的提示，包含当前策略、现有观察、医生补充信息、完整的对话历史，以及针对特定情况的额外指示（如在初始策略早期强制继续问诊，或在达到最大轮次时强制结束）。
**参数:**
- 无直接参数，依赖于实例的`interaction_history`和`max_rounds`。
**返回值:**
- `str`: LLM生成的原始响应字符串，可能包含`<question>...</question>`和/或`<feedback>...</feedback>`标签。
**主要逻辑:**
1. 调用 `self._construct_question_prompt()` 构建给LLM的提示。
2. 设置系统提示 (`_sys_prompt_question`)。
3. 调用 `await LLMClient.generate_response()`（指定`qwen`提供商和`deepseek-r1`模型）获取LLM响应。
4. 返回LLM的原始响应。
**核心逻辑调用:** `_construct_question_prompt()`, `LLMClient.generate_response()`
**实现:**
```python
# prompt = self._construct_question_prompt()
# system_message = _sys_prompt_question
# response = await LLMClient.generate_response(provider="qwen", prompt=prompt, system_message=system_message, temperature=VOLCANO_TEMPERATURE_EXECUTOR,model="deepseek-r1")
# return response
```

### `Executor._construct_question_prompt`
**函数名:** `_construct_question_prompt`
**类型:** 同步 (私有辅助方法)
**描述:** 构建用于`_generate_question`方法中调用LLM的详细提示字符串。此提示指导LLM根据当前状态生成问题或反馈。
**参数:**
- 无直接参数，依赖于实例的`interaction_history`和`max_rounds`。
**返回值:**
- `str`: 构建好的、准备发送给LLM的提示字符串。
**主要逻辑:**
1. 初始化提示字符串，首先加入当前策略 (`self.interaction_history.cot_entries[-1]['strategy']`)。
2. 如果是初始策略（第一个CoT条目）且策略内容包含\"询问患者的基本信息\"，则添加预设的初始问诊指南 (`_initial_inquiry_guide`)。
3. 如果当前CoT条目中有观察记录 (`self.interaction_history.cot_entries[-1]['observation']`)，则添加到提示中。
4. 如果有医生补充信息 (`self.interaction_history.doctor_supplementary_info`)，则格式化并添加到提示中。
5. 遍历所有`interaction_history.cot_entries`中的`dialogue_history`，将完整的对话历史格式化（区分AI医生和患者角色）并添加到提示中。
6. 追加用户指令模板 (`_user_prompt_question`)。
7. **条件性提示**: 
    - 计算当前策略下的对话轮数。如果是初始策略且轮数小于6轮，则追加一个强提示，要求LLM更仔细地收集信息，不要过早结束问诊（即不要生成`<feedback>`）。
    - 如果当前轮数接近或达到`self.max_rounds - 1`，则追加一个强提示，要求LLM必须生成`<feedback>`来结束当前策略的问诊，不要再提新问题。
**实现:**
```python
# Refer to executor.py for the detailed prompt assembly logic.
```

### `Executor._check_end_of_inquiry`
**函数名:** `_check_end_of_inquiry`
**类型:** 异步 `async def` (私有辅助方法)
**描述:** (**注意：此函数在`executor.py`的当前主要逻辑中似乎未被直接调用，其功能可能已被整合到`_generate_question`的提示工程中，或者是一个遗留/备用方法。**) 调用LLM判断基于当前策略、观察和对话历史，是否应该结束本轮问诊。
**参数:**
- 无直接参数，依赖于实例的`interaction_history`。
**返回值:**
- `bool`: 如果LLM的响应包含\"结束\"字样，则返回`True`，否则返回`False`。
**主要逻辑:**
1. 如果总对话历史长度小于2（即几乎没有对话），直接返回`False`。
2. 调用 `self._construct_end_inquiry_prompt()` 构建提示。
3. 设置系统提示 (`_sys_prompt_end_inquiry`)。
4. 调用 `await LLMClient.generate_response()`（指定`volcano`提供商）获取LLM响应。
5. 检查LLM响应中是否包含\"结束\"一词来决定返回值。
**核心逻辑调用:** `_construct_end_inquiry_prompt()`, `LLMClient.generate_response()`
**实现:**
```python
# prompt = self._construct_end_inquiry_prompt()
# system_message = _sys_prompt_end_inquiry
# response = await LLMClient.generate_response(provider="volcano", prompt=prompt, system_message=system_message, temperature=VOLCANO_TEMPERATURE_EXECUTOR)
# return "结束" in response
```

### `Executor._construct_end_inquiry_prompt`
**函数名:** `_construct_end_inquiry_prompt`
**类型:** 同步 (私有辅助方法)
**描述:** 构建用于`_check_end_of_inquiry`方法中调用LLM的提示字符串。此提示指导LLM判断是否应结束当前问诊。
**参数:**
- 无直接参数，依赖于实例的`interaction_history`。
**返回值:**
- `str`: 构建好的提示字符串。
**主要逻辑:**
1. 初始化提示字符串，加入当前诊断策略、当前观察（如果存在）、以及完整的对话历史。
2. 追加用户指令模板 (`_user_prompt_end_inquiry`)。
**实现:**
```python
# Refer to executor.py for prompt assembly.
```

### `Executor.update_observation`
**函数名:** `update_observation`
**类型:** 异步 `async def`
**描述:** 调用LLM来根据当前策略、反馈、现有观察、检查信息、医生补充信息以及完整的对话历史，生成一个新的、综合的患者观察记录。这个新的观察记录会更新到当前`interaction_history`的最后一个CoT条目中。
**参数:**
- 无直接参数，依赖于实例的`interaction_history`。
**返回值:**
- `str`: LLM生成的更新后的观察记录文本。
**主要逻辑:**
1. 调用 `self._construct_observation_prompt()` 构建提示。
2. 设置系统提示 (`_sys_prompt_update_observation_executor`)。
3. 调用 `await LLMClient.generate_response()`（指定`qwen`提供商和`deepseek-r1`模型）获取LLM响应。
4. 将LLM的响应（即新的观察记录）赋值给 `self.interaction_history.cot_entries[-1]["observation"]`。
5. 返回新的观察记录。
**核心逻辑调用:** `_construct_observation_prompt()`, `LLMClient.generate_response()`
**实现:**
```python
# prompt = self._construct_observation_prompt()
# system_message = _sys_prompt_update_observation_executor
# response = await LLMClient.generate_response(provider="qwen", prompt=prompt, system_message=system_message, temperature=VOLCANO_TEMPERATURE_EXECUTOR, model="deepseek-r1")
# self.interaction_history.cot_entries[-1]["observation"] = response
# return response
```

### `Executor._construct_observation_prompt`
**函数名:** `_construct_observation_prompt`
**类型:** 同步 (私有辅助方法)
**描述:** 构建用于`update_observation`方法中调用LLM的详细提示字符串。此提示指导LLM综合所有可用信息来更新患者的观察记录。
**参数:**
- 无直接参数，依赖于实例的`interaction_history`。
**返回值:**
- `str`: 构建好的提示字符串。
**主要逻辑:**
1. 初始化提示字符串。
2. 添加当前诊断策略、当前策略反馈（如果存在）、当前患者观察（如果存在）。
3. **添加检查信息**: 如果`interaction_history`中有`test_recommendation`，则格式化每个检查项目的详细信息（名称、结果、状态、范围等）并添加到提示中。
4. **添加医生补充信息**: 如果`interaction_history`中有`doctor_supplementary_info`，则逐条添加到提示中。
5. **添加所有问诊历史的问题**: 遍历所有CoT条目的对话历史，仅提取医生提出的问题，并添加到提示中，标题为\"所有问诊历史\"。
6. **添加当前对话历史**: 遍历所有CoT条目的对话历史，将完整的医患对话（区分角色）添加到提示中，标题为\"当前对话历史\"。
7. 追加用户指令模板 (`_user_prompt_update_observation_executor`)。
**实现:**
```python
# Refer to executor.py for detailed prompt assembly logic, especially for formatting test_recommendation.
```

### `Executor.provide_feedback`
**函数名:** `provide_feedback`
**类型:** 异步 `async def`
**描述:** (**注意：此函数在`executor.py`的当前主要逻辑中似乎未被直接调用，其功能可能已被整合到`_generate_question`的反馈生成部分，或者是一个遗留/备用方法。**) 调用LLM根据对话历史和当前策略生成反馈。
**参数:**
- 无直接参数，依赖于实例的`interaction_history`。
**返回值:**
- `str`: LLM生成的反馈文本。
**主要逻辑:**
1. 调用 `self._construct_feedback_prompt()` 构建提示。
2. 设置系统提示 (`_sys_prompt_feedback`)。
3. 调用 `await LLMClient.generate_response()`（指定`volcano`提供商）获取LLM响应。
4. 返回LLM的响应。
**核心逻辑调用:** `_construct_feedback_prompt()`, `LLMClient.generate_response()`
**实现:**
```python
# prompt = self._construct_feedback_prompt()
# system_message = _sys_prompt_feedback
# response = await LLMClient.generate_response(provider="volcano", prompt=prompt, system_message=system_message, temperature=VOLCANO_TEMPERATURE_EXECUTOR)
# return response
```

### `Executor._construct_feedback_prompt`
**函数名:** `_construct_feedback_prompt`
**类型:** 同步 (私有辅助方法)
**描述:** 构建用于`provide_feedback`方法中调用LLM的提示字符串。此提示指导LLM生成对当前问诊情况的反馈。
**参数:**
- 无直接参数，依赖于实例的`interaction_history`。
**返回值:**
- `str`: 构建好的提示字符串。
**主要逻辑:**
1. 初始化提示字符串，加入当前诊断策略、当前观察（如果存在）、以及完整的对话历史。
2. 追加用户指令模板 (`_user_prompt_feedback`)。
**实现:**
```python
# Refer to executor.py for prompt assembly.
```

### `Executor.get_observation`
**函数名:** `get_observation`
**类型:** 同步
**描述:** 获取当前最新的观察记录。
**参数:**
- 无。
**返回值:**
- `str`: 当前存储在`interaction_history`最后一个CoT条目中的观察记录。如果历史为空，则返回空字符串。
**实现:**
```python
# return self.interaction_history.cot_entries[-1]["observation"] if self.interaction_history.cot_entries else ""
```

### `Executor.format_dialogue_history`
**函数名:** `format_dialogue_history`
**类型:** 同步
**描述:** 将存储在`interaction_history`中所有CoT条目的对话历史格式化为一个单一的、人类可读的字符串。
**参数:**
- 无。
**返回值:**
- `str`: 格式化后的完整对话历史。如果没有任何对话记录，则返回提示信息 \"没有对话记录。\"。
**主要逻辑:**
1. 初始化一个包含标题 \"对话记录：\\n\" 的字符串。
2. 遍历`interaction_history.cot_entries`中的每一个条目。
3. 对于每个条目，遍历其`dialogue_history`中的每一段对话。
4. 根据对话的`role`（'doctor' 或 'patient'）添加角色前缀（\"AI医生: \" 或 \"患者: \"），然后追加对话内容和换行符。
5. 如果没有任何条目，则追加 \"没有对话记录。\\n\"。
6. 返回去除末尾多余空白的字符串。
**实现:**
```python
# formatted_history = "对话记录：\\n"
# if len(self.interaction_history.cot_entries) > 0:
#     for entry in self.interaction_history.cot_entries:
#         for dialogue in entry["dialogue_history"]:
#             role = "AI医生" if dialogue["role"] == "doctor" else "患者"
#             formatted_history += f"{role}: {dialogue['content']}\\n"
# else:
#     formatted_history += "没有对话记录。\\n"
# return formatted_history.strip()
```


## Planner Module Functions (from `src/planner.py`)

### `Planner.__init__`
**函数名:** `__init__`
**类型:** 同步构造函数
**描述:** 初始化`Planner`类的一个实例。`Planner`负责根据当前的交互历史和观察结果生成诊断策略、最终诊断、病情描述和治疗建议。它还可以选择性地使用CoT（思维链）检索来辅助决策。
**参数:**
- `max_rounds` (int, 可选, 默认=10): AI医生进行诊断的最大轮次数（或策略迭代次数）。
- `init_strategy` (str, 可选, 默认=None): 初始的诊断策略。如果提供，会作为第一个CoT条目添加到交互历史中。
- `init_reasoning` (str, 可选, 默认=None): 与初始策略相对应的初始推理。如果提供，会与`init_strategy`一同添加。
- `interaction_history` (InteractionHistory, 可选, 默认=None): 一个`InteractionHistory`对象实例，用于存储和恢复整个交互过程的状态。
- `enable_cot_retrieval` (bool, 可选, 默认=False): 是否启用CoT检索功能。如果为`True`，则会尝试初始化相关的组件（如Qdrant客户端、嵌入模型）。
**返回值:**
- 无 (构造函数不返回值)。
**主要逻辑:**
1. 存储传入的参数到实例变量。
2. 如果`enable_cot_retrieval`为`True`，则调用`_init_cot_retrieval()`初始化CoT检索组件，并设置`cot_retrieval_available`标志。
3. 如果提供了`init_strategy`和`init_reasoning`，则将它们作为第一个条目添加到`interaction_history`中。
**实现:**
```python
# self.interaction_history = interaction_history
# self.max_rounds = max_rounds
# self.enable_cot_retrieval = enable_cot_retrieval
# if self.enable_cot_retrieval:
#     self._init_cot_retrieval()
# if init_strategy and init_reasoning:
#     self.interaction_history.add_cot_entry(init_strategy, init_reasoning, "", [])
```

### `Planner._init_cot_retrieval`
**函数名:** `_init_cot_retrieval`
**类型:** 同步 (私有辅助方法)
**描述:** 初始化用于CoT（思维链）案例检索的相关组件，包括加载预训练的BERT模型（用于文本嵌入）和连接到Qdrant向量数据库客户端。
**参数:**
- 无。
**返回值:**
- 无。
**抛出异常:**
- 如果模型加载、Qdrant客户端连接或任何其他初始化步骤失败，会记录错误并向上抛出异常。
**主要逻辑:**
1. 加载预训练的BERT分词器和模型 (来自 Hugging Face `BAAI/bge-large-zh-v1.5`)。
2. 将嵌入模型设置为评估模式 (`eval()`)。
3. 从环境变量或配置中获取Qdrant数据库的URL和API密钥。
4. 初始化`QdrantClient`，设置URL、API密钥和超时时间。
5. 设置用于检索的Qdrant集合名称 (硬编码为 "COT")。
**实现:**
```python
# self.tokenizer = BertTokenizer.from_pretrained(model_path)
# self.embedding_model = BertModel.from_pretrained(model_path)
# self.embedding_model.eval()
# self.qdrant_client = QdrantClient(url=QDRANT_URL, api_key=QDRANT_API_KEY, timeout=600)
# self.collection_name = "COT"
```

### `Planner._extract_symptoms`
**函数名:** `_extract_symptoms`
**类型:** 同步 (私有辅助方法)
**描述:** 从给定的患者观察文本中提取关键症状信息，主要关注\"【主诉】\"和\"【现病史】\"部分。这些提取出的文本将用作CoT案例检索的查询。
**参数:**
- `observation` (str): 必须。包含患者信息的观察文本。
**返回值:**
- `str`: 提取并组合的主诉和现病史文本。如果无法提取到明确的主诉或现病史，则返回观察文本的前100个字符（如果观察文本存在）。如果观察文本为空，则返回空字符串。
**主要逻辑:**
1. 使用正则表达式分别尝试匹配并提取\"【主诉】\"和\"【现病史】\"标签后的内容。
2. 如果提取到主诉和/或现病史，则将它们组合起来作为查询字符串。
3. 如果两者都未提取到，则截取`observation`的前100个字符作为查询。
**实现:**
```python
# import re
# chief_match = re.search(r"【主诉】\s*(.*?)(?:\n|【|$)", observation)
# illness_match = re.search(r"【现病史】\s*(.*?)(?:\n|【|$)", observation)
# query = (chief_complaint + " " + present_illness).strip() or (observation[:100] if observation else "")
```

### `Planner._generate_embedding`
**函数名:** `_generate_embedding`
**类型:** 同步 (私有辅助方法)
**描述:** 使用初始化时加载的BERT模型将给定的文本转换为向量嵌入（embedding）。
**参数:**
- `text` (str): 必须。需要转换为向量的文本。
**返回值:**
- `numpy.ndarray` 或 `None`:
    - 如果CoT检索功能可用且嵌入生成成功，则返回一个NumPy数组表示的向量。
    - 如果CoT检索功能不可用或嵌入生成过程中发生错误，则返回`None`。
**主要逻辑:**
1. 检查`self.cot_retrieval_available`标志，如果不可用则直接返回`None`。
2. 使用`self.tokenizer`对输入文本进行分词和预处理，转换为模型输入格式。
3. 在`torch.no_grad()`上下文（禁用梯度计算）中，将处理后的输入传递给`self.embedding_model`。
4. 从模型输出中提取最后一层的隐藏状态，并通常取`[CLS]`标记对应的向量作为整个文本的嵌入。
5. 将PyTorch张量转换为NumPy数组并返回。
**实现:**
```python
# inputs = self.tokenizer(text, return_tensors='pt', padding=True, truncation=True, max_length=512)
# with torch.no_grad():
#     outputs = self.embedding_model(**inputs)
# return outputs.last_hidden_state[:, 0, :].squeeze().numpy()
```

### `Planner.retrieve_cot_examples`
**函数名:** `retrieve_cot_examples`
**类型:** 异步 `async def`
**描述:** 基于当前患者的观察信息，从Qdrant向量数据库中检索相似的CoT（思维链）案例。这些案例可以作为LLM生成新策略时的参考。
**参数:**
- `observation` (str): 必须。当前的患者观察信息。
- `top_k` (int, 可选, 默认=2): 希望从数据库中检索到的最相似案例的数量。
**返回值:**
- `list[str]`: 一个包含检索到的CoT案例文本的列表。每个元素是案例的字符串内容。如果CoT检索功能不可用、无法提取症状、无法生成查询向量或检索过程中发生错误（包括超时），则返回空列表。
**主要逻辑:**
1. 检查`self.cot_retrieval_available`标志，如果不可用则返回空列表。
2. 调用`self._extract_symptoms(observation)`从观察信息中提取查询文本。
3. 调用`self._generate_embedding(query_text)`将查询文本转换为向量嵌入。
4. 如果查询文本为空或向量生成失败，则返回空列表。
5. 调用`self.qdrant_client.search()`方法，在指定的集合 (`self.collection_name`) 中使用查询向量进行相似性搜索，限制返回数量为`top_k`，并要求返回payload。设置了10秒的超时。
6. 从搜索结果中提取每个匹配项的payload中的'COT'字段内容，添加到结果列表中。
**核心逻辑调用:** `_extract_symptoms()`, `_generate_embedding()`, `qdrant_client.search()`
**实现:**
```python
# query_text = self._extract_symptoms(observation)
# query_embedding = self._generate_embedding(query_text)
# search_result = self.qdrant_client.search(... query_vector=query_embedding.tolist(), limit=top_k ...)
# examples = [result.payload['COT'] for result in search_result if 'COT' in result.payload]
```

### `Planner._construct_strategy_prompt`
**函数名:** `_construct_strategy_prompt`
**类型:** 异步 `async def` (私有辅助方法)
**描述:** 构建用于请求LLM生成新诊断策略和相应推理的提示字符串。该提示会整合当前的患者观察、检查信息、历史策略和推理、以及（如果启用并成功检索到）相关的CoT案例作为参考。
**参数:**
- 无直接参数，依赖于实例的`interaction_history`和CoT检索设置。
**返回值:**
- `str`: 构建好的、准备发送给LLM的提示字符串。
**主要逻辑:**
1. 初始化提示，首先加入当前的患者观察信息。
2. 添加格式化的医生提供的检查信息（如果存在于`interaction_history.test_recommendation`）。
3. 添加历史记录：遍历`interaction_history.cot_entries`，格式化每一轮的策略、推理和反馈。
4. **CoT案例检索与添加**: 如果`self.enable_cot_retrieval`为`True`、CoT组件可用且观察信息非空：
    a. 调用 `await self.retrieve_cot_examples(observation)` 检索相似案例。
    b. 如果检索到案例，则将它们格式化并添加到提示中，作为LLM的参考。
5. 追加用户指令模板 (`_user_prompt_strategy`)。
6. **强制诊断提示**: 如果`interaction_history.cot_entries`的长度大于等于2（即至少完成了一轮完整的策略制定和执行），则在提示末尾追加一条强制诊断的指令，要求LLM直接生成诊断。
**核心逻辑调用:** `retrieve_cot_examples()`
**实现:**
```python
# Refer to planner.py for detailed prompt assembly, including formatting of test results and CoT examples.
```

### `Planner.generate_strategy`
**函数名:** `generate_strategy`
**类型:** 异步 `async def`
**描述:** 生成AI医生的下一个诊断策略和相应的推理。它首先调用`_generate_new_strategy`来获取LLM的输出，然后将新的策略和推理添加到`interaction_history`中，并最后检查是否满足终止诊断流程的条件。
**参数:**
- 无直接参数。
**返回值:**
- `tuple[str, str, bool]`:
    - `new_strategy` (str): LLM生成的新诊断策略。
    - `reasoning` (str): 与新策略对应的推理过程。
    - `should_terminate` (bool): 一个布尔标志，指示是否应该终止整个诊断流程。
**主要逻辑:**
1. 调用 `await self._generate_new_strategy()` 获取新的策略和推理。
2. 调用 `self.interaction_history.add_cot_entry(new_strategy, reasoning, "", [])` 将新生成的策略和推理（反馈和对话历史暂时为空）添加到交互历史中。
3. 调用 `self._check_termination()` 判断是否应该终止。
4. 返回新的策略、推理和终止标志。
**核心逻辑调用:** `_generate_new_strategy()`, `_check_termination()`, `interaction_history.add_cot_entry()`
**实现:**
```python
# new_strategy, reasoning = await self._generate_new_strategy()
# self.interaction_history.add_cot_entry(new_strategy, reasoning, "", [])
# should_terminate = self._check_termination()
# return new_strategy, reasoning, should_terminate
```

### `Planner._generate_new_strategy`
**函数名:** `_generate_new_strategy`
**类型:** 异步 `async def` (私有辅助方法)
**描述:** 负责实际调用LLM来生成新的诊断策略和推理。它构建提示，然后请求LLM给出响应，并解析该响应。
**参数:**
- 无直接参数。
**返回值:**
- `tuple[str, str]`:
    - `strategy` (str): 从LLM响应中解析出的诊断策略。
    - `reasoning` (str): 从LLM响应中解析出的推理过程。
**主要逻辑:**
1. 调用 `await self._construct_strategy_prompt()` 构建给LLM的提示。
2. 设置系统提示 (`_sys_prompt_strategy`)。
3. 调用 `await LLMClient.generate_response()`（指定`qwen`提供商和`deepseek-r1`模型）获取LLM响应。
4. 调用 `self._parse_strategy_response(response)` 解析LLM的响应，提取策略和推理。
**核心逻辑调用:** `_construct_strategy_prompt()`, `LLMClient.generate_response()`, `_parse_strategy_response()`
**实现:**
```python
# prompt = await self._construct_strategy_prompt()
# system_message = _sys_prompt_strategy
# response = await LLMClient.generate_response(provider="qwen", prompt=prompt, system_message=system_message, temperature=VOLCANO_TEMPERATURE_PLANNER, model="deepseek-r1")
# strategy, reasoning = self._parse_strategy_response(response)
# return strategy, reasoning
```

### `Planner._parse_strategy_response`
**函数名:** `_parse_strategy_response`
**类型:** 同步 (私有辅助方法)
**描述:** 从LLM生成的包含新策略和推理的文本响应中，解析并分别提取出策略部分和推理部分。
**参数:**
- `response` (str): 必须。LLM的原始文本响应。
**返回值:**
- `tuple[str, str]`:
    - `strategy` (str): 提取出的策略文本。如果未找到特定前缀（如\"问诊策略：\", \"诊断策略：\"等），则可能为空。
    - `reasoning` (str): 提取出的以\"推理：\"或\"推理:\"开头的推理文本。如果未找到，则可能为空。
**主要逻辑:**
1. 初始化`strategy`和`reasoning`为空字符串。
2. 按行分割LLM的响应文本。
3. 遍历每一行，检查是否以预定义的策略前缀（如\"问诊策略：\", \"诊断策略：\", \"查体策略：\"及其不带冒号的变体）或推理前缀（\"推理：\", \"推理:\"）开头。
4. 如果找到匹配的行，则提取相应前缀之后的内容作为策略或推理。
5. 一旦策略和推理都找到，即停止解析。
**实现:**
```python
# lines = response.split('\n')
# for line in lines:
#     if not strategy and (line.startswith("问诊策略:") or ...):
#         strategy = ...
#     elif not reasoning and (line.startswith("推理:") or ...):
#         reasoning = ...
#     if strategy and reasoning: break
```

### `Planner._check_termination`
**函数名:** `_check_termination`
**类型:** 同步 (私有辅助方法)
**描述:** 判断当前的诊断流程是否应该终止。终止条件包括达到最大轮次数，或者最新的策略中明确包含了诊断结果。
**参数:**
- 无直接参数，依赖于实例的`interaction_history`和`max_rounds`。
**返回值:**
- `bool`: 如果满足任一终止条件，则返回`True`；否则返回`False`。
**主要逻辑:**
1. 检查 `len(self.interaction_history.cot_entries)` 是否大于或等于 `self.max_rounds`。
2. 检查最新的CoT条目 (`self.interaction_history.cot_entries[-1]['strategy']`) 中是否包含子字符串 \"诊断:\" 或 \"诊断：\"。
3. 如果任一条件为真，则返回`True`。
**实现:**
```python
# if len(self.interaction_history.cot_entries) >= self.max_rounds: return True
# if self.interaction_history.cot_entries and ("诊断:" in self.interaction_history.cot_entries[-1]['strategy'] or "诊断：" in self.interaction_history.cot_entries[-1]['strategy']): return True
# return False
```

### `Planner.get_final_diagnosis`
**函数名:** `get_final_diagnosis`
**类型:** 同步
**描述:** 从`interaction_history`中存储的策略历史记录中，反向查找并提取第一个包含明确诊断信息的策略，并将其作为最终诊断结果返回。
**参数:**
- 无。
**返回值:**
- `str`: 提取到的最终诊断文本。如果遍历完所有历史策略后仍未找到包含\"诊断:\"或\"诊断：\"的条目，则返回一条提示信息\"未达成明确诊断。可能需要进一步检查。\"。
**主要逻辑:**
1. 反向遍历`self.interaction_history.cot_entries`。
2. 对于每个条目的`'strategy'`字段，检查是否包含\"诊断:\"或\"诊断：\"。
3. 如果找到，则提取从该诊断标记开始到字符串末尾的部分作为诊断结果，并返回。
4. 如果遍历完成仍未找到，则返回预设的未诊断消息。
**实现:**
```python
# for entry in reversed(self.interaction_history.cot_entries):
#     if "诊断:" in entry['strategy'] or "诊断：" in entry['strategy']:
#         # ... extract and return diagnosis ...
# return "未达成明确诊断。可能需要进一步检查。"
```

### `Planner.format_medical_advice`
**函数名:** `format_medical_advice`
**类型:** 同步
**描述:** 将结构化的诊疗计划数据（通常是包含药品推荐、生活建议、是否住院等信息的字典或JSON字符串）格式化为人类可读的文本字符串。
**参数:**
- `data` (str or dict): 必须。结构化的诊疗计划。如果是字符串，会尝试将其解析为JSON。
**返回值:**
- `str`: 格式化后的诊疗计划文本。如果输入`data`是无法解析的JSON字符串，则返回错误信息。
**主要逻辑:**
1. **JSON解析**: 如果输入`data`是字符串，尝试解析：
    a. 特殊格式处理：检查是否包含\"推理过程:\"和\"最终回答:\"标记，如果是，则分别提取推理过程和最终回答部分。最终回答部分可能包含Markdown JSON代码块，需要进一步提取和解析。
    b. 常规JSON解析：如果上述特殊格式不存在，则直接尝试将整个字符串作为JSON解析。
    c. 如果解析失败，返回错误消息。
2. **信息提取与格式化**:
    a. **药品推荐**: 从`data.get('药品', [])`中提取每种药品的名称、规格、服用方法、剂量安排、使用目的，并格式化为文本块。
    b. **生活建议**: 从`data.get('生活建议', [])`中提取每条建议，并格式化为列表项。
    c. **推理过程**: 如果解析后的`data`中包含`'推理过程'`键，则提取并准备添加到最终输出。
3. **住院信息处理**: 如果`data.get("是否住院") == "是"`，则格式化的输出将仅包含药品推荐、生活建议和`data.get('住院理由', '未提供')`。
4. **组合输出**: 将格式化后的药品信息、生活建议（以及可能的住院理由和推理过程）组合成一个单一的文本字符串。
**实现:**
```python
# Refer to planner.py for detailed JSON parsing logic and text formatting rules.
```

### `Planner.get_treatments`
**函数名:** `get_treatments`
**类型:** 异步 `async def`
**描述:** 根据给定的诊断和症状（从交互历史中提取），生成治疗方案。此过程包括：
    1. 调用外部API检索相关的西药和中药信息。
    2. 调用`get_guide`函数检索相关的医学诊疗指南。
    3. 将检索到的药品信息和指南内容整合，构建提示，然后调用LLM生成包含药品推荐、生活建议等的结构化诊疗计划。
    4. 最后，使用`format_medical_advice`将LLM生成的结构化计划格式化为文本。
**参数:**
- `diagnosis` (str): 必须。当前的诊断结论。
- `symptoms` (str): 必须。患者的症状描述或相关的观察信息（在函数内部，此参数似乎未使用，而是从`interaction_history`中重新获取观察信息）。
**返回值:**
- `str`: 格式化后的人类可读的治疗方案文本。
**工作流程:**
1. **药品信息检索**: 
    a. 定义一个内部函数`fetch_medicines(medicine_type, top_k)`，用于向硬编码的药品检索API (`http://47.94.171.56:8090/search`)发送POST请求，根据诊断、症状和药品类型（西药/中药）检索药品。
    b. 分别调用`fetch_medicines`获取西药（top_k=15）和中药（top_k=10）信息。
    c. 从API响应中提取并格式化所需的药品字段（通用名称、适应症/功能主治、规格、用法用量）。
2. **观察信息获取**: 从`interaction_history`的CoT条目中反向查找一个足够长的观察记录（`observation_used`）。
3. **诊疗指南检索**: 
    a. 检查`interaction_history.guidance_for_treatment`是否已有指南。如果有，则直接使用。
    b. 如果没有，则调用`await get_guide(diagnosis, previous_observation, ...)`来检索指南，并将结果存储回`interaction_history.guidance_for_treatment`。
    c. 将检索到的指南内容格式化为文本 (`treatment_guide_text`)。
4. **LLM调用生成诊疗计划**: 
    a. 构建用户提示 (`_user_prompt_treatment_rag`)，包含`observation_used`、`diagnosis`、格式化后的药品检索结果 (`meds_retriveal_extracted`)和`treatment_guide_text`。
    b. 设置系统提示 (`_sys_prompt_treatment_rag`)。
    c. 调用`await LLMClient.generate_response()`（指定`qwen`提供商，`deepseek-r1`模型，并要求显示推理过程）获取结构化的诊疗计划。
5. **格式化输出**: 调用`self.format_medical_advice()`将LLM返回的（可能为JSON字符串的）诊疗计划格式化为最终的文本输出。
**核心逻辑调用:** `requests.post` (for medicine retrieval), `get_guide()`, `LLMClient.generate_response()`, `format_medical_advice()`
**实现:**
```python
# Refer to planner.py for details on medicine API calls, prompt construction, and guide retrieval.
```

### `Planner.update_observation_from_diagnosis`
**函数名:** `update_observation_from_diagnosis`
**类型:** 异步 `async def`
**描述:** 从LLM生成的包含诊断、病情和诊断依据的响应文本中，使用正则表达式分别提取这些信息。
**参数:**
- `response` (str): 必须。LLM生成的文本响应，期望包含`<诊断>`, `<病情>`, 和 `<诊断依据>`等标签。
**返回值:**
- `tuple[str, str, str]`:
    - `diagnosis` (str): 提取到的诊断文本。
    - `condition` (str): 提取到的病情描述文本。
    - `basis` (str): 提取到的诊断依据文本。
    - 如果某个标签未找到，则对应的返回字符串为空。
**主要逻辑:**
1. 使用`re.search()`和相应的正则表达式（如`r"<诊断>(.*?)</诊断>"`）分别尝试提取`<诊断>`、`<病情>`和`<诊断依据>`标签内的内容。
2. 对提取到的内容去除首尾空格。
**实现:**
```python
# import re
# diagnosis_match = re.search(r"<诊断>(.*?)</诊断>", response, re.DOTALL)
# condition_match = re.search(r"<病情>(.*?)</病情>", response, re.DOTALL)
# basis_match = re.search(r"<诊断依据>(.*?)</诊断依据>", response, re.DOTALL)
# diagnosis = diagnosis_match.group(1).strip() if diagnosis_match else ""
# ... (similarly for condition and basis)
```

### `Planner.update_observation_only`
**函数名:** `update_observation_only`
**类型:** 异步 `async def`
**描述:** 当医生在问诊流程中提供了新的补充信息或检查结果后，调用LLM来更新患者的病史记录。更新后的病史会存储在交互历史的倒数第二个CoT条目的观察中。
**参数:**
- 无直接参数，依赖于实例的`interaction_history` (特别是`doctor_supplementary_info` 和 `test_recommendation` 以及倒数第二个条目的`observation`)。
**返回值:**
- `str`: LLM生成的更新后的病史文本。如果未能从LLM响应中提取到`<病史>`标签内容，则返回调用LLM前的当前病史。
**主要逻辑:**
1. 获取当前的病史记录（从`interaction_history.cot_entries[-2]['observation']`）。
2. 构建提示，包含当前病史、医生补充信息（如果有）、以及格式化的检查结果（如果有）。
3. 设置系统提示 (`_sys_prompt_update_observation`)。
4. 调用`await LLMClient.generate_response()`（指定`qwen`提供商，`deepseek-r1`模型）获取LLM响应。
5. 使用正则表达式从LLM响应中提取`<病史>`标签内的内容。
6. 如果成功提取，则用此更新后的病史替换`interaction_history.cot_entries[-2]['observation']`并返回该更新后的病史。
7. 如果提取失败，则返回原始的当前病史。
**核心逻辑调用:** `LLMClient.generate_response()`
**实现:**
```python
# current_observation = self.interaction_history.cot_entries[-2]['observation']
# prompt = f"...当前病史：{current_observation} ...医生补充信息... ...检查结果..."
# response = await LLMClient.generate_response(...)
# history_match = re.search(r"<病史>(.*?)</病史>", response, re.DOTALL)
# if history_match:
#     updated_history = history_match.group(1).strip()
#     self.interaction_history.cot_entries[-2]['observation'] = updated_history
#     return updated_history
# return current_observation
```

### `Planner._build_condition_prompt`
**函数名:** `_build_condition_prompt`
**类型:** 异步 `async def` (私有辅助方法，尽管其调用处在`final_diagnosis_and_condition`中是同步上下文，但其自身声明为async，这里按声明来。如果实际不需await，可改为同步)
**描述:** 构建用于请求LLM仅生成病情描述的提示字符串和相应的系统消息。
**参数:**
- 无直接参数，依赖于实例的`interaction_history` (特别是倒数第二个条目的`observation`, `doctor_supplementary_info`, `test_recommendation`)。
**返回值:**
- `tuple[str, str]`:
    - `prompt` (str): 构建好的、用于请求病情描述的提示。
    - `system_message` (str): 对应的系统提示 (`_sys_prompt_condition_only`)。
**主要逻辑:**
1. 从`interaction_history.cot_entries[-2]['observation']`获取患者病史。
2. 构建提示，包含患者病史、医生补充信息（如果有）、以及格式化的检查结果（如果有）。
3. 返回构建的提示和预设的系统消息`_sys_prompt_condition_only`。
**实现:**
```python
# observation = self.interaction_history.cot_entries[-2]['observation']
# prompt = f"请根据以下患者信息，仅给出病情描述：\n\n患者病史：{observation} ...医生补充信息... ...检查结果..."
# return prompt, _sys_prompt_condition_only
```

### `Planner.final_diagnosis_and_condition`
**函数名:** `final_diagnosis_and_condition`
**类型:** 异步 `async def`
**描述:** 根据是否有新的医生输入（补充信息或检查结果），决定诊断流程。如果无新输入，则仅基于现有信息生成病情描述。如果有新输入，则会执行完整的最终诊断流程，包括重新生成诊断、病情描述、诊断依据，并可能更新诊疗指南。
**参数:**
- `has_input` (bool): 必须。一个标志，指示自上次主要诊断步骤以来，医生是否提供了新的信息。
**返回值:**
- `tuple[str, str, str]`:
    - `formatted_diagnosis_with_basis` (str): 格式化的诊断结果，通常包含诊断名称和诊断依据，以换行符分隔。
    - `condition` (str): 患者的病情描述。
    - `reasoning` (str): 生成诊断和病情时的推理过程（如果`has_input`为True且LLM响应包含推理过程）。如果`has_input`为False，此项为空字符串。
**工作流程:**
1. **如果 `has_input` 为 `True` (有新信息):**
    a. 调用 `await self.generate_final_diagnosis_prompt()` 构建用于最终诊断的完整提示。
    b. 调用`LLMClient.generate_response()`（要求显示推理）获取LLM对最终诊断的响应。
    c. 调用 `await self.update_observation_from_diagnosis(response)` 从LLM响应中提取诊断、病情和诊断依据。
    d. 检查新提取的诊断 (`diagnosis`) 是否与存储在`self.interaction_history.diagnosis`中的现有诊断不同。如果不同，则清空`self.interaction_history.guidance_for_treatment`（表示需要重新获取指南），并更新`self.interaction_history.diagnosis`。
    e. 从LLM响应中提取推理过程。
    f. 返回格式化的诊断（诊断+依据）、病情和推理过程。
2. **如果 `has_input` 为 `False` (无新信息):**
    a. 调用 `await self._build_condition_prompt()` 构建仅用于生成病情描述的提示。
    b. 调用`LLMClient.generate_response()`获取LLM对病情描述的响应。
    c. 从LLM响应中提取病情描述 (`<病情>`标签内容)。
    d. 返回存储在`self.interaction_history.diagnosis`中的现有诊断、新生成的病情描述和空字符串（作为推理）。
**核心逻辑调用:** `generate_final_diagnosis_prompt()` (条件性), `update_observation_from_diagnosis()` (条件性), `_build_condition_prompt()` (条件性), `LLMClient.generate_response()`
**实现:**
```python
# Refer to planner.py for the conditional logic and detailed prompt/response handling.
```

### `Planner.generate_final_diagnosis_prompt`
**函数名:** `generate_final_diagnosis_prompt`
**类型:** 异步 `async def`
**描述:** 构建一个非常全面的提示，用于请求LLM给出最终的确诊结果。此提示整合了初步拟诊信息、相关的（已检索或新检索的）医学指南、完整的诊断过程历史（所有CoT条目的策略、推理、反馈）、患者当前病史、医生补充信息以及检查结果。
**参数:**
- 无直接参数，依赖于实例的`interaction_history`。
**返回值:**
- `tuple[str, str]`:
    - `prompt` (str): 构建好的、用于最终确诊的综合性提示。
    - `system_message` (str): 对应的系统提示 (`_sys_prompt_final_diagnosis`)。
**主要逻辑:**
1. 初始化提示。
2. 添加初步拟诊信息 (`self.interaction_history.diagnosis`)。
3. **医学指南处理**: 
    - 如果`self.interaction_history.guidance_for_treatment`已有内容，则直接使用。
    - 否则，调用`await get_guide()`根据当前诊断检索新的指南，将结果存入`guidance_for_treatment`，并添加到提示中。同时，将新的诊断与指南对应关系记录到`self.interaction_history.diagnosis_guidelines`。
4. 添加诊断过程历史（遍历所有CoT条目，格式化策略、推理、反馈）。
5. 添加患者当前病史（从最新的有效CoT条目中获取`observation`）。
6. 添加医生补充信息（格式化）。
7. 添加检查结果（格式化详细信息）。
8. 返回构建的提示和预设的系统消息`_sys_prompt_final_diagnosis`。
**核心逻辑调用:** `get_guide()` (条件性)
**实现:**
```python
# Refer to planner.py for detailed assembly of various information sources into the prompt.
```

### `Planner.generate_secondary_diagnosis`
**函数名:** `generate_secondary_diagnosis`
**类型:** 异步 `async def`
**描述:** 在已有初步诊断的基础上，结合患者观察、完整的CoT历史、医生补充信息和检查结果，请求LLM生成一个"二次拟诊"（可能是对初步诊断的确认、修正或补充）以及相应的额外检查建议。
**参数:**
- `initial_diagnosis` (str): 必须。作为起点的初步诊断结果。
**返回值:**
- `tuple[str, str, str]`:
    - `secondary_diagnosis` (str): LLM生成的二次拟诊诊断名称。
    - `additional_tests` (str): LLM建议的额外检查项目。
    - `reasoning` (str): LLM生成二次拟诊和检查建议时的推理过程。
    - 如果未能从LLM响应中解析出相应部分，则对应的返回字符串可能为空。
**主要逻辑:**
1. 格式化CoT历史、患者观察信息（从最新的有效CoT条目获取）、医生补充信息和检查结果。
2. 构建用户提示 (`_user_prompt_secondary_diagnosis`)，包含上述所有信息以及`initial_diagnosis`。
3. 设置系统提示 (`_sys_prompt_secondary_diagnosis`)。
4. 调用`await LLMClient.generate_response()`（要求显示推理）获取LLM响应。
5. 使用正则表达式从LLM响应中分别提取`<拟诊>`（二次诊断名称）、`<检查>`（额外检查建议）和推理过程文本。
**核心逻辑调用:** `LLMClient.generate_response()`
**实现:**
```python
# user_prompt = _user_prompt_secondary_diagnosis.format(...)
# response = await LLMClient.generate_response(...)
# diagnosis_match = re.search(r"<拟诊>(.*?)</拟诊>", response, re.DOTALL)
# tests_match = re.search(r"<检查>(.*?)</检查>", response, re.DOTALL)
# reasoning_match = re.search(r"推理过程:\n(.*?)\n\n最终回答:", response, re.DOTALL)
```

### `Planner.generate_preliminary_diagnosis`
**函数名:** `generate_preliminary_diagnosis`
**类型:** 异步 `async def`
**描述:** 基于给出的初步诊断（通常来自`final_diagnosis_parser`的输出）、初步检查建议和患者观察信息，结合从`get_guide`函数召回的医学指南，请求LLM对初步诊断进行核实或细化，并给出可能调整后的诊断（及理由）和进一步的检查建议。
**参数:**
- `first_diagnosis` (str): 必须。初始的诊断名称。
- `examination` (str): 必须。与初始诊断相关的初步检查建议。
- `observation` (str): 必须。当前的患者观察信息。
**返回值:**
- `tuple[str, str, str, dict or str]`:
    - `verified_diagnosis_with_reason` (str): LLM核实/调整后的诊断名称，可能附带诊断理由，以换行符分隔。
    - `additional_tests` (str): LLM建议的额外检查。
    - `reasoning` (str): LLM进行判断时的推理过程。
    - `guide_response` (dict or str): `get_guide`函数返回的原始指南响应。如果获取指南失败或过程中出错，此项可能是包含错误信息的字符串。
**工作流程:**
1. 调用`await get_guide(main_disease=first_diagnosis, ...)`检索与`first_diagnosis`最相关的医学指南。
2. 如果指南检索失败，则返回原始诊断和错误信息。
3. 格式化CoT历史、医生补充信息、检查结果。
4. 构建用户提示 (`_user_prompt_verify_diagnosis`)，包含`first_diagnosis`、`examination`、`observation`、召回的指南内容 (`original_content`) 以及上述格式化信息。
5. 设置系统提示 (`_sys_prompt_verify_diagnosis`)。
6. 调用`await LLMClient.generate_response()`（要求显示推理）获取LLM响应。
7. 使用正则表达式从LLM响应中提取`<拟诊>`（核实后的诊断名称）、`<拟诊理由>`、`<检查>`（额外检查建议）和推理过程文本。
8. **更新交互历史**: 
    - 如果`first_diagnosis`与核实后的诊断`verified_diagnosis`一致，则将`first_diagnosis`存入`self.interaction_history.diagnosis`，并将`guide_response`存入`self.interaction_history.guidance_for_treatment`。
    - 否则（诊断有变化），将`verified_diagnosis`存入`self.interaction_history.diagnosis`（此时`guidance_for_treatment`可能在后续步骤中被更新或清空）。
    - 将本次的`first_diagnosis`和`guide_response`记录到`self.interaction_history.diagnosis_guidelines`列表中。
9. 返回格式化的诊断（诊断+理由）、额外检查、推理过程和原始指南响应。
**核心逻辑调用:** `get_guide()`, `LLMClient.generate_response()`
**实现:**
```python
# guide_response = await get_guide(main_disease=first_diagnosis, ...)
# prompt = _user_prompt_verify_diagnosis.format(...guidelines=original_content...)
# response = await LLMClient.generate_response(...)
# # ... (parse response and update interaction_history) ...
```

## Get Guide Module Functions (from `src/get_guide.py`)

### `get_guide`
**函数名:** `get_guide`
**类型:** 异步 `async def`
**描述:** 根据给定的疾病名称和症状，从医学知识库中检索相关的诊疗指南内容。该函数使用向量数据库（Qdrant）进行语义检索，并通过LLM进行结果的优化筛选，为医生提供针对特定疾病的标准诊疗建议。
**参数:**
- `main_disease` (str): 必须。主要疾病名称，作为检索的主要关键词。
- `diagnostic_symptoms` (str, 可选, 默认=None): 诊断相关的症状描述，用于增强检索的精确度。
- `top_k_qdrant` (int, 可选, 默认=12): 从Qdrant向量数据库中检索的结果数量。
- `top_k_llm` (int, 可选, 默认=1): LLM筛选后保留的最相关指南数量。
**返回值:**
- `dict` 或 `str`:
  - 成功时: 返回一个包含检索到的指南信息的字典，通常包含原始检索内容、已筛选的指南和相关元数据。
  - 失败时: 返回一个错误描述字符串。
**工作流程:**
1. **初始步骤检查**:
   - 验证输入的疾病名称是否为空，如果为空则返回错误信息。
   - 准备用于向量数据库查询的搜索文本（结合疾病名称和症状）。
2. **向量生成与检索**:
   - 调用`generate_embedding()`将搜索文本转换为向量表示。
   - 使用该向量在Qdrant数据库的"guidelines"集合中进行相似性搜索，获取前`top_k_qdrant`个结果。
3. **检索结果处理**:
   - 从搜索结果中提取每个匹配项的payload（包含指南内容、疾病名称、来源等）。
   - 如果未找到结果，尝试使用截断的疾病名称（取前两个词）重新检索。
   - 如果仍未找到结果，返回未找到指南的消息。
4. **LLM筛选与优化**:
   - 构建提示，包含检索到的所有指南内容以及用户查询的疾病和症状。
   - 调用LLM模型（通常为deepseek-r1）来评估和筛选最相关的指南，仅保留前`top_k_llm`个结果。
   - LLM还会提取每个指南的关键信息并进行结构化。
5. **返回结果构建**:
   - 构造一个包含原始检索内容、筛选后指南、以及各种元数据的字典结构。
   - 如果任一步骤失败，返回相应的错误信息。
**核心逻辑调用:**
- `generate_embedding()`: 文本向量化
- `qdrant_client.search()`: 向量数据库查询
- `LLMClient.generate_response()`: LLM指南筛选与优化
**实现:**
```python
# 待实现
# 包含向量生成、Qdrant查询、LLM提示构建与调用、结果处理等详细逻辑
```

## LLM Client Module Functions (from `src/llm_client.py`)

### `LLMClient.generate_response`
**函数名:** `generate_response`
**类型:** 静态异步方法 `@staticmethod async def`
**描述:** 工厂方法，根据指定的LLM提供商名称调用相应的客户端生成AI响应。该函数作为统一入口，封装了与不同AI服务提供商（如OpenAI、Volcano/Deepseek、Claude、Qwen等）的通信细节。
**参数:**
- `provider` (str, 可选, 默认="openai"): LLM提供商名称，支持"openai"、"volcano"、"claude"、"qwen"或"jiuzhang"。
- `prompt` (str, 可选, 默认=""): 发送给模型的提示文本。
- `system_message` (str, 可选, 默认=None): 系统消息，用于指导模型行为。
- `max_tokens` (int, 可选, 默认=None): 生成响应的最大token数。
- `temperature` (float, 可选, 默认=0.7): 生成时的温度参数，控制输出的随机性。
- `show_reasoning` (bool, 可选, 默认=False): 是否在响应中包含模型的推理过程。
- `stream` (bool, 可选, 默认=False): 是否使用流式响应模式。
- `model` (str, 可选, 默认=None): 具体的模型名称，用于支持多模型的提供商。
**返回值:**
- `str`: 模型生成的响应文本。如果`show_reasoning=True`且模型支持，则包含格式化的推理过程和最终回答。
**主要逻辑:**
1. 根据`provider`参数选择相应的客户端类：
   - "openai": 调用`OpenAIClient.generate_response()`
   - "volcano": 调用`VolcanoClient.generate_response()`
   - "claude": 调用`ClaudeClient.generate_response()`
   - "qwen": 调用`QwenClient.generate_response()`
   - "jiuzhang": 调用`JiuzhangClient.generate_response()`
2. 传递所有相关参数给选定的客户端生成响应。
3. 如果`provider`不是已知的提供商，则返回错误消息。
**实现:**
```python
# if provider.lower() == "openai":
#     return await OpenAIClient.generate_response(prompt, system_message, max_tokens, temperature=temperature, show_reasoning=show_reasoning)
# elif provider.lower() == "volcano":
#     return await VolcanoClient.generate_response(prompt, system_message, max_tokens, temperature=temperature, show_reasoning=show_reasoning)
# ... (其他提供商的处理)
# else:
#     logger.error(f"Unknown provider: {provider}")
#     return f"未知的LLM提供商: {provider}"
```

### `OpenAIClient.generate_response`
**函数名:** `generate_response`
**类型:** 类方法 `@classmethod async def`
**描述:** 调用OpenAI API生成响应，特别支持O1推理模型的使用。
**参数:**
- `prompt` (str): 必须。发送给API的提示文本。
- `system_message` (str, 可选, 默认=None): 系统消息，指导模型的行为。
- `max_tokens` (int, 可选, 默认=None): 生成的最大token数。
- `temperature` (float, 可选, 默认=0.7): 控制生成随机性的参数。
- `show_reasoning` (bool, 可选, 默认=False): 是否在响应中包含推理过程。
**返回值:**
- `str`: OpenAI生成的响应文本，如果`show_reasoning=True`且模型支持，则包含推理过程。
**工作流程:**
1. 构建API请求消息数组，包含系统消息（如果提供）和用户提示。
2. 使用O1推理模型调用OpenAI API，设置适当的参数如推理努力级别。
3. 提取API响应的文本内容。
4. 如果`show_reasoning=True`且响应中包含推理过程，则组合推理过程和最终答案为格式化输出。
**核心逻辑调用:** `await cls.client.responses.create()`
**实现:**
```python
# messages = []
# if system_message:
#     messages.append({"role": "system", "content": system_message})
# messages.append({"role": "user", "content": prompt})
# 
# reasoning_params = {"effort": "medium"}
# response = await cls.client.responses.create(
#     model="o1",
#     reasoning=reasoning_params,
#     input=messages,
# )
# 
# response_text = response.output_text.strip()
# 
# if show_reasoning and hasattr(response, 'reasoning') and response.reasoning:
#     reasoning_text = response.reasoning.get('text', '')
#     if reasoning_text:
#         return f"推理过程:\n{reasoning_text}\n\n最终回答:\n{response_text}"
# 
# return response_text
```

### `VolcanoClient.generate_response`
**函数名:** `generate_response`
**类型:** 类方法 `@classmethod async def`
**描述:** 调用Volcano API（Deepseek-r1）生成响应，并处理推理过程的提取和格式化。
**参数:**
- `prompt` (str): 必须。发送给API的提示文本。
- `system_message` (str, 可选, 默认=None): 系统消息，指导模型的行为。
- `max_tokens` (int, 可选, 默认=None): 生成的最大token数。
- `temperature` (float, 可选, 默认=0.7): 控制生成随机性的参数。
- `show_reasoning` (bool, 可选, 默认=False): 是否在响应中包含推理过程。
**返回值:**
- `str`: Volcano API生成的响应文本，如果`show_reasoning=True`且能提取到推理内容，则包含格式化的推理过程。
**工作流程:**
1. 构建API请求消息数组，包含系统消息和用户提示。
2. 调用Volcano API发送请求。
3. 提取API响应的文本内容。
4. 如果`show_reasoning=True`，尝试从响应中提取推理内容：
   a. 首先尝试从`reasoning_content`字段提取。
   b. 如果失败，尝试解析JSON响应或查找特定文本标记来提取推理过程。
5. 如成功提取推理过程，返回格式化的带推理文本；否则返回原始响应文本。
**核心逻辑调用:** `await cls.client.chat.completions.create()`
**实现:**
```python
# 详细实现涉及API调用、响应解析和推理内容提取的复杂逻辑
```

### `ClaudeClient.generate_response`
**函数名:** `generate_response`
**类型:** 类方法 `@classmethod async def`
**描述:** 调用Claude API生成响应，通过修改提示引导模型输出推理过程。
**参数:**
- `prompt` (str): 必须。发送给API的提示文本。
- `system_message` (str, 可选, 默认=None): 系统消息，指导模型的行为。
- `max_tokens` (int, 可选, 默认=None): 生成的最大token数，默认1000。
- `temperature` (float, 可选, 默认=0.7): 控制生成随机性的参数。
- `show_reasoning` (bool, 可选, 默认=False): 是否要求模型显示推理过程。
**返回值:**
- `str`: Claude API生成的响应文本。
**工作流程:**
1. 如果`show_reasoning=True`，修改系统消息和提示文本，要求模型先详细说明推理过程再给出答案。
2. 调用Claude API发送请求，传入模型名称、系统消息、用户提示等参数。
3. 提取并返回API响应的文本内容。
**核心逻辑调用:** `await cls.client.messages.create()`
**实现:**
```python
# if show_reasoning:
#     if modified_system:
#         modified_system += "\n请在回答问题时，先详细说明你的推理过程，然后再给出最终答案。"
#     else:
#         modified_system = "请在回答问题时，先详细说明你的推理过程，然后再给出最终答案。"
#     
#     if "请详细说明你的推理过程" not in modified_prompt:
#         modified_prompt += "\n\n请详细说明你的推理过程，然后再给出最终答案。"
# 
# response = await cls.client.messages.create(
#     model=CLAUDE_MODEL,
#     system=modified_system,
#     messages=[
#         {"role": "user", "content": modified_prompt}
#     ],
#     temperature=temperature,
#     max_tokens=max_tokens,
# )
# 
# response_text = response.content[0].text
```

### `QwenClient.generate_response`
**函数名:** `generate_response`
**类型:** 类方法 `@classmethod async def`
**描述:** 调用通义千问API生成响应，支持普通和流式响应，并处理推理过程的提取和格式化。
**参数:**
- `prompt` (str): 必须。发送给API的提示文本。
- `system_message` (str, 可选, 默认=None): 系统消息，指导模型的行为。
- `max_tokens` (int, 可选, 默认=None): 生成的最大token数。
- `temperature` (float, 可选, 默认=0.7): 控制生成随机性的参数。
- `show_reasoning` (bool, 可选, 默认=False): 是否在响应中包含推理过程。
- `stream` (bool, 可选, 默认=False): 是否使用流式响应模式。
- `model` (str, 可选, 默认=None): 指定使用的模型名称，覆盖配置中的默认模型。
**返回值:**
- `str`: 千问API生成的响应文本。
**工作流程:**
1. 如果`stream=True`，则调用专用的流式响应方法`generate_stream_response()`。
2. 构建API请求消息数组，包含系统消息和用户提示。
3. 确定要使用的模型，优先使用传入的`model`参数，其次使用配置中的默认模型。
4. 如果`show_reasoning=True`，修改系统消息和提示文本，要求模型显示推理过程。
5. 调用千问API发送请求。
6. 提取API响应的文本内容。
7. 如果`show_reasoning=True`且在响应中检测到JSON格式或推理过程标记，则尝试提取和格式化推理过程。
**核心逻辑调用:** 
- 普通模式: `await cls.client.chat.completions.create()`
- 流式模式: `await cls.generate_stream_response()`
**实现:**
```python
# 详细实现包括普通模式和流式模式的API调用、响应解析和推理内容提取
```

### `QwenClient.generate_stream_response`
**函数名:** `generate_stream_response`
**类型:** 类方法 `@classmethod async def`
**描述:** 以流式方式调用通义千问API，实时获取和处理响应内容。
**参数:**
- 参数与`generate_response`相同，但没有`stream`参数，因为此方法总是使用流式模式。
**返回值:**
- `str`: 累积的完整响应文本。
**工作流程:**
1. 构建API请求消息数组，包含系统消息和用户提示。
2. 确定使用的模型，处理`show_reasoning`等参数。
3. 以流式模式调用千问API，设置相关参数。
4. 迭代处理流式响应的每个片段，累积完整响应文本。
5. 如果需要显示推理过程，尝试从累积的响应中提取并格式化推理内容。
**核心逻辑调用:** `async for chunk in cls.client.chat.completions.create(..., stream=True)`
**实现:**
```python
# full_response = ""
# async for chunk in cls.client.chat.completions.create(..., stream=True):
#     # 处理每个响应片段，提取文本内容
#     content = chunk.choices[0].delta.content or ""
#     if content:
#         full_response += content
# 
# # 处理推理过程提取和格式化
```

### `JiuzhangClient.generate_response`
**函数名:** `generate_response`
**类型:** 类方法 `@classmethod async def`
**描述:** 调用九章API生成响应，处理正常和流式响应，并支持推理过程提取。
**参数:**
- 参数与`QwenClient.generate_response`基本相同，支持模型、流式响应和推理过程显示。
**返回值:**
- `str`: 九章API生成的响应文本。
**工作流程:**
1. 构建请求消息，设置模型和其他参数。
2. 处理`show_reasoning`和`stream`选项。
3. 调用API获取响应，根据需要处理流式或普通响应。
4. 提取和返回响应文本，必要时处理推理过程的提取和格式化。
**核心逻辑调用:** 与`QwenClient`类似，使用九章特定的API端点和参数。
**实现:**
```python
# 与QwenClient.generate_response实现类似，但使用九章特定的API配置
```

