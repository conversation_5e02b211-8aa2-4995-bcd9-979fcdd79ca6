server:
  port: ${SERVER_PORT}

spring:
  application:
    name: ai-doctor-prod
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: org.postgresql.Driver
      url: ${DB_URL}
      username: ${DB_USERNAME}
      password: ${DB_PASSWORD}
      initial-size: ${DB_INITIAL_SIZE}
      max-active: ${DB_MAX_ACTIVE}
      min-idle: ${DB_MIN_IDLE}
      max-wait: ${DB_MAX_WAIT}
      # 连接验证和超时配置 - 防止连接泄露
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      validation-query: SELECT 1
      validation-query-timeout: 30
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      max-evictable-idle-time-millis: 900000
      # 连接泄露检测
      remove-abandoned: true
      remove-abandoned-timeout: 1800
      log-abandoned: true
      # 连接池监控
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      # Druid监控配置
      stat-view-servlet:
        enabled: true                     # 启用StatViewServlet
        url-pattern: /druid/*            # 访问监控页面的URL
        reset-enable: false               # 禁用重置按钮
        login-username: ${DRUID_ADMIN_USERNAME}   # 监控页面的用户名
        login-password: ${DRUID_ADMIN_PASSWORD}   # 监控页面的密码
        allow: ${DRUID_ALLOW_IP}          # 允许访问的IP，为空则允许所有IP
        deny:                             # 拒绝访问的IP，优先级高于allow
      web-stat-filter:
        enabled: true                     # 启用WebStatFilter
        url-pattern: /*                   # 拦截所有请求
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"  # 不拦截的资源
      filter:
        stat:
          enabled: true                   # 启用SQL监控
          log-slow-sql: true              # 记录慢SQL
          slow-sql-millis: 1000           # 慢SQL的标准（毫秒）
          merge-sql: true                 # 合并相似SQL
        wall:
          enabled: true                   # 启用防火墙，防止SQL注入
          config:
            multi-statement-allow: false  # 禁止一次执行多条SQL
        slf4j:
          enabled: true                   # 启用日志输出
          statement-executable-sql-log-enable: true  # 记录可执行SQL
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.makiyo.ai_doctor_prod.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl

# TTS 语音合成服务配置
tts:
  api:
    url: ${TTS_API_URL}
    appId: ${TTS_APP_ID}
    accessToken: ${TTS_ACCESS_TOKEN}
  audio:
    voiceType: ${TTS_VOICE_TYPE}
    encoding: ${TTS_ENCODING}
    cluster: ${TTS_CLUSTER}
    basePath: ${TTS_BASE_PATH:audio}
    baseUrl: ${TTS_BASE_URL}
    speedRatio: ${TTS_SPEED_RATIO}
    volumeRatio: ${TTS_VOLUME_RATIO}
    pitchRatio: ${TTS_PITCH_RATIO}

# Python AI 服务配置
python:
  prod:
    inquiry:
      url: ${PYTHON_PROD_INQUIRY_URL}
    diagnosis:
      url: ${PYTHON_PROD_DIAGNOSIS_URL}
    diagnose:
      url: ${PYTHON_PROD_DIAGNOSE_URL}
    # HTTP客户端超时配置
    http:
      connect-timeout: 300000   # 连接超时5分钟
      socket-timeout: 300000    # Socket超时5分钟

# Springdoc OpenAPI Configuration (Swagger UI)
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    display-request-duration: true
    doc-expansion: none
  info:
    title: AI Doctor Prod API
    version: 1.0.0
    description: AI Doctor 生产环境应用程序的 API 文档
    contact:
      name: Makiyo

# 语音识别服务配置
asr:
  api:
    url: ${ASR_API_URL}
    appId: ${ASR_APP_ID}
    token: ${ASR_TOKEN}
  temp:
    dir: ${ASR_TEMP_DIR}

# OCR specific configurations
ocr:
  python:
    prod:
      service:
        url: ${OCR_PYTHON_PROD_SERVICE_URL}
  image:
    prod:
      save:
        path: ${OCR_IMAGE_SAVE_PATH:images}
  # HTTP超时和重试配置，防止连接泄露
  retry:
    max-attempts: 3
    initial-delay: 1000
    multiplier: 2
  temp:
    file:
      max-age-hours: 24

# Spring Boot Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,connections  # 暴露健康检查和连接监控端点
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized  # 显示详细健康信息
      show-components: always
  health:
    connection:
      enabled: true  # 启用连接健康检查 