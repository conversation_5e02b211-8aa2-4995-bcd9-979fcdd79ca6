import pandas as pd
import json

# 读取 Excel 文件中的两个工作表
df_xiyao = pd.read_excel("../data/meds.xlsx", sheet_name="西药", header=None)
df_zhongyao = pd.read_excel("../data/meds.xlsx", sheet_name="中药", header=None)

# 初始化字典来存储分类结果
medications = {"西药": {}, "中药": {}}

# 定义一个函数来处理药品分类
def process_medications(df, category_dict):
    current_category = None

    # 遍历每一行数据
    for index, row in df.iterrows():
        # 获取当前行的内容
        cell_value = str(row[0]).strip()
        

        # 检查当前行是否为类别行（以数字和“药物”结尾）
        # if cell_value and cell_value[0].isdigit() and "药物" in cell_value:
        if cell_value and cell_value[0].isdigit():
            print("这是一类：", cell_value)
            # 这是一类药物，更新当前类别
            current_category = cell_value
            # 在字典中为该类别初始化一个空列表
            category_dict[current_category] = []
        elif current_category and cell_value and not cell_value[0].isdigit():
            # 这是一行药品数据，且当前类别已被识别
            medication_info = {
                "名称": cell_value,
                "规格": str(df.iloc[index, 1]).strip() if len(df.columns) > 1 else "",  # 规格列
                "单位": str(df.iloc[index, 2]).strip() if len(df.columns) > 2 else ""  # 单位列
            }
            # 将药品信息添加到当前类别的列表中
            category_dict[current_category].append(medication_info)

# 处理西药和中药
process_medications(df_xiyao, medications["西药"])
process_medications(df_zhongyao, medications["中药"])

# 输出药品的分类结果，只输出种类名，不输出具体药品信息
for category, meds in medications.items():
    print(f"{category}: {list(meds.keys())}")

# 将结果输出为 JSON 格式
with open("medications.json", "w", encoding="utf-8") as json_file:
    json.dump(medications, json_file, ensure_ascii=False, indent=4)


#输出medications的示例内容，每一个对象只打印前两个成员
print("medications示例内容：")
for category, meds in medications.items():
    print(f"{category}:")
    for category_name, meds_list in meds.items():
        print(f"    {category_name}: {meds_list[:2]}")



print("JSON文件已成功保存。")
