﻿{
    "interaction_history": {
        "diagnosis": "",
        "cot_entries": [
            {
                "feedback": "",
                "strategy": "璇㈤棶鎮ｈ€呯殑鍩烘湰淇℃伅锛堟€у埆銆佸勾榫勶級鍜屼富璇夌棁鐘讹紝浜嗚В鍙戠梾鏃堕棿銆佽捣鐥呮儏鍐点€佺棁鐘剁壒鐐癸紙鎬ц川銆佺▼搴︺€佹寔缁椂闂淬€侀鐜囷級銆佷即闅忕棁鐘躲€佺梾鎯呮紨鍙樹互鍙婁箣鍓嶇殑璇婄枟缁忚繃銆?,
                "reasoning": "涓昏瘔鏄棶璇婄殑鍒囧叆鐐癸紝閫氳繃浜嗚В鎮ｈ€呯殑鍩烘湰淇℃伅鍜屼富瑕佺棁鐘讹紝鍙互蹇€熷缓绔嬪垵姝ュ嵃璞°€傝繘涓€姝ヨ闂彂鐥呮椂闂淬€佽捣鐥呮儏鍐点€佺棁鐘剁壒鐐广€佷即闅忕棁鐘躲€佺梾鎯呮紨鍙樺拰璇婄枟缁忚繃锛屾湁鍔╀簬鍏ㄩ潰浜嗚В鐜扮梾鍙诧紝涓哄悗缁瘖鏂彁渚涘叧閿俊鎭€傝繖绉嶇郴缁熸€х殑闂瘖绛栫暐鑳藉甯姪鎴戜滑鍑嗙‘鎶婃彙鎮ｈ€呯殑鐥呮儏锛岄伩鍏嶉仐婕忛噸瑕佷俊鎭€?,
                "observation": "",
                "dialogue_history": [
                    { "role": "doctor", "content": "鎮ㄥソ锛屾垜鏄湰娆＄殑鍖荤敓锛岃闂瀛愮殑骞撮緞锛屾€у埆锛屼富瑕佺殑鐥囩姸鏄粈涔堬紵" },
                    { "role": "patient", "content": "鐢凤紝涓夊瞾锛屾湁鐐瑰彂鐑? },
                    { "role": "doctor", "content": "瀛╁瓙鏄粠浠€涔堟椂鍊欏紑濮嬪彂鐑х殑锛? },
                    { "role": "patient", "content": "涓夊ぉ鍓嶅紑濮嬪彂鐑? },
                    { "role": "doctor", "content": "瀛╁瓙鍙戠儳鍓嶆湁娌℃湁浠€涔堢壒鍒殑浜嬫儏锛屾瘮濡傜潃鍑夈€佹帴瑙︾敓鐥呯殑浜烘垨鑰呭墽鐑堟椿鍔紵" }
                ]
            }
        ],
        "test_recommendation": [],
        "preliminary_diagnosis": null,
        "treatment_recommendation": [],
        "doctor_supplementary_info": []
    }
}
