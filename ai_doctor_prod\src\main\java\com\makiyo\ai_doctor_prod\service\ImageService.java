package com.makiyo.ai_doctor_prod.service;

import com.mongodb.client.gridfs.model.GridFSFile;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.gridfs.GridFsOperations;
import org.springframework.data.mongodb.gridfs.GridFsResource;
import org.springframework.data.mongodb.gridfs.GridFsTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * MongoDB GridFS 图片存储服务 (生产环境)
 */
@Service
public class ImageService {
    private static final Logger log = LoggerFactory.getLogger(ImageService.class);

    private final GridFsTemplate gridFsTemplate;
    private final GridFsOperations gridFsOperations;

    @Autowired
    public ImageService(GridFsTemplate gridFsTemplate, GridFsOperations gridFsOperations) {
        this.gridFsTemplate = gridFsTemplate;
        this.gridFsOperations = gridFsOperations;
        log.info("[PROD ImageService] 图片存储服务已初始化");
    }

    /**
     * 存储图片到 GridFS
     *
     * @param file 要存储的图片文件
     * @param caseId 关联的案例ID
     * @param category 图片分类（如"OCR"）
     * @return 包含存储元信息的Map
     * @throws IOException 如果文件操作失败
     */
    public Map<String, Object> storeImage(MultipartFile file, String caseId, String category) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("无法存储空文件");
        }

        // 创建图片元数据
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("caseId", caseId);
        metadata.put("category", category);
        metadata.put("originalFilename", file.getOriginalFilename());
        metadata.put("contentType", file.getContentType());
        metadata.put("uploadTime", LocalDateTime.now().toString());

        try (InputStream inputStream = file.getInputStream()) {
            // 存储文件并获取ID
            ObjectId fileId = gridFsTemplate.store(
                inputStream,
                file.getOriginalFilename(),
                file.getContentType(),
                metadata
            );

            log.info("[PROD ImageService] 图片已存储到GridFS，ID: {}, 案例ID: {}, 分类: {}", 
                    fileId.toString(), caseId, category);

            // 构建返回的元数据
            Map<String, Object> imageMetadata = new HashMap<>(metadata);
            imageMetadata.put("gridFsId", fileId.toString());
            imageMetadata.put("size", file.getSize());
            return imageMetadata;
        }
    }

    /**
     * 根据GridFS ID检索图片
     *
     * @param fileId GridFS文件ID
     * @return 图片资源，如果找不到则为null
     */
    public Optional<GridFsResource> getImageById(String fileId) {
        try {
            // 根据ID查询文件
            GridFSFile gridFSFile = gridFsTemplate.findOne(
                    new Query(Criteria.where("_id").is(new ObjectId(fileId)))
            );

            if (gridFSFile == null) {
                log.warn("[PROD ImageService] 未找到ID为{}的图片", fileId);
                return Optional.empty();
            }

            // 获取文件资源
            GridFsResource resource = gridFsOperations.getResource(gridFSFile);
            return Optional.of(resource);
        } catch (Exception e) {
            log.error("[PROD ImageService] 检索ID为{}的图片时出错: {}", fileId, e.getMessage(), e);
            return Optional.empty();
        }
    }

    /**
     * 根据案例ID检索所有相关图片的元数据
     *
     * @param caseId 案例ID
     * @return 包含每个图片元数据的列表
     */
    public Iterable<GridFSFile> findImagesByCaseId(String caseId) {
        try {
            Query query = new Query(Criteria.where("metadata.caseId").is(caseId));
            return gridFsTemplate.find(query);
        } catch (Exception e) {
            log.error("[PROD ImageService] 检索案例ID为{}的图片时出错: {}", caseId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 删除GridFS中的图片
     *
     * @param fileId 要删除的文件ID
     * @return 是否删除成功
     */
    public boolean deleteImage(String fileId) {
        try {
            gridFsTemplate.delete(new Query(Criteria.where("_id").is(new ObjectId(fileId))));
            log.info("[PROD ImageService] 已删除ID为{}的图片", fileId);
            return true;
        } catch (Exception e) {
            log.error("[PROD ImageService] 删除ID为{}的图片时出错: {}", fileId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取图片元数据，不加载图片内容
     *
     * @param fileId 图片ID
     * @return 图片元数据，如果找不到则为空
     */
    public Optional<GridFSFile> getImageMetadata(String fileId) {
        try {
            GridFSFile gridFSFile = gridFsTemplate.findOne(
                    new Query(Criteria.where("_id").is(new ObjectId(fileId)))
            );
            
            if (gridFSFile == null) {
                log.warn("[PROD ImageService] 未找到ID为{}的图片元数据", fileId);
                return Optional.empty();
            }
            
            return Optional.of(gridFSFile);
        } catch (Exception e) {
            log.error("[PROD ImageService] 获取ID为{}的图片元数据时出错: {}", fileId, e.getMessage(), e);
            return Optional.empty();
        }
    }
}