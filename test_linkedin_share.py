import requests
import json

# --- 配置 (!!! 请务必替换为您的实际值 !!!) ---

# 1. 您的访问令牌 (通过 OAuth 2.0 获取) - 不要硬编码在生产代码中！
ACCESS_TOKEN = "YOUR_ACCESS_TOKEN_HERE"

# 2. 要代表发帖的用户的 Person URN (通过 OAuth 2.0 或 Profile API 获取)
PERSON_URN = "urn:li:person:YOUR_PERSON_URN_HERE"

# 3. 要分享的文本内容
SHARE_TEXT = "Hello World from Python! Testing the LinkedIn Share API. #LinkedInAPI #Python"

# API 端点
API_ENDPOINT = "https://api.linkedin.com/v2/ugcPosts"

# --- 测试逻辑 ---

def share_text_on_linkedin():
    """使用提供的 Access Token 和 Person URN 在 LinkedIn 上分享文本内容"""

    if ACCESS_TOKEN == "YOUR_ACCESS_TOKEN_HERE" or PERSON_URN == "urn:li:person:YOUR_PERSON_URN_HERE":
        print("错误：请在脚本中替换 'YOUR_ACCESS_TOKEN_HERE' 和 'YOUR_PERSON_URN_HERE' 为您的实际值。")
        return

    # 准备请求头
    headers = {
        "Authorization": f"Bearer {ACCESS_TOKEN}",
        "Content-Type": "application/json",
        "X-Restli-Protocol-Version": "2.0.0", # 根据文档要求
        "LinkedIn-Version": "202403" # 使用一个较新的稳定版本号，或根据需要调整
    }

    # 准备请求体 (根据文档的 Text Share 示例)
    payload = {
        "author": PERSON_URN,
        "lifecycleState": "PUBLISHED", # 必须是 PUBLISHED
        "specificContent": {
            "com.linkedin.ugc.ShareContent": {
                "shareCommentary": {
                    "text": SHARE_TEXT
                },
                "shareMediaCategory": "NONE" # 文本分享设为 NONE
            }
        },
        "visibility": {
            "com.linkedin.ugc.MemberNetworkVisibility": "PUBLIC" # 或 "CONNECTIONS"
        }
    }

    print(f"正在发送 POST 请求到: {API_ENDPOINT}")
    print("请求头:")
    print(headers) # 注意：实际应用中不要打印包含 Token 的完整请求头
    print("\n请求体:")
    print(json.dumps(payload, indent=4))

    try:
        # 发送 POST 请求
        response = requests.post(API_ENDPOINT, headers=headers, json=payload, timeout=30)

        print(f"\n收到响应状态码: {response.status_code}")

        # 检查响应
        if response.status_code == 201: # 成功创建是 201 Created
            # 从响应头获取新帖子的 ID
            post_id = response.headers.get('X-Restli-Id')
            print("分享成功！")
            if post_id:
                print(f"新帖子的 URN (ID): {post_id}")
                # 您通常可以将 post_id 转换为帖子的 URL，格式可能类似于:
                # https://www.linkedin.com/feed/update/{post_id}/
                # 但具体 URL 格式最好通过实际测试或文档确认
            else:
                print("响应头中未找到 X-RestLi-Id。")
        else:
            print("分享失败！")
            # 尝试打印错误响应体
            try:
                error_data = response.json()
                print("错误响应体:")
                print(json.dumps(error_data, indent=4))
            except json.JSONDecodeError:
                print("错误响应体 (非 JSON):")
                print(response.text)

    except requests.exceptions.Timeout:
        print("\n错误：请求超时！")
    except requests.exceptions.ConnectionError:
        print("\n错误：无法连接到 LinkedIn API！")
    except requests.exceptions.RequestException as e:
        print(f"\n发生请求错误: {e}")

if __name__ == "__main__":
    share_text_on_linkedin()
