# 接口文档 (OCR 基础信息)

此文档描述了用于基础信息提取的三个核心 API：图片识别、信息保存、信息查询。

---

## 1. 图片识别接口

**功能**: 上传医学报告图片，仅识别其中的姓名、性别、年龄等基础信息。

- **URL**: `POST /images/baseinfo`
- **Content-Type**: `multipart/form-data`

### 请求参数

| 字段名 | 类型   | 必填 | 描述             |
| :----- | :----- | :--- | :--------------- |
| `file` | `File` | 是   | 待识别的图片文件 |

### 成功响应 (HTTP 200)

```json
{
  "name": "孙锐涵",
  "gender": "男",
  "age": "5岁9月"
}
```

### 失败响应

| HTTP Status | Body 示例                                         | 说明               |
| :---------- | :------------------------------------------------ | :----------------- |
| `400`       | `{"error":"需要提供图片文件。"}`                     | 未上传文件         |
| `500`       | `{"error":"OCR 服务调用失败"}`                      | 后端 Python 服务异常 |

---

## 2. 保存病例基础信息接口

**功能**: 根据病例 ID 保存或更新其基础信息（如姓名、性别、年龄）。

- **URL**: `POST /images/case/info`
- **Content-Type**: `application/json`

### 请求体 (JSON)

```json
{
  "caseId": "case_new_uuid_001421414",
  "info": {
    "name": "孙锐涵",
    "gender": "男",
    "age": "5岁9月"
  }
}
```

### 成功响应 (HTTP 200)

```json
{ "message": "保存成功" }
```

### 失败响应

| HTTP Status | Body 示例                                      | 说明                               |
| :---------- | :--------------------------------------------- | :--------------------------------- |
| `400`       | `{"error":"请求参数缺失或格式不正确"}`           | `caseId` 或 `info` 缺失，或 `info` 不是对象 |
| `500`       | `{"error":"保存信息时发生错误: <detail>"}`      | 数据库持久化异常                   |

**注意**:
- 若 `caseId` 已存在，则覆盖更新；否则为新增。
- 数据存储在 MongoDB 的 `case_base_info` 集合中。

---

## 3. 查询病例基础信息接口

**功能**: 根据病例 ID 查询已保存的基础信息。

- **URL**: `GET /images/case/info/{caseId}`

### 路径参数

| 参数名   | 类型     | 必填 | 描述         |
| :------- | :------- | :--- | :----------- |
| `caseId` | `String` | 是   | 病例的唯一ID |

### 成功响应 (HTTP 200)

```json
{
  "name": "孙锐涵",
  "gender": "男",
  "age": "5岁9月"
}
```

### 失败响应

| HTTP Status | Body 示例                                    | 说明         |
| :---------- | :------------------------------------------- | :----------- |
| `404`       | `{"error":"未找到信息"}`                       | 记录不存在   |
| `500`       | `{"error":"查询信息时发生错误: <detail>"}`   | 数据库查询异常 |

---

## 调用流程示例

1.  **通过图片识别基础信息**

    ```bash
    # 请求
    curl -X POST -F "file=@/path/to/your/report.jpg" http://localhost:9000/images/baseinfo

    # 假设返回
    # {"name":"孙锐涵","gender":"男","age":"5岁9月"}
    ```

2.  **将识别出的信息存入数据库**

    ```bash
    # 请求
    curl -X POST -H "Content-Type: application/json" \
         -d '{
               "caseId":"case_new_uuid_001421414",
               "info":{"name":"孙锐涵","gender":"男","age":"5岁9月"}
             }' \
         http://localhost:9000/images/case/info
    ```

3.  **后续可随时查询该病例的基础信息**

    ```bash
    # 请求
    curl http://localhost:9000/images/case/info/case_new_uuid_001421414
    ``` 