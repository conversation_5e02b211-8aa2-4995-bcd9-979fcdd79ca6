<template>
    <div class="chat-container">
      <div class="main-content">
        <!-- 左侧会话列表 -->
        <div class="session-list" :class="{ collapsed: isCollapsed }">
          <div class="session-header">
            <span class="user-info">
              <a-avatar :src="getAvatarSrc(userInfo?.profilePicture)" />
              <span class="username" v-show="!isCollapsed">{{ userInfo?.username || '用户' }}</span>
            </span>
          </div>
          <div class="new-session-wrapper" v-show="!isCollapsed">
            <a-button type="primary" @click="createNewSession" class="new-session-button">
              <span class="plus-icon">+</span>
              New Chat
            </a-button>
          </div>
          <div class="session-items">
            <a-list :data-source="sessions" :loading="loading">
              <template #renderItem="{ item }">
                <a-list-item
                  :class="['session-item', { active: currentSessionId === item.id }]"
                  @click="switchSession(item.id)"
                >
                  <div class="session-info" v-show="!isCollapsed">
                    <span class="session-title">会话 {{ item.id }}</span>
                    <span class="session-time">{{ formatTime(item.createdAt) }}</span>
                  </div>
                  <span class="session-number" v-show="isCollapsed">{{ item.id }}</span>
                </a-list-item>
              </template>
            </a-list>
          </div>
          <div class="collapse-button" @click="toggleCollapse">
            <svg viewBox="0 0 24 24" width="16" height="16">
              <path fill="currentColor" d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
            </svg>
          </div>
        </div>

        <!-- 右侧聊天区域 -->
        <div class="chat-main">
          <div class="chat-title">
            <span class="session-id-text">当前会话: {{ currentSessionId ? '会话 ' + currentSessionId : '请选择会话' }}</span>
            <div class="title-action-buttons" v-if="currentSessionId">
               <a-button
                  @click="openSupplementaryModal"
                  :disabled="loading || ocrProcessing || !currentSessionId"
                  class="title-action-button supplementary-button"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-pencil-square" viewBox="0 0 16 16">
                    <path d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z"/>
                    <path fill-rule="evenodd" d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5z"/>
                  </svg>
                  补充
                </a-button>
                <!-- 新增 COT 按钮 -->
                <a-button
                  @click="openCotModal"
                  :disabled="loading || ocrProcessing || !currentSessionId || !latestRealtimeCot"
                  class="title-action-button cot-button"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-journal-text" viewBox="0 0 16 16">
                    <path d="M5 10.5a.5.5 0 0 1 .5-.5h2a.5.5 0 0 1 0 1h-2a.5.5 0 0 1-.5-.5m0-2a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5m0-2a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5m0-2a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5"/>
                    <path d="M3 0h10a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2v-1h1v1a1 1 0 0 0 1 1h10a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1H3a1 1 0 0 0-1 1v1H1V2a2 2 0 0 1 2-2"/>
                    <path d="M1 5v-.5a.5.5 0 0 1 1 0V5h.5a.5.5 0 0 1 0 1h-2a.5.5 0 0 1 0-1zm0 3v-.5a.5.5 0 0 1 1 0V8h.5a.5.5 0 0 1 0 1h-2a.5.5 0 0 1 0-1zm0 3v-.5a.5.5 0 0 1 1 0v.5h.5a.5.5 0 0 1 0 1h-2a.5.5 0 0 1 0-1z"/>
                  </svg>
                  COT
                </a-button>
                <!-- 添加 Guide 按钮 - 移除v-if条件，使其始终可见 -->
                <a-button
                  @click="openGuideModal"
                  :disabled="loading || ocrProcessing || !currentSessionId"
                  class="title-action-button guide-button"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-book-half" viewBox="0 0 16 16">
                    <path d="M8.5 2.687c.654-.689 1.782-.886 3.112-.752 1.234.124 2.503.523 3.388 1.17.953.713 1.364 1.76.99 2.745l-.16.293V12a1 1 0 0 1-1 1h-2.5a1 1 0 0 1-1-1V3.727c.002-.01.005-.02.007-.03.002-.01.004-.018.006-.026zM6.25 12a.75.75 0 0 0-.75-.75H3.5a.75.75 0 0 0 0 1.5h2a.75.75 0 0 0 .75-.75"/>
                    <path d="M6.5 1.998v10.004a1 1 0 0 1-1 1H1.5a1 1 0 0 1-1-1V2.998a1 1 0 0 1 1-1h4zm-.5 0H1.5A.5.5 0 0 0 1 2.5v10.004a.5.5 0 0 0 .5.5H5a.5.5 0 0 0 .5-.5V2z"/>
                  </svg>
                  指南
                </a-button>
                <a-button
                  @click="triggerDiagnoseV2"
                  :loading="diagnoseLoading"
                  :disabled="loading || ocrProcessing || !currentSessionId || diagnoseLoading"
                  class="title-action-button diagnose-button"
                >
                   <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-clipboard2-pulse-fill" viewBox="0 0 16 16">
                     <path d="M10 .5a.5.5 0 0 0-.5-.5h-3a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 .5-.5V.5Z"/>
                     <path d="M10.002 2.5a1.499 1.499 0 0 1-1.5-1.5h-2.997A1.5 1.5 0 0 0 4 2.497V14.5a1.5 1.5 0 0 0 1.5 1.5h7a1.5 1.5 0 0 0 1.5-1.5V2.5h-1.501Z"/>
                     <path d="M9.979 5.356a.5.5 0 0 0-.968.04L7.92 10.49l-.94-3.135a.5.5 0 0 0-.928-.085L4.49 10.886a.5.5 0 0 0 .766.647l.94-1.408.956 3.567a.5.5 0 0 0 .986 0l1.09-4.071 1.186 1.779a.5.5 0 0 0 .81-.576l-1.3-2.87Z"/>
                   </svg>
                  诊断
                </a-button>
                <!-- 新增上传按钮 -->
                <a-button
                  @click="openUploadModal"
                  :disabled="loading || ocrProcessing || !currentSessionId"
                  class="title-action-button upload-button"
                >
                  <!-- 可以使用 antd 图标或 SVG -->
                  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-upload" viewBox="0 0 16 16">
                    <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z"/>
                    <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708l3-3z"/>
                  </svg>
                  上传检查报告
                </a-button>
            </div>
          </div>
          <div class="chat-content">
            <div class="chat-messages" ref="messageContainer"> <!-- Changed ref to messageContainer -->
              <template v-if="currentSessionId">
                <div
                  v-for="(displayMsg, index) in displayedMessages"
                  :key="displayMsg.id || `disp-${index}`"
                  :class="['message-item', displayMsg.speaker === 'doctor' ? 'doctor' : 'patient']"
                >
                  <a-avatar
                    :src="displayMsg.speaker === 'doctor' ? doctorLogo : getAvatarSrc(userInfo?.profilePicture)"
                    :class="displayMsg.speaker"
                  />
                  <div class="message-content">
                    <div v-html="formatMessage(displayMsg.message)"></div>
                    <!-- Optional: COT trigger next to doctor messages -->
                    <!-- <a-button v-if="displayMsg.speaker === 'doctor' && displayMsg.cot" @click="showSpecificCot(displayMsg.cot)" size="small" type="link">COT</a-button> -->
                  </div>
                </div>
                <!-- Typing indicator already exists, will be handled by isLoading or a specific typing ref -->
                <!-- Preliminary Diagnosis Loading Indicator -->
                <div v-if="preliminaryDiagnoseLoading" class="message-item doctor">
                    <a-avatar :src="doctorLogo" class="doctor" />
                    <div class="message-content">
                        <a-spin tip="Performing preliminary diagnosis..."></a-spin>
                    </div>
                </div>
              </template>
              <div v-else class="no-session">
                请选择一个会话或创建新会话
              </div>
            </div>
            <div class="loading-container" v-if="loading">
              <div class="loading-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
            <div class="chat-input-container" v-if="currentSessionId">
              <div class="input-wrapper">
                <a-textarea
                  v-model:value="inputMessage"
                  :rows="2"
                  placeholder="请输入消息..."
                  :disabled="loading || ocrProcessing || isAwaitingPreliminaryAction || preliminaryDiagnoseLoading"
                  @keypress.enter.prevent="handleKeyPress"
                  autocomplete="off"
                  class="chat-input"
                  ref="chatInputRef"
                />
                <div class="button-group">
                  <div class="voice-button" :class="{ recording: isRecording }" @click="toggleVoice" :disabled="isAwaitingPreliminaryAction || preliminaryDiagnoseLoading">
                    <img v-if="!isRecording" src="@/assets/voice-off.svg" alt="开始录音" />
                    <img v-else src="@/assets/voice.svg" alt="停止录音" />
                  </div>

                  <a-button
                    type="link"
                    class="send-button"
                    :loading="loading"
                    :disabled="loading || ocrProcessing || !inputMessage.trim() || isAwaitingPreliminaryAction || preliminaryDiagnoseLoading"
                    @click="sendMessage"
                  >
                    <span class="send-icon"></span>
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 新增上传图片组件 -->
      <a-modal
          v-model:visible="showUploadModal"
          title="上传图片"
          :footer="null" 
          @cancel="closeUploadModal" 
          width="600px" 
      >
        <!-- 放入您提供的上传组件结构 -->
        <div class="clearfix">
          <a-upload
            action=""
            list-type="picture-card"
            :file-list="uploadFileList" 
            @preview="handleUploadPreview" 
            @change="handleUploadChange" 
            :disabled="ocrProcessing"
            :customRequest="() => {}"
          >
            <div v-if="uploadFileList.length < 8 && !ocrProcessing">
              <plus-outlined /> 
              <div class="ant-upload-text">
                上传
              </div>
            </div>
          </a-upload>
          <a-modal :visible="uploadPreviewVisible" :footer="null" @cancel="handleUploadCancel"> 
            <img alt="预览" style="width: 100%" :src="uploadPreviewImage" /> 
          </a-modal>
        </div>
        <!-- 可选：在这里添加确认上传或处理的按钮 -->
        <div style="text-align: right; margin-top: 15px;">
          <a-button @click="closeUploadModal" style="margin-right: 8px;">关闭</a-button>
          <!-- 处理图片按钮已移除 -->
        </div>
      </a-modal>

      
      <!-- Supplementary Info Modal -->
      <a-modal
        v-model:visible="showSupplementaryModal"
        title="添加补充信息"
        @ok="submitSupplementaryInfo"
        :confirm-loading="supplementaryLoading"
      >
        <a-textarea
          v-model:value="supplementaryText"
          placeholder="请输入医生补充信息..."
          :rows="4"
        />
      </a-modal>

      <!-- 新增 COT Modal -->
      <a-modal
        v-model:visible="showCotModal"
        title="思考链 (Chain of Thought)"
        :footer="null"
        width="70%"
      >
        <div v-html="formattedCotDisplay" class="cot-display-wrapper"></div>
      </a-modal>

      <!-- Guide Modal HTML -->
      <a-modal
        v-model:visible="showGuideModal"
        title="诊疗指南详情"
        :footer="null"
        width="70%"
        @cancel="showGuideModal = false"
      >
        <a-spin :spinning="guideModalLoading">
          <div v-if="currentTreatmentGuideForModal && currentTreatmentGuideForModal.results" class="guide-content-wrapper" style="max-height: 70vh; overflow-y: auto; padding: 10px;">
            <div class="guide-header">
              <h2 class="guide-title">AI辅助诊疗指南</h2>
              <p class="guide-subtitle">基于临床指南生成的参考信息</p>
            </div>
            
            <div v-if="currentTreatmentGuideForModal.results && currentTreatmentGuideForModal.results.length > 0">
              <a-tabs default-active-key="1">
                <a-tab-pane v-for="(result, index) in currentTreatmentGuideForModal.results" :key="index + 1" :tab="`结果 ${index + 1}`">
                  <div class="guide-result-content">
                    <div v-if="result.title" class="guide-info-section guide-title-section">
                      <h3 class="guide-section-title">{{ result.title }}</h3>
                    </div>

                    <div v-if="result.source_file" class="guide-info-section guide-source-section">
                      <div class="guide-info-label">来源:</div>
                      <div class="guide-info-content">{{ result.source_file }}</div>
                    </div>

                    <div v-if="result.payload && result.payload.diagnostic_symptoms" class="guide-info-section guide-symptoms-section">
                      <div class="guide-info-label" style="font-weight: bold; color: #1890ff;">诊断症状:</div>
                      <div class="guide-info-content guide-symptoms-content" style="background-color: #f0f7ff; padding: 10px; border-radius: 8px;">{{ result.payload.diagnostic_symptoms }}</div>
                    </div>

                    <div v-if="result.payload && result.payload.main_disease" class="guide-info-section guide-disease-section">
                      <div class="guide-info-label" style="font-weight: bold; font-size: 1.1em;">主要疾病:</div>
                      <div class="guide-info-content" style="font-weight: bold; font-size: 1.1em;">{{ result.payload.main_disease }}</div>
                    </div>

                    <div v-if="result.payload && result.payload.relevant_content && result.payload.relevant_content.length > 0" class="guide-relevant-content">
                      <div class="guide-info-label">相关内容:</div>
                      <div class="guide-relevant-items">
                        <div v-for="(item, itemIndex) in result.payload.relevant_content" :key="itemIndex" class="guide-relevant-item">
                          <div v-if="item.title" class="guide-relevant-title">{{ item.title }}</div>
                          <div v-html="formatMarkdown(item.content)" class="guide-relevant-content-text"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </a-tab-pane>
              </a-tabs>
            </div>
          </div>
          <div v-else-if="currentTreatmentGuideForModal && Object.keys(currentTreatmentGuideForModal).length > 0" class="guide-content-wrapper" style="max-height: 70vh; overflow-y: auto; padding: 10px;">
            <!-- 处理MongoDB存储的指南内容格式 -->
            <div class="guide-header">
              <h2 class="guide-title">AI辅助诊疗指南</h2>
              <p class="guide-subtitle">基于临床指南生成的参考信息</p>
            </div>
            
            <div class="guide-mongodb-content">
              <a-card class="guide-card">
                <template #title>
                  <div class="guide-card-title">
                    <span class="guide-card-icon">📑</span>
                    指南详情
                  </div>
                </template>
                <div v-if="typeof currentTreatmentGuideForModal === 'object'" class="guide-sections">
                  <div v-for="(value, key) in guideContentDisplay(currentTreatmentGuideForModal)" :key="key" class="guide-section">
                    <h3 class="guide-section-title">{{ key }}</h3>
                    <div class="guide-section-content" v-html="formatGuideContent(value)"></div>
                  </div>
                </div>
                <div v-else class="guide-fallback-content">
                  <pre>{{ currentTreatmentGuideForModal }}</pre>
                </div>
              </a-card>
            </div>
          </div>
          <div v-else class="guide-empty-content" style="text-align: center; padding: 40px;">
            <a-empty description="暂无可用的指南数据" />
            <div style="margin-top: 20px;">
              <a-button type="primary" @click="handleGuideRetry">重新获取</a-button>
            </div>
          </div>
        </a-spin>
      </a-modal>

      <!-- Enhanced Preliminary Diagnosis Modal -->
      <a-modal
          v-model:visible="showPreliminaryDiagnosisModal"
          title="智能诊断助手"
          :maskClosable="false"
          :closable="false"
          :width="500"
          centered
      >
        <div class="preliminary-modal-content">
          <div class="preliminary-modal-header">
            <div class="prelim-modal-icon">🏥</div>
            <div class="prelim-modal-title">AI已生成初步诊断策略</div>
          </div>
          
          <div class="preliminary-modal-description">
            <p>根据患者提供的信息，AI已经形成初步诊断策略。您可以选择以下操作：</p>
          </div>
          
          <div class="preliminary-modal-actions">
            <div class="action-item">
              <div class="action-icon">📋</div>
              <div class="action-content">
                <div class="action-title">完成诊断</div>
                <div class="action-description">AI将生成完整的诊断报告、病情分析和治疗建议</div>
                <a-button type="primary" block @click="handlePreliminaryComplete" :loading="preliminaryDiagnoseLoading">
                  完成诊断
                </a-button>
              </div>
            </div>
            
            <div class="action-item">
              <div class="action-icon">📝</div>
              <div class="action-content">
                <div class="action-title">医生补充信息</div>
                <div class="action-description">添加更多医学信息辅助AI进行更精确的诊断</div>
                <a-button block @click="triggerSupplementaryFromPreliminary" :disabled="preliminaryDiagnoseLoading">
                  添加补充信息
                </a-button>
              </div>
            </div>
            
            <div class="action-item">
              <div class="action-icon">🔍</div>
              <div class="action-content">
                <div class="action-title">上传检查报告</div>
                <div class="action-description">上传医学检查报告提供更多诊断依据</div>
                <a-button block @click="triggerUploadFromPreliminary" :disabled="preliminaryDiagnoseLoading">
                  上传报告
                </a-button>
              </div>
            </div>
          </div>
        </div>
        
        <template #footer>
          <div class="preliminary-modal-footer">
            <a-button key="skip" @click="handlePreliminarySkip" :disabled="preliminaryDiagnoseLoading">暂不处理</a-button>
          </div>
        </template>
      </a-modal>

      <!-- Upload Modal -->
      <a-modal
        v-model:visible="uploadModalVisible"
        title="Upload Medical Report"
        @ok="handleUploadOk"
        @cancel="handleUploadCancel"
        :confirmLoading="uploadLoading"
      >
        <!-- ... existing code ... -->
      </a-modal>

      <!-- Supplementary Info Modal -->
      <a-modal
        v-model:visible="supplementaryModalVisible"
        title="Supplementary Information"
        @ok="handleSupplementaryOk"
        @cancel="handleSupplementaryCancel"
        :confirmLoading="supplementaryLoading"
        width="600px"
      >
        <!-- ... existing code ... -->
      </a-modal>

      <div class="chat-input-area">
        <a-textarea
          v-model:value="inputMessage"
          placeholder="Type your message here..."
          :rows="3"
          @keypress.enter.exact.prevent="sendMessage"
          :disabled="isLoading || isAwaitingPreliminaryAction || preliminaryDiagnoseLoading"
        />
        <div class="input-actions">
          <a-button type="primary" @click="sendMessage" :loading="isLoading" :disabled="isLoading || isAwaitingPreliminaryAction || preliminaryDiagnoseLoading">
            Send
          </a-button>
          <a-button @click="handleInquiry('commonCold')" :disabled="isLoading || isAwaitingPreliminaryAction || preliminaryDiagnoseLoading">Common Cold Inquiry</a-button>
          <a-button @click="handleInquiry('gastroenteritis')" :disabled="isLoading || isAwaitingPreliminaryAction || preliminaryDiagnoseLoading">Gastroenteritis Inquiry</a-button>
          <a-button @click="openUploadModal" :disabled="isLoading || isAwaitingPreliminaryAction || preliminaryDiagnoseLoading">Upload Report</a-button>
          <a-button @click="openSupplementaryModal" :disabled="isLoading || isAwaitingPreliminaryAction || preliminaryDiagnoseLoading">Supplementary Info</a-button>
          <a-button @click="resetSession" :disabled="isLoading || isAwaitingPreliminaryAction || preliminaryDiagnoseLoading">Reset Session</a-button>
        </div>
      </div>
    </div>
  </template>
  <script>
  import { ref, computed, onMounted, nextTick, reactive, onUnmounted, watch } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { message, Modal as AModal, List as AList, Avatar as AAvatar, Button as AButton, Textarea as ATextarea, Upload } from 'ant-design-vue'
  import { PlusOutlined, LoadingOutlined } from '@ant-design/icons-vue'; // 确认 PlusOutlined 已导入
  import request from '@/utils/request'
  import { useUserStore } from '@/stores/user'
  import doctorLogo from '@/assets/doctor.png'
  
  export default {
    name: 'ChatV2Page',
    components: { AModal, AList, AAvatar, AButton, ATextarea, Upload, PlusOutlined, LoadingOutlined },
    setup() {
      const route = useRoute()
      const router = useRouter()
      const loading = ref(false)
      const userStore = useUserStore()
      const userInfo = ref({ userId: null, username: '', profilePicture: '', bio: '' })
      const sessions = ref([])
      const currentSessionId = ref(null)
      const inputMessage = ref('')
      const messageContainer = ref(null)
      const isCollapsed = ref(false)
      const chatInputRef = ref(null)
      const sessionMessages = reactive({})
      let messageObserver = null
      const isSwitchingSession = ref(false)
      const isRecording = ref(false)
      const mediaRecorder = ref(null)
      const audioChunks = ref([])
      let currentEventSource = null
      let currentAudio = null;
      let lastReceivedAudioUrl = null;
      
      const showSupplementaryModal = ref(false)
      const supplementaryText = ref('')
      const supplementaryLoading = ref(false)
      const diagnoseLoading = ref(false)
      
      const showCotModal = ref(false);
      // *** 修改: 存储解析后的实时 COT 对象或错误信息 ***
      const latestRealtimeCot = ref(null); 
      const currentTreatmentGuideForModal = ref(null);
      const showGuideModal = ref(false);
      
      const currentRawMessages = computed(() => {
        return currentSessionId.value ? (sessionMessages[currentSessionId.value] || []) : [];
      })
      
      const parseAndExtractV2Data = (messageContent) => {
        if (typeof messageContent !== 'string' || !messageContent.trim()) {
          return null;
        }
        const trimmedContent = messageContent.trim();

        if (trimmedContent.startsWith('{') && trimmedContent.endsWith('}')) {
          try {
            const parsed = JSON.parse(trimmedContent);
            
            let historySource = parsed; // 默认从顶层读取
            // *** 修改: 检查是否存在 interaction_history ***
            if (parsed.interaction_history && typeof parsed.interaction_history === 'object') {
              console.log('[ChatV2 parseAndExtract] Found interaction_history, using it as source.')
              historySource = parsed.interaction_history; // 如果存在，则从它内部读取
            }

            // *** 修改: 从确定的来源读取 ***
            const entries = historySource.cot_entries;
            const diagnosis = historySource.diagnosis || null;
            const treatmentRecommendation = historySource.treatment_recommendation || null;
            const lastObservation = historySource.lastObservation || null; // <-- 提取 lastObservation
            const treatmentGuide = historySource.treatment_guide || null; // <-- 提取 treatment_guide
            const condition = historySource.condition || null; // <-- 提取 condition
            // 提取初步诊断和检查建议
            const preliminaryDiagnosis = historySource.preliminary_diagnosis || null;
            const inspectionSuggestions = historySource.inspection_suggestions || null;

            let dialogueTurns = [];
            let lastStrategy = null;

            if (entries && Array.isArray(entries)) {
              for (const entry of entries) {
                if (entry?.dialogue_history && Array.isArray(entry.dialogue_history)) {
                    const turnsWithDefaults = entry.dialogue_history.map(turn => ({
                        role: turn.role || 'unknown',
                        content: turn.content || '',
                        ...turn // 保留其他可能的字段
                    }));
                    dialogueTurns = dialogueTurns.concat(turnsWithDefaults);
                }
              }
              if (entries.length > 0) {
                const lastEntry = entries[entries.length - 1];
                 if (lastEntry && lastEntry.strategy && typeof lastEntry.strategy === 'string') {
                     lastStrategy = lastEntry.strategy.trim();
                     if (lastStrategy === "") lastStrategy = null;
                 }
              }
            }
              
            // *** 新增日志 ***
            console.log('[ChatV2 parseAndExtract Result]', { 
                dialogueTurns: dialogueTurns?.length, 
                lastStrategy, 
                diagnosis, 
                treatmentRecommendation, 
                lastObservation, // <-- 添加到日志
                treatmentGuide,  // <-- 添加到日志
                condition,  // <-- 添加到日志
                preliminaryDiagnosis, // <-- 添加到日志
                inspectionSuggestions // <-- 添加到日志
            });
            
            // 如果有treatmentGuide，设置到全局state中
            if (treatmentGuide && typeof treatmentGuide === 'object' && treatmentGuide.query) {
                console.log('[ChatV2] Setting treatmentGuide from parsed message');
                currentTreatmentGuideForModal.value = treatmentGuide;
            }
            
            return { 
              dialogueTurns, 
              lastStrategy, 
              diagnosis, 
              treatmentRecommendation, 
              lastObservation,
              treatmentGuide,
              condition,
              preliminaryDiagnosis, // <-- 返回初步诊断
              inspectionSuggestions // <-- 返回检查建议
            }; // <-- 返回 condition

          } catch (e) {
            console.error("[ChatV2 parseAndExtract] Failed to parse V2 JSON:", e, "Content snippet:", trimmedContent.substring(0, 100));
            return null;
          }
        } else {
          return null;
        }
      };
  
      // Helper function to format the parsed treatment plan JSON
      const formatTreatmentPlan = (plan) => {
        if (!plan || typeof plan !== 'object') {
          return '无法解析治疗计划内容。';
        }

        let html = '';

        // Display Drugs as JSON string
        if (plan['药品'] && Array.isArray(plan['药品'])) {
          html += '<strong>药品：</strong><br>';
          try {
            // Use JSON.stringify to show the raw array data, with indentation for readability
            html += `<pre style="white-space: pre-wrap; word-break: break-all; margin: 0; padding: 5px; background-color: #f8f8f8; border-radius: 4px;">${JSON.stringify(plan['药品'], null, 2)}</pre>`;
          } catch (e) {
            html += '无法格式化药品信息。<br>';
            console.error("Error stringifying drugs:", e);
          }
        } else {
          html += '<strong>药品：</strong>无<br>';
        }

        // Display Life Advice as JSON string (or simple list if preferred later)
        if (plan['生活建议'] && Array.isArray(plan['生活建议'])) {
          html += '<br><strong>生活建议：</strong><br>';
           try {
              // Use JSON.stringify to show the raw array data
              html += `<pre style="white-space: pre-wrap; word-break: break-all; margin: 0; padding: 5px; background-color: #f8f8f8; border-radius: 4px;">${JSON.stringify(plan['生活建议'], null, 2)}</pre>`;
           } catch (e) {
             html += '无法格式化生活建议。<br>';
             console.error("Error stringifying life advice:", e);
           }
        } else {
           html += '<br><strong>生活建议：</strong>无<br>';
        }

         // Keep hospitalization info formatting
         if (plan.hasOwnProperty('是否住院')) {
             // *** 修改: 直接比较字符串值 ***
             const isHospitalized = String(plan['是否住院']).trim() === '是';
             html += `<br><strong>是否住院：</strong> ${isHospitalized ? '是' : '否'}<br>`; 
             if (plan['住院理由'] && String(plan['住院理由']).trim()) {
                  html += `<strong>住院理由：</strong> ${String(plan['住院理由']).trim()}<br>`;
             }
         }

        return html;
      };
  
      const displayedMessages = computed(() => {
        const messagesToDisplay = [];
        const rawMessages = currentRawMessages.value;

        for (let i = 0; i < rawMessages.length; i++) {
          const msg = rawMessages[i];
          let messageContent = msg.message;

          let isV2Structure = false;
          let parsedResult = null;

          if (typeof messageContent === 'string') {
            const trimmedContent = messageContent.trim();
            if (trimmedContent.startsWith('{') && trimmedContent.endsWith('}') && trimmedContent.includes('"interaction_history"')) {
              try {
                parsedResult = parseAndExtractV2Data(messageContent);
                if (parsedResult) {
                  isV2Structure = true;
                  // 检查是否有treatment_guide数据，更新到全局状态
                  if (parsedResult.treatmentGuide && typeof parsedResult.treatmentGuide === 'object' && parsedResult.treatmentGuide.query) {
                    console.log('[ChatV2 displayedMessages] Found treatmentGuide in parsed message');
                    currentTreatmentGuideForModal.value = parsedResult.treatmentGuide;
                  }
                }
              } catch(e) {
                console.error("Error parsing potential V2 JSON in displayedMessages:", e);
                isV2Structure = false;
              }
            }
          }

          if (isV2Structure && parsedResult) {
            console.log('[ChatV2 displayedMessages] Processing V2 message, parsedResult:', JSON.parse(JSON.stringify(parsedResult || {})));

            parsedResult.dialogueTurns.forEach((turn, index) => {
              messagesToDisplay.push({
                id: `${msg.id || 'msg'}-${i}-turn-${index}`,
                speaker: turn.role === 'doctor' ? 'doctor' : 'patient',
                message: turn.content,
                createdAt: turn.createdAt || msg.createdAt
              });
            });

            const strategyContent = parsedResult.lastStrategy;
            if (strategyContent && typeof strategyContent === 'string' && strategyContent.includes('诊断：')) {
              console.log('[ChatV2 displayedMessages] Found strategy with diagnosis:', strategyContent);
              messagesToDisplay.push({
                  id: `${msg.id || 'msg'}-${i}-strategy`,
                  speaker: 'doctor',
                  message: strategyContent,
                  createdAt: msg.createdAt
              });
            }

            // --- Final Diagnosis and Observation Display ---
            const finalDiagnosis = parsedResult.diagnosis;
            const lastObservation = parsedResult.lastObservation; // Get observation
            const patientCondition = parsedResult.condition; // Get condition for prescription
            const preliminaryDiagnosis = parsedResult.preliminaryDiagnosis;
            const inspectionSuggestions = parsedResult.inspectionSuggestions;

            // Combine diagnosis and observation for display if both exist
            let diagnosisDisplayMessage = '';
            
            // 添加初步诊断如果存在
            if (preliminaryDiagnosis && String(preliminaryDiagnosis).trim()) {
              diagnosisDisplayMessage += `<div class="preliminary-diagnosis-section">
                <div class="preliminary-diagnosis-header">
                  <span class="preliminary-diagnosis-icon">🔍</span>
                  <div class="preliminary-diagnosis-title">初步诊断</div>
                </div>
                <div class="preliminary-diagnosis-content">${String(preliminaryDiagnosis).trim()}</div>
              </div>`;
            }

            // 添加检查建议如果存在
            if (inspectionSuggestions) {
              if (diagnosisDisplayMessage) {
                diagnosisDisplayMessage += '\n\n';
              }
              
              let inspectionContent = '';
              if (typeof inspectionSuggestions === 'string') {
                inspectionContent = String(inspectionSuggestions).trim();
              } else if (Array.isArray(inspectionSuggestions)) {
                inspectionContent = '<ul class="inspection-list">' + 
                  inspectionSuggestions.map(item => `<li class="inspection-item"><span class="inspection-bullet">•</span> ${String(item).trim()}</li>`).join('') + 
                  '</ul>';
              } else if (typeof inspectionSuggestions === 'object') {
                try {
                  inspectionContent = '<pre class="inspection-json">' + JSON.stringify(inspectionSuggestions, null, 2) + '</pre>';
                } catch (e) {
                  inspectionContent = '无法解析检查建议内容';
                }
              }
              
              diagnosisDisplayMessage += `<div class="inspection-suggestions-section">
                <div class="inspection-suggestions-header">
                  <span class="inspection-suggestions-icon">📋</span>
                  <div class="inspection-suggestions-title">检查建议</div>
                </div>
                <div class="inspection-suggestions-content">${inspectionContent}</div>
              </div>`;
            }

            // 添加最终诊断如果存在
            if (finalDiagnosis && String(finalDiagnosis).trim()) {
              if (diagnosisDisplayMessage) {
                diagnosisDisplayMessage += '\n\n';
              }
              diagnosisDisplayMessage += `<div class="diagnosis-section">
                <div class="diagnosis-title">最终诊断</div>
                <div class="diagnosis-content">${String(finalDiagnosis).trim()}</div>
              </div>`;
            }

            // Add patient condition for prescription if it exists
            if (patientCondition && String(patientCondition).trim()) {
              if (diagnosisDisplayMessage) { // Add separator if diagnosis exists
                diagnosisDisplayMessage += '\n\n';
              }
              diagnosisDisplayMessage += `<div class="condition-section">
                <div class="condition-title">用于开药患者症状</div>
                <div class="condition-content">${String(patientCondition).trim()}</div>
              </div>`;
            }

            // Add observation if it exists
            if (lastObservation && String(lastObservation).trim()) {
               if (diagnosisDisplayMessage) { // Add separator if diagnosis exists
                   diagnosisDisplayMessage += '\n\n';
               }
               diagnosisDisplayMessage += `<div class="observation-section">
                 <div class="observation-title">观察记录 (一诉五史)</div>
                 <div class="observation-content">${formatSymptomText(String(lastObservation).trim())}</div>
               </div>`;
            }

            // Only push if there's something to display (either diagnosis or observation or both)
            if (diagnosisDisplayMessage.trim()) {
               messagesToDisplay.push({
                  id: `${msg.id || 'msg'}-${i}-final-diagnosis-obs`, // Use a distinct ID
                  speaker: 'doctor',
                  message: `<div class="diagnosis-observation-container">${diagnosisDisplayMessage}</div>`, // Display combined message
                  createdAt: msg.createdAt
               });
               console.log('[ChatV2 displayedMessages] Added final diagnosis/observation block.');
            }
            // --- End Final Diagnosis and Observation Display ---

            // --- Treatment Recommendation Handling ---
            const treatmentRec = parsedResult.treatmentRecommendation;
            // 修改：只有当存在诊断结果时才显示治疗建议
            if (finalDiagnosis && treatmentRec) {
              let treatmentRecStr = '';
              if (typeof treatmentRec === 'object') {
                // If it's an object, try to stringify it prettily for display, or handle specific structure
                try {
                  treatmentRecStr = JSON.stringify(treatmentRec, null, 2);
                } catch (e) {
                  treatmentRecStr = String(treatmentRec);
                }
              } else {
                treatmentRecStr = String(treatmentRec);
              }


              if (treatmentRecStr.trim()) {
                const jsonStartMarker = '最终回答:\n{';
                const jsonStartIndex = treatmentRecStr.indexOf(jsonStartMarker);
                let reasoningText = '';
                let formattedPlan = '';
                let displayMessage = ''; // Initialize displayMessage

                if (jsonStartIndex !== -1) {
                  // Extract reasoning part (before the marker)
                  reasoningText = treatmentRecStr.substring(0, jsonStartIndex)
                                          .replace(/^无法解析诊疗计划:\s*推理过程:/, '') // Remove prefix more reliably
                                          .trim();

                  // Try to extract and parse the nested JSON
                  const jsonString = treatmentRecStr.substring(jsonStartIndex + jsonStartMarker.length - 1); // Get the part starting with '{'
                  console.log('[ChatV2 Treatment] Extracted potential JSON string:', jsonString);
                  try {
                    const parsedPlan = JSON.parse(jsonString);
                    console.log('[ChatV2 Treatment] Successfully parsed treatment plan JSON:', parsedPlan);
                    formattedPlan = formatTreatmentPlan(parsedPlan); // Use the helper function
                  } catch (parseError) {
                    console.error('[ChatV2 Treatment] Failed to parse treatment plan JSON:', parseError, 'Falling back to raw string.');
                    // Fallback: show raw string as plan if parsing fails
                    formattedPlan = treatmentRecStr.substring(jsonStartIndex).trim(); // Show part from marker onwards
                  }
                } else {
                  // If the marker isn't found, display the original string as the plan
                  console.log('[ChatV2 Treatment] "最终回答:" marker not found, displaying raw string as plan.');
                  formattedPlan = treatmentRecStr.trim();
                }

                // Construct the final display message including reasoning and plan
                if (reasoningText) {
                  displayMessage += `推理过程：\n${reasoningText}\n\n`;
                }
                displayMessage += `治疗建议：\n${formattedPlan || '无有效内容'}`;

                messagesToDisplay.push({
                  id: `${msg.id || 'msg'}-${i}-treatment`,
                  speaker: 'doctor',
                  message: displayMessage, // Use the combined message
                  createdAt: msg.createdAt
                });
              }
            }
            // --- End Treatment Recommendation Handling ---

          } else {
            if (msg.message !== '正在思考中...') {
              messagesToDisplay.push({
                  ...msg,
                  id: msg.id || `msg-${i}-direct`
              });
            }
          }
        }
        return messagesToDisplay;
      });
  
      const formatTime = (timestamp) => {
        if (!timestamp) return '';
        const date = new Date(timestamp);
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
      }
  
      const formatMessage = (content) => {
        if (!content) return '';
        
        // 转换转义字符到实际字符
        let processedContent = String(content)
          .replace(/\\n/g, '\n')  // 将\n字符串转换为实际换行符
          .replace(/\\"/g, '"')   // 将\"转换为实际引号
          .replace(/\\t/g, '\t'); // 将\t转换为实际制表符
          
        // 处理包含治疗建议和药品的消息
        if (processedContent.includes('治疗建议：') || 
            processedContent.includes('药品名:') || 
            processedContent.includes('药品名：') ||
            processedContent.includes('药品推荐：')) {
          
          // 标准化换行符（解决不同操作系统换行符问题）
          processedContent = processedContent.replace(/\r\n/g, '\n');
          
          // 分割推理过程和治疗建议部分，但保留两者
          let treatmentPart = processedContent;
          let reasoningPart = '';
          
          const reasoningIndex = processedContent.indexOf('推理过程：');
          if (reasoningIndex !== -1) {
            treatmentPart = processedContent.substring(0, reasoningIndex).trim();
            reasoningPart = processedContent.substring(reasoningIndex).trim();
          }
          
          // 将多个连续换行压缩为两个换行（一个段落间隔）
          treatmentPart = treatmentPart.replace(/\n{3,}/g, '\n\n');
          
          // 将分隔线转换为HTML分隔线
          treatmentPart = treatmentPart.replace(/[-]{5,}/g, '<hr class="treatment-divider">');
          
          // 检测药品部分
          const hasMedications = treatmentPart.includes('药品名:') || 
                                treatmentPart.includes('药品名：') || 
                                treatmentPart.includes('药品推荐：');
          
          // 创建药品卡片
          if (hasMedications) {
            // 处理"药品推荐："格式
            if (treatmentPart.includes('药品推荐：')) {
              treatmentPart = treatmentPart.replace('药品推荐：', '<div class="medications-title">药品推荐</div>');
            }
            
            // 使用正则表达式识别药品块（从"药品名"开始到下一个"药品名"或结尾）
            let medicationBlocks = [];
            const medNameRegex = /(药品名[:：])\s*([^\n]+)/g;
            let medMatch;
            let lastIndex = 0;
            let medStartIndices = [];
            
            // 找出所有药品名的位置
            while ((medMatch = medNameRegex.exec(treatmentPart)) !== null) {
              medStartIndices.push(medMatch.index);
            }
            
            // 如果没有找到药品名格式，尝试直接转换整个部分
            if (medStartIndices.length === 0) {
              // 将药品部分包装成通用卡片
              const medContentStartIndex = treatmentPart.indexOf('药品推荐：') + '药品推荐：'.length;
              const medContent = treatmentPart.substring(medContentStartIndex).trim();
              const medEndIndex = medContent.indexOf('生活建议：') !== -1 ? 
                                 medContentStartIndex + medContent.indexOf('生活建议：') : treatmentPart.length;
              
              const medPart = treatmentPart.substring(medContentStartIndex, medEndIndex).trim();
              const formattedMedPart = '<div class="medications-container"><div class="medication-card">' + 
                                      medPart.replace(/\n/g, '<br>') + '</div></div>';
              
              treatmentPart = treatmentPart.substring(0, medContentStartIndex) + 
                             formattedMedPart + 
                             (medEndIndex < treatmentPart.length ? treatmentPart.substring(medEndIndex) : '');
            } else {
              // 处理每个药品块
              let formattedMeds = '<div class="medications-container">';
              
              for (let i = 0; i < medStartIndices.length; i++) {
                const startIdx = medStartIndices[i];
                const endIdx = (i < medStartIndices.length - 1) ? medStartIndices[i+1] : treatmentPart.length;
                const medBlock = treatmentPart.substring(startIdx, endIdx);
                
                // 提取药品名
                const nameMatch = medBlock.match(/(药品名[:：])\s*([^\n]+)/);
                const medName = nameMatch ? nameMatch[2].trim() : "药品";
                
                // 提取各个字段
                const specMatch = medBlock.match(/(规格[:：])\s*([^\n]+)/);
                const methodMatch = medBlock.match(/(服用方法[:：])\s*([^\n]+)/);
                const dosageMatch = medBlock.match(/(剂量安排[:：])\s*([^\n]+)/);
                const purposeMatch = medBlock.match(/(使用目的[:：])\s*([^\n]+)/);
                
                formattedMeds += `
                  <div class="medication-card">
                    <div class="medication-header">
                      <span class="med-icon">💊</span>
                      <span class="med-name">${medName}</span>
                    </div>
                    <div class="medication-details">
                      ${specMatch ? `<div class="med-detail"><span class="med-label">规格:</span> ${specMatch[2]}</div>` : ''}
                      ${methodMatch ? `<div class="med-detail"><span class="med-label">服用方法:</span> ${methodMatch[2]}</div>` : ''}
                      ${dosageMatch ? `<div class="med-detail"><span class="med-label">剂量安排:</span> ${dosageMatch[2]}</div>` : ''}
                      ${purposeMatch ? `<div class="med-detail"><span class="med-label">使用目的:</span> ${purposeMatch[2]}</div>` : ''}
                    </div>
                  </div>
                `;
              }
              
              // 关闭药品卡片容器
              formattedMeds += '</div>';
              
              // 替换原始药品部分
              const medSectionStart = medStartIndices[0];
              let medSectionEnd = treatmentPart.indexOf('生活建议：');
              
              if (medSectionEnd === -1) {
                medSectionEnd = treatmentPart.indexOf('------------------------------');
                if (medSectionEnd === -1) {
                  medSectionEnd = treatmentPart.length;
                }
              }
              
              const beforeMeds = treatmentPart.substring(0, medSectionStart);
              const afterMeds = medSectionEnd !== -1 && medSectionEnd < treatmentPart.length ? 
                              treatmentPart.substring(medSectionEnd) : '';
              
              treatmentPart = beforeMeds + formattedMeds + afterMeds;
            }
          } else {
            // 为药品详情添加样式（旧方法，当新方法失败时使用）
          treatmentPart = treatmentPart.replace(/(药品名|规格|服用方法|剂量安排|使用目的)[:：]\s*([^\n]+)/g, 
            '<div style="margin: 5px 0;"><span style="font-weight: bold; color: #1890ff;">$1:</span> $2</div>');
          }
          
          // 处理生活建议部分
          if (treatmentPart.includes('生活建议：')) {
            treatmentPart = treatmentPart.replace('生活建议：', 
              '<div class="lifestyle-header">生活建议</div><div class="lifestyle-content">');
            
            // 处理生活建议中的列表项
            treatmentPart = treatmentPart.replace(/- (.+?)(?=<br>|$)/g, 
              '<div class="lifestyle-item"><span class="lifestyle-bullet">•</span> $1</div>');
            
            // 确保生活建议部分正确关闭
            const lifeAdviceStartIdx = treatmentPart.indexOf('lifestyle-content') + 'lifestyle-content'.length + 2;
            let lifeAdviceEndIdx = treatmentPart.indexOf('推理过程：');
            
            if (lifeAdviceEndIdx === -1) {
              lifeAdviceEndIdx = treatmentPart.length;
            }
            
            // 关闭lifestyle-content div
            if (lifeAdviceStartIdx < lifeAdviceEndIdx) {
              treatmentPart = treatmentPart.substring(0, lifeAdviceEndIdx) + '</div>' + treatmentPart.substring(lifeAdviceEndIdx);
            }
          } else if (treatmentPart.includes('------------------------------') && treatmentPart.split('------------------------------')[1].trim().startsWith('生活建议')) {
            // 处理使用分隔线后的生活建议
            const parts = treatmentPart.split('------------------------------');
            if (parts.length > 1) {
              const lifestylePart = parts[1].trim();
              
              // 替换生活建议标题
              const formattedLifestyle = '<div class="lifestyle-header">生活建议</div><div class="lifestyle-content">' +
                                       lifestylePart.replace(/生活建议：?/, '')
                                       .replace(/- (.+?)(?=<br>|$)/g, 
                                       '<div class="lifestyle-item"><span class="lifestyle-bullet">•</span> $1</div>') +
                                       '</div>';
              
              treatmentPart = parts[0] + formattedLifestyle;
            }
          }
          
          // 包装治疗建议部分成卡片样式
          treatmentPart = '<div class="treatment-container">' + 
                          treatmentPart.replace(/治疗建议：/g, '<div class="treatment-header">治疗建议</div>') + 
                          '</div>';
          
          // 处理推理过程部分(如果存在)
          if (reasoningPart) {
            // 将推理过程格式化为引用块样式
            reasoningPart = reasoningPart.replace(/推理过程：/g, 
              '<div class="reasoning-header"><span class="reasoning-icon">💡</span>推理过程</div>');
            
            // 处理结构化的推理内容
            reasoningPart = reasoningPart.replace(/好的，我需要处理这个(.*?)病例/g, 
              '<div class="reasoning-step"><span class="reasoning-step-marker">分析：</span>我需要处理这个$1病例</div>');
            
            // 给关键句子添加高亮
            reasoningPart = reasoningPart.replace(/(首先|接下来|查看|分析|综合|总结)([^。，；：？！]+[。，；：？！])/g, 
              '<span class="reasoning-highlight">$1</span>$2');
            
            // 处理要点列表
            reasoningPart = reasoningPart.replace(/([1-9]\. )(.*?)(?=<br>|$)/g, 
              '<div class="reasoning-point"><span class="reasoning-number">$1</span>$2</div>');
            
            // 处理药物提及
            reasoningPart = reasoningPart.replace(/(头孢|莫米松|青霉素|阿莫西林|布洛芬|对乙酰氨基酚|氨溴索|沙丁胺醇|羟甲唑啉)(片|胶囊|颗粒|糖浆|喷雾剂|注射液|溶液|滴剂)/g,
              '<span class="reasoning-med">$1$2</span>');
              
            // 处理诊断名称
            reasoningPart = reasoningPart.replace(/(急性|慢性|细菌性|病毒性|过敏性|化脓性|支原体)(鼻窦炎|中耳炎|咽炎|扁桃体炎|支气管炎|肺炎|胃肠炎|腹泻|感染)/g,
              '<span class="reasoning-diagnosis">$1$2</span>');
              
            // 处理段落分隔
            reasoningPart = reasoningPart.replace(/\n\n/g, '<div class="reasoning-separator"></div>');
            
            reasoningPart = '<div class="reasoning-container">' + 
                           reasoningPart + '</div>';
          }
          
          // 组合两部分
          processedContent = treatmentPart + (reasoningPart ? reasoningPart : '');
        }
        
        // 处理基本换行
        return processedContent.replace(/\n/g, '<br>').trim();
      }
  
      // 新增：处理markdown格式的内容
      const formatMarkdown = (content) => {
        if (!content) return '';
        
        // 处理数组格式 - 如果内容是数组，则将其连接为字符串
        if (Array.isArray(content)) {
          if (content.length === 1 && typeof content[0] === 'string') {
            // 如果数组只有一个字符串元素，直接使用它
            content = content[0];
          } else {
            // 否则，尝试将数组连接成一个字符串
            content = content.join('');
          }
        }
        
        // 确保content是一个字符串
        if (typeof content !== 'string') {
          try {
            content = String(content);
          } catch (e) {
            return '无法显示内容';
          }
        }
        
        let formatted = content;
        
        // 检测单个字符逐行显示的情况并修复
        if (formatted.length > 3 && 
            formatted.split('').join('\n') === formatted || 
            formatted.split('').join('\r\n') === formatted) {
          // 如果内容是每个字符一行，则合并为正常文本
          formatted = formatted.replace(/\n|\r\n/g, '');
        }
        
        // 基本Markdown格式处理
        // 1. 处理标题 (# Heading)
        formatted = formatted.replace(/### (.*?)($|\n)/g, '<h3>$1</h3>');
        formatted = formatted.replace(/## (.*?)($|\n)/g, '<h2>$1</h2>');
        formatted = formatted.replace(/# (.*?)($|\n)/g, '<h1>$1</h1>');
        
        // 2. 保留原始换行符，但转换为HTML
        formatted = formatted.replace(/\n/g, '<br>');
        
        // 3. 处理粗体 (**text**)
        formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        
        // 4. 处理斜体 (*text*)
        formatted = formatted.replace(/\*(.*?)\*/g, '<em>$1</em>');
        
        // 5. 处理链接 [text](url)
        formatted = formatted.replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2" target="_blank">$1</a>');
        
        return formatted.trim();
      }
      
      const isV2SessionFormat = (messageContent) => {
        if (typeof messageContent !== 'string' || !messageContent.trim()) {
          return false;
        }
        const trimmedContent = messageContent.trim();

        // *** 修改: 检查 interaction_history 或 (cot_entries 或 diagnosis) ***
        if (trimmedContent.startsWith('{') && trimmedContent.endsWith('}')) {
          try {
            const parsedObject = JSON.parse(trimmedContent);
            if (parsedObject && typeof parsedObject === 'object') {
              // 检查是否存在 interaction_history 或者 (cot_entries 或 diagnosis)
              const hasInteractionHistory = parsedObject.hasOwnProperty('interaction_history');
              const hasCotOrDiagnosis = parsedObject.hasOwnProperty('cot_entries') || parsedObject.hasOwnProperty('diagnosis');
              
              if (hasInteractionHistory || hasCotOrDiagnosis) {
                // console.log('[ChatV2 isV2SessionFormat] Found V2 signature (interaction_history or cot_entries/diagnosis).');
                return true;
              } else {
                // console.log('[ChatV2 isV2SessionFormat] Parsed JSON but did not find V2 signature.');
                return false;
              }
            } else {
                 // console.log('[ChatV2 isV2SessionFormat] Parsed content is not an object.');
                return false;
            }
          } catch (e) {
            // console.warn('[ChatV2 isV2SessionFormat] Failed to parse content as JSON:', e.message);
            return false; 
          }
        } else {
          // console.log('[ChatV2 isV2SessionFormat] Content does not start/end with braces.');
        return false;
        }
      };
  
      const fetchMessagesForSession = async (sessionId, silent = false) => {
        if (!sessionId) return;
        console.log(`[ChatV2 fetchMessages] Attempting to fetch full messages for session ${sessionId}`);
        try {
            const msgResponse = await request({
                url: '/message/getSessionMessages',
                method: 'get',
                params: { sessionId: sessionId }
            });
            if (msgResponse.code === 200 && msgResponse.content) {
                sessionMessages[sessionId] = msgResponse.content;
                console.log(`[ChatV2 fetchMessages] Successfully fetched ${msgResponse.content.length} messages for session ${sessionId}.`);
                saveMessagesToLocal();
            } else {
                 console.warn(`[ChatV2 fetchMessages] Failed to fetch messages for session ${sessionId} or no messages found.`);
                 sessionMessages[sessionId] = [];
            }
        } catch (error) {
            console.error(`[ChatV2 fetchMessages] Error fetching messages for session ${sessionId}:`, error);
            sessionMessages[sessionId] = [];
        }
    };
  
      // 添加新函数: 从会话消息中提取COT数据
      const extractCotFromSessionMessages = (sessionId) => {
        if (!sessionMessages[sessionId] || sessionMessages[sessionId].length === 0) return;
        
        // 获取最后几条消息(查找最近的包含COT数据的消息)
        const recentMessages = sessionMessages[sessionId].slice(-5).reverse();
        
        for (const msg of recentMessages) {
          if (typeof msg.message === 'string') {
            try {
              // 尝试解析消息内容
              if (msg.message.trim().startsWith('{') && msg.message.trim().endsWith('}')) {
                const parsed = JSON.parse(msg.message);
                
                // 检查是否包含interaction_history
                const historySource = parsed.interaction_history || parsed;
                
                // 如果包含cot_entries，则提取并设置
                if (historySource.cot_entries && Array.isArray(historySource.cot_entries)) {
                  latestRealtimeCot.value = parsed; // 或者只保存 historySource
                  console.log('[ChatV2] 从会话消息中恢复了COT数据');
                  break; // 找到数据后退出循环
                }
              }
            } catch (e) {
              console.error('[ChatV2] 尝试从消息提取COT数据时出错:', e);
            }
          }
        }
      };
  
      const switchSession = async (sessionId) => {
         isSwitchingSession.value = true;
         if (currentAudio) {
           console.log('切换会话，停止当前音频播放');
           currentAudio.pause();
           currentAudio.src = '';
           currentAudio = null;
         }
         currentSessionId.value = sessionId;
         // *** 修改: 清除实时 COT ***
         latestRealtimeCot.value = null;
         currentTreatmentGuideForModal.value = null; // 清除治疗指南数据

         // Fetch full messages if they aren't already loaded or seem incomplete
         const needsFullFetch = !sessionMessages[sessionId] || sessionMessages[sessionId].length <= 1; // Basic check
         if (needsFullFetch) {
              console.log(`[ChatV2 switchSession] Messages for session ${sessionId} potentially incomplete, fetching full history...`);
             await fetchMessagesForSession(sessionId);
         } else {
              console.log(`[ChatV2 switchSession] Messages for session ${sessionId} already seem complete.`);
         }
         
         // 新增: 尝试从会话消息中提取COT数据
         extractCotFromSessionMessages(sessionId);

         nextTick(() => {
             scrollToBottom();
         });
         // Allow potential audio playback after a brief delay
         setTimeout(() => { isSwitchingSession.value = false; }, 100);
    };
  
      const createNewSession = async () => {
        try {
          loading.value = true;
          isSwitchingSession.value = true;
          const response = await request({
            url: '/session/addSessionV2',
            method: 'post',
            params: { userId: route.params.userId }
          });
          if (response.code === 200) {
            const newSession = response.content;
            sessions.value.unshift(newSession);
            
            const messagesResponse = await request({
              url: '/user/chatV2',
              method: 'post',
              params: { userId: route.params.userId }
            });
            if (messagesResponse.code === 200 && messagesResponse.content.sessionMessages) {
              if (messagesResponse.content.sessionMessages[newSession.id]) {
                sessionMessages[newSession.id] = messagesResponse.content.sessionMessages[newSession.id];
                console.log("Fetched and set messages for new session:", JSON.stringify(sessionMessages[newSession.id]));
              } else {
                sessionMessages[newSession.id] = [];
              }
            } else {
              sessionMessages[newSession.id] = [];
            }

            // *** 修改: 清除实时 COT ***
            latestRealtimeCot.value = null;
            currentSessionId.value = newSession.id;
            currentTreatmentGuideForModal.value = null; // Clear guide on new session
            saveMessagesToLocal();
            await nextTick();
            scrollToBottom();
            message.success('新 V2 会话创建成功');
          } else {
            message.error(response.message || '创建 V2 会话失败');
          }
        } catch (error) {
          console.error('创建 V2 会话错误:', error);
          message.error('创建 V2 会话失败');
        } finally {
          loading.value = false;
          isSwitchingSession.value = false;
        }
      }
  
      onMounted(async () => {
        const userId = route.params.userId;
        console.log(`[ChatV2 Debug] onMounted started for userId: ${userId}`);
        if (!userId) {
          message.error('请先登录');
          router.push('/');
          return;
        }

        // 移除测试数据

        loading.value = true;
        isSwitchingSession.value = true;

        try {
          console.log(`[ChatV2 Debug] Fetching initial data from /user/chatV2...`);
          const initialResponse = await request({
            url: `/user/chatV2`,
            method: 'post',
            params: { userId: parseInt(userId) }
          });
          console.log('[ChatV2 Debug] Received initial response:', JSON.parse(JSON.stringify(initialResponse)));

          if (initialResponse.code !== 200) {
            console.error('[ChatV2 Debug] Initial API call failed:', initialResponse);
            throw new Error(initialResponse.message || '获取用户信息和会话列表失败');
          }

          const data = initialResponse.content;
          console.log('[ChatV2 Debug] Initial backend data content:', JSON.parse(JSON.stringify(data)));

          userInfo.value = { ...data.userInfo, userId: parseInt(userId), profilePicture: data.userInfo?.profilePicture || '' };
          userStore.setUserInfo(userInfo.value);
          console.log('[ChatV2 Debug] User info updated.');

          const allSessionsFromServer = data.sessions || [];
          console.log(`[ChatV2 Debug] All sessions received from server: ${allSessionsFromServer.length}`, JSON.parse(JSON.stringify(allSessionsFromServer)));

          const allMessagesFromServer = data.sessionMessages || {};
          Object.assign(sessionMessages, allMessagesFromServer);
          console.log(`[ChatV2 Debug] Pre-populated sessionMessages from initial call.`);

          console.log('[ChatV2 Debug] Starting session type determination loop...');
          const sessionTypePromises = allSessionsFromServer.map(async (session) => {
            console.log(`[ChatV2 Debug] Processing session ${session.id}...`);
            let firstMessageContent = null;
            const existingMessages = sessionMessages[session.id];
            console.log(`[ChatV2 Debug] Session ${session.id}: Initial existingMessages from cache:`, JSON.parse(JSON.stringify(existingMessages || null)));

            if (existingMessages && existingMessages.length > 0 && typeof existingMessages[0]?.message === 'string') {
              firstMessageContent = existingMessages[0].message;
              console.log(`[ChatV2 Debug] Session ${session.id}: Found first message in initial cache.`);
            } else {
              console.log(`[ChatV2 Debug] Session ${session.id}: No valid first message in initial cache, fetching via API...`);
              try {
                const msgResponse = await request({
                  url: '/message/getSessionMessages',
                  method: 'get',
                  params: { sessionId: session.id }
                });
                console.log(`[ChatV2 Debug] Session ${session.id}: API response for getSessionMessages:`, JSON.parse(JSON.stringify(msgResponse)));
                if (msgResponse.code === 200 && msgResponse.content && msgResponse.content.length > 0) {
                  sessionMessages[session.id] = msgResponse.content;
                  firstMessageContent = sessionMessages[session.id][0]?.message;
                  console.log(`[ChatV2 Debug] Session ${session.id}: Fetched messages successfully from API.`);
                } else {
                  console.warn(`[ChatV2 Debug] Session ${session.id}: Failed to fetch first message or session is empty via API.`);
                  if (!sessionMessages[session.id]) {
                    sessionMessages[session.id] = [];
                  }
                }
              } catch (msgError) {
                console.error(`[ChatV2 Debug] Session ${session.id}: Error fetching messages via API:`, msgError);
                if (!sessionMessages[session.id]) {
                  sessionMessages[session.id] = [];
                }
              }
            }

            console.log(`[ChatV2 Debug] Session ${session.id}: Evaluating content for V2 format:
-------
${firstMessageContent}
-------`);

            const isV2 = isV2SessionFormat(firstMessageContent);
            console.log(`[ChatV2 Debug] Session ${session.id}: isV2SessionFormat result: ${isV2}`);
            return { ...session, isV2Format: isV2 };
          });

          const sessionsWithType = await Promise.all(sessionTypePromises);
          console.log('[ChatV2 Debug] Sessions with type determined:', JSON.parse(JSON.stringify(sessionsWithType)));

          const filteredSessions = sessionsWithType.filter(s => s.isV2Format);
          console.log(`[ChatV2 Debug] Filtered V2 sessions (${filteredSessions.length}):`, JSON.parse(JSON.stringify(filteredSessions)));
          sessions.value = filteredSessions;

          let selectedSessionId = null;
          const lastSelectedId = currentSessionId.value;
          const sessionExists = filteredSessions.some(s => s.id === lastSelectedId);

          if (lastSelectedId && sessionExists) {
            selectedSessionId = lastSelectedId;
            console.log(`[ChatV2 Debug] Restoring previous V2 session ID: ${selectedSessionId}`);
          } else if (filteredSessions.length > 0) {
            selectedSessionId = filteredSessions[0].id;
            console.log(`[ChatV2 Debug] Setting currentSessionId to the first V2 session: ${selectedSessionId}`);
          } else {
            console.log(`[ChatV2 Debug] No V2 sessions found after filtering.`);
            selectedSessionId = null;
          }
          currentSessionId.value = selectedSessionId;
          console.log(`[ChatV2 Debug] Final currentSessionId.value: ${currentSessionId.value}`);

          if (currentSessionId.value) {
            const needsFullFetch = !sessionMessages[currentSessionId.value] || sessionMessages[currentSessionId.value].length <= 1;
            if (needsFullFetch) {
              console.log(`[ChatV2 Debug] Fetching full messages for initially selected session ${currentSessionId.value} (using old API for now).`);
              await fetchMessagesForSession(currentSessionId.value);
            } else {
              console.log(`[ChatV2 Debug] Full messages for initial session ${currentSessionId.value} likely loaded already.`);
            }
            
            // 新增: 尝试从当前会话消息中提取COT数据
            extractCotFromSessionMessages(currentSessionId.value);
          }

          await nextTick();
          scrollToBottom();
          observeMessages();

        } catch (error) {
          console.error('[ChatV2 Debug] Initialization error:', error);
          message.error(error.message || '初始化失败');
        } finally {
          loading.value = false;
          setTimeout(() => { isSwitchingSession.value = false; }, 100);
          console.log('[ChatV2 Debug] onMounted finished.');
        }
      });
  
      const connectionTimeout = 360000;
      let messageTimer = null;
      let connectionCheckTimer = null;
  
      const createEventSource = (userMessage, doctorMessageObj) => {
        let isConnectionClosed = false;
        let eventSource = null;
        
        const safeCloseConnection = () => {
          if (!isConnectionClosed && eventSource) {
            try {
              eventSource.close();
              isConnectionClosed = true;
              console.log('EventSource连接已安全关闭');
            } catch (err) {
              console.error('关闭EventSource连接时出错:', err);
            }
          }
        };
        
        const cleanupResources = () => {
          clearTimeout(messageTimer);
          if (connectionCheckTimer) {
            clearInterval(connectionCheckTimer);
          }
          loading.value = false;
        };
        
        try {
          const endpointUrl = `/message/chat_ai_doctor_v2?sessionId=${currentSessionId.value}&message=${encodeURIComponent(userMessage)}`;
          eventSource = new EventSource(endpointUrl);
          console.log('Creating EventSource connection to:', endpointUrl);
          
          messageTimer = setTimeout(() => {
            console.log('等待响应中...');
          }, 30000);
          messageTimer = setTimeout(() => {
            console.log('请求超过6分钟，关闭连接');
            if (doctorMessageObj.message === '正在思考中...') {
              doctorMessageObj.message = '回复生成中...\n\n可能需要较长时间...';
            }
            message.destroy();
            safeCloseConnection();
            cleanupResources();
            message.info('回复生成需要较长时间...', 3);
            saveMessagesToLocal();
          }, connectionTimeout);

          const handleBeforeUnload = () => {
            safeCloseConnection();
            cleanupResources();
          };
          window.addEventListener('beforeunload', handleBeforeUnload);

          eventSource.onopen = () => {
            console.log('EventSource connection opened');
          };

          eventSource.onmessage = async (event) => {
            try {
              clearTimeout(messageTimer);

              if (event.data === '[DONE]') {
                console.log('对话完成');
                safeCloseConnection();
                cleanupResources();
                window.removeEventListener('beforeunload', handleBeforeUnload);
                
                console.log(`[DONE Handler] Checking conditions: lastReceivedAudioUrl = ${lastReceivedAudioUrl}, isSwitchingSession.value = ${isSwitchingSession.value}`);
                
                if (lastReceivedAudioUrl && !isSwitchingSession.value) {
                    console.log("Playing audio from lastReceivedAudioUrl:", lastReceivedAudioUrl);
                    await playAudio(lastReceivedAudioUrl);
                } else {
                     console.log("No audio URL received or switching session.");
                }
                
                saveMessagesToLocal(); 
                return;
              }

              const data = JSON.parse(event.data);
              console.log('解析后的消息数据:', data);

              if (data.content) {
                const messages = sessionMessages[currentSessionId.value];
                const placeholderIndex = messages.findIndex(m => m.id === doctorMessageObj.id);

                if (placeholderIndex !== -1) {
                    const currentPlaceholder = messages[placeholderIndex];
                    const updatedMessage = currentPlaceholder.message === '正在思考中...' ? data.content : currentPlaceholder.message + data.content;

                    const newDoctorMessage = {
                        ...currentPlaceholder, 
                        message: updatedMessage,
                        audioUrl: data.audioUrl || currentPlaceholder.audioUrl 
                    };
                    
                    if (data.audioUrl) {
                         lastReceivedAudioUrl = data.audioUrl;
                         console.log(`[onmessage] Updated lastReceivedAudioUrl: ${lastReceivedAudioUrl}`);
                    }

                    messages.splice(placeholderIndex, 1, newDoctorMessage);
                    console.log("Replaced placeholder with new message object.");
                    
                    /* 注释掉这部分重复添加消息的代码
                    const isStrategy = data.content.includes('诊断');
                    if (isStrategy) {
                      console.log('检测到 Strategy 内容，将额外添加一条消息:', data.content);
                      const strategyMessageObj = {
                        id: Date.now() + 2, 
                        sessionId: currentSessionId.value,
                        speaker: 'doctor',
                        message: data.content, 
                        createdAt: new Date().toISOString(),
                      };
                      messages.push(strategyMessageObj); 
                      console.log('已添加额外的 Strategy 消息:', strategyMessageObj);
                    }
                    */

                } else {
                    console.error("Could not find placeholder message object to update!");
                }

                saveMessagesToLocal();
                nextTick(() => {
                  scrollToBottom();
                });
              }

              // *** 修改: 解析并存储实时 COT 对象 ***
              if (data.cot) {
                try {
                  const parsedCot = JSON.parse(data.cot); 
                  latestRealtimeCot.value = parsedCot; // 存储解析后的对象
                  // console.log('[SSE COT Parsed]');
                } catch (e) {
                  console.error('[SSE COT Parse Error]', e, 'Raw COT:', data.cot);
                  latestRealtimeCot.value = '无法解析收到的思考链信息。请检查后端日志。'; // 存储错误信息
                }
              } else {
                // 如果当前消息块没有 cot，可以选择清除旧的，或者保留
                // latestRealtimeCot.value = null; // 取消注释以在没有新 COT 时清除
              }

            } catch (error) {
              console.error('消息处理错误:', error);
            }
          };

          eventSource.onerror = (error) => {
            clearTimeout(messageTimer);
            message.destroy();
            safeCloseConnection();
            cleanupResources();
            window.removeEventListener('beforeunload', handleBeforeUnload);
            if (doctorMessageObj.message === '正在思考中...') {
              doctorMessageObj.message = '回复生成中...\n\n可能需要较长时间...';
            }
            message.info('连接暂时不可用...', 3);
            saveMessagesToLocal();
          };

          return {
            eventSource,
            close: () => {
              safeCloseConnection();
              cleanupResources();
              message.destroy();
              window.removeEventListener('beforeunload', handleBeforeUnload);
            }
          };
        } catch (error) {
          console.error('创建EventSource连接失败:', error);
          cleanupResources();
          message.destroy();
          message.info('连接服务器失败...', 3);
          return {
            close: () => {
              console.log('连接已关闭（从未建立）');
            }
          };
        }
      };
  
      const sendMessage = async () => {
        const trimmedMessage = inputMessage.value.trim();
        if (!trimmedMessage || loading.value || !currentSessionId.value) {
          if (!currentSessionId.value) message.warning('请先选择或创建会话');
          return;
        }
        message.destroy();
        if (currentEventSource) {
          currentEventSource.close();
          currentEventSource = null;
        }

        try {
          loading.value = true;
          const userMessage = trimmedMessage;
          
          // 尝试方法1：记录清空前的值
          console.log('清空输入框前的值:', inputMessage.value);
          inputMessage.value = '';
          console.log('清空输入框后的值:', inputMessage.value);
          
          // 尝试方法2：使用setTimeout延迟清空，确保Vue有时间更新UI
          setTimeout(() => {
            console.log('setTimeout中再次尝试清空前:', inputMessage.value);
            inputMessage.value = '';
            console.log('setTimeout中再次尝试清空后:', inputMessage.value);
          }, 0);

          const userMessageObj = { id: Date.now(), sessionId: currentSessionId.value, speaker: 'patient', message: userMessage, createdAt: new Date().toISOString() };
          if (!sessionMessages[currentSessionId.value]) sessionMessages[currentSessionId.value] = [];
          sessionMessages[currentSessionId.value].push(userMessageObj);
          console.log('添加用户消息:', userMessageObj);

          const doctorMessageObj = { id: Date.now() + 1, sessionId: currentSessionId.value, speaker: 'doctor', message: '正在思考中...', createdAt: new Date().toISOString() };
          sessionMessages[currentSessionId.value].push(doctorMessageObj);
          console.log('添加医生消息占位:', doctorMessageObj);

          saveMessagesToLocal();
          console.log('当前所有会话消息:', sessionMessages);
          
          // 尝试方法3：在nextTick回调中再次尝试清空
          await nextTick(() => {
            console.log('nextTick中再次尝试清空前:', inputMessage.value);
            inputMessage.value = '';
            console.log('nextTick中再次尝试清空后:', inputMessage.value);
          });
          
          scrollToBottom();

          currentEventSource = createEventSource(userMessage, doctorMessageObj);

          // 尝试方法4：确保在createEventSource后再次尝试清空
          console.log('createEventSource后再次尝试清空前:', inputMessage.value);
          inputMessage.value = '';  
          console.log('createEventSource后再次尝试清空后:', inputMessage.value);

        } catch (error) {
          console.error('Error in sendMessage:', error);
          loading.value = false;
        }
      };
  
      const scrollToBottom = () => {
        if (messageContainer.value) {
          const container = messageContainer.value;
          container.scrollTo({ top: container.scrollHeight, behavior: 'smooth' });
        }
      };
  
      const observeMessages = () => {
        if (messageContainer.value) {
          if (messageObserver) messageObserver.disconnect();
          messageObserver = new MutationObserver(scrollToBottom);
          messageObserver.observe(messageContainer.value, { childList: true, subtree: true, characterData: true });
        }
      };
  
      const handleKeyPress = (e) => {
        if (e.key === 'Enter' && !e.shiftKey && !loading.value) {
          e.preventDefault();
          sendMessage();
        }
      };
  
      const toggleCollapse = () => {
        isCollapsed.value = !isCollapsed.value;
      };
  
      const getAvatarSrc = (profilePicture) => {
        if (!profilePicture) return `https://api.dicebear.com/7.x/avataaars/svg?seed=${userInfo.value?.username || 'default'}`;
        if (profilePicture.startsWith('data:image')) return profilePicture;
        if (profilePicture.startsWith('http')) return profilePicture;
        return `data:image/jpeg;base64,${profilePicture}`;
      };
  
      const toggleVoice = async () => {
        if (!isRecording.value) {
          try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            mediaRecorder.value = new MediaRecorder(stream, { mimeType: 'audio/webm', audioBitsPerSecond: 16000 });
          } catch (e) {
            console.warn('getUserMedia or MediaRecorder failed:', e);
            message.error('无法访问麦克风或不支持录音格式');
            return;
          }
          audioChunks.value = [];
          mediaRecorder.value.ondataavailable = (event) => audioChunks.value.push(event.data);
          mediaRecorder.value.onstop = async () => {
            const audioBlob = new Blob(audioChunks.value, { type: mediaRecorder.value.mimeType });
            const formData = new FormData();
            formData.append('audio', audioBlob, `recorded_audio.webm`);
            try {
              const loadingMsg = message.loading('正在识别语音...', 0);
              const response = await fetch('/api/asr/recognize', { method: 'POST', body: formData, timeout: 60000 });
              if (!response.ok) {
                loadingMsg();
                throw new Error(`Upload failed: ${response.status}`);
              }
              const audioId = await response.text();
              const eventSource = new EventSource(`/api/asr/recognize?audioId=${audioId}`, { withCredentials: true });
              const timeout = setTimeout(() => {
                console.warn('识别超时');
                try {
                  eventSource.close();
                } catch (e) {}
                loadingMsg();
              }, 120000);
              eventSource.onopen = () => console.log('ASR SSE opened');
              eventSource.addEventListener('result', (e) => {
                clearTimeout(timeout);
                loadingMsg();
                inputMessage.value = e.data;
              });
              eventSource.addEventListener('error', (err) => {
                clearTimeout(timeout);
                loadingMsg();
                try {
                  eventSource.close();
                } catch (e) {}
                console.error('ASR SSE Error:', err);
                message.info('语音识别未成功，请重试');
              });
            } catch (error) {
              console.error('Upload/ASR failed:', error);
              message.info('语音识别未成功，请重试');
            }
          };
          mediaRecorder.value.start();
          isRecording.value = true;
          message.success('开始录音');
        } else {
          mediaRecorder.value.stop();
          isRecording.value = false;
          mediaRecorder.value.stream.getTracks().forEach(track => track.stop());
          message.success('录音结束');
        }
      };
  
      const saveMessagesToLocal = () => {
        const localData = { userInfo: userInfo.value, sessions: sessions.value, sessionMessages: sessionMessages };
        localStorage.setItem('userInfo', JSON.stringify(localData));
      };
  
      const loadMessagesFromLocal = () => {
        const storedInfo = localStorage.getItem('userInfo');
        if (storedInfo) {
          const info = JSON.parse(storedInfo);
          userInfo.value = info.userInfo;
          userStore.setUserInfo(info.userInfo);
          sessions.value = info.sessions || [];
          Object.entries(info.sessionMessages || {}).forEach(([sessionId, msgs]) => {
            sessionMessages[sessionId] = msgs;
          });
        }
      };
  
      onUnmounted(() => {
        if (messageObserver) messageObserver.disconnect();
        if (currentEventSource) currentEventSource.close();
        if (currentAudio) {
          currentAudio.pause();
          currentAudio.src = '';
        }
        clearTimeout(messageTimer);
        saveMessagesToLocal();
      });
  
      const playAudio = async (audioUrl) => {
        if (isSwitchingSession.value) return;
        try {
          if (!audioUrl) return;
          const fullAudioUrl = `/audio/stream?audioUrl=${encodeURIComponent(audioUrl)}`;
          if (currentAudio) {
            currentAudio.pause();
            currentAudio.src = '';
          }
          const audio = new Audio();
          const errorHandler = (e) => {
            console.error('音频错误:', e);
            if (audio.error?.code === 4) {
              setTimeout(() => {
                if (!isSwitchingSession.value && currentAudio === audio) {
                  audio.load();
                  audio.play().catch(err => console.error('重试播放失败:', err));
                }
              }, 5555);
            } else {
              message.error(`音频加载失败: ${audio.error?.code}`);
            }
          };
          audio.addEventListener('error', errorHandler, true);
          audio.addEventListener('canplay', () => message.success('开始播放语音'));
          audio.addEventListener('ended', () => currentAudio = null);
          const timeoutPromise = new Promise((_, reject) => setTimeout(() => reject(new Error('音频加载超时')), 30000));
          audio.src = fullAudioUrl;
          audio.volume = 1.0;
          audio.preload = 'auto';
          currentAudio = audio;
          try {
            await Promise.race([audio.play(), timeoutPromise]);
          } catch (playError) {
            if (isSwitchingSession.value) return;
            console.error('播放失败:', playError);
            if (playError.message === '音频加载超时') {
              setTimeout(async () => {
                if (!isSwitchingSession.value && currentAudio === audio) {
                  currentAudio.load();
                  await currentAudio.play().catch(err => console.error('重试播放失败:', err));
                }
              }, 5555);
              return;
            }
            try {
              audio.muted = true;
              await audio.play();
              audio.muted = false;
            } catch (mutedPlayError) {
              console.error('静音播放也失败:', mutedPlayError);
            }
          }
        } catch (error) {
          console.error('音频处理失败:', error);
        }
      };
  
      window.addEventListener('error', (event) => {
        if (event.message?.includes('EventSource') || event.message?.includes('SSE')) {
          console.warn('捕获到SSE相关错误，已忽略:', event.message);
          event.preventDefault();
          return true;
        }
      });
  
      window.addEventListener('unhandledrejection', (event) => {
        if (event.reason?.message?.includes('EventSource') || event.reason?.message?.includes('SSE') || typeof event.reason === 'string' && (event.reason.includes('EventSource') || event.reason.includes('SSE'))) {
          console.warn('捕获到SSE相关Promise拒绝，已忽略:', event.reason);
          event.preventDefault();
          return true;
        }
      });
  
      const openSupplementaryModal = () => {
        supplementaryText.value = '' 
        showSupplementaryModal.value = true
      }

      const submitSupplementaryInfo = async () => {
        const text = supplementaryText.value.trim()
        if (!text) {
          message.warning('补充信息不能为空')
          return
        }
        if (!currentSessionId.value) {
          message.error('未选择会话')
          return
        }

        supplementaryLoading.value = true
        try {
          const response = await request({
            url: '/message/add_supplementary_info',
            method: 'post',
            params: {
              sessionId: currentSessionId.value,
              supplementary_info: text
            }
          })

          if (response.code === 200) {
            message.success('补充信息添加成功')
            showSupplementaryModal.value = false
            supplementaryText.value = ''
            
            // 修改部分: 检查是否需要返回初步诊断弹窗
            if (preliminaryDiagnosisData.value && preliminaryDiagnosisData.value.fromPreliminary) {
              nextTick(() => {
                showPreliminaryDiagnosisModal.value = true;
              });
            } else {
              // 如果不是从初步诊断弹窗来的，才执行原有的重新显示初步诊断逻辑
              // 这里为空是为了避免嵌套显示初步诊断弹窗
            }
          } else {
            message.error(response.message || '添加补充信息失败')
          }
        } catch (error) {
          console.error('添加补充信息错误:', error)
          message.error('添加补充信息时发生错误')
        } finally {
          supplementaryLoading.value = false
        }
      }

      const triggerDiagnoseV2 = async () => {
        if (!currentSessionId.value) {
          message.error('未选择会话')
          return
        }

        diagnoseLoading.value = true
        try {
          console.log('[ChatV2 triggerDiagnoseV2] 发起诊断请求...');
          const response = await request({
            url: '/message/diagnoseV2',
            method: 'post',
            params: { sessionId: currentSessionId.value }
          })

          if (response.code === 200 && response.content) {
            console.log('[ChatV2 triggerDiagnoseV2] 收到诊断响应:', response.content);
            const { diagnosis, condition, treatment_recommendation, observation, treatment_guide } = response.content // <-- 提取 observation 和 treatment_guide

            // 检查并设置指南数据
            console.log('[ChatV2 triggerDiagnoseV2] Received treatment_guide:', treatment_guide);
            if (treatment_guide) {
              if (typeof treatment_guide === 'object' && treatment_guide.query) {
                console.log('[ChatV2 triggerDiagnoseV2] Setting valid treatment guide data');
                currentTreatmentGuideForModal.value = treatment_guide;
              } else {
                console.log('[ChatV2 triggerDiagnoseV2] Received treatment_guide but in unexpected format:', typeof treatment_guide);
              }
            } else {
              console.log('[ChatV2 triggerDiagnoseV2] No treatment_guide received');
            }

            // 检查是否有有效的诊断或建议内容
            // *** 修改: 检查条件，确保即使只有 observation 也显示 ***
            if (diagnosis || treatment_recommendation || observation) { 
               // 构造 V2 格式的 payload
               const v2Payload = {
                 interaction_history: {
                   diagnosis: diagnosis || null, 
                   treatment_recommendation: treatment_recommendation || null, 
                   lastObservation: observation || null, // <-- 添加 observation, 使用 lastObservation 键名以匹配显示逻辑
                   treatment_guide: treatment_guide || null, // <-- 添加 treatment_guide
                   condition: condition || null // <-- 添加 condition
                 }
               };

               // 创建消息对象，message 字段为 V2 JSON 字符串
               const diagnosisMessageObj = {
                 id: `diag-${Date.now()}`, // 使用特定前缀的 ID
                 sessionId: currentSessionId.value,
                 speaker: 'doctor',
                 message: JSON.stringify(v2Payload), // 存储 JSON 字符串
                 createdAt: new Date().toISOString()
               };

               // 确保会话的消息数组存在
               if (!sessionMessages[currentSessionId.value]) {
                 sessionMessages[currentSessionId.value] = [];
               }

               // 添加 V2 格式的诊断消息
               sessionMessages[currentSessionId.value].push(diagnosisMessageObj);
               console.log('添加 V2 格式的诊断结果消息:', diagnosisMessageObj);

               // 保存状态，触发 UI 更新，并通知用户
               saveMessagesToLocal();
               await nextTick(); // 确保 DOM 更新
               scrollToBottom(); // 滚动到底部
               message.success('获取最终诊断成功'); // 保留成功提示

            } else {
               message.warning('诊断服务未返回有效结果');
            }
          } else {
            message.error(response.message || '获取最终诊断失败');
          }
        } catch (error) {
          console.error('最终诊断错误:', error)
          message.error('获取最终诊断时发生错误')
        } finally {
          diagnoseLoading.value = false
        }
      }

      const openCotModal = () => {
        showCotModal.value = true;
      };

      // *** 新增: 计算属性用于格式化 COT 显示 ***
      const formattedCotDisplay = computed(() => {
        const cotData = latestRealtimeCot.value;

        // Handle null, undefined, or non-object states (including error strings)
        if (!cotData || typeof cotData !== 'object') {
          return typeof cotData === 'string' ? cotData : '请先提问以获取思考链信息。';
        }

        try {
          // Safely access nested properties
          const history = cotData?.interaction_history;
          const entries = history?.cot_entries;

          // Validate the structure
          if (!entries || !Array.isArray(entries) || entries.length === 0) {
            console.warn('[formattedCotDisplay] Invalid or empty cot_entries structure:', cotData);
            return '未能提取到有效的思考链条目。';
          }

          let displayHtml = '<div class="cot-container">';
          
          entries.forEach((entry, index) => {
            // Ensure entry is an object before accessing properties
            if (entry && typeof entry === 'object') {
              // 根据索引值确定阶段名称
              let stageName = '';
              switch (index) {
                case 0:
                  stageName = '问诊阶段';
                  break;
                case 1:
                  stageName = '查体阶段';
                  break;
                case 2:
                  stageName = '查体阶段';
                  break;
                case 3:
                  stageName = '初步拟诊';
                  break;
                default:
                  stageName = `回合 ${index + 1}`;
              }
              
              displayHtml += `<div class="cot-entry">
              <div class="cot-entry-header">${stageName}</div>
              <div class="cot-entry-content">`;

                // Dialogue History - check if it's a valid array
                if (Array.isArray(entry.dialogue_history) && entry.dialogue_history.length > 0) {
                displayHtml += '<div class="cot-section cot-dialogue">';
                displayHtml += '<div class="cot-section-title">对话历史</div>';
                displayHtml += '<div class="cot-section-content">';
                
                  entry.dialogue_history.forEach(turn => {
                    // Ensure turn is an object
                    if (turn && typeof turn === 'object') {
                    const role = turn.role === 'doctor' ? '医生' : '患者';
                      const content = turn.content || '';
                    displayHtml += `<div class="cot-dialogue-turn ${turn.role}">
                      <span class="cot-role">${role}:</span> 
                      <span class="cot-content">${content}</span>
                    </div>`;
                    }
                  });
                
                displayHtml += '</div></div>';
                }

                // Reasoning - check if it's a non-empty string
                if (entry.reasoning && typeof entry.reasoning === 'string') {
                displayHtml += '<div class="cot-section cot-reasoning">';
                displayHtml += '<div class="cot-section-title">推理过程</div>';
                displayHtml += `<div class="cot-section-content">${entry.reasoning.trim()}</div>`;
                displayHtml += '</div>';
                }

                // Strategy - check if it's a non-empty string
                if (entry.strategy && typeof entry.strategy === 'string') {
                displayHtml += '<div class="cot-section cot-strategy">';
                displayHtml += '<div class="cot-section-title">策略</div>';
                displayHtml += `<div class="cot-section-content">${entry.strategy.trim()}</div>`;
                displayHtml += '</div>';
                }

                // Observation - check if it's a non-empty string
                if (entry.observation && typeof entry.observation === 'string') {
                displayHtml += '<div class="cot-section cot-observation">';
                displayHtml += '<div class="cot-section-title">观察</div>';
                displayHtml += `<div class="cot-section-content">${entry.observation.trim().replace(/\n/g, '<br>')}</div>`;
                displayHtml += '</div>';
                }

                // Feedback - check if it's a non-empty string
                if (entry.feedback && typeof entry.feedback === 'string') {
                displayHtml += '<div class="cot-section cot-feedback">';
                displayHtml += '<div class="cot-section-title">反馈</div>';
                displayHtml += `<div class="cot-section-content">${entry.feedback.trim()}</div>`;
                displayHtml += '</div>';
                }

              displayHtml += '</div></div>';
            } else {
                console.warn(`[formattedCotDisplay] Entry at index ${index} is not a valid object:`, entry);
            }
          });

          displayHtml += '</div>';
          
          // 添加CSS样式
          displayHtml += `
          <style>
            .cot-container {
              font-family: 'Microsoft YaHei', sans-serif;
              line-height: 1.5;
              color: #333;
            }
            .cot-entry {
              margin-bottom: 20px;
              border: 1px solid #e0e0e0;
              border-radius: 8px;
              overflow: hidden;
            }
            .cot-entry-header {
              background-color: #4D6BFE;
              color: white;
              font-weight: bold;
              padding: 8px 15px;
              font-size: 16px;
            }
            .cot-entry-content {
              padding: 0;
            }
            .cot-section {
              padding: 0;
              border-bottom: 1px solid #eee;
            }
            .cot-section:last-child {
              border-bottom: none;
            }
            .cot-section-title {
              font-weight: 600;
              padding: 8px 15px;
              background-color: #f5f5f5;
              border-bottom: 1px solid #eee;
              color: #333;
            }
            .cot-section-content {
              padding: 10px 15px;
              white-space: pre-wrap;
              line-height: 1.6;
            }
            .cot-dialogue-turn {
              margin-bottom: 8px;
              padding: 5px 0;
            }
            .cot-dialogue-turn.doctor {
              color: #1976d2;
            }
            .cot-dialogue-turn.patient {
              color: #43a047;
            }
            .cot-role {
              font-weight: 600;
              margin-right: 5px;
            }
            .cot-reasoning { background-color: #fff8e1; }
            .cot-strategy { background-color: #e8f5e9; }
            .cot-observation { background-color: #e3f2fd; }
            .cot-feedback { background-color: #fce4ec; }
          </style>`;

          return displayHtml;

        } catch (error) {
          console.error('[ChatV2 formattedCotDisplay] Error formatting COT data:', error, 'Input data:', cotData);
          return '格式化思考链信息时出错。';
        }
      });
      // --- COT 相关结束 ---

      // --- 新增状态用于上传模态框 ---
      const showUploadModal = ref(false);
      const uploadFileList = ref([]); // 用于 a-upload 的 fileList
      const uploadPreviewVisible = ref(false); // 控制预览模态框
      const uploadPreviewImage = ref(''); // 预览图片的 URL 或 base64
      const ocrProcessing = ref(false); // 新增 OCR 处理状态，替换 uploadProcessing

      // --- 新增方法用于上传模态框 ---
      const openUploadModal = () => {
        uploadFileList.value = []; // 清空上次的文件
        showUploadModal.value = true;
      };

      const closeUploadModal = () => {
        showUploadModal.value = false;
        // 如果是从初步诊断流程过来的，返回初步诊断弹窗
        if (preliminaryDiagnosisData.value && preliminaryDiagnosisData.value.fromPreliminary) {
          nextTick(() => {
            showPreliminaryDiagnosisModal.value = true;
          });
        }
      };

      // 关闭图片预览
      const handleUploadCancel = () => {
        uploadPreviewVisible.value = false;
      };

      // 处理图片预览 (来自您的示例)
      const handleUploadPreview = async (file) => {
        if (!file.url && !file.preview) {
          // originFileObj 存储的是原始 File 对象
          file.preview = await getBase64(file.originFileObj);
        }
        uploadPreviewImage.value = file.url || file.preview;
        uploadPreviewVisible.value = true;
      };

      // 处理文件状态变化 (来自您的示例)
      // 这个函数会在文件添加、上传中、上传成功/失败、移除时被调用
      const handleUploadChange = async ({ file, fileList: newFileList }) => {
        // 同步 antd 组件的文件列表状态
        uploadFileList.value = newFileList;

        // 只处理新添加的、有原始文件对象、且不是移除操作的文件
        if (file.status && file.status !== 'removed' && file.originFileObj && !file.processed) {
            if (!currentSessionId.value) {
                message.error('请先选择一个会话');
                // 从 UI 文件列表中移除该文件，因为它无法处理
                uploadFileList.value = uploadFileList.value.filter(f => f.uid !== file.uid);
                return;
            }

            // 标记该文件正在处理，防止重复提交
            file.processed = true; // 添加一个自定义标记
            ocrProcessing.value = true; // 设置全局 OCR 处理状态
            file.status = 'uploading'; // 手动设置状态为上传中，UI 显示加载指示

            // 准备 FormData
            const formData = new FormData();
            formData.append('image', file.originFileObj);
            formData.append('name', userInfo.value?.username || '未知用户'); // 获取用户名

            // 获取并格式化当前时间 YYYY-MM-DD HH:mm
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const currentTime = `${year}-${month}-${day} ${hours}:${minutes}`;
            formData.append('time', currentTime);

            formData.append('sessionId', String(currentSessionId.value)); // 获取会话 ID

            try {
                console.log(`[OCR Upload] Sending request for file ${file.name} to /ocr/process`);
                // 使用 request 工具发送请求
                const response = await request({
                    url: '/ocr/process', // 使用完整的后端URL
                    method: 'post',
                    data: formData,
                    headers: {
                        // 'Content-Type': 'multipart/form-data' // request 工具通常会自动处理
                    },
                    timeout: 120000 // 设置较长超时，例如 120 秒
                });
                console.log(`[OCR Upload] Received response for ${file.name}:`, response);

                // 无论响应内容如何，只要收到响应就视为成功
                file.status = 'done'; // 标记文件处理成功
                
                // 静默刷新消息数据，不显示任何提示
                await fetchMessagesForSession(currentSessionId.value);
                
                // 恢复一次性成功提示
                message.success({
                    content: `OCR处理成功`,
                    duration: 2
                });

            } catch (error) {
                console.error(`调用 OCR 接口失败 (${file.name}):`, error);
                message.error(`${file.name} 上传或处理失败: ${error.message || '网络错误或服务不可用'}`);
                file.status = 'error'; // 标记文件处理失败
            } finally {
                ocrProcessing.value = false; // 处理结束（无论成功或失败）
                // 确保 UI 文件列表状态已更新
                uploadFileList.value = uploadFileList.value.map(f => {
                    if (f.uid === file.uid) {
                        return { ...f, status: file.status }; // 使用更新后的状态
                    }
                    return f;
                });
                
                // 处理完成后，如果是从初步诊断过来的，则返回初步诊断弹窗
                if (preliminaryDiagnosisData.value && preliminaryDiagnosisData.value.fromPreliminary) {
                    nextTick(() => {
                        showPreliminaryDiagnosisModal.value = true;
                    });
                }
            }
        } else if (file.status === 'removed') {
            // 文件被移除时的逻辑（如果需要）
            console.log('File removed:', file.name);
        }
    };

      // Method to open the new Guide modal
      const openGuideModal = async () => {
        console.log('[ChatV2 openGuideModal] Opening guide modal for session:', currentSessionId.value);
        
        if (!currentSessionId.value) {
          message.error('未选择会话');
          return;
        }

        // 从后端获取数据，不检查缓存
        try {
          guideModalLoading.value = true;
          showGuideModal.value = true; // 立即显示模态框，加载状态将由Spin控制
          
          console.log('[ChatV2 openGuideModal] Fetching guide data for sessionId:', currentSessionId.value);
          
          const response = await request({
            url: '/guidelines/getBySessionId',
            method: 'get',
            params: { sessionId: currentSessionId.value },
            timeout: 15555 // 15秒超时
          });
          
          console.log('[ChatV2 openGuideModal] Received response:', response);

          if (response.code === 200) {
            if (response.content === "无指南") {
              console.log('[ChatV2 openGuideModal] No guide data available for this session');
              currentTreatmentGuideForModal.value = null;
              message.info('当前会话暂无指南数据');
            } else if (response.content && typeof response.content === 'object') {
              console.log('[ChatV2 openGuideModal] Successfully received guide data');
              
              // 移除查询病症部分，确保不显示查询相关信息
              const guideData = {...response.content};
              if (guideData.query) {
                console.log('[ChatV2 openGuideModal] Removing query section from guide data');
                delete guideData.query;
              }
              
              currentTreatmentGuideForModal.value = guideData;
            } else {
              console.warn('[ChatV2 openGuideModal] Invalid response format:', response.content);
              currentTreatmentGuideForModal.value = response.content; // 直接显示原始内容
              message.warning('获取到的指南数据格式不标准，将以原始格式显示');
            }
          } else {
            console.error('[ChatV2 openGuideModal] Error response:', response);
            currentTreatmentGuideForModal.value = null;
            message.error(response.message || '获取指南数据失败');
          }
        } catch (error) {
          console.error('[ChatV2 openGuideModal] Request error:', error);
          currentTreatmentGuideForModal.value = null;
          
          // Check if it's a network error
          if (error.message && error.message.includes('Network Error')) {
            message.error('网络错误，无法连接到服务器');
          } else if (error.message && error.message.includes('timeout')) {
            message.error('请求超时，服务器响应时间过长');
          } else {
            message.error('获取指南数据时发生错误: ' + error.message);
          }
        } finally {
          guideModalLoading.value = false;
        }
      };

      // 添加watcher监控指南数据变化
      watch(currentTreatmentGuideForModal, (newVal, oldVal) => {
        console.log('[ChatV2 watch] currentTreatmentGuideForModal changed:', 
          newVal ? '有数据' : '无数据', 
          oldVal ? '(之前有数据)' : '(之前无数据)');
      });

      // 新增：检查字符串是否为JSON格式
      const isJsonString = (str) => {
        if (typeof str !== 'string') return false;
        try {
          const result = JSON.parse(str);
          return typeof result === 'object' && result !== null;
        } catch (e) {
          return false;
        }
      };

      // 新增：格式化JSON字符串为便于阅读的格式
      const formatJsonString = (str) => {
        try {
          const obj = JSON.parse(str);
          
          // 对于诊疗指南查询内容的特殊处理
          if (obj.diagnostic_symptoms && obj.main_disease && obj.source_file) {
            // 这是诊疗指南查询内容格式
            return JSON.stringify({
              诊断症状: obj.diagnostic_symptoms || "",
              主要疾病: obj.main_disease || "",
              来源文件: obj.source_file || "",
              关键词: obj.keywords || "",
              检索数量: obj.top_k || ""
            }, null, 2);
          }
          
          // 默认格式化
          return JSON.stringify(obj, null, 2);
        } catch (e) {
          return str;
        }
      };

      // 新增：用于将查询JSON转换为表格显示的字段
      const parseQueryJson = (str) => {
        try {
          const obj = JSON.parse(str);
          
          // 返回包含中文标签的对象
          return {
            "诊断症状": obj.diagnostic_symptoms || "",
            "主要疾病": obj.main_disease || "",
            "来源文件": obj.source_file || "",
            "关键词": obj.keywords || "",
            "检索数量": obj.top_k || ""
          };
        } catch (e) {
          return { "内容": str };
        }
      };

      // 新增：格式化症状文本，使其更易读，在重要标点后添加换行
      const formatSymptomText = (text) => {
        if (!text) return '';
        
        // 1. 替换常见标点符号为带换行的版本
        let formatted = text
          .replace(/。/g, '。<br>')  // 句号后换行
          .replace(/；/g, '；<br>')  // 分号后换行
          .replace(/\.\s+/g, '.<br>') // 英文句号后换行
          .replace(/\n/g, '<br>');  // 原有换行保留
        
        // 2. 在医学术语前添加换行和间隔
        const medicalTerms = [
          '【基本信息】', '【主诉】', '【现病史】', '【既往史】', 
          '【传染病接触史】', '【家族史】', '【体格检查】', '【辅助检查】'
        ];
        
        medicalTerms.forEach(term => {
          formatted = formatted.replace(new RegExp(term, 'g'), `<br><b>${term}</b>`);
        });
        
        // 3. 处理项目符号，使列表更清晰
        formatted = formatted.replace(/- /g, '<br>• ');
        formatted = formatted.replace(/• /g, '<span style="color: #4D6BFE;">•</span> ');
        
        return formatted;
      };

      const sessionId = ref(null);
      const currentMessage = ref('');
      const messages = ref([]);
      const isLoading = ref(false);
      const showTypingIndicator = ref(false);

      // New data properties for preliminary diagnosis modal
      const showPreliminaryDiagnosisModal = ref(false);
      const isAwaitingPreliminaryAction = ref(false);
      const preliminaryDiagnoseLoading = ref(false);
      const preliminaryDiagnosisData = ref({});

      const handleUploadMedicalReport = () => {
        // ... existing code ...
      };

      // Placeholder methods for modal actions (to be implemented later)
      const triggerSupplementaryFromPreliminary = () => {
        console.log("触发医生补充信息模态框");
        showPreliminaryDiagnosisModal.value = false; // 关闭当前模态框
        // 标记来源是初步诊断
        preliminaryDiagnosisData.value = { 
          ...preliminaryDiagnosisData.value,
          fromPreliminary: true 
        };
        showSupplementaryModal.value = true; // 打开补充信息模态框
      };

      const triggerUploadFromPreliminary = () => {
        console.log("触发上传检查报告模态框");
        showPreliminaryDiagnosisModal.value = false; // 关闭当前模态框
        // 标记来源是初步诊断
        preliminaryDiagnosisData.value = {
          ...preliminaryDiagnosisData.value,
          fromPreliminary: true
        };
        showUploadModal.value = true; // 打开上传检查报告模态框
      };

      const handlePreliminaryComplete = () => {
        console.log("Preliminary diagnosis marked as complete by user. Initiating API call.");
        showPreliminaryDiagnosisModal.value = false;
        executePreliminaryDiagnosis();
      };

      const handlePreliminarySkip = () => {
        console.log("Preliminary diagnosis skipped by user.");
        showPreliminaryDiagnosisModal.value = false;
        isAwaitingPreliminaryAction.value = false;
        // Potentially send a "skip" message or just re-enable input
        // For now, just re-enable chat
        // isLoading.value = false; // Assuming no API call on skip
      };

      // --- How to trigger the modal? ---
      // Watch for specific AI messages that should trigger the modal.
      // This is a robust way if messages are added to sessionMessages directly.
      watch(currentRawMessages, (newMessages, oldMessages) => {
          if (!newMessages || newMessages.length === 0) return;
          const lastMessage = newMessages[newMessages.length - 1];

          // Define the trigger phrases - update to match the actual format used
          const triggerPhrases = ["[诊断策略:]", "【初步诊断策略】", "【诊断策略】"];

          if (lastMessage && lastMessage.speaker === 'doctor' && lastMessage.message) {
              // 检查是否有diagnosis字段，如果有则不弹窗
              const parsedData = parseAndExtractV2Data(lastMessage.message);
              if (parsedData && parsedData.diagnosis) {
                  console.log("Message already has diagnosis, skipping preliminary diagnosis modal");
                  return;
              }
              
              // Check if any of the defined trigger phrases is in the message
              const hasTriggerPhrase = triggerPhrases.some(phrase => lastMessage.message.includes(phrase));
              
              if (hasTriggerPhrase) {
                  // Prevent re-triggering if modal is already active for this flow
                  if (!showPreliminaryDiagnosisModal.value && !preliminaryDiagnoseLoading.value) {
                       console.log("Detected preliminary diagnosis trigger phrase in new message:", lastMessage.message);
                       // Extract relevant data if needed, e.g., message ID, or the content itself
                       triggerPreliminaryDiagnosisModal({
                           messageId: lastMessage.id,
                           sessionId: lastMessage.sessionId,
                       });
                  }
              }
          }
      }, { deep: true });

      const executePreliminaryDiagnosis = async () => {
          if (!currentSessionId.value) {
              message.error("No active session for preliminary diagnosis.");
              return;
          }
          console.log(`Executing preliminary diagnosis for session: ${currentSessionId.value}`);
          preliminaryDiagnoseLoading.value = true;
          isAwaitingPreliminaryAction.value = false; // Action is being processed

          try {
              // Make the actual API call to the backend
              const response = await request({
                  url: '/message/preliminaryDiagnoseV2',
                  method: 'post',
                  params: { 
                      sessionId: currentSessionId.value
                  }
              });

              if (response.code === 200 && response.content) {
                  message.success('初步诊断成功!');
                  console.log("Preliminary Diagnosis Response:", response.content);

                  const { 
                      preliminary_diagnosis,
                      inspection_suggestions,
                      guidelines_content,
                      reasoning 
                  } = response.content;
                  
                  // 创建美化后的初步诊断结果内容
                  let resultMessageContent = `
                  <div class="preliminary-diagnosis-card">
                      <div class="prelim-diagnosis-header">
                          <div class="prelim-icon">🏥</div>
                          <div class="prelim-title">AI初步诊断结果</div>
                      </div>
                  `;
                  
                  // 初步诊断部分
                  if (preliminary_diagnosis) {
                      resultMessageContent += `
                      <div class="prelim-section diagnosis-section">
                          <div class="prelim-section-header">
                              <span class="section-icon">📋</span>
                              <span class="section-title">初步诊断</span>
                          </div>
                          <div class="prelim-section-content">
                              ${preliminary_diagnosis}
                          </div>
                      </div>
                      `;
                  }
                  
                  // 检查建议部分
                  if (inspection_suggestions) {
                      resultMessageContent += `
                      <div class="prelim-section suggestion-section">
                          <div class="prelim-section-header">
                              <span class="section-icon">🔍</span>
                              <span class="section-title">检查建议</span>
                          </div>
                          <div class="prelim-section-content">
                              ${inspection_suggestions}
                          </div>
                      </div>
                      `;
                  }
                  
                  // 指南内容部分
                  if (guidelines_content) {
                      const guideContent = typeof guidelines_content === 'string' 
                          ? guidelines_content 
                          : JSON.stringify(guidelines_content, null, 2);
                          
                      resultMessageContent += `
                      <div class="prelim-section guidelines-section">
                          <div class="prelim-section-header">
                              <span class="section-icon">📘</span>
                              <span class="section-title">诊疗指南</span>
                          </div>
                          <div class="prelim-section-content guidelines-content">
                              <pre>${guideContent}</pre>
                          </div>
                      </div>
                      `;
                  }
                  
                  // 诊断依据部分
                  if (reasoning) {
                      resultMessageContent += `
                      <div class="prelim-section reasoning-section">
                          <div class="prelim-section-header">
                              <span class="section-icon">🔬</span>
                              <span class="section-title">诊断依据</span>
                          </div>
                          <div class="prelim-section-content">
                              ${reasoning}
                          </div>
                      </div>
                      `;
                  }
                  
                  resultMessageContent += `</div>`;

                  const resultMessageObj = {
                      id: `prelim-diag-${Date.now()}`,
                      sessionId: currentSessionId.value,
                      speaker: 'doctor',
                      message: resultMessageContent,
                      createdAt: new Date().toISOString(),
                  };
                  
                  if (!sessionMessages[currentSessionId.value]) {
                      sessionMessages[currentSessionId.value] = [];
                  }
                  sessionMessages[currentSessionId.value].push(resultMessageObj);
                  saveMessagesToLocal();
                  await nextTick();
                  scrollToBottom();
                  
              } else {
                  message.error(response.message || '初步诊断失败');
                  console.error("Preliminary Diagnosis Error:", response);
              }
          } catch (error) {
              message.error('初步诊断API调用过程中发生错误');
              console.error("Preliminary Diagnosis API Call Error:", error);
          } finally {
              preliminaryDiagnoseLoading.value = false;
              // Chat input remains disabled until explicitly re-enabled or modal is skipped/closed
          }
      };

      // --- Modal trigger method ---
      const triggerPreliminaryDiagnosisModal = (data) => {
          console.log("Triggering preliminary diagnosis modal with data:", data);
          preliminaryDiagnosisData.value = data; // Store any relevant data
          showPreliminaryDiagnosisModal.value = true;
          isAwaitingPreliminaryAction.value = true; // Disable chat input
      };

      // 处理上传确认
      const handleUploadOk = () => {
        // 关闭上传模态框
        showUploadModal.value = false;
        
        // 如果是从初步诊断流程过来的，返回初步诊断弹窗
        if (preliminaryDiagnosisData.value && preliminaryDiagnosisData.value.fromPreliminary) {
          nextTick(() => {
            showPreliminaryDiagnosisModal.value = true;
          });
        }
      };

      // 处理补充信息确认
      const handleSupplementaryOk = () => {
        showSupplementaryModal.value = false;
        
        // 如果是从初步诊断流程过来的，返回初步诊断弹窗
        if (preliminaryDiagnosisData.value && preliminaryDiagnosisData.value.fromPreliminary) {
          nextTick(() => {
            showPreliminaryDiagnosisModal.value = true;
          });
        }
      };

      // 处理补充信息取消
      const handleSupplementaryCancel = () => {
        showSupplementaryModal.value = false;
        
        // 如果是从初步诊断流程过来的，返回初步诊断弹窗
        if (preliminaryDiagnosisData.value && preliminaryDiagnosisData.value.fromPreliminary) {
          nextTick(() => {
            showPreliminaryDiagnosisModal.value = true;
          });
        }
      };

      // --- 指南相关功能 ---
      const guideModalLoading = ref(false);
      
      // 格式化指南内容，将对象或字符串转换为HTML
      const formatGuideContent = (content) => {
        if (typeof content === 'string') {
          // 处理字符串内容，添加换行和格式化
          return content
            .replace(/\n/g, '<br>')
            .replace(/- /g, '<br>• ')
            .replace(/• /g, '<span style="color: #4D6BFE;">•</span> ');
        } else if (typeof content === 'object') {
          // 处理对象内容，转换为JSON
          try {
            return `<pre class="json-content">${JSON.stringify(content, null, 2)}</pre>`;
          } catch (e) {
            console.error('Error formatting guide content object:', e);
            return String(content);
          }
        }
        return String(content);
      };
      
      // 处理指南内容显示，将原始对象转换为用户友好的格式
      const guideContentDisplay = (guideObj) => {
        if (!guideObj || typeof guideObj !== 'object') return {};
        
        const displayObj = {};
        
        // 定义键名映射（英文到中文）
        const keyMapping = {
          'diagnosis': '诊断',
          'diagnostic_symptoms': '诊断症状',
          'main_disease': '主要疾病',
          'treatment_recommendation': '治疗建议',
          'condition': '病情描述',
          'reasoning': '诊断依据',
          'guidelines_content': '指南详情'
        };
        
        // 遍历原始对象的键，应用映射
        Object.keys(guideObj).forEach(key => {
          if (key === 'results' || key === 'query') return; // 跳过已经处理或不需要显示的字段
          
          const displayKey = keyMapping[key] || key; // 使用映射或原始键名
          displayObj[displayKey] = guideObj[key];
        });
        
        return displayObj;
      };
      
      // 重新获取指南数据
      const handleGuideRetry = () => {
        openGuideModal();
      };

      return {
        loading,
        userInfo,
        sessions,
        currentSessionId,
        displayedMessages,
        inputMessage,
        messageContainer,
        formatTime,
        formatMessage,
        formatMarkdown, // 导出新增的markdown格式化函数
        formatSymptomText, // 导出新增的症状文本格式化函数
        switchSession,
        createNewSession,
        sendMessage,
        isCollapsed,
        toggleCollapse,
        getAvatarSrc,
        handleKeyPress,
        router,
        doctorLogo,
        observeMessages,
        isRecording,
        toggleVoice,
        chatInputRef,
        showSupplementaryModal,
        supplementaryText,
        supplementaryLoading,
        openSupplementaryModal,
        submitSupplementaryInfo,
        diagnoseLoading,
        triggerDiagnoseV2,
        showCotModal,
        latestRealtimeCot,
        formattedCotDisplay,
        openCotModal,
        // Guide Modal
        currentTreatmentGuideForModal,
        showGuideModal,
        openGuideModal,
        isJsonString,
        formatJsonString,
        parseQueryJson,
        // --- OCR 相关导出 ---
        showUploadModal,
        uploadFileList,
        uploadPreviewVisible,
        uploadPreviewImage,
        ocrProcessing, // 导出新的 loading 状态
        openUploadModal,
        closeUploadModal,
        handleUploadCancel,
        handleUploadPreview,
        handleUploadChange, // 导出修改后的函数
        // processUploadedFiles 和 uploadProcessing 已移除
        PlusOutlined,
        sessionId,
        currentMessage,
        messages,
        isLoading,
        showTypingIndicator,
        showPreliminaryDiagnosisModal,
        isAwaitingPreliminaryAction,
        preliminaryDiagnoseLoading,
        preliminaryDiagnosisData,
        handleUploadMedicalReport,
        triggerSupplementaryFromPreliminary,
        triggerUploadFromPreliminary,
        handlePreliminaryComplete,
        handlePreliminarySkip,
        executePreliminaryDiagnosis,
        triggerPreliminaryDiagnosisModal,
        handleUploadOk,
        handleSupplementaryOk,
        handleSupplementaryCancel,
        guideModalLoading,
        formatGuideContent,
        guideContentDisplay,
        handleGuideRetry,
      };
    }
  }
  </script>
    <style scoped>
  .chat-container {
    height: calc(100vh - 80px);
    background-color: #f8fafc;
    overflow: hidden;
    position: fixed;
    top: 100px;
    left: 0;
    right: 0;
    bottom: 0;
  }
  
  .main-content {
    display: flex;
    height: 100%;
    width: 100%;
  }
  
  .session-list {
    width: 280px;
    min-width: 280px;
    background-color: #fff;
    border-right: none;
    display: flex;
    flex-direction: column;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 4px 0 12px rgba(0, 0, 0, 0.03);
  }
  
  .session-list.collapsed {
    width: 80px;
    min-width: 80px;
  }
  
  .session-header {
    padding: 16px;
    border-bottom: none;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
  }
  
  .user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 0;
  }
  
  .session-list.collapsed .user-info {
    justify-content: center;
  }
  
  :deep(.ant-avatar) {
    width: 40px;
    height: 40px;
    border: 2px solid rgba(77, 107, 254, 0.1);
    transition: all 0.3s ease;
    border-radius: 8px;
    margin-top: 8px;
  }
  
  :deep(.ant-avatar:hover) {
    transform: scale(1.05);
    border-color: rgba(77, 107, 254, 0.3);
  }
  
  .session-list.collapsed .session-item {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 12px 8px;
  }
  
  .session-list.collapsed .session-number {
    font-size: 14px;
    font-weight: 500;
    color: #4D6BFE;
  }
  
  .session-items {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
  }
  
  .session-item {
    padding: 12px 16px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 12px;
    margin: 4px 8px;
    background: transparent;
    position: relative;
  }
  
  .session-item:hover {
    background-color: rgba(77, 107, 254, 0.08);
    transform: translateX(2px);
    box-shadow: 0 2px 8px rgba(77, 107, 254, 0.05);
  }
  
  .session-item.active {
    background-color: rgba(77, 107, 254, 0.12);
    box-shadow: 0 2px 8px rgba(77, 107, 254, 0.08);
  }
  
  .session-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 0;
    transition: all 0.3s ease;
  }
  
  .session-title {
    font-weight: 600;
    letter-spacing: 0.2px;
    color: #333;
    transition: color 0.3s ease;
  }
  
  .session-item:hover .session-title {
    color: #4D6BFE;
  }
  
  .session-time {
    font-size: 12px;
    color: #999;
    font-weight: 500;
    transition: color 0.3s ease;
  }
  
  .session-item:hover .session-time {
    color: rgba(77, 107, 254, 0.7);
  }
  
  .session-number {
    font-size: 14px;
    font-weight: 600;
    color: #4D6BFE;
  }
  
  .collapse-button {
    position: absolute;
    right: -20px;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #4D6BFE, #3D5BEE);
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(77, 107, 254, 0.2);
  }
  
  .collapse-button:hover {
    background: linear-gradient(135deg, #3D5BEE, #2D4BDE);
    box-shadow: 0 4px 12px rgba(77, 107, 254, 0.3);
    transform: translateY(-50%) scale(1.05);
  }
  
  .collapse-button svg {
    width: 24px;
    height: 24px;
    color: #fff;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: rotate(0deg);
  }
  
  .session-list.collapsed .collapse-button svg {
    transform: rotate(180deg);
  }
  
  .session-list.collapsed + .chat-main {
    width: calc(100% - 80px);
  }
  
  .chat-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #f5f5f5;
    transition: all 0.3s ease;
    height: 100%;
    overflow: hidden;
  }
  
  .chat-title {
    padding: 16px;
    background-color: #fff;
    font-weight: 600;
    letter-spacing: 0.3px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .session-id-text {
    flex-grow: 1;
  }
  
  .title-action-buttons {
    display: flex;
    gap: 10px;  /* 增加按钮间距 */
    flex-shrink: 0;
  }
  
  .title-action-button {
    display: flex;
    align-items: center;
    gap: 6px;  /* 略微增加图标和文字间距 */
    border-radius: 8px; /* 稍微增加圆角半径 */
    font-size: 14px; /* 增大字体大小 */
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    padding: 6px 12px; /* 增大内边距使按钮更大 */
    height: auto; /* 确保高度自适应 */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); /* 添加轻微阴影 */
  }
  
  .title-action-button svg {
    margin-bottom: 0px;
    width: 16px;  /* 统一设置图标大小 */
    height: 16px;
  }
  
  /* 统一使用主题色调，保持一致性 */
  .title-action-button.supplementary-button {
    background-color: #eef5ff;
    border-color: #c2d6ff;
    color: #4D6BFE;
  }
  .title-action-button.supplementary-button:hover:not(:disabled) {
    background-color: #dae8ff;
    border-color: #a3c0ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(77, 107, 254, 0.15);
  }

  .title-action-button.diagnose-button {
    background-color: #f0f5ff;
    border-color: #d6e4ff;
    color: #4D6BFE;
  }
  .title-action-button.diagnose-button:hover:not(:disabled) {
    background-color: #d6e4ff;
    border-color: #adc6ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(77, 107, 254, 0.15);
  }

  .title-action-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
  
  .chat-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    margin: 16px;
    border-radius: 24px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
    position: relative;
    height: calc(100% - 32px);
  }
  
  .chat-messages {
    flex: 1;
    padding: 16px 24px;
    padding-bottom: 160px;
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
    gap: 16px;
    position: relative;
  }
   
  .chat-messages::after {
    display: none;
  }
  
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 16px;
    position: absolute;
    bottom: 120px;
    left: 0;
    right: 0;
    background: transparent;
  }
  
  .loading-dots {
    display: flex;
    gap: 8px;
  }
  
  .loading-dots span {
    width: 8px;
    height: 8px;
    background-color: #4D6BFE;
    border-radius: 50%;
    display: inline-block;
    animation: bounce 1.4s infinite ease-in-out both;
  }
  
  .loading-dots span:nth-child(1) {
    animation-delay: -0.32s;
  }
  
  .loading-dots span:nth-child(2) {
    animation-delay: -0.16s;
  }
  
  @keyframes bounce {
    0%, 80%, 100% { 
      transform: scale(0);
    } 
    40% { 
      transform: scale(1.0);
    }
  }
  
  .chat-input-container {
    height: 120px;
    background: #fff;
    border-radius: 0 0 24px 24px;
    position: sticky;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.05);
    padding: 0;
  }
  
  .input-wrapper {
    height: 100%;
    padding: 20px;
    position: relative;
    background: #fff;
    border-radius: 0 0 24px 24px;
  }
  
  .chat-input {
    width: 100%;
    height: 80px;
    border: 2px solid #4D6BFE;
    resize: none;
    padding: 12px 16px;
    font-size: 14px;
    font-weight: 500;
    background-color: #fff;
    transition: all 0.3s ease;
    line-height: 1.6;
    border-radius: 16px;
    letter-spacing: 0.2px;
    box-shadow: inset 0 1px 3px rgba(77, 107, 254, 0.1);
  }
  
  .chat-input:focus {
    border-color: #4D6BFE;
    box-shadow: 0 0 0 3px rgba(77, 107, 254, 0.2);
    outline: none;
  }
  
  .chat-input:hover {
    border-color: #4D6BFE;
    box-shadow: 0 0 0 3px rgba(77, 107, 254, 0.1);
  }
  
  .button-group {
    position: absolute;
    right: 36px;
    bottom: 32px;
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 0px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }
  
  .voice-button {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 30px;
    background: linear-gradient(135deg, #f5f5f5, #e8e8e8);
    cursor: pointer;
    transition: all 0.3s ease;
  }
  
  .voice-button:hover {
    background: linear-gradient(135deg, #e8e8e8, #ddd);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  .voice-button.recording {
    background: linear-gradient(135deg, #4D6BFE, #3D5BEE);
    animation: pulse 2s infinite;
  }
  
  .voice-button.recording:hover {
    background: linear-gradient(135deg, #3D5BEE, #2D4BDE);
    box-shadow: 0 4px 12px rgba(77, 107, 254, 0.3);
  }
  
  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(77, 107, 254, 0.4);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(77, 107, 254, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(77, 107, 254, 0);
    }
  }
  
  .voice-button img {
    width: 45px;
    height: 45px;
    transition: all 0.3s ease;
    filter: brightness(0.4);
  }
  
  .voice-button.recording img {
    filter: brightness(0) invert(1);
  }
  
  .voice-button:hover img {
    transform: scale(1.05);
  }
  
  .send-button {
    width: 60px;
    height: 60px;
    padding: 0;
    border: none;
    background: linear-gradient(135deg, #4D6BFE, #3D5BEE) !important;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    cursor: pointer;
    border-radius: 30px;
    transition: all 0.3s ease;
  }
  
  .send-button:hover:not(:disabled) {
    background: linear-gradient(135deg, #3D5BEE, #2D4BDE) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(77, 107, 254, 0.3);
  }
  
  .send-icon {
    width: 24px;
    height: 24px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .send-icon::before {
    content: '';
    width: 20px;
    height: 3px;
    background: #fff;
    position: absolute;
    transform: rotate(45deg);
    transform-origin: right center;
    right: 0;
    transition: all 0.3s ease;
  }
  
  .send-icon::after {
    content: '';
    width: 20px;
    height: 3px;
    background: #fff;
    position: absolute;
    transform: rotate(-45deg);
    transform-origin: right center;
    right: 0;
    transition: all 0.3s ease;
  }
  
  .send-button:disabled {
    background: #f0f0f0 !important;
    cursor: not-allowed;
  }
  
  .send-button:disabled .send-icon::before,
  .send-button:disabled .send-icon::after {
    background: #ccc;
  }
  
  .message-item {
    display: flex;
    gap: 16px;
    padding: 12px 20px;
    position: relative;
    margin: 8px 0;
    border-radius: 16px;
    transition: background-color 0.3s ease;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;
  }
  
  .message-item:hover {
    background-color: rgba(0, 0, 0, 0.02);
  }
  
  .message-item.patient {
    flex-direction: row-reverse;
    justify-content: flex-start;
  }
  
  .message-content {
    padding: 12px 16px;
    max-width: 80%;
    word-break: break-word;
    font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
    font-size: 15px;
    font-weight: normal;
    letter-spacing: 0.2px;
    line-height: 1.8;
    color: #333;
    background: rgba(245, 245, 245, 0.8);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    border-radius: 12px;
    text-align: left;
  }
  
  .message-item.doctor .message-content {
    background: rgba(245, 245, 245, 0.8);
    color: #333;
    text-align: left;
    margin-right: auto;
  }
  
  .message-item.patient .message-content {
    background: rgba(230, 240, 255, 0.8);
    color: #333;
    text-align: left;
    margin-left: auto;
  }
  
  :deep(.ant-avatar) {
    width: 40px;
    height: 40px;
    border: 2px solid rgba(77, 107, 254, 0.1);
    transition: all 0.3s ease;
    border-radius: 8px;
    margin-top: 8px;
  }
  
  .message-item.patient :deep(.ant-avatar) {
    margin-right: 0;
    margin-left: 8px;
  }
  
  .message-item.doctor :deep(.ant-avatar) {
    margin-right: 8px;
    margin-left: 0;
  }
  
  .no-session {
    text-align: center;
    color: #999;
    margin-top: 40px;
    font-weight: 500;
    letter-spacing: 0.3px;
  }
  
  .message-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  
  .message {
    display: flex;
    gap: 12px;
    max-width: 80%;
  }
  
  .message.user {
    flex-direction: row-reverse;
    margin-left: auto;
  }
  
  .message .avatar {
    width: 40px;
    height: 40px;
    flex-shrink: 0;
  }
  
  .message .avatar img {
    width: 100%;
    height: 100%;
    border-radius: 8px;
  }
  
  .message-content {
    background: #f0f2f5;
    padding: 12px 16px;
    border-radius: 12px;
    font-size: 14px;
    line-height: 1.5;
    white-space: pre-wrap;
    word-break: break-word;
  }
  
  .message.user .message-content {
    background: #e3f2fd;
  }
  
  .message.doctor .message-content {
    background: #f5f5f5;
  }
  
  .input-area {
    border-top: 1px solid #e0e0e0;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  .input-area textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    resize: none;
    font-size: 14px;
    line-height: 1.5;
  }
  
  .button-group {
    display: flex;
    justify-content: flex-end;
  }
  
  .button-group button {
    padding: 8px 24px;
    background: #1976d2;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.2s;
  }
  
  .button-group button:hover {
    background: #1565c0;
  }
  
  .button-group button:disabled {
    background: #ccc;
    cursor: not-allowed;
  }
  
  .new-session-wrapper {
    padding: 16px 0;
    display: flex;
    justify-content: center;
    border-bottom: none;
    margin-bottom: 8px;
  }
  
  .new-session-button {
    width: 90%;
    height: 40px;
    font-size: 16px;
    font-weight: 600;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background: linear-gradient(135deg, #4D6BFE, #3D5BEE);
    border-radius: 20px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
  }
  
  .new-session-button:hover {
    background: linear-gradient(135deg, #3D5BEE, #2D4BDE);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(77, 107, 254, 0.3);
  }
  
  .plus-icon {
    font-size: 20px;
    font-weight: bold;
    line-height: 1;
  }
  
  .username {
    font-weight: 600;
    letter-spacing: 0.3px;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    transition: all 0.3s ease;
  }
  
  .audio-controls,
  .play-button {
    display: none;
  }

  .title-action-button.cot-button {
    background-color: #eef5ff;
    border-color: #c2d6ff;
    color: #4D6BFE;
  }
  .title-action-button.cot-button:hover:not(:disabled) {
    background-color: #dae8ff;
    border-color: #a3c0ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(77, 107, 254, 0.15);
  }
  
  .title-action-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  /* 您提供的上传组件样式 */
  /* you can make up upload button and sample style by using stylesheets */
  .ant-upload-select-picture-card i {
    font-size: 32px;
    color: #999;
  }

  .ant-upload-select-picture-card .ant-upload-text {
    margin-top: 8px;
    color: #666;
  }

  /* 为新按钮添加一些样式 (可选) */
  .title-action-button.upload-button {
    background-color: #eef5ff;
    border-color: #c2d6ff;
    color: #4D6BFE;
  }
  .title-action-button.upload-button:hover:not(:disabled) {
    background-color: #dae8ff;
    border-color: #a3c0ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(77, 107, 254, 0.15);
  }

  /* 确保 clearfix 生效 (如果需要) */
  .clearfix::after {
    content: "";
    display: table;
    clear: both;
  }

  .title-action-button.guide-button {
    background-color: #eef5ff;
    border-color: #c2d6ff;
    color: #4D6BFE;
  }
  .title-action-button.guide-button:hover:not(:disabled) {
    background-color: #dae8ff;
    border-color: #a3c0ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(77, 107, 254, 0.15);
  }

  /* 您提供的上传组件样式 */
  /* you can make up upload button and sample style by using stylesheets */
  .ant-upload-select-picture-card i {
    font-size: 32px;
    color: #999;
  }

  .ant-upload-select-picture-card .ant-upload-text {
    margin-top: 8px;
    color: #666;
  }

  /* 为新按钮添加一些样式 (可选) */
  .title-action-button.upload-button {
    background-color: #eef5ff;
    border-color: #c2d6ff;
    color: #4D6BFE;
  }
  .title-action-button.upload-button:hover:not(:disabled) {
    background-color: #dae8ff;
    border-color: #a3c0ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(77, 107, 254, 0.15);
  }

  /* 确保 clearfix 生效 (如果需要) */
  .clearfix::after {
    content: "";
    display: table;
    clear: both;
  }

  /* 添加消息内容样式，确保HTML内容正确渲染 */
  .message-content :deep(hr) {
    border: 0;
    height: 1px;
    background: #ddd;
    margin: 10px 0;
  }

  .message-content :deep(ul) {
    margin-left: 20px;
    padding-left: 0;
  }

  .message-content :deep(li) {
    margin-bottom: 5px;
  }

  /* 让治疗建议卡片在消息中有更好的外观 */
  .message-content :deep(.treatment-card) {
    background-color: #f9f9f9;
    border-radius: 8px;
    border-left: 4px solid #1890ff;
    padding: 10px;
    margin-top: 5px;
  }

  /* 添加新的样式用于指南内容显示 */
  :deep(.guide-text-content) {
    line-height: 1.6;
    letter-spacing: normal;
    text-align: left;
    display: block;
    margin: 5px 0;
  }

  :deep(.guide-item-content) {
    line-height: 1.6;
    letter-spacing: normal;
    text-align: left;
  }

  :deep(.guide-relevant-content) {
    margin-top: 5px;
  }

  :deep(.guide-relevant-item) {
    border-left: 3px solid #4D6BFE;
  }

  :deep(.guide-result-item) {
    background-color: #fdfdfd;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
  }

  /* 为查询表格添加样式 */
  .query-table-container {
    margin: 10px 0;
    background-color: #f9f9f9;
    border-radius: 6px;
    padding: 10px;
    border: 1px solid #eee;
  }

  .query-table {
    width: 100%;
    border-collapse: collapse;
    font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
    font-size: 14px;
  }

  .query-table tr {
    border-bottom: 1px solid #eee;
  }

  .query-table tr:last-child {
    border-bottom: none;
  }

  .query-label {
    font-weight: bold;
    color: #333;
    padding: 8px 10px 8px 0;
    width: 100px;
    vertical-align: top;
    text-align: right;
  }

  .query-value {
    color: #555;
    padding: 8px 0;
    white-space: pre-wrap;
    line-height: 1.5;
    word-break: break-word;
  }

  /* COT显示包装器样式 */
  .cot-display-wrapper {
    height: 70vh;
    overflow-y: auto;
    padding: 0;
    font-family: 'Microsoft YaHei', sans-serif;
  }

  /* 修复v-html中样式的显示问题 */
  :deep(.cot-container),
  :deep(.cot-entry),
  :deep(.cot-section-content) {
    width: 100%;
  }

  /* 诊疗指南详情样式 */
  .guide-content-wrapper {
    background-color: #f9f9f9;
  }

  .guide-query-section {
    padding: 20px;
    margin-bottom: 20px;
    background-color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  }

  .guide-section-title {
    font-size: 18px;
    font-weight: 600;
    color: #4D6BFE;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e8e8e8;
  }

  .query-table-container {
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
  }

  .query-table {
    width: 100%;
    border-collapse: collapse;
  }

  .query-table tr:nth-child(odd) {
    background-color: #f5f9ff;
  }

  .query-label {
    width: 120px;
    padding: 12px 15px;
    font-weight: 600;
    color: #333;
    border-right: 1px solid #e8e8e8;
    vertical-align: top;
  }

  .query-value {
    padding: 12px 15px;
    color: #555;
    line-height: 1.6;
  }

  .symptom-text {
    white-space: pre-wrap;
    line-height: 1.6;
  }

  .guide-results-wrapper {
    padding: 0 20px 20px 20px;
  }

  .guide-result-card {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  }

  .guide-info-item {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  .guide-info-item:last-child {
    border-bottom: none;
  }

  .guide-info-label {
    font-weight: 600;
    margin-bottom: 8px;
    color: #4D6BFE;
    font-size: 16px;
  }

  .guide-info-content {
    color: #333;
    line-height: 1.8;
    white-space: pre-wrap;
  }

  .guide-relevant-content {
    margin-top: 20px;
  }

  .guide-relevant-items {
    margin-top: 10px;
  }

  .guide-relevant-item {
    background-color: #f5f9ff;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 12px;
  }

  .guide-relevant-title {
    font-weight: 600;
    margin-bottom: 8px;
    color: #1976d2;
  }

  .guide-relevant-content-text {
    white-space: pre-wrap;
    line-height: 1.6;
  }

  /* 覆盖 ant-design 的 tabs 样式 */
  :deep(.ant-tabs-nav) {
    margin-bottom: 20px;
  }

  :deep(.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn) {
    color: #4D6BFE;
  }

  :deep(.ant-tabs-ink-bar) {
    background-color: #4D6BFE;
  }

  /* 诊断结果和观察记录容器样式 */
  :deep(.diagnosis-observation-container) {
    background-color: #f9f9ff;
    border-radius: 10px;
    padding: 16px;
    margin: 12px 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid #e8eeff;
  }

  :deep(.diagnosis-section) {
    margin-bottom: 16px;
    background-color: #f0f7ff;
    border-radius: 8px;
    padding: 12px;
    border-left: 4px solid #4D6BFE;
  }

  :deep(.diagnosis-title) {
    font-weight: 600;
    font-size: 16px;
    color: #4D6BFE;
    margin-bottom: 8px;
    padding-bottom: 6px;
    border-bottom: 1px solid rgba(77, 107, 254, 0.2);
  }

  :deep(.diagnosis-content) {
    white-space: pre-wrap;
    line-height: 1.6;
    color: #333;
    font-size: 15px;
    font-weight: 500;
  }

  :deep(.observation-section) {
    background-color: #f7fdf7;
    border-radius: 8px;
    padding: 12px;
    border-left: 4px solid #42b983;
  }

  :deep(.observation-title) {
    font-weight: 600;
    font-size: 16px;
    color: #42b983;
    margin-bottom: 8px;
    padding-bottom: 6px;
    border-bottom: 1px solid rgba(66, 185, 131, 0.2);
  }

  :deep(.observation-content) {
    white-space: pre-wrap;
    line-height: 1.6;
    color: #333;
  }

  /* 治疗建议容器样式 */
  :deep(.treatment-container) {
    background-color: #f9fafb;
    border-radius: 10px;
    padding: 16px;
    margin: 12px 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid #eef1f6;
  }

  :deep(.treatment-header) {
    font-weight: 600;
    font-size: 18px;
    color: #4D6BFE;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(77, 107, 254, 0.15);
  }

  /* 药品卡片样式 */
  :deep(.medications-container) {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 20px;
  }

  :deep(.medications-title) {
    font-weight: 600;
    font-size: 18px;
    color: #4D6BFE;
    margin-bottom: 10px;
    padding-bottom: 6px;
    border-bottom: 1px solid rgba(77, 107, 254, 0.15);
  }

  :deep(.medication-card) {
    background: linear-gradient(145deg, #ffffff, #f8fbff);
    border-radius: 10px;
    padding: 12px;
    box-shadow: 0 3px 10px rgba(77, 107, 254, 0.08);
    border: 1px solid rgba(77, 107, 254, 0.1);
    overflow: hidden;
  }

  :deep(.medication-header) {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    border-bottom: 1px dashed rgba(77, 107, 254, 0.2);
    padding-bottom: 8px;
  }

  :deep(.med-icon) {
    font-size: 18px;
    margin-right: 8px;
  }

  :deep(.med-name) {
    font-weight: 600;
    font-size: 16px;
    color: #4D6BFE;
  }

  :deep(.medication-details) {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  :deep(.med-detail) {
    display: flex;
    padding: 4px 0;
  }

  :deep(.med-label) {
    font-weight: 500;
    color: #666;
    min-width: 80px;
  }

  /* 生活建议样式 */
  :deep(.lifestyle-header) {
    font-weight: 600;
    font-size: 16px;
    color: #42b983;
    margin: 20px 0 12px 0;
    padding-bottom: 6px;
    border-bottom: 1px solid rgba(66, 185, 131, 0.2);
  }

  :deep(.lifestyle-content) {
    background-color: #f7fdf7;
    border-radius: 8px;
    padding: 12px;
    margin-top: 8px;
    border-left: 3px solid #42b983;
  }

  :deep(.lifestyle-item) {
    display: flex;
    align-items: flex-start;
    margin-bottom: 8px;
    line-height: 1.5;
  }

  :deep(.lifestyle-bullet) {
    color: #42b983;
    font-size: 18px;
    margin-right: 8px;
    flex-shrink: 0;
    margin-top: -2px;
  }

  /* 推理过程样式 */
  :deep(.reasoning-container) {
    background-color: #f8f9fa;
    border-left: 4px solid #5c6ac4;
    padding: 16px;
    margin-top: 20px;
    color: #444;
    font-size: 14px;
    border-radius: 0 8px 8px 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  :deep(.reasoning-header) {
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 16px;
    color: #5c6ac4;
    margin-bottom: 14px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(92, 106, 196, 0.2);
  }

  :deep(.reasoning-icon) {
    font-size: 18px;
    margin-right: 8px;
  }

  :deep(.reasoning-step) {
    margin: 12px 0;
    padding: 8px 12px;
    background-color: rgba(92, 106, 196, 0.05);
    border-radius: 6px;
    border-left: 3px solid #5c6ac4;
  }

  :deep(.reasoning-step-marker) {
    font-weight: 600;
    color: #5c6ac4;
    margin-right: 4px;
  }

  :deep(.reasoning-highlight) {
    font-weight: 600;
    color: #5c6ac4;
  }

  :deep(.reasoning-point) {
    display: flex;
    margin: 8px 0;
    padding: 4px 0;
  }

  :deep(.reasoning-number) {
    color: #5c6ac4;
    font-weight: 600;
    margin-right: 6px;
    min-width: 22px;
  }

  :deep(.reasoning-med) {
    color: #e95c43;
    font-weight: 500;
    background-color: rgba(233, 92, 67, 0.1);
    padding: 0 4px;
    border-radius: 3px;
  }

  :deep(.reasoning-diagnosis) {
    color: #3b7ea0;
    font-weight: 500;
    background-color: rgba(59, 126, 160, 0.1);
    padding: 0 4px;
    border-radius: 3px;
  }

  :deep(.reasoning-separator) {
    height: 1px;
    background: linear-gradient(to right, rgba(92, 106, 196, 0), rgba(92, 106, 196, 0.3), rgba(92, 106, 196, 0));
    margin: 14px 0;
  }

  :deep(.treatment-divider) {
    border-top: 1px dashed #ccc;
    margin: 15px 0;
    background: none;
    height: 1px;
  }

  :deep(.condition-section) {
    background-color: #fff8f0;
    border-radius: 8px;
    padding: 12px;
    border-left: 4px solid #ff9800;
    margin-bottom: 16px;
  }

  :deep(.condition-title) {
    font-weight: 600;
    font-size: 16px;
    color: #ff9800;
    margin-bottom: 8px;
    padding-bottom: 6px;
    border-bottom: 1px solid rgba(255, 152, 0, 0.2);
  }

  :deep(.condition-content) {
    white-space: pre-wrap;
    line-height: 1.6;
    color: #333;
    font-size: 15px;
  }

  .modal-actions {
    margin-top: 20px;
    display: flex;
    flex-direction: column; /* Stack buttons vertically */
    gap: 10px; /* Space between buttons */
  }

  .modal-footer-buttons {
    display: flex;
    justify-content: flex-end; /* Align buttons to the right */
    gap: 10px; /* Space between footer buttons */
    margin-top: 20px;
  }

  .modal-actions {
    margin-top: 20px;
    display: flex;
    flex-direction: column; /* Stack buttons vertically */
    gap: 10px; /* Space between buttons */
  }

  .preliminary-diagnosis-result {
    padding: 10px;
    border-radius: 5px;
    background-color: #f0f8ff; /* AliceBlue, or any other suitable color */
    margin-top: 5px;
    border: 1px solid #d4e8ff;
  }

  .preliminary-diagnosis-result h4 {
    margin-top: 0;
    margin-bottom: 5px;
    color: #0056b3; /* Darker blue for headings */
  }

  .preliminary-diagnosis-result p {
    margin-bottom: 8px;
    line-height: 1.6;
  }
  .preliminary-diagnosis-result p:last-child {
    margin-bottom: 0;
  }

  /* Ensure message-item and doctor/patient classes are defined for the loading indicator if not already generic enough */
  .message-item.doctor .message-content {
      /* Styles for doctor messages, ensure they accommodate the spinner */
  }

  /* Preliminary Diagnosis Styles */
  :deep(.preliminary-diagnosis-card) {
    background-color: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    padding: 20px;
    margin-top: 15px;
    margin-bottom: 15px;
    border: 1px solid #e8eef5;
    overflow: hidden;
  }

  :deep(.prelim-diagnosis-header) {
    display: flex;
    align-items: center;
    margin-bottom: 18px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e8eef5;
  }

  :deep(.prelim-icon) {
    font-size: 24px;
    margin-right: 12px;
  }

  :deep(.prelim-title) {
    font-size: 18px;
    font-weight: 600;
    color: #1890ff;
  }

  :deep(.prelim-section) {
    background-color: #f9fbfd;
    border-radius: 10px;
    padding: 16px;
    margin-bottom: 16px;
    border-left: 4px solid transparent;
    transition: all 0.3s ease;
  }

  :deep(.prelim-section:hover) {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  :deep(.diagnosis-section) {
    border-left-color: #1890ff;
  }

  :deep(.suggestion-section) {
    border-left-color: #52c41a;
  }

  :deep(.guidelines-section) {
    border-left-color: #722ed1;
  }

  :deep(.reasoning-section) {
    border-left-color: #fa8c16;
  }

  :deep(.prelim-section-header) {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px dashed rgba(0, 0, 0, 0.1);
  }

  :deep(.section-icon) {
    font-size: 18px;
    margin-right: 10px;
  }

  :deep(.section-title) {
    font-weight: 600;
    font-size: 16px;
    color: #333;
  }

  :deep(.prelim-section-content) {
    font-size: 15px;
    line-height: 1.6;
    color: #333;
    white-space: pre-wrap;
  }

  :deep(.guidelines-content pre) {
    background-color: #f5f7fa;
    padding: 12px;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    white-space: pre-wrap;
    max-height: 300px;
    overflow-y: auto;
  }

  /* Preliminary Diagnosis Modal Styles */
  .preliminary-modal-content {
    padding: 10px 0;
  }

  .preliminary-modal-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
  }

  .prelim-modal-icon {
    font-size: 28px;
    margin-right: 15px;
  }

  .prelim-modal-title {
    font-size: 20px;
    font-weight: 600;
    color: #1890ff;
  }

  .preliminary-modal-description {
    margin-bottom: 20px;
  }

  .preliminary-modal-description p {
    font-size: 15px;
    line-height: 1.6;
    color: #595959;
    margin: 0;
  }

  .preliminary-modal-actions {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .action-item {
    display: flex;
    background-color: #f8fafd;
    border-radius: 10px;
    padding: 15px;
    transition: all 0.3s ease;
    border: 1px solid #e8eef5;
  }

  .action-item:hover {
    background-color: #f0f7ff;
    border-color: #d7e8ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
  }

  .action-icon {
    font-size: 24px;
    margin-right: 15px;
    margin-top: 4px;
  }

  .action-content {
    flex: 1;
  }

  .action-title {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin-bottom: 6px;
  }

  .action-description {
    font-size: 14px;
    color: #595959;
    margin-bottom: 12px;
    line-height: 1.5;
  }

  .preliminary-modal-footer {
    display: flex;
    justify-content: flex-end;
  }

  /* Guide Modal Styles */
  .guide-header {
    margin-bottom: 20px;
    text-align: center;
    padding-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
  }

  .guide-title {
    font-size: 24px;
    color: #1890ff;
    margin-bottom: 8px;
  }

  .guide-subtitle {
    color: #666;
    font-size: 14px;
  }

  .guide-card {
    margin-bottom: 16px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  }

  .guide-card-title {
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: 500;
  }

  .guide-card-icon {
    margin-right: 8px;
    font-size: 20px;
  }

  .guide-section {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  .guide-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
  }

  .guide-section-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
  }

  .guide-section-content {
    font-size: 14px;
    line-height: 1.6;
    color: #333;
  }

  .guide-info-section {
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px dashed #f0f0f0;
  }

  .guide-info-label {
    font-weight: 500;
    margin-bottom: 8px;
    color: #595959;
  }

  .guide-symptoms-content {
    line-height: 1.6;
  }

  .guide-relevant-content {
    margin-top: 16px;
  }

  .guide-relevant-items {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 12px;
  }

  .guide-relevant-item {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #eee;
  }

  .guide-relevant-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
  }

  .guide-relevant-title {
    font-weight: 500;
    margin-bottom: 8px;
    font-size: 15px;
  }

  .guide-relevant-content-text {
    font-size: 14px;
    line-height: 1.6;
  }

  .json-content {
    background-color: #f5f7fa;
    padding: 12px;
    border-radius: 6px;
    overflow-x: auto;
    font-family: Consolas, Monaco, 'Andale Mono', monospace;
    font-size: 13px;
    line-height: 1.5;
    color: #333;
  }

  /* 初步诊断样式 */
  .preliminary-diagnosis-section {
    margin-top: 15px;
    margin-bottom: 15px;
    background-color: #f0f7ff;
    border-radius: 8px;
    padding: 15px;
    border-left: 4px solid #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
  }

  .preliminary-diagnosis-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }

  .preliminary-diagnosis-icon {
    font-size: 18px;
    margin-right: 8px;
    color: #1890ff;
  }

  .preliminary-diagnosis-title {
    font-weight: 600;
    color: #1890ff;
    font-size: 16px;
  }

  .preliminary-diagnosis-content {
    font-size: 14px;
    line-height: 1.6;
    color: #333;
    padding: 5px 0 0 26px;
  }

  /* 检查建议样式 */
  .inspection-suggestions-section {
    margin-top: 15px;
    margin-bottom: 15px;
    background-color: #f6fff0;
    border-radius: 8px;
    padding: 15px;
    border-left: 4px solid #52c41a;
    box-shadow: 0 2px 8px rgba(82, 196, 26, 0.1);
  }

  .inspection-suggestions-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }

  .inspection-suggestions-icon {
    font-size: 18px;
    margin-right: 8px;
    color: #52c41a;
  }

  .inspection-suggestions-title {
    font-weight: 600;
    color: #52c41a;
    font-size: 16px;
  }

  .inspection-suggestions-content {
    font-size: 14px;
    line-height: 1.6;
    color: #333;
    padding: 5px 0 0 26px;
  }

  .inspection-list {
    list-style-type: none;
    padding: 0;
    margin: 0;
  }

  .inspection-item {
    margin-bottom: 6px;
    display: flex;
    align-items: flex-start;
  }

  .inspection-bullet {
    color: #52c41a;
    margin-right: 8px;
    font-size: 16px;
    line-height: 1.4;
  }

  .inspection-json {
    background-color: #f9f9f9;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #eaeaea;
    white-space: pre-wrap;
    font-family: monospace;
    font-size: 13px;
    line-height: 1.5;
    color: #333;
  }
  </style> 