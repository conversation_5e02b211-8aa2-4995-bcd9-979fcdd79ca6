package com.makiyo.aidoctor.service;

import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.File;

/**
 * 语音识别服务接口
 * 处理音频文件并流式返回识别结果
 */
public interface ASRService {
    
    /**
     * 处理音频文件并通过SSE发射器流式返回识别结果
     * 
     * @param audioFile 音频文件
     * @param emitter SSE发射器，用于流式返回识别结果
     * @throws Exception 处理过程中可能发生的异常
     */
    void processAudioFile(File audioFile, SseEmitter emitter) throws Exception;
} 