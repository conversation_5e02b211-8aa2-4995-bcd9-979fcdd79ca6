package com.makiyo.aidoctor.controller;

import com.makiyo.aidoctor.service.OcrQueueService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 用于测试OCR队列服务的控制器
 */
@RestController
@RequestMapping("/ocr/queue")
public class OcrQueueTestController {
    private static final Logger log = LoggerFactory.getLogger(OcrQueueTestController.class);
    
    // 用于跟踪队列统计信息
    private final AtomicInteger totalRequestsReceived = new AtomicInteger(0);
    private final AtomicInteger totalRequestsProcessed = new AtomicInteger(0);
    private final AtomicInteger totalRequestsFailed = new AtomicInteger(0);
    private final ConcurrentHashMap<Integer, Long> processingTimes = new ConcurrentHashMap<>();
    
    private final OcrQueueService ocrQueueService;
    
    @Autowired
    public OcrQueueTestController(OcrQueueService ocrQueueService) {
        this.ocrQueueService = ocrQueueService;
    }
    
    /**
     * 获取OCR队列的统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getQueueStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalRequestsReceived", totalRequestsReceived.get());
        stats.put("totalRequestsProcessed", totalRequestsProcessed.get());
        stats.put("totalRequestsFailed", totalRequestsFailed.get());
        
        // 计算平均处理时间
        long totalTime = 0;
        int count = 0;
        for (Long time : processingTimes.values()) {
            totalTime += time;
            count++;
        }
        double avgProcessingTime = count > 0 ? (double) totalTime / count : 0;
        
        stats.put("averageProcessingTimeMs", avgProcessingTime);
        stats.put("currentQueueSize", ocrQueueService.getQueueSize());
        
        return ResponseEntity.ok(stats);
    }
    
    /**
     * 获取OCR队列的当前状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getQueueStatus() {
        Map<String, Object> status = new HashMap<>();
        
        // 获取当前队列大小
        int queueSize = ocrQueueService.getQueueSize();
        
        // 计算队列健康状态
        String healthStatus;
        if (queueSize == 0) {
            healthStatus = "IDLE";
        } else if (queueSize < 5) {
            healthStatus = "NORMAL";
        } else if (queueSize < 10) {
            healthStatus = "BUSY";
        } else {
            healthStatus = "OVERLOADED";
        }
        
        // 计算统计信息
        int totalRequests = totalRequestsReceived.get();
        int completedRequests = totalRequestsProcessed.get() + totalRequestsFailed.get();
        double completionRate = totalRequests > 0 ? (double) completedRequests / totalRequests * 100 : 0;
        double successRate = completedRequests > 0 ? (double) totalRequestsProcessed.get() / completedRequests * 100 : 0;
        
        status.put("queueSize", queueSize);
        status.put("healthStatus", healthStatus);
        status.put("completionRate", String.format("%.2f%%", completionRate));
        status.put("successRate", String.format("%.2f%%", successRate));
        status.put("pendingRequests", totalRequests - completedRequests);
        
        return ResponseEntity.ok(status);
    }
    
    /**
     * 用于OCR控制器更新统计信息
     * @param sessionId 会话ID
     * @param success 是否成功
     * @param processingTimeMs 处理时间（毫秒）
     */
    public void updateStats(Integer sessionId, boolean success, long processingTimeMs) {
        if (success) {
            totalRequestsProcessed.incrementAndGet();
        } else {
            totalRequestsFailed.incrementAndGet();
        }
        processingTimes.put(sessionId, processingTimeMs);
        
        // 记录统计信息
        log.info("OCR处理统计更新 - 会话ID: {}, 成功: {}, 处理时间: {}ms, 总接收: {}, 总成功: {}, 总失败: {}", 
                 sessionId, success, processingTimeMs,
                 totalRequestsReceived.get(), totalRequestsProcessed.get(), totalRequestsFailed.get());
    }
    
    /**
     * 记录新收到的请求
     */
    public void recordNewRequest() {
        totalRequestsReceived.incrementAndGet();
    }
} 