<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.makiyo.ai_doctor_prod.mapper.CaseItemMapper">
  <resultMap id="BaseResultMap" type="com.makiyo.ai_doctor_prod.entity.CaseItem">
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="profile" jdbcType="OTHER" property="profile" />
    <result column="interaction_history" property="interactionHistory" jdbcType="OTHER"
            typeHandler="com.makiyo.ai_doctor_prod.utils.JsonbTypeHandler"/>
  </resultMap>
  <insert id="insert" parameterType="com.makiyo.ai_doctor_prod.entity.CaseItem">
    insert into "case_item" (id, created_at, updated_at, 
      profile, interaction_history)
    values (#{id,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP}, 
      #{profile,jdbcType=OTHER}, 
      #{interactionHistory,jdbcType=OTHER,typeHandler=com.makiyo.ai_doctor_prod.utils.JsonbTypeHandler})
  </insert>
  <insert id="insertSelective" parameterType="com.makiyo.ai_doctor_prod.entity.CaseItem">
    insert into "case_item"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="profile != null">
        profile,
      </if>
      <if test="interactionHistory != null">
        interaction_history,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="profile != null">
        #{profile,jdbcType=OTHER},
      </if>
      <if test="interactionHistory != null">
        #{interactionHistory,jdbcType=OTHER,typeHandler=com.makiyo.ai_doctor_prod.utils.JsonbTypeHandler},
      </if>
    </trim>
  </insert>

  <!-- 根据 ID 查询 CaseItem -->
  <select id="findById" resultMap="BaseResultMap">
    select id, created_at, updated_at, profile, interaction_history
    from "case_item"
    where id = #{id,jdbcType=VARCHAR}
  </select>

  <!-- 查询所有 CaseItem 记录 -->
  <select id="findAll" resultMap="BaseResultMap">
    select id, created_at, updated_at, profile, interaction_history
    from "case_item"
    <where>
      <if test="startDate != null">
        and created_at &gt;= #{startDate}
      </if>
      <if test="endDate != null">
        and created_at &lt;= #{endDate}
      </if>
    </where>
    order by created_at desc
  </select>

  <select id="findAllWithFiltersAndPagination" resultMap="BaseResultMap">
    select id, created_at, updated_at, profile, interaction_history
    from "case_item"
    <where>
      <if test="startDate != null">
        and created_at &gt;= #{startDate,jdbcType=TIMESTAMP}
      </if>
      <if test="endDate != null">
        and created_at &lt;= #{endDate,jdbcType=TIMESTAMP}
      </if>
    </where>
    order by created_at desc
    limit #{limit} offset #{offset}
  </select>

  <select id="countAllWithFilters" resultType="long">
    select count(*)
    from "case_item"
    <where>
      <if test="startDate != null">
        and created_at &gt;= #{startDate,jdbcType=TIMESTAMP}
      </if>
      <if test="endDate != null">
        and created_at &lt;= #{endDate,jdbcType=TIMESTAMP}
      </if>
    </where>
  </select>

  <!-- 添加用于测试数据库连接的简单查询 -->
  <select id="testConnection" resultType="int">
    SELECT 1
  </select>

  <!-- 根据 ID 更新 interaction_history 字段 -->
  <update id="updateInteractionHistoryById">
    update "case_item"
    set interaction_history = #{interactionHistory,jdbcType=OTHER,typeHandler=com.makiyo.ai_doctor_prod.utils.JsonbTypeHandler}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>