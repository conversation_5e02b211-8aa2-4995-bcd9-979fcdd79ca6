import pandas as pd

class Doctor:
    def __init__(self, age,gender, chief_complaint, history_of_present_illness, 
                 past_history, contact_history_of_infectious_diseases, 
                 personal_history, family_history):
        self.age = age
        self.gender = gender
        self.chief_complaint = chief_complaint
        self.history_of_present_illness = history_of_present_illness
        self.past_history = past_history
        self.contact_history_of_infectious_diseases = contact_history_of_infectious_diseases
        self.personal_history = personal_history
        self.family_history = family_history
    
    def __repr__(self):
        return f"Doctor(age={self.age}, gender={self.gender},chief_complaint={self.chief_complaint}, history_of_present_illness={self.history_of_present_illness}, past_history={self.past_history}, contact_history_of_infectious_diseases={self.contact_history_of_infectious_diseases}, personal_history={self.personal_history}, family_history={self.family_history})"
    
    @classmethod
    def load_from_csv(cls, file_path):
        # 读取CSV文件
        df = pd.read_csv(file_path)

        print(df.columns)


        # 创建Doctor实例列表
        doctors = []
        for _, row in df.iterrows():
            doctor = cls(
                age=row['就诊时年龄'],
                gender=row['性别'],
                chief_complaint=row['主诉'],
                history_of_present_illness=row['现病史'],
                past_history=row['既往史'],
                contact_history_of_infectious_diseases=row['传染病接触史'],
                personal_history=row['个人史'],
                family_history=row['家族史']
            )
            doctors.append(doctor)
        return doctors
    

    @classmethod
    def load_from_json(cls, file_path):
        # 读取JSON文件
        df = pd.read_json(file_path)
        # 创建Doctor实例列表
        doctors = []
        
if __name__ == '__main__':
    doctors = Doctor.load_from_csv('../data/cases.csv')
    for doctor in doctors:
        print(doctor)
