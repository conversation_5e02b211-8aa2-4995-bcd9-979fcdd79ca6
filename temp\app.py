import asyncio
import json
import os
import pandas as pd
from src.config import MAX_ROUNDS
from src.interaction_history import InteractionHistory
from src.logger import get_logger
from src.planner import Planner
from src.executor import Executor
import requests
import base64
import json
from tqdm import tqdm
import time
from datetime import datetime
import re  # 导入 re 模块用于解析
import async_timeout
import atexit
import platform
from PIL import Image, ImageOps
import pytesseract
from pytesseract import Output
import io

from flask import Flask, render_template, request, redirect, url_for, jsonify

app = Flask(__name__)

logger = get_logger("test")

# --- Tesseract-OCR 路径配置 ---
if platform.system() == 'Windows':
    pytesseract.pytesseract.tesseract_cmd = r'D:\\Tesseract-OCR\\tesseract.exe'
elif platform.system() == 'Linux':
    os.environ['TESSDATA_PREFIX'] = r'/usr/share/tesseract/tessdata'
    pass


def correct_image_orientation(image: Image.Image) -> Image.Image:
    """
    根据Tesseract的OSD（方向和文字识别）功能校正图像的方向。

    参数:
        image: 一个Pillow的Image对象。

    返回:
        一个方向校正后的Pillow Image对象。
    """
    try:
        osd = pytesseract.image_to_osd(image, output_type=Output.DICT)
        rotation = osd.get('rotate', 0)
        if rotation > 0:
            image = image.rotate(-rotation, expand=True)
    except pytesseract.TesseractError as e:
        # OSD 失败是常见情况，特别是对于内容很少或没有清晰文本的图片。
        # 如果是"字符过少"的特定警告，我们只记录一条简洁信息。
        if "Too few characters" in str(e):
            print("OSD Warning: Too few characters to detect orientation. Using original image.")
        else:
            # 对于其他未知Tesseract错误，打印更详细的信息以供调试。
            print(f"An unexpected Tesseract OSD error occurred: {e}. Using original image.")
    return image


def convert_to_grayscale(image: Image.Image) -> Image.Image:
    """
    将Pillow Image对象转换为灰度图。

    参数:
        image: 一个Pillow的Image对象。

    返回:
        一个灰度化的Pillow Image对象。
    """
    return ImageOps.grayscale(image)


def process_image_for_ocr(image_path: str) -> Image.Image:
    """
    加载图像，校正其方向，并将其转换为灰度图，为OCR处理做准备。
    此函数能处理无文件扩展名的图像。

    参数:
        image_path: 图像文件的路径。

    返回:
        一个处理完成、可用于OCR的Pillow Image对象。
    """
    try:
        # 以二进制读取模式打开文件。这使得Pillow能通过文件内容（魔数）来识别
        # 图像格式，即使文件名没有扩展名。
        with open(image_path, 'rb') as f:
            img = Image.open(f)
            # 将图像数据加载到内存中。这是一个好习惯，可以确保在'with'语句
            # 关闭文件句柄之前，数据已被完全读取。
            img.load()

        # 第一步：校正方向
        oriented_img = correct_image_orientation(img)
        # 第二步：转换为灰度图
        grayscale_img = convert_to_grayscale(oriented_img)
        return grayscale_img
    except FileNotFoundError:
        print(f"错误: 在路径 {image_path} 未找到图像文件")
        raise
    except Exception as e:
        print(f"处理图像 {image_path} 时发生错误: {e}")
        raise

# ocr Api
API_BASE = "http://183.166.183.107:5901/v1/chat/completions"
HEADERS = {
    "Content-Type": "application/json",
    "Connection": "close"  # 添加Connection: close头，显式告知服务器不要保持连接
}

# 设置全局API超时时间为5分钟
API_TIMEOUT = 300  # 5分钟超时

# 创建全局session对象，配置连接池和重试策略
HTTP_SESSION = requests.Session()
# 配置适当的连接池参数
adapter = requests.adapters.HTTPAdapter(
    pool_connections=10,  # 降低连接池中保持的连接数
    pool_maxsize=10,  # 降低连接池允许的最大连接数
    max_retries=2  # 减少失败重试次数
)
# 将适配器挂载到session上
HTTP_SESSION.mount('http://', adapter)
HTTP_SESSION.mount('https://', adapter)


def shutdown_session():
    """确保应用关闭时释放所有连接资源"""
    if HTTP_SESSION:
        HTTP_SESSION.close()
        logger.info("Global HTTP session closed on application exit.")


atexit.register(shutdown_session)


def encode_image(image: Image.Image):
    """Encodes a PIL Image object to a base64 string."""
    try:
        buffered = io.BytesIO()
        # Using PNG format for lossless quality, suitable for OCR.
        image.save(buffered, format="PNG")
        img_str = base64.b64encode(buffered.getvalue()).decode("utf-8")
        return img_str
    except Exception as e:
        logger.error(f"Error encoding image object: {e}", exc_info=True)
        raise


def clean_treatment_recommendation(treatment_plan_str: str) -> str:
    """
    Removes the reasoning part from the treatment recommendation string.
    """
    if not isinstance(treatment_plan_str, str):
        return treatment_plan_str

    reasoning_markers = [
        "推理过程：", "推理过程:",
        "Reasoning:", "Reasoning :",
        "Thinking:", "Thinking :",
        "思考过程：", "思考过程:",
        "Underlying Logic:", "Rationale:"
    ]

    cleaned_plan = treatment_plan_str

    # First, try to find a marker and split
    for marker in reasoning_markers:
        if marker in cleaned_plan:
            parts = cleaned_plan.split(marker, 1)
            cleaned_plan = parts[0].strip()
            logger.info(f"Removed reasoning using marker: '{marker}'")
            return cleaned_plan

    # If no marker is found, try to identify sections based on common patterns
    # like "----" or multiple newlines followed by phrases like "The reasoning is"
    # This part can be made more sophisticated if needed.
    # For now, we rely primarily on the markers.

    # Fallback: look for a significant break (e.g., multiple newlines)
    # and then check if the subsequent part looks like reasoning.
    # This is harder to do reliably without more specific patterns.
    # A simpler approach might be to remove text after a certain number
    # of "----" or similar separators if they consistently appear before reasoning.

    # If "----" is used as a separator before the reasoning section
    if "\n\n\n------------------------------\n\n\n推理过程：" in treatment_plan_str:
        cleaned_plan = treatment_plan_str.split("\n\n\n------------------------------\n\n\n推理过程：", 1)[0].strip()
        logger.info("Removed reasoning using complex separator and '推理过程：'")
        return cleaned_plan

    # A more general check for "----" followed by reasoning keywords
    # This is a bit risky as "----" could be used elsewhere.
    separator_candidates = ["----", "====="]
    for sep in separator_candidates:
        if sep in cleaned_plan:
            potential_parts = cleaned_plan.split(sep)
            # Iterate backwards to find the last segment that might be reasoning
            for i in range(len(potential_parts) - 1, 0, -1):
                segment_to_check = potential_parts[i]
                is_reasoning_segment = False
                for marker_keyword in ["推理", "Reasoning", "Thinking", "思考"]:  # Just keywords, not full markers
                    if marker_keyword in segment_to_check:
                        is_reasoning_segment = True
                        break
                if is_reasoning_segment:
                    cleaned_plan = sep.join(potential_parts[:i]).strip()
                    logger.info(f"Tentatively removed reasoning after separator '{sep}' and keyword match.")
                    return cleaned_plan  # Return after first plausible removal from end

    # If the reasoning is just appended without a clear marker but after main content
    # and it's known to start with "好的，我现在需要处理这个病例"
    specific_reasoning_start_phrase = "好的，我现在需要处理这个病例"
    if specific_reasoning_start_phrase in cleaned_plan:
        # Check if this phrase appears after some substantial content
        phrase_index = cleaned_plan.find(specific_reasoning_start_phrase)
        if phrase_index > len(cleaned_plan) / 2:  # Heuristic: if it's in the latter half
            # Check for preceding "生活建议："
            life_advice_marker = "生活建议："
            life_advice_index = cleaned_plan.rfind(life_advice_marker, 0, phrase_index)

            if life_advice_index != -1:
                # Find the end of "生活建议" section (e.g., by double newline or start of reasoning phrase)
                end_of_life_advice_content = cleaned_plan.find("\n\n", life_advice_index + len(life_advice_marker))
                if end_of_life_advice_content != -1 and end_of_life_advice_content < phrase_index:
                    # Ensure the reasoning phrase immediately follows or is close
                    text_between = cleaned_plan[end_of_life_advice_content:phrase_index].strip()
                    if len(text_between) < 50:  # Allow for some whitespace or minor text
                        cleaned_plan = cleaned_plan[:phrase_index].strip()
                        logger.info(
                            f"Removed reasoning starting with specific phrase: '{specific_reasoning_start_phrase}' after '生活建议：'")
                        return cleaned_plan
                elif end_of_life_advice_content == -1:  # Life advice might be the last thing before reasoning
                    cleaned_plan = cleaned_plan[:phrase_index].strip()
                    logger.info(
                        f"Removed reasoning starting with specific phrase (no double newline after life advice): '{specific_reasoning_start_phrase}'")
                    return cleaned_plan

    logger.info(
        "No explicit reasoning marker found or specific pattern matched for removal. Returning plan as is or after partial clean.")
    return cleaned_plan

def api_call(image: Image.Image, question):
    try:
        base64_image = encode_image(image)
    except Exception:
        return None  # 如果编码失败，则无法继续

    payload = {
        "model": "BaichuanMed-OCR-7B",
        "messages": [
            {
                "role": "system",
                "content": [{
                    "type": "text",
                    "text": "A conversation between User and Assistant. The user asks a question, and the Assistant solves it. The assistant "
                            "first thinks about the reasoning process in the mind and then provides the user with the answer. The reasoning "
                            "process and answer are enclosed within <think> </think> and <answer> </answer> tags, respectively, i.e., "
                            "<think> reasoning process here </think><answer> answer here </answer>"  # 保持与评测脚本一致
                }]
            },
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/jpeg;base64,{base64_image}"
                        }
                    },
                    {
                        "type": "text",
                        "text": f"{question} First output the thinking process in <think> </think> tags and then output the final answer in <answer> </answer> tags. Output the final answer in JSON format."
                    }
                ]
            }
        ],
        "temperature": 0.0,
        "max_tokens": 5555,
        "stream": True
    }

    response = None
    full_response = ""

    try:
        # 使用全局HTTP_SESSION发送请求，而不是创建新session
        logger.debug(f"Sending API request to {API_BASE} with global session")
        response = HTTP_SESSION.post(
            API_BASE,
            headers=HEADERS,
            json=payload,
            stream=True,
            timeout=API_TIMEOUT  # 使用全局超时设置
        )

        # 记录请求状态码
        logger.debug(f"API response status code: {response.status_code}")
        response.raise_for_status()  # 检查 HTTP 错误状态

        # 流式响应处理
        for chunk in response.iter_lines():
            if chunk:
                chunk_data = chunk.decode('utf-8')
                if chunk_data.startswith("data:"):
                    chunk_data = chunk_data[5:].strip()

                    # 处理流结束标记 [DONE]
                    if chunk_data == '[DONE]':
                        break

                    if not chunk_data:
                        continue

                    try:
                        decoded = json.loads(chunk_data)
                        content = decoded.get("choices", [{}])[0].get("delta", {}).get("content", "")
                        full_response += content
                    except json.JSONDecodeError as e:
                        logger.warning(f"Error decoding JSON chunk: {e}. Problematic chunk: {chunk_data}")
                        continue
                    except (KeyError, IndexError) as e:
                        logger.warning(f"Error accessing stream structure: {e}. Problematic chunk: {chunk_data}")
                        continue

    except requests.exceptions.RequestException as e:
        logger.error(f"API request failed: {e}", exc_info=True)
        return None  # 返回 None 表示 API 调用失败
    except Exception as e:
        logger.error(f"Error processing stream: {e}", exc_info=True)
        return None  # 流处理出错也返回 None
    finally:
        # 确保释放响应资源，但保持session连接池活动
        if response is not None:
            # 确保完全消费响应内容
            if hasattr(response, 'raw') and hasattr(response.raw, 'read'):
                try:
                    response.raw.read()
                    logger.debug("Response raw content fully consumed")
                except Exception as e:
                    logger.warning(f"Failed to consume response raw content: {e}")

            # 关闭响应但不关闭底层连接
            response.close()
            logger.debug("Response object closed properly")

    if not full_response:
        logger.warning("API returned an empty response string.")
        return None

    # --- 添加更详细的调试日志 ---
    logger.debug(f"API response length: {len(full_response)}")
    logger.debug(f"Response first 300 chars: {repr(full_response[:300])}")
    logger.debug(
        f"Response last 300 chars: {repr(full_response[-300:] if len(full_response) >= 300 else full_response)}")

    # 保存整个响应到文件，以便后续检查
    debug_file_path = f"debug_ocr_response_{int(time.time())}.txt"
    try:
        with open(debug_file_path, "w", encoding="utf-8") as debug_file:
            debug_file.write(full_response)
        logger.info(f"Saved full API response to {debug_file_path} for debugging")
    except Exception as e:
        logger.warning(f"Failed to save debug file: {e}")

    # 检查标签的各种可能变体
    answer_start_variants = ["<answer>", "<Answer>", "<ANSWER>", "< answer >", "<answer >", "< answer>"]
    answer_end_variants = ["</answer>", "</Answer>", "</ANSWER>", "</ answer >", "</answer >", "</ answer>"]

    found_start_tag = None
    found_end_tag = None

    for variant in answer_start_variants:
        if variant in full_response:
            found_start_tag = variant
            logger.debug(f"Found start tag variant: {variant}")
            break

    for variant in answer_end_variants:
        if variant in full_response:
            found_end_tag = variant
            logger.debug(f"Found end tag variant: {variant}")
            break

    # 检查标签出现的索引位置
    if found_start_tag:
        start_pos = full_response.find(found_start_tag)
        logger.debug(f"Start tag '{found_start_tag}' found at position: {start_pos}")
        # 查看标签周围的内容以了解上下文
        context_start = max(0, start_pos - 30)
        context_end = min(len(full_response), start_pos + len(found_start_tag) + 30)
        logger.debug(f"Start tag context: {repr(full_response[context_start:context_end])}")
    else:
        logger.warning("No start tag variant found in the response")

    if found_end_tag:
        end_pos = full_response.find(found_end_tag)
        logger.debug(f"End tag '{found_end_tag}' found at position: {end_pos}")
        # 查看标签周围的内容以了解上下文
        context_start = max(0, end_pos - 30)
        context_end = min(len(full_response), end_pos + len(found_end_tag) + 30)
        logger.debug(f"End tag context: {repr(full_response[context_start:context_end])}")
    else:
        logger.warning("No end tag variant found in the response")

    # 如果找到了特定变体的标签，使用它们
    if found_start_tag and found_end_tag:
        # 使用找到的确切标签变体进行分割
        logger.info(f"Using detected tag variants: {found_start_tag} and {found_end_tag}")
        try:
            # 分割步骤 1: 找到开始标签之后的内容
            after_answer_tag = full_response.split(found_start_tag, 1)[1]
            # 分割步骤 2: 找到结束标签之前的内容
            answer_content = after_answer_tag.split(found_end_tag, 1)[0].strip()
            logger.info("Successfully extracted content between answer tags using detected variants.")
        except Exception as e:
            logger.error(f"Error extracting content between detected tags: {e}")
            answer_content = ""

            # 检查是否包含 ```json 标记 (原逻辑不变)
    return full_response


def ocr(image_path):
    """
    Processes an image for OCR, calls the OCR API, and extracts the JSON content.
    """
    PREDEFINED_OCR_ERROR = {
        "检查时间": "",
        "检查名称": "",
        "content": "模型无法正确识别，请重新摆正报告位置，逐个上传"
    }

    try:
        # Process the image: correct orientation and convert to grayscale
        logger.info(f"Processing image for OCR: {image_path}")
        processed_image = process_image_for_ocr(image_path)
    except Exception as e:
        logger.error(f"Failed to process image {image_path}: {e}")
        return PREDEFINED_OCR_ERROR

    question = f"""
<image>

    你是一个标注人员，主要将医疗报告图片的内容转换为结构化数据，并输出json格式的结果。
    例如：
    <think>首先，我需要确认这张图片是否与医疗相关。从图片内容来看，这是一份河北省儿童医院检验科的检验报告单，包含患者的个人信息、检查项目、结果等信息，因此可以确定这是一份医疗报告。接下来，我将提取报告中的关键信息并转换为JSON格式。</think>
    <answer>
    {{
        "检查时间": "2025-03-15 18:25",
        "检查名称": "呼吸道病毒五项及肺炎支原体核酸检测",
        "content": [
            {{
                "基础信息": {{
                    "姓名": "",
                    "性别": "",
                    "年龄": "",
                    "科室": "",
                    "检查部位": ""
                }}
            }},
            {{
                "检查结果": [
                    {{
                        "项目名称": "",
                        "结果": "",
                        "单位": "",
                        "参考范围": "",
                        "异常标记": "",
                        "检测方法": "",
                        "结果状态": "",
                        "检查描述": "",
                        "诊断": "",
                        "处理建议": "",
                        "样本采集时间": "",
                        "其他": ""
                    }}
                ]
            }}
        ]
    }}
    </answer>

    你需要遵循以下要求:
    1.首先判断照片是否和医疗有关，如果是与医疗不相关的图片比如风景图、自拍、美食等，中断执行直接返回如下json格式
    {{  "检查时间": "",
        "检查名称": "",
        "content": "识别失败"
    }}
    4. 你要遵循的answer格式如下:
    {{
        "检查时间": "",  // 对患者进行检查的时间，没有则输出为空
        "检查名称": "",  // 对患者进行检查项目名称，没有则输出为空，如"血常规"、"便常规"、"肝功能、肾功三项、心肌酶,血清蛋白"等

        "content": [
            {{
                "基础信息": {{
                    "姓名": "",  // 患者的姓名，没有则输出为空
                    "性别": "",  // 患者的性别，没有则输出为空
                    "年龄": "",  // 患者的年龄，没有则输出为空
                    "科室": "",  // 对患者进行检查的科室，没有则输出为空
                    "检查部位": ""  // 对患者进行检查的部位，例如 "咽拭子", "血液", "尿液" 等，请务必准确提取，没有则输出为空
                }}
            }},
            {{
                "检查结果": [
                    {{
                        "项目名称": "",  // 检查的项目名称，中文名和英文名若都有则都包括，若有缩写也包括，例如"MP-IgM/肺炎支原体IgM抗体"，"HDL-C高密度脂蛋白胆固醇"等，没有则输出为空
                        "结果": "",  // 检查的项目结果，没有则输出为空
                        "单位": "",  // 检查的项目单位，没有则输出为空
                        "参考范围": "",  // 检查的项目参考范围或值或区间，没有则输出为空
                        "异常标记": "",  // "↑"或"↓"、"*"等符号，表示该项目结果异常，没有则输出为空
                        "检测方法": "",  // 检查的项目所采用的检测方法，没有则输出为空
                        "结果状态": "",  // 根据异常标记内容，判断检查的项目结果是否异常（例如："正常"、"偏高"、"偏低"、"异常"），请务必提取或推断，没有则输出为空
                        "检查描述": "",  // 对检查结果的描述，如影像所见、超声所见、标本描述、大体所见、镜下所见、病理描述等，没有则输出为空
                        "诊断": "",  // 对检查结果的初步/临床/病理诊断；并标识出是哪一种诊断。没有则输出为空
                        "处理建议": "",  // 报告中给出的处理建议，没有则输出为空
                        "样本采集时间": "",  // 检查样本的采集时间，没有则输出为空
                        "其他": ""  // 报告中对于医疗报告解读有影响但没有被包含在上述字段中的信息，没有则输出为空
                    }}
                ]
            }}
        ]
    }}

    5. 对于医疗报告解读比较重要的信息在输出json数据中不能有缺失。 **请特别注意准确提取 "检查部位" 和 "结果状态" 字段。**

    请根据上述要求输出符合输出格式的内容。
    """

    # 调用API获取完整响应字符串
    full_response_str = api_call(processed_image, question)

    if full_response_str is None:
        logger.error("API call failed or returned None.")
        return PREDEFINED_OCR_ERROR  # 返回错误字典

    # --- 添加更详细的调试日志 ---
    logger.debug(f"API response length: {len(full_response_str)}")
    logger.debug(f"Response first 300 chars: {repr(full_response_str[:300])}")
    logger.debug(
        f"Response last 300 chars: {repr(full_response_str[-300:] if len(full_response_str) >= 300 else full_response_str)}")

    # 保存整个响应到文件，以便后续检查
    debug_file_path = f"debug_ocr_response_{int(time.time())}.txt"
    try:
        with open(debug_file_path, "w", encoding="utf-8") as debug_file:
            debug_file.write(full_response_str)
        logger.info(f"Saved full API response to {debug_file_path} for debugging")
    except Exception as e:
        logger.warning(f"Failed to save debug file: {e}")

    # 检查标签的各种可能变体
    answer_start_variants = ["<answer>", "<Answer>", "<ANSWER>", "< answer >", "<answer >", "< answer>"]
    answer_end_variants = ["</answer>", "</Answer>", "</ANSWER>", "</ answer >", "</answer >", "</ answer>"]

    found_start_tag = None
    found_end_tag = None

    for variant in answer_start_variants:
        if variant in full_response_str:
            found_start_tag = variant
            logger.debug(f"Found start tag variant: {variant}")
            break

    for variant in answer_end_variants:
        if variant in full_response_str:
            found_end_tag = variant
            logger.debug(f"Found end tag variant: {variant}")
            break

    # 检查标签出现的索引位置
    if found_start_tag:
        start_pos = full_response_str.find(found_start_tag)
        logger.debug(f"Start tag '{found_start_tag}' found at position: {start_pos}")
        # 查看标签周围的内容以了解上下文
        context_start = max(0, start_pos - 30)
        context_end = min(len(full_response_str), start_pos + len(found_start_tag) + 30)
        logger.debug(f"Start tag context: {repr(full_response_str[context_start:context_end])}")
    else:
        logger.warning("No start tag variant found in the response")

    if found_end_tag:
        end_pos = full_response_str.find(found_end_tag)
        logger.debug(f"End tag '{found_end_tag}' found at position: {end_pos}")
        # 查看标签周围的内容以了解上下文
        context_start = max(0, end_pos - 30)
        context_end = min(len(full_response_str), end_pos + len(found_end_tag) + 30)
        logger.debug(f"End tag context: {repr(full_response_str[context_start:context_end])}")
    else:
        logger.warning("No end tag variant found in the response")

    # 尝试使用标签变体提取内容
    extracted_content = None

    # 首先尝试使用找到的标签变体
    if found_start_tag and found_end_tag:
        try:
            after_start = full_response_str.split(found_start_tag, 1)[1]
            extracted_content = after_start.split(found_end_tag, 1)[0].strip()
            logger.info(
                f"Successfully extracted content using detected variants: {found_start_tag} and {found_end_tag}")
        except Exception as e:
            logger.warning(f"Failed to extract content using detected variants: {e}")

    # 如果上面的方法失败，尝试简单地搜索花括号
    if not extracted_content:
        try:
            # 查找 { 开始 和 } 结束的最大范围
            start_brace = full_response_str.find('{')
            if start_brace >= 0:
                # 从 { 开始找到匹配的最后一个 }
                end_brace = full_response_str.rfind('}')
                if end_brace > start_brace:
                    extracted_content = full_response_str[start_brace:end_brace + 1]
                    logger.info(f"Extracted content using braces: from position {start_brace} to {end_brace}")
        except Exception as e:
            logger.warning(f"Failed to extract content using braces: {e}")

    # 如果有提取到内容，尝试解析为JSON
    if extracted_content:
        try:
            parsed_json = json.loads(extracted_content)
            logger.info("Successfully parsed extracted content as JSON")
            # 检查是否是错误响应
            if isinstance(parsed_json, dict) and parsed_json.get("content") in ["识别失败"]:
                logger.warning(f"OCR API returned specific error: {parsed_json.get('content')}")
            return parsed_json
        except json.JSONDecodeError as e:
            logger.error(
                f"Failed to parse extracted content as JSON: {e}. Content snippet: {extracted_content[:200]}...")

            # 检查是否包含 ```json 标记
            if "```json" in extracted_content and "```" in extracted_content.split("```json", 1)[1]:
                try:
                    json_block = extracted_content.split("```json", 1)[1]
                    json_str = json_block.split("```", 1)[0].strip()
                    logger.info("Found ```json block, trying to parse it")
                    parsed_json = json.loads(json_str)
                    return parsed_json
                except json.JSONDecodeError as json_e:
                    logger.error(f"Failed to parse ```json block as JSON: {json_e}")
                except Exception as other_e:
                    logger.error(f"Other error when processing ```json block: {other_e}")

    # 如果所有尝试都失败，检查整个响应是否可以直接解析为JSON
    try:
        full_response_json = json.loads(full_response_str)
        logger.info("Successfully parsed full response as JSON")
        return full_response_json
    except json.JSONDecodeError:
        logger.debug("Full response is not valid JSON")
    except Exception as e:
        logger.debug(f"Error parsing full response as JSON: {e}")

    # 如果所有尝试都失败，返回错误
    logger.error("All extraction methods failed")
    logger.error(
        f"Response has these promising characters: < appears {full_response_str.count('<')} times, > appears {full_response_str.count('>')} times, {{ appears {full_response_str.count('{')} times, }} appears {full_response_str.count('}')} times")

    return PREDEFINED_OCR_ERROR


def final_diagnosis_parser(final_diagnosis):
    """
    解析final_diagnosis，提取诊断结果
    输入: final_diagnosis 类似"诊断：上呼吸道感染，检查：血常规+C反应蛋白"
    输出: 诊断结果，类似"上呼吸道感染" 和 检查建议，类似"血常规+C反应蛋白"
    """
    diagnosis = ""
    examination = ""

    if not final_diagnosis:
        return diagnosis, examination

    # 处理不同的分隔符情况
    if "诊断：" in final_diagnosis:
        parts = final_diagnosis.split("诊断：")
    elif "诊断:" in final_diagnosis:
        parts = final_diagnosis.split("诊断:")
    else:
        # 尝试处理没有标准前缀的情况
        logger.info(f"No standard diagnosis prefix found in: '{final_diagnosis}'")

        # 尝试根据"检查："或"检查:"分割
        if "检查：" in final_diagnosis:
            diagnosis = final_diagnosis.split("检查：")[0].strip()
            examination = final_diagnosis.split("检查：")[1].strip()
            return diagnosis, examination
        elif "检查:" in final_diagnosis:
            diagnosis = final_diagnosis.split("检查:")[0].strip()
            examination = final_diagnosis.split("检查:")[1].strip()
            return diagnosis, examination

        # 尝试提取常见诊断名称
        if "上呼吸道感染" in final_diagnosis:
            diagnosis = "上呼吸道感染"
            # 尝试提取检查建议，如果有
            if "，" in final_diagnosis:
                parts = final_diagnosis.split("，")
                for part in parts:
                    if "检查" in part:
                        examination = part.strip()
                        break
        elif "呼吸道感染" in final_diagnosis:
            diagnosis = "呼吸道感染"
        elif "感染" in final_diagnosis:
            diagnosis = final_diagnosis  # 整个字符串作为诊断
        elif "策略" in final_diagnosis and "诊断" in final_diagnosis:
            # 尝试处理格式如"[诊断策略:] 诊断：上呼吸道感染 ， "检查：无""
            match = re.search(r'诊断[：:]\s*([^，,]*)', final_diagnosis)
            if match:
                diagnosis = match.group(1).strip()
            match = re.search(r'检查[：:]\s*([^"]*)', final_diagnosis)
            if match:
                examination = match.group(1).strip()
        else:
            # 如果没有标准格式，使用整个字符串作为诊断
            diagnosis = final_diagnosis.strip()

        return diagnosis, examination

    if len(parts) > 1:
        # 提取诊断部分
        diagnosis_part = parts[1]

        # 检查是否包含检查建议
        if "检查：" in diagnosis_part:
            diagnosis = diagnosis_part.split("检查：")[0].strip()
            examination = diagnosis_part.split("检查：")[1].strip()
        elif "检查:" in diagnosis_part:
            diagnosis = diagnosis_part.split("检查:")[0].strip()
            examination = diagnosis_part.split("检查:")[1].strip()
        else:
            diagnosis = diagnosis_part.strip()

    return diagnosis, examination


async def batch_test_patient(doctor_info, auxiliary_exam, enable_cot_retrieval=False):
    """
    Test the medical diagnosis pipeline with a mock initial strategy.

    参数:
        doctor_info (str): 医生提供的病例信息
        auxiliary_exam (str): 辅助检查信息
        enable_cot_retrieval (bool): 是否启用COT召回功能，默认为False
    """
    print("\n" + "=" * 50)
    print("TESTING AI DOCTOR SYSTEM")
    print("=" * 50)
    print("This is a test run of the AI Doctor system.")
    print("Please respond as if you were a patient with symptoms.")
    print("=" * 50 + "\n")

    # Create a mock initial strategy for testing
    initial_strategy = "询问患者的基本信息（性别、年龄）和主诉症状，了解发病时间、起病情况、症状特点（性质、程度、持续时间、频率）、伴随症状、病情演变以及之前的诊疗经过。"
    initial_reasoning = "主诉是问诊的切入点，通过了解患者的基本信息和主要症状，可以快速建立初步印象。进一步询问发病时间、起病情况、症状特点、伴随症状、病情演变和诊疗经过，有助于全面了解现病史，为后续诊断提供关键信息。这种系统性的问诊策略能够帮助我们准确把握患者的病情，避免遗漏重要信息。"

    # Create Planner and Executor
    # Create InteractionHistory
    interaction_history = InteractionHistory()
    interaction_history.add_test_recommendation({
        "检查": "之前检查信息",
        "结果": auxiliary_exam,
    })
    planner = Planner(max_rounds=12, init_strategy=initial_strategy, init_reasoning=initial_reasoning,
                      interaction_history=interaction_history, enable_cot_retrieval=enable_cot_retrieval)
    executor = Executor(max_rounds=9, doctor_info=doctor_info, interaction_history=interaction_history)

    # Set initial strategy for Executor
    # executor.set_strategy(initial_strategy)

    # Main diagnosis loop
    round_num = 1
    while True:
        logger.info(f"Starting test round {round_num}")

        # Execute inquiry based on current strategy
        feedback, end_of_inquiry = await executor.inquire()

        # Update observation
        await executor.update_observation()
        observation = executor.get_observation()

        # Generate new strategy
        new_strategy, reasoning, should_terminate = await planner.generate_strategy()

        # Print round summary
        print("\n" + "=" * 50)
        print(f"TEST ROUND {round_num} SUMMARY")
        print("=" * 50)
        print(f"Strategy: {new_strategy}")
        print("-" * 50)
        print(f"Reasoning: {reasoning}")
        print("=" * 50 + "\n")

        logger.info(f"interaction_history: \n {planner.interaction_history.format_cot_entries()}")

        # Check if we should terminate
        if should_terminate or round_num >= 10:
            logger.info("Terminating test diagnosis process")
            break

        # Update strategy for next round
        # executor.set_strategy(new_strategy)
        round_num += 1

    # Get final diagnosis
    final_diagnosis = planner.get_final_diagnosis()
    dialogue_history = executor.format_dialogue_history()

    # 获取医生补充
    print("生成最终诊断和病情描述...")
    condition = await planner.generate_condition_only()
    print(f"生成的诊断结果:\n{final_diagnosis}")
    print(f"生成的病情描述:\n{condition}")

    # 获取治疗方案
    print("\n" + "=" * 50)
    print("获取治疗方案...")

    # 使用try-except块捕获可能的错误
    try:
        treatment_plan = await planner.get_treatments(final_diagnosis, condition)
    except Exception as e:
        print(f"获取治疗方案时出错: {e}")
        treatment_plan = "无法获取治疗方案"

    # 打印最终诊断和病情描述
    # Print final diagnosis
    print("\n" + "=" * 50)
    print("拟诊")
    print("=" * 50)
    print(final_diagnosis)
    print("=" * 50 + "\n")
    # 添加COT启用信息到文件名
    jsonl_filename = f"data\\batchdata_0427_R1{'_with_cot' if enable_cot_retrieval else ''}.jsonl"
    planner.interaction_history.to_jsonl(jsonl_filename)
    print(f"病情描述: {condition}")

    # 打印治疗方案
    print("\n" + "=" * 50)
    print("治疗方案")
    print("=" * 50)
    print(treatment_plan)
    print("=" * 50 + "\n")

    # 生成拟诊和检查建议
    logger.info("Generating secondary diagnosis and additional tests...")
    secondary_diagnosis, additional_tests, reasoning = asyncio.run(
        planner.generate_secondary_diagnosis(final_diagnosis)
    )

    # 保存拟诊到交互历史
    planner.interaction_history.preliminary_diagnosis = secondary_diagnosis

    # 获取倒数第二个cot_entry的observation
    observation = ""
    logger.info(f"Number of cot_entries: {len(interaction_history.cot_entries)}")
    if interaction_history.cot_entries and len(interaction_history.cot_entries) > 1:
        second_last_entry = interaction_history.cot_entries[-2]
        logger.info(f"Second last entry keys: {second_last_entry.keys()}")
        if "observation" in second_last_entry:
            observation = second_last_entry["observation"]
            logger.info(f"Found observation in second last entry: {observation[:100]}...")
        else:
            logger.warning("No 'observation' key in second last entry")
    else:
        logger.warning("Not enough cot_entries to get second last entry")

    # 构建返回结果
    result = {
        "preliminary_diagnosis": secondary_diagnosis + additional_tests,
        "reasoning": reasoning,
        "observation": observation
    }

    logger.info(f"Secondary diagnosis generated: {secondary_diagnosis}")
    return final_diagnosis, planner.interaction_history.format_cot_output(), observation, dialogue_history, treatment_plan


async def batch_process_jsonl(enable_cot_retrieval=False):
    """
    Batch process patient data from Excel file and save results to a JSONL file.

    参数:
        enable_cot_retrieval (bool): 是否启用COT召回功能，默认为False
    """
    # Read the Excel file
    # df = pd.read_excel('data/0417RI评价.xlsx')
    df = pd.read_excel('data/检查和药品0224.xlsx')
    # 定义需要跳过的案例编号列表
    # skip_case_ids = ['C06', 'C22', 'C24', 'C36', 'C51', 'C52', 'C04', 'C10', 'C13', 'C54']

    # 过滤掉需要跳过的案例
    # filtered_df = df[~df['案例编号'].isin(skip_case_ids)]

    # 输出文件路径
    output_path = f'data/batch_process_0427{"_with_cot" if enable_cot_retrieval else ""}.jsonl'

    logger.info(f"启动批处理，COT检索功能：{'启用' if enable_cot_retrieval else '禁用'}")
    logger.info(f"结果将保存到: {output_path}")

    # 检查输出文件是否存在，如果不存在则创建
    if not os.path.exists(output_path):
        with open(output_path, 'w', encoding='utf-8') as f:
            pass

    # 打开 JSONL 文件准备写入（使用 append 模式）
    with open(output_path, 'a', encoding='utf-8') as f:
        # 取过滤后的前10个案例
        for index, row in df.iloc[0:30].iterrows():
            doctor_info = row['病例合并'].replace("&#xA;", "\n")
            # 提取辅助检查信息
            auxiliary_exam = ""
            if "【辅助检查】" in doctor_info:
                auxiliary_exam_parts = doctor_info.split("【辅助检查】")
                if len(auxiliary_exam_parts) > 1:
                    auxiliary_exam = auxiliary_exam_parts[1].strip()
                    doctor_info_clean = auxiliary_exam_parts[0].strip()

            index_for_xlsx = row["案例编号"]
            doctor_check = row['初步检查']
            doctor_diagnosis = row['诊断标准']
            doctor_treatment = row['治疗药物']
            # print(f"doctor：{doctor_info}")
            logger.info(f"处理患者 {index + 1}，医生信息: {doctor_info_clean}")
            logger.info(f"提取的辅助检查信息: {auxiliary_exam}")

            # 调用异步处理函数，传入辅助检查信息和COT启用标志
            batch_test_patient_result = await batch_test_patient(doctor_info, auxiliary_exam, enable_cot_retrieval)

            # 构建结果字典
            result = {
                "案例编号": index_for_xlsx,
                '病例合并': doctor_info,
                '最终诊断': batch_test_patient_result[0],
                '标准诊断': doctor_diagnosis,
                '标准检查': doctor_check,
                '对话历史': batch_test_patient_result[3],
                '诊断历史': batch_test_patient_result[1],
                '观察': batch_test_patient_result[2],
                '治疗方案': batch_test_patient_result[4],
                '治疗药物': doctor_treatment,
                'COT检索启用': enable_cot_retrieval
            }

            # 写入一行 JSON（ensure_ascii=False 可保持中文）
            f.write(json.dumps(result, ensure_ascii=False) + '\n')
            f.flush()  # 确保数据立即写入文件
            os.fsync(f.fileno())  # 确保操作系统将数据写入磁盘
            logger.info(f"成功写入案例编号: {index_for_xlsx}")

    print(f"Batch processing completed. Results saved to '{output_path}'.")


async def batch_process(save_format='xlsx'):
    """
    Batch process patient data from Excel file and save results to a new file in the specified format.
    """
    # Read the Excel file
    df = pd.read_excel('data/检查和药品0224.xlsx')

    # Prepare a list to store results
    results = []

    # Process each patient
    for index, row in df.iloc[1:10].iterrows():
        doctor_info = row['病例合并']
        index_for_xlsx = row["案例编号"]
        print(f"doctor：{doctor_info}")
        print(f"Processing patient {index + 1} with doctor info: {doctor_info}")
        # Call the batch test function
        batch_test_patient_result = await batch_test_patient(doctor_info)
        # Store the result
        result = {
            "案例编号": index_for_xlsx,
            '病例合并': doctor_info,
            '最终诊断': batch_test_patient_result[0],
            '对话历史': batch_test_patient_result[1],
            '观察': batch_test_patient_result[2],
            'CoT': batch_test_patient_result[3],
            '治疗方案': batch_test_patient_result[4]
        }
        results.append(result)

    # Save results based on the specified format
    if save_format == 'xlsx':
        with pd.ExcelWriter('data/batch_process.xlsx', mode='a', engine='openpyxl',
                            if_sheet_exists='overlay') as writer:
            sheet_name = 'Results'
            if sheet_name in writer.sheets:
                startrow = writer.sheets[sheet_name].max_row
            else:
                startrow = 0
            result_df = pd.DataFrame(results)
            result_df.to_excel(
                writer,
                index=False,
                header=(startrow == 0),
                startrow=startrow + 1 if startrow != 0 else startrow)
    elif save_format == 'json':
        if os.path.exists('data/batch_process.json'):
            with open('data/batch_process.json', 'r+', encoding='utf-8') as file:
                existing_data = pd.read_json(file)
                result_df = pd.DataFrame(results)
                combined_data = pd.concat([existing_data, result_df], ignore_index=True)
                file.seek(0)
                combined_data.to_json(file, force_ascii=False, orient='records', lines=True)
        else:
            result_df = pd.DataFrame(results)
            result_df.to_json('data/batch_process.json', force_ascii=False, orient='records', lines=True)
    print(f"Batch processing completed. Results saved to 'data/batch_process.{save_format}'.")


async def api_next_response(interaction_history, quick_demo_mode=False):
    """
    输入: interaction_history
    输出: 下一个问题或者最后的诊断；以及更新后的interaction_history
    注意：同时要更新 interaction_history

    参数:
        interaction_history: 交互历史
        quick_demo_mode (bool): 是否启用快速演示模式，默认为False
    """
    # 只在没有existing策略时设置初始策略
    initial_strategy = None
    initial_reasoning = None

    if not interaction_history.cot_entries:  # 只有当没有已存在的策略时才设置初始策略
        if quick_demo_mode:
            initial_strategy = "询问患者的基本信息（年龄、性别、主要症状）以快速了解病情概况"
            initial_reasoning = "快速演示模式：优先收集核心信息，为后续快速查体和诊断做准备"
        else:
            initial_strategy = "询问患者的基本信息（性别、年龄）和主诉症状，了解发病时间、起病情况、症状特点（性质、程度、持续时间、频率）、伴随症状、病情演变以及之前的诊疗经过。"
            initial_reasoning = "这个策略可以帮助我们全面了解患者的基本情况和症状的详细信息。"

    # 创建 Planner 和 Executor
    planner = Planner(max_rounds=9, interaction_history=interaction_history, enable_cot_retrieval=False,
                      init_strategy=initial_strategy, init_reasoning=initial_reasoning)
    planner.quick_demo_mode = quick_demo_mode  # 设置快速演示模式
    executor = Executor(max_rounds=12, interaction_history=interaction_history)
    executor.quick_demo_mode = quick_demo_mode  # 设置快速演示模式

    # 添加安全计数器防止无限循环
    main_loop_count = 0
    max_main_loops = 20  # 主循环最大次数

    while main_loop_count < max_main_loops:
        main_loop_count += 1
        logger.debug(f"Main loop iteration: {main_loop_count}/{max_main_loops}")

        # 内层循环安全计数器
        inner_loop_count = 0
        max_inner_loops = 10  # 内层循环最大次数

        while inner_loop_count < max_inner_loops:
            inner_loop_count += 1
            logger.debug(f"Inner loop iteration: {inner_loop_count}/{max_inner_loops}")

            try:
                feedback, question, end_of_inquiry = await executor.next_question()
                if question or end_of_inquiry:
                    break
                logger.warning("Generated question and end_of_inquiry are both False. Regenerating question.")
            except Exception as e:
                logger.error(f"Error in next_question: {e}")
                # 发生异常时强制结束
                end_of_inquiry = True
                feedback = "系统异常，结束当前问诊。"
                question = ""
                break

        # 如果内层循环达到最大次数，强制结束
        if inner_loop_count >= max_inner_loops:
            logger.warning("Inner loop reached maximum iterations, forcing end of inquiry")
            end_of_inquiry = True
            feedback = "系统达到最大重试次数，结束当前问诊。"
            question = ""

        if question:
            # 这里可以添加对问题的处理逻辑
            logger.info(f"Generated question: {question}")
            interaction_history.cot_entries[-1]["dialogue_history"].append({"role": "doctor", "content": question})
            return question, interaction_history
        elif end_of_inquiry:
            interaction_history.cot_entries[-1]["feedback"] = feedback  # 更新反馈
            await executor.update_observation()
            # 本轮strategy对应的问诊结束，生成新的strategy，进行下一轮问诊
            new_strategy, reasoning, should_terminate = await planner.generate_strategy()
            # Print round summary
            round_num = interaction_history.get_round_num()
            logger.info(f"TEST ROUND {round_num} SUMMARY")
            logger.info("=" * 50)
            logger.info(f"Strategy: {new_strategy}")
            logger.info("-" * 50)
            logger.info(f"Reasoning: {reasoning}")
            logger.info("=" * 50 + "\n")
            # Check if we should terminate
            if should_terminate or round_num >= MAX_ROUNDS:
                logger.info("Terminating test diagnosis process")
                final_diagnosis = planner.get_final_diagnosis()
                logger.info(f"Final diagnosis: {final_diagnosis}")
                logger.info("=" * 50 + "\n")

                # 在快速演示模式下，将最终诊断设置为preliminary_diagnosis
                if quick_demo_mode and final_diagnosis and final_diagnosis != "未达成明确诊断。可能需要进一步检查。":
                    interaction_history.preliminary_diagnosis = final_diagnosis
                    logger.info(f"Set preliminary_diagnosis in quick demo mode: {final_diagnosis}")

                return final_diagnosis, interaction_history
            else:
                # 继续进行下一轮问诊
                continue

    # 如果主循环达到最大次数，返回错误信息
    logger.error("Main loop reached maximum iterations, terminating with error")
    error_message = "系统达到最大循环次数，无法继续问诊。请重新开始。"
    return error_message, interaction_history


async def api_diagnose_response(interaction_history):
    """
    输入: interaction_history
    输出: 诊断、病情描述和不含思考过程的治疗方案
    """
    # 创建 Planner
    planner = Planner(max_rounds=9, interaction_history=interaction_history)
    # 更新观察结果
    await planner.update_observation_only()

    # 获取诊断结果
    # Determine has_input for final_diagnosis_and_condition
    # This reflects if new clinical information was provided in this request
    has_new_input_for_final_diagnosis = bool(
        interaction_history.doctor_supplementary_info or \
        interaction_history.test_recommendation  # Assuming presence implies new data to consider for diagnosis
    )
    # final_diagnosis_and_condition updates planner.interaction_history.diagnosis internally
    # and returns (formatted_diagnosis_with_basis, condition, reasoning)
    diagnosis, condition, _ = await planner.final_diagnosis_and_condition(has_input=has_new_input_for_final_diagnosis)

    # 获取治疗方案（原始，包含思考过程）
    raw_treatment_plan = await planner.get_treatments(diagnosis, condition)

    # 清理治疗方案，移除思考过程
    cleaned_treatment_plan = raw_treatment_plan
    # 如果治疗方案包含明确的思考过程标记，可以在这里清理
    if "思考过程" in raw_treatment_plan or "Thinking:" in raw_treatment_plan or "推理过程:" in raw_treatment_plan:
        # 使用更复杂的清理逻辑，确保移除所有思考过程
        lines = raw_treatment_plan.split("\n")
        cleaned_lines = []
        skip_mode = False
        for line in lines:
            if "思考过程" in line or "Thinking:" in line or "推理过程:" in line:
                skip_mode = True
                continue
            if "治疗方案" in line or "Treatment:" in line:
                skip_mode = False
            if not skip_mode:
                cleaned_lines.append(line)
        cleaned_treatment_plan = "\n".join(cleaned_lines)

    # 如果没有找到明确的标记，尝试截断到第一个空行后的"推理过程"或"思考过程"
    if cleaned_treatment_plan == raw_treatment_plan:
        parts = cleaned_treatment_plan.split("\n\n")
        for i, part in enumerate(parts):
            if "推理过程" in part or "思考过程" in part:
                cleaned_treatment_plan = "\n\n".join(parts[:i])
                break

    logger.info(f"清理后的治疗方案: {cleaned_treatment_plan}")
    return diagnosis, condition, cleaned_treatment_plan


@app.route('/ocr_api', methods=['POST'])
def ocr_api():
    """API endpoint for OCR processing using JSON input for image path."""
    logger.info("Received POST request for /ocr_api")
    if not request.is_json:
        logger.error("Request is not in JSON format")
        return jsonify({"error": "Request must be JSON"}), 415

    data = request.get_json()
    image_path = data.get('image_path')
    # name = data.get('name') # Removed name retrieval

    if not image_path:
        logger.error("Missing 'image_path' in JSON payload")
        return jsonify({"error": "Missing 'image_path' in JSON payload"}), 400

    # if not name: # Removed name check
    #     logger.error("Missing 'name' in JSON payload")
    #     return jsonify({"error": "Missing 'name' in JSON payload"}), 400

    # 定义合法的图片后缀名列表
    VALID_IMAGE_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'}

    try:
        # 分离基本路径和后缀
        base_path, ext = os.path.splitext(image_path)

        # 检查后缀是否不在合法列表中（包括没有后缀的情况, ext会是空字符串）
        if ext.lower() not in VALID_IMAGE_EXTENSIONS:
            new_image_path = base_path + '.jpg'

            # 安全地重命名
            if os.path.exists(image_path) and not os.path.exists(new_image_path):
                logger.info(f"Image '{image_path}' has an invalid extension ('{ext}'). Renaming to '{new_image_path}'.")
                os.rename(image_path, new_image_path)
                image_path = new_image_path
            elif os.path.exists(new_image_path):
                logger.warning(f"Did not rename '{image_path}' because target '{new_image_path}' already exists. Using existing target.")
                image_path = new_image_path
            # 如果原始文件不存在，让后续逻辑去处理
    except Exception as e:
        logger.error(f"An error occurred while handling image extension: {e}", exc_info=True)
        # 出错时回退到原始路径

    # 基本的文件存在性检查
    if not os.path.exists(image_path):
        logger.error(f"Image file not found at path: {image_path}")
        return jsonify({"error": f"Image file not found at path: {image_path}"}), 404
    if not os.path.isfile(image_path):
        logger.error(f"Path is not a file: {image_path}")
        return jsonify({"error": f"Path is not a file: {image_path}"}), 400

    try:
        # 调用更新后的 ocr 函数，它现在返回字典（成功或错误）
        ocr_result_dict = ocr(image_path)  # Call ocr without name

        # 错误情况现在会返回一个标准的JSON结构，所以这个'error'键的检查逻辑不再需要。
        # 所有从ocr返回的字典都直接作为JSON响应返回。
        if isinstance(ocr_result_dict, dict):
            logger.info(f"OCR process for {image_path} returned a dictionary. Returning as JSON.")
            return jsonify(ocr_result_dict)
        else:
            # 以防万一 ocr 函数返回了非预期的类型
            logger.error(f"OCR function returned unexpected type: {type(ocr_result_dict)}")
            return jsonify({"error": "Internal server error after OCR processing"}), 500

    except FileNotFoundError:
        # 这个错误应该在调用 ocr 之前被捕获，但在 encode_image 中也可能发生
        logger.error(f"Image file not found during processing: {image_path}")
        return jsonify({"error": f"Image file not found at path: {image_path}"}), 404
    except Exception as e:
        logger.error(f"Unhandled exception in /ocr_api for {image_path}: {e}", exc_info=True)  # Log without name
        return jsonify({"error": "An internal error occurred during OCR processing."}), 500


@app.route('/ai_doctor_preliminary_diagnosis', methods=['POST'])
def ai_doctor_preliminary_diagnosis():
    logger.info("Received POST request for /ai_doctor_v2_preliminary_diagnosis")
    data = request.get_json()
    if not data or 'interaction_history' not in data:
        logger.error("Request JSON is missing 'interaction_history'.")
        return jsonify({"error": "Missing 'interaction_history' in request JSON."}), 400

    history_dict = data['interaction_history']
    interaction_history = InteractionHistory()
    # 安全获取属性
    interaction_history.cot_entries = history_dict.get('cot_entries', [])
    interaction_history.diagnosis = history_dict.get(
        'diagnosis')  # This is the initial diagnosis (final_diagnosis from your example)
    interaction_history.test_recommendation = history_dict.get('test_recommendation', [])
    interaction_history.treatment_recommendation = history_dict.get('treatment_recommendation')
    interaction_history.preliminary_diagnosis = history_dict.get('preliminary_diagnosis')
    interaction_history.preliminary_diagnosis = history_dict.get(
        'preliminary_diagnosis')  # This will be updated by the planner
    interaction_history.doctor_supplementary_info = history_dict.get('doctor_supplementary_info', [])
    interaction_history.lastObservation = history_dict.get('lastObservation', '')

    supplementary_info = data.get('supplementary_info')  # Get supplementary_info from request body
    if supplementary_info:
        if isinstance(supplementary_info, str):
            interaction_history.doctor_supplementary_info.append(supplementary_info)
        elif isinstance(supplementary_info, list):
            interaction_history.doctor_supplementary_info.extend(supplementary_info)
        else:
            logger.warning(f"supplementary_info is not a string or list: {type(supplementary_info)}")

    current_observation = ""
    if len(interaction_history.cot_entries) >= 2:
        current_observation = interaction_history.cot_entries[-2].get("observation", "")
    elif interaction_history.cot_entries:  # if only one entry, maybe use its observation if available
        current_observation = interaction_history.cot_entries[-1].get("observation", "")
    else:
        logger.warning("Not enough cot_entries to get current_observation.")

    initial_diagnosis_str = interaction_history.preliminary_diagnosis  # Changed from interaction_history.diagnosis
    if not initial_diagnosis_str:
        logger.error("Initial diagnosis (interaction_history.preliminary_diagnosis) is missing.")
        return jsonify({"error": "Initial diagnosis (interaction_history.preliminary_diagnosis) is missing."}), 400

    diagnosis_parsed, examination_parsed = final_diagnosis_parser(initial_diagnosis_str)

    planner = Planner(max_rounds=9, interaction_history=interaction_history)

    try:
        diagnosis_content, additional_tests, reasoning, guidelines_content = asyncio.run(
            planner.generate_preliminary_diagnosis(
                diagnosis_parsed,
                examination_parsed,
                current_observation
            )
        )
        # The generate_preliminary_diagnosis method in planner.py updates interaction_history.diagnosis
        # and interaction_history.guidance_for_treatment internally.
        # We should return the updated interaction_history as well.
        # 获取倒数第二个cot_entry的observation
        observation = ""
        logger.info(f"Number of cot_entries: {len(interaction_history.cot_entries)}")
        if interaction_history.cot_entries and len(interaction_history.cot_entries) > 1:
            second_last_entry = interaction_history.cot_entries[-2]
            logger.info(f"Second last entry keys: {second_last_entry.keys()}")
            if "observation" in second_last_entry:
                observation = second_last_entry["observation"]
                logger.info(f"Found observation in second last entry: {observation[:100]}...")
            else:
                logger.warning("No 'observation' key in second last entry")
        else:
            logger.warning("Not enough cot_entries to get second last entry")
        # Construct the response dictionary
        response_data = {
            "preliminary_diagnosis": diagnosis_content,
            "inspection_suggestions": additional_tests,
            "reasoning": reasoning,
            "guidelines_content": guidelines_content,  # guidelines_content is the direct response from get_guide
            "observation": observation
        }
        return jsonify(response_data)

    except Exception as e:
        logger.error(f"Error during preliminary diagnosis generation: {e}", exc_info=True)
        return jsonify({"error": f"An error occurred: {str(e)}"}), 500


@app.route('/ai_doctor_diagnose', methods=['POST'])
def ai_doctor_diagnose():
    logger.info("Received POST request for /ai_doctor_v2_diagnosis")
    data = request.get_json()
    if not data or 'interaction_history' not in data:
        logger.error("Request JSON is missing 'interaction_history'.")
        return jsonify({"error": "Missing 'interaction_history' in request JSON."}), 400

    history_dict = data['interaction_history']
    interaction_history = InteractionHistory()
    # 安全获取属性
    interaction_history.cot_entries = history_dict.get('cot_entries', [])
    interaction_history.diagnosis = history_dict.get('preliminary_diagnosis')
    interaction_history.test_recommendation = history_dict.get('test_recommendation', [])
    interaction_history.treatment_recommendation = history_dict.get('treatment_recommendation')
    interaction_history.preliminary_diagnosis = history_dict.get('preliminary_diagnosis')
    interaction_history.doctor_supplementary_info = history_dict.get('doctor_supplementary_info', [])
    interaction_history.lastObservation = history_dict.get('lastObservation', '')

    # 在调用planner前，确保test_recommendation中的键名与planner期望的一致
    # 检查并转换键名，将'项目名称'转换为'检查'以匹配planner.py的要求
    for test in interaction_history.test_recommendation:
        if '项目名称' in test and '检查' not in test:
            test['检查'] = test['项目名称']

    planner = Planner(max_rounds=9, interaction_history=interaction_history)

    # 获取最终诊断结果

    has_new_input_for_final_diagnosis = bool(
        interaction_history.doctor_supplementary_info or interaction_history.test_recommendation
        # Assuming presence implies new data to consider for diagnosis
    )
    print(has_new_input_for_final_diagnosis)
    diagnosis, condition, reasoning = asyncio.run(
        planner.final_diagnosis_and_condition(has_input=has_new_input_for_final_diagnosis)
    )

    treatment_plan = asyncio.run(planner.get_treatments(planner.interaction_history.diagnosis, condition))
    cleaned_treatment_plan = clean_treatment_recommendation(treatment_plan)

    return jsonify({"diagnosis": diagnosis, "condition": condition, "treatment_recommendation": cleaned_treatment_plan,
                    "treatment_guide": interaction_history.guidance_for_treatment})


@app.route('/ai_doctor_v2_inquiry', methods=['POST'])
def ai_doctor_v2_inquiry():
    logger.info("Received POST request for /ai_doctor_v2_inquiry")

    # 原有代码
    try:
        data = request.get_json()
        if not data or 'interaction_history' not in data:
            logger.error("Request JSON is missing 'interaction_history'.")
            return jsonify({"error": "Missing 'interaction_history' in request JSON."}), 400
        history_dict = data['interaction_history']
        interaction_history = InteractionHistory()
        # Load history data into the instance
        interaction_history.cot_entries = history_dict.get('cot_entries', [])
        interaction_history.diagnosis = history_dict.get('diagnosis')
        interaction_history.test_recommendation = history_dict.get('test_recommendation', [])
        interaction_history.treatment_recommendation = history_dict.get('treatment_recommendation')
        interaction_history.preliminary_diagnosis = history_dict.get('preliminary_diagnosis')
        interaction_history.doctor_supplementary_info = history_dict.get('doctor_supplementary_info', [])
        interaction_history.lastObservation = history_dict.get('lastObservation', '')

        # 确保test_recommendation中的键名与planner期望的一致
        for test in interaction_history.test_recommendation:
            if '项目名称' in test and '检查' not in test:
                test['检查'] = test['项目名称']

        # 只在第一次问答交互时更新observation（即dialogue_history长度小于等于2）
        is_first_interaction = False
        if interaction_history.cot_entries and len(interaction_history.cot_entries) > 0:
            last_entry = interaction_history.cot_entries[-1]
            if "dialogue_history" in last_entry and len(last_entry["dialogue_history"]) <= 2:
                is_first_interaction = True

        if is_first_interaction:
            logger.info("First interaction detected, updating observation")
            executor = Executor(max_rounds=12, interaction_history=interaction_history)
            asyncio.run(executor.update_observation())
            logger.info("Updated observation before inquiry")

        # 调用api_next_response函数，使用asyncio.run()执行异步函数
        response, updated_interaction_history = asyncio.run(api_next_response(interaction_history))

        # 打印问题或诊断
        logger.info(f"API next response: {response}")
        # 注释掉下面这行代码，防止 inquiry 接口更新 diagnosis
        # if "诊断：" in response or "诊断:" in response:
        #     updated_interaction_history.diagnosis = response
        #     logger.info(f"Final diagnosis detected: {response}")

        # 直接手动构建返回的字典
        logger.info("Manually constructing dict from updated InteractionHistory.")
        history_dict_to_return = {
            'cot_entries': updated_interaction_history.cot_entries,
            'diagnosis': updated_interaction_history.diagnosis,
            'test_recommendation': updated_interaction_history.test_recommendation,
            'treatment_recommendation': updated_interaction_history.treatment_recommendation,
            'preliminary_diagnosis': updated_interaction_history.preliminary_diagnosis,
            'doctor_supplementary_info': updated_interaction_history.doctor_supplementary_info,
            'lastObservation': updated_interaction_history.lastObservation if hasattr(updated_interaction_history,
                                                                                      'lastObservation') else ''
        }
        # 返回构建好的字典
        return jsonify(history_dict_to_return)
    except asyncio.TimeoutError:
        logger.error("Request to /ai_doctor_v2_inquiry timed out on the server.")
        return jsonify({"error": "AI service took too long to respond and the request was terminated."}), 504
    except Exception as e:
        logger.error(f"An unexpected error occurred in /ai_doctor_v2_inquiry: {e}", exc_info=True)
        return jsonify({"error": "An internal server error occurred."}), 500


@app.route('/ai_doctor_v2_quick_inquiry', methods=['POST'])
def ai_doctor_v2_quick_inquiry():
    logger.info("Received POST request for /ai_doctor_v2_quick_inquiry")
    data = request.get_json()
    if not data or 'interaction_history' not in data:
        logger.error("Request JSON is missing 'interaction_history'.")
        return jsonify({"error": "Missing 'interaction_history' in request JSON."}), 400
    try:
        history_dict = data['interaction_history']
        interaction_history = InteractionHistory()
        # Load history data into the instance
        interaction_history.cot_entries = history_dict.get('cot_entries', [])
        interaction_history.diagnosis = history_dict.get('diagnosis')
        interaction_history.test_recommendation = history_dict.get('test_recommendation', [])
        interaction_history.treatment_recommendation = history_dict.get('treatment_recommendation')
        interaction_history.preliminary_diagnosis = history_dict.get('preliminary_diagnosis')
        interaction_history.doctor_supplementary_info = history_dict.get('doctor_supplementary_info', [])

        # 调用api_next_response函数，使用asyncio.run()执行异步函数
        # 关键区别：传递 quick_demo_mode=True
        response, updated_interaction_history = asyncio.run(
            api_next_response(interaction_history, quick_demo_mode=True))

        # 打印问题或诊断
        logger.info(f"API next response (quick mode): {response}")

        # 直接手动构建返回的字典
        logger.info("Manually constructing dict from updated InteractionHistory (quick mode).")
        history_dict_to_return = {
            'cot_entries': updated_interaction_history.cot_entries,
            'diagnosis': updated_interaction_history.diagnosis,
            'test_recommendation': updated_interaction_history.test_recommendation,
            'treatment_recommendation': updated_interaction_history.treatment_recommendation,
            'preliminary_diagnosis': updated_interaction_history.preliminary_diagnosis,
            'doctor_supplementary_info': updated_interaction_history.doctor_supplementary_info
        }
        # 返回构建好的字典
        return jsonify(history_dict_to_return)
    except asyncio.TimeoutError:
        logger.error("Request to /ai_doctor_v2_quick_inquiry timed out on the server.")
        return jsonify({"error": "AI service took too long to respond and the request was terminated."}), 504
    except Exception as e:
        logger.error(f"An unexpected error occurred in /ai_doctor_v2_quick_inquiry: {e}", exc_info=True)
        return jsonify({"error": "An internal server error occurred."}), 500


@app.route('/ai_doctor_v2_preliminary_diagnosis', methods=['POST'])
def ai_doctor_v2_preliminary_diagnosis():
    logger.info("Received POST request for /ai_doctor_v2_preliminary_diagnosis")
    data = request.get_json()
    if not data or 'interaction_history' not in data:
        logger.error("Request JSON is missing 'interaction_history'.")
        return jsonify({"error": "Missing 'interaction_history' in request JSON."}), 400

    history_dict = data['interaction_history']
    interaction_history = InteractionHistory()
    # 安全获取属性
    interaction_history.cot_entries = history_dict.get('cot_entries', [])
    interaction_history.diagnosis = history_dict.get('diagnosis')
    interaction_history.test_recommendation = history_dict.get('test_recommendation', [])
    interaction_history.treatment_recommendation = history_dict.get('treatment_recommendation')
    interaction_history.preliminary_diagnosis = history_dict.get('preliminary_diagnosis')
    interaction_history.doctor_supplementary_info = history_dict.get('doctor_supplementary_info', [])
    interaction_history.lastObservation = history_dict.get('lastObservation', '')

    supplementary_info = data.get('supplementary_info')  # Get supplementary_info from request body
    if supplementary_info:
        if isinstance(supplementary_info, str):
            interaction_history.doctor_supplementary_info.append(supplementary_info)
        elif isinstance(supplementary_info, list):
            interaction_history.doctor_supplementary_info.extend(supplementary_info)
        else:
            logger.warning(f"supplementary_info is not a string or list: {type(supplementary_info)}")

    current_observation = ""
    if len(interaction_history.cot_entries) >= 2:
        current_observation = interaction_history.cot_entries[-2].get("observation", "")
    elif interaction_history.cot_entries:  # if only one entry, maybe use its observation if available
        current_observation = interaction_history.cot_entries[-1].get("observation", "")
    else:
        logger.warning("Not enough cot_entries to get current_observation.")

    # 优先使用preliminary_diagnosis，如果没有则使用diagnosis字段
    initial_diagnosis_str = interaction_history.preliminary_diagnosis or interaction_history.diagnosis
    if not initial_diagnosis_str:
        logger.error("Both preliminary_diagnosis and diagnosis are missing from interaction_history.")
        return jsonify({
                           "error": "Initial diagnosis is missing. Please ensure either preliminary_diagnosis or diagnosis is provided."}), 400

    logger.info(f"Using initial diagnosis: {initial_diagnosis_str}")
    diagnosis_parsed, examination_parsed = final_diagnosis_parser(initial_diagnosis_str)

    planner = Planner(max_rounds=9, interaction_history=interaction_history)

    try:
        diagnosis_content, additional_tests, reasoning, guidelines_content = asyncio.run(
            planner.generate_preliminary_diagnosis(
                diagnosis_parsed,
                examination_parsed,
                current_observation
            )
        )
        # The generate_preliminary_diagnosis method in planner.py updates interaction_history.diagnosis
        # and interaction_history.guidance_for_treatment internally.
        # We should return the updated interaction_history as well.
        # 获取倒数第二个cot_entry的observation
        observation = ""
        logger.info(f"Number of cot_entries: {len(interaction_history.cot_entries)}")
        if interaction_history.cot_entries and len(interaction_history.cot_entries) > 1:
            second_last_entry = interaction_history.cot_entries[-2]
            logger.info(f"Second last entry keys: {second_last_entry.keys()}")
            if "observation" in second_last_entry:
                observation = second_last_entry["observation"]
                logger.info(f"Found observation in second last entry: {observation[:100]}...")
            else:
                logger.warning("No 'observation' key in second last entry")
        else:
            logger.warning("Not enough cot_entries to get second last entry")

        # Construct the response dictionary
        response_data = {
            "preliminary_diagnosis": diagnosis_content,
            "inspection_suggestions": additional_tests,
            "reasoning": reasoning,
            "guidelines_content": guidelines_content,  # guidelines_content is the direct response from get_guide
            "observation": observation
        }
        return jsonify(response_data)

    except Exception as e:
        logger.error(f"Error during preliminary diagnosis generation: {e}", exc_info=True)
        return jsonify({"error": f"An error occurred: {str(e)}"}), 500


@app.route('/ai_doctor_v2_diagnosis', methods=['POST'])
def ai_doctor_v2_diagnosis():
    logger.info("Received POST request for /ai_doctor_v2_diagnosis")
    data = request.get_json()
    if not data or 'interaction_history' not in data:
        logger.error("Request JSON is missing 'interaction_history'.")
        return jsonify({"error": "Missing 'interaction_history' in request JSON."}), 400

    history_dict = data['interaction_history']
    interaction_history = InteractionHistory()
    # 安全获取属性
    interaction_history.cot_entries = history_dict.get('cot_entries', [])
    interaction_history.diagnosis = history_dict.get('preliminary_diagnosis')
    interaction_history.test_recommendation = history_dict.get('test_recommendation', [])
    interaction_history.treatment_recommendation = history_dict.get('treatment_recommendation')
    interaction_history.preliminary_diagnosis = history_dict.get('preliminary_diagnosis')
    interaction_history.doctor_supplementary_info = history_dict.get('doctor_supplementary_info', [])
    interaction_history.lastObservation = history_dict.get('lastObservation', '')

    # 在调用planner前，确保test_recommendation中的键名与planner期望的一致
    # 检查并转换键名，将'项目名称'转换为'检查'以匹配planner.py的要求
    for test in interaction_history.test_recommendation:
        if '项目名称' in test and '检查' not in test:
            test['检查'] = test['项目名称']

    planner = Planner(max_rounds=9, interaction_history=interaction_history)

    # 获取最终诊断结果

    has_new_input_for_final_diagnosis = bool(
        interaction_history.doctor_supplementary_info or interaction_history.test_recommendation
        # Assuming presence implies new data to consider for diagnosis
    )
    print(has_new_input_for_final_diagnosis)
    diagnosis, condition, reasoning = asyncio.run(
        planner.final_diagnosis_and_condition(has_input=has_new_input_for_final_diagnosis)
    )

    treatment_plan = asyncio.run(planner.get_treatments(planner.interaction_history.diagnosis, condition))
    cleaned_treatment_plan = clean_treatment_recommendation(treatment_plan)

    return jsonify({"diagnosis": diagnosis, "condition": condition, "treatment_recommendation": cleaned_treatment_plan,
                    "treatment_guide": interaction_history.guidance_for_treatment})


@app.route('/ai_doctor_v2', methods=['POST'])
def ai_doctor_v2():  # 改为同步函数 def，移除 interaction_history 参数
    """
    处理 POST 请求，接收 interaction_history 和可选的 answer,
    先更新observation，再调用异步的 api_next_response，并返回 JSON 结果。
    """
    logger.info("Received POST request for /ai_doctor_v2")

    data = request.get_json()
    if not data or 'interaction_history' not in data:
        logger.error("Request JSON is missing 'interaction_history'.")
        return jsonify({"error": "Missing 'interaction_history' in request JSON."}), 400

    try:
        history_dict = data['interaction_history']
        interaction_history = InteractionHistory()  # 创建空实例

        # 加载交互历史的各个属性
        interaction_history.cot_entries = history_dict.get('cot_entries', [])
        interaction_history.diagnosis = history_dict.get('diagnosis')
        interaction_history.test_recommendation = history_dict.get('test_recommendation', [])
        interaction_history.treatment_recommendation = history_dict.get('treatment_recommendation')
        interaction_history.preliminary_diagnosis = history_dict.get('preliminary_diagnosis')
        interaction_history.doctor_supplementary_info = history_dict.get('doctor_supplementary_info', [])
        interaction_history.lastObservation = history_dict.get('lastObservation', '')

        # 确保test_recommendation中的键名与planner期望的一致
        for test in interaction_history.test_recommendation:
            if '项目名称' in test and '检查' not in test:
                test['检查'] = test['项目名称']

        logger.debug("Successfully loaded interaction history from request.")
    except Exception as e:
        logger.error(f"Failed to load interaction history from request JSON: {e}", exc_info=True)
        return jsonify({"error": "An internal error occurred during processing."}), 500

    try:
        # 只在第一次问答交互时更新observation（即dialogue_history长度小于等于2）
        is_first_interaction = False
        if interaction_history.cot_entries and len(interaction_history.cot_entries) > 0:
            last_entry = interaction_history.cot_entries[-1]
            if "dialogue_history" in last_entry and len(last_entry["dialogue_history"]) <= 2:
                is_first_interaction = True

        if is_first_interaction:
            logger.info("First interaction detected, updating observation")
            executor = Executor(max_rounds=12, interaction_history=interaction_history)
            asyncio.run(executor.update_observation())
            logger.info("Updated observation before generating next response")

        # 调用api_next_response生成下一个问题或诊断
        response_content, updated_interaction_history = asyncio.run(api_next_response(interaction_history))
        logger.info(f"Generated response content: {response_content}")

        # 手动构建返回的字典
        response_dict = {
            "interaction_history": {
                "cot_entries": updated_interaction_history.cot_entries,
                "diagnosis": updated_interaction_history.diagnosis,
                "test_recommendation": updated_interaction_history.test_recommendation,
                "treatment_recommendation": updated_interaction_history.treatment_recommendation,
                "preliminary_diagnosis": updated_interaction_history.preliminary_diagnosis,
                "doctor_supplementary_info": updated_interaction_history.doctor_supplementary_info,
                "lastObservation": updated_interaction_history.lastObservation if hasattr(updated_interaction_history,
                                                                                          'lastObservation') else ''
            }
        }
        return jsonify(response_dict)

    except Exception as e:
        logger.error(f"Error processing request: {e}", exc_info=True)
        return jsonify({"error": f"处理请求时发生错误: {str(e)}"}), 500


if __name__ == "__main__":
    # 配置服务器运行参数
    app.run(host='0.0.0.0',
            port=5555,
            debug=True,
            use_reloader=True,  # 开启自动重载，方便开发调试
            threaded=True,  # 启用线程模式
            processes=1  # 使用单进程
            )