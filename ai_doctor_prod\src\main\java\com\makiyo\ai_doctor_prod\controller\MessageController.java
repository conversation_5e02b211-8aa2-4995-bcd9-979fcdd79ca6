package com.makiyo.ai_doctor_prod.controller;

import com.makiyo.ai_doctor_prod.service.MessageService;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.format.annotation.DateTimeFormat;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.time.ZonedDateTime;
import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;
import io.swagger.v3.oas.annotations.media.Schema;
import com.makiyo.ai_doctor_prod.form.SupplementaryInfoRequest;
import com.makiyo.ai_doctor_prod.form.AddTestRecommendationRequest;
import com.makiyo.ai_doctor_prod.response.Response;
import com.makiyo.ai_doctor_prod.response.PageInfo;

@RestController
@RequestMapping("/message")
@Tag(name = "消息接口 (Prod)", description = "提供生产环境的消息处理和 AI 对话相关接口")
@Validated  // 添加此注解以启用方法参数验证
public class MessageController {

    private static final Logger log = LoggerFactory.getLogger(MessageController.class);

    @Resource
    private MessageService messageService;

    @Operation(summary = "测试数据库连接", description = "调用 Service 层的方法测试与数据库的连接是否正常")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "数据库连接成功"),
            @ApiResponse(responseCode = "500", description = "数据库连接失败")
    })
    @GetMapping("/test-db-connection")
    public ResponseEntity<String> testDatabaseConnection() {
        boolean isConnected = messageService.testDatabaseConnection();
        if (isConnected) {
            return ResponseEntity.ok("Database connection test successful via Mapper.");
        } else {
            // 使用 500 Internal Server Error 表示连接失败
            return ResponseEntity.status(500).body("Database connection test failed via Mapper. Check logs for details.");
        }
    }

    @Operation(summary = "AI 问诊对话 (Prod)", description = "与生产环境 AI 模型进行流式问诊对话 (SSE)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "成功建立 SSE 连接并开始流式传输"),
        @ApiResponse(responseCode = "400", description = "请求参数无效 (例如 ID 或消息为空)"),
        @ApiResponse(responseCode = "404", description = "找不到指定的 CaseItem ID"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误或 AI 服务调用失败")
    })
    @GetMapping(value = "/inquiry", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter aiDoctorInquiry(
            @Parameter(description = "CaseItem 的唯一标识符", required = true, example = "case_uuid_123")
            @RequestParam @NotBlank(message = "案例ID不能为空") String id,
            @Parameter(description = "用户发送给 AI 的消息内容", required = true, example = "孩子发烧三天了")
            @RequestParam @NotBlank(message = "消息内容不能为空") String message,
            @Parameter(description = "初始化标志, 1/2=不同UI的初始化, 0或不传=正常对话, 3=默认欢迎语", required = false, example = "0")
            @RequestParam(required = false, defaultValue = "0") Integer flag) {
        
        log.info("[PROD Controller] Received /inquiry request for case ID: {}, message: {}, flag: {}", id, message, flag);
        if (flag != null && (flag == 1 || flag == 2)) {
            return messageService.initialInteractionHistory(id, message, flag);
        } else if (flag != null && flag == 3) {
            return messageService.sendDefaultWelcomeMessage(id);
        } else {
            return messageService.aiDoctorInquiry(id, message);
        }
    }

    @Operation(summary = "AI 快速问诊对话 (Prod)", description = "与生产环境 AI 模型进行快速流式问诊对话 (SSE)，使用优化的速度设置")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "成功建立 SSE 连接并开始流式传输"),
        @ApiResponse(responseCode = "400", description = "请求参数无效 (例如 ID 或消息为空)"),
        @ApiResponse(responseCode = "404", description = "找不到指定的 CaseItem ID"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误或 AI 服务调用失败")
    })
    @GetMapping(value = "/chat_ai_doctor_v2_quick", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter aiDoctorQuickInquiry(
            @Parameter(description = "CaseItem 的唯一标识符", required = true, example = "case_uuid_123")
            @RequestParam @NotBlank(message = "案例ID不能为空") String id,
            @Parameter(description = "用户发送给 AI 的消息内容", required = true, example = "孩子发烧三天了")
            @RequestParam @NotBlank(message = "消息内容不能为空") String message) {
        log.info("[PROD Controller] Received /chat_ai_doctor_v2_quick request for case ID: {}, message: {}", id, message);
        return messageService.aiDoctorQuickInquiry(id, message);
    }

    @Operation(
        summary = "添加医生补充信息 (Prod)",
        description = "向指定案例的交互历史中添加医生提供的补充信息。",
        requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "包含ID和补充信息的JSON对象",
            required = true,
            content = @io.swagger.v3.oas.annotations.media.Content(
                mediaType = "application/json",
                schema = @Schema(implementation = SupplementaryInfoRequest.class)
            )
        )
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "补充信息添加成功"),
        @ApiResponse(responseCode = "400", description = "请求参数无效（ID 或补充信息为空，或请求体格式错误）"),
        @ApiResponse(responseCode = "404", description = "未找到指定的 CaseItem ID"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误（例如数据库更新失败）")
    })
    @PostMapping("/add_supplementary_info")
    public ResponseEntity<String> addDoctorSupplementaryInfoProd(
        @Valid @RequestBody SupplementaryInfoRequest request
    ) {
        log.info("[PROD Controller] Received request to add supplementary info for case ID: {}", request.getId());
        
        try {
            boolean success = messageService.addDoctorSupplementaryInfoProd(request.getId(), request.getSupplementaryInfo());
            if (success) {
                log.info("[PROD Controller] Successfully added supplementary info for case ID: {}", request.getId());
                return ResponseEntity.ok("补充信息添加成功");
            } else {
                log.warn("[PROD Controller] Failed to add supplementary info for case ID: {}. Service returned false.", request.getId());
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("添加补充信息失败，请检查日志获取详情");
            }
        } catch (Exception e) {
            log.error("[PROD Controller] Error processing add supplementary info request for case ID {}: {}", request.getId(), e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("处理添加补充信息请求时发生服务器内部错误");
        }
    }

    @Operation(
        summary = "获取 AI 诊断结果 (Prod)",
        description = "根据案例 ID 获取交互历史，调用 AI 诊断服务获取诊断结果，并将结果更新回数据库。"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "成功获取诊断结果", content = @io.swagger.v3.oas.annotations.media.Content(mediaType = "application/json")),
        @ApiResponse(responseCode = "400", description = "请求参数无效（如缺少 ID）或交互历史结构错误"),
        @ApiResponse(responseCode = "404", description = "未找到指定的 CaseItem ID"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误（如数据库操作失败、诊断服务响应解析失败）"),
        @ApiResponse(responseCode = "503", description = "无法连接到 AI 诊断服务")
    })
    @PostMapping("/diagnose")
    public ResponseEntity<?> diagnoseAiDoctorProd(
        @Parameter(description = "CaseItem 的唯一标识符", required = true, example = "case_uuid_123")
        @RequestParam @NotBlank(message = "案例ID不能为空") String id
    ) {
        log.info("[PROD Controller] Received request to diagnose case ID: {}", id);
        
        try {
            Map<String, Object> result = messageService.diagnoseAiDoctorProd(id);

            if (result.containsKey("error")) {
                log.warn("[PROD Controller] Diagnosis failed for case ID {}: {}", id, result.get("error"));
                HttpStatus status = HttpStatus.INTERNAL_SERVER_ERROR; // Default to 500
                String errorMsg = result.get("error").toString().toLowerCase();
                if (errorMsg.contains("未找到案例记录")) {
                    status = HttpStatus.NOT_FOUND; // 404
                } else if (errorMsg.contains("缺少案例 id") || errorMsg.contains("结构无效")) {
                    status = HttpStatus.BAD_REQUEST; // 400
                } else if (errorMsg.contains("连接诊断服务失败")) {
                     status = HttpStatus.SERVICE_UNAVAILABLE; // 503
                }
                return ResponseEntity.status(status).body(result);
            } else {
                log.info("[PROD Controller] Diagnosis successful for case ID: {}", id);
                return ResponseEntity.ok(result); // 返回包含诊断结果和 cot 的 Map
            }
        } catch (Exception e) {
            log.error("[PROD Controller] Error processing diagnose request for case ID {}: {}", id, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("error", "处理诊断请求时发生服务器内部错误"));
        }
    }

    @Operation(
        summary = "重置案例交互历史 (Prod)",
        description = "将指定案例的交互历史清空为初始状态。"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "交互历史重置成功"),
        @ApiResponse(responseCode = "400", description = "请求参数无效（如缺少 ID）"),
        @ApiResponse(responseCode = "404", description = "未找到指定的 CaseItem ID"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误（如数据库更新失败）")
    })
    @PostMapping("/reset")
    public ResponseEntity<Map<String, Object>> resetInteractionHistory(
        @Parameter(description = "需要重置交互历史的 CaseItem 的唯一标识符", required = true, example = "case_uuid_123")
        @RequestParam @NotBlank(message = "案例ID不能为空") String id
    ) {
        log.info("[PROD Controller] Received request to reset interaction history for case ID: {}", id);
        Map<String, Object> response = new HashMap<>();

        try {
            boolean success = messageService.resetInteractionHistory(id);
            if (success) {
                log.info("[PROD Controller] Interaction history reset successfully for case ID: {}", id);
                response.put("success", true);
                response.put("message", "交互历史已成功重置");
                return ResponseEntity.ok(response);
            } else {
                 log.error("[PROD Controller] Reset failed for case ID {} (Service returned false - check logs for reason like Not Found or DB Error).", id);
                 response.put("success", false);
                 response.put("message", "重置交互历史失败，可能是案例不存在或服务器内部错误，请检查日志");
                 return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
            }
        } catch (Exception e) {
            log.error("[PROD Controller] Error processing reset request for case ID {}: {}", id, e.getMessage(), e);
            response.put("success", false);
            response.put("message", "处理重置请求时发生服务器内部错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @Operation(
        summary = "添加检查建议到案例 (Prod)",
        description = "向指定案例的交互历史中的 test_recommendation 列表添加新的检查建议。",
        requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "包含案例ID和检查建议列表的JSON对象",
            required = true,
            content = @io.swagger.v3.oas.annotations.media.Content(
                mediaType = "application/json",
                schema = @Schema(implementation = AddTestRecommendationRequest.class)
            )
        )
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "检查建议添加成功"),
        @ApiResponse(responseCode = "400", description = "请求参数无效（如ID为空、检查建议列表为空，或请求体格式错误）"),
        @ApiResponse(responseCode = "404", description = "未找到指定的 CaseItem ID"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误（例如数据库更新失败）")
    })
    @PostMapping("/add_test_recommendation")
    public ResponseEntity<Map<String, Object>> addTestRecommendation(
        @Valid @RequestBody AddTestRecommendationRequest request
    ) {
        log.info("[PROD Controller] Received request to add test recommendation for case ID: {}", request.getId());
        Map<String, Object> response = new HashMap<>();

        try {
            boolean success = messageService.addTestRecommendationToInteractionHistory(request.getId(), request.getTestRecommendation());
            if (success) {
                log.info("[PROD Controller] Successfully added test recommendation for case ID: {}", request.getId());
                response.put("success", true);
                response.put("message", "检查建议添加成功");
                return ResponseEntity.ok(response);
            } else {
                log.warn("[PROD Controller] Failed to add test recommendation for case ID: {}. Service returned false.", request.getId());
                response.put("success", false);
                response.put("message", "添加检查建议失败，可能是案例不存在或服务器内部错误，请检查日志");
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
            }
        } catch (Exception e) {
            log.error("[PROD Controller] Error processing add test recommendation request for case ID {}: {}", request.getId(), e.getMessage(), e);
            response.put("success", false);
            response.put("message", "处理添加检查建议请求时发生服务器内部错误: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @Operation(
        summary = "获取拟诊结果 (Prod)",
        description = "根据案例ID调用Python服务获取拟诊结果，保存到数据库，返回病情描述和拟诊内容"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "拟诊获取成功"),
        @ApiResponse(responseCode = "400", description = "请求参数无效（如缺少ID）"),
        @ApiResponse(responseCode = "404", description = "未找到指定的CaseItem ID"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误或Python服务调用失败")
    })
    @PostMapping("/preliminary_diagnosis")
    public ResponseEntity<?> getPreliminaryDiagnosisProd(
        @Parameter(description = "CaseItem的唯一标识符", required = true, example = "case_uuid_123")
        @RequestParam @NotBlank(message = "案例ID不能为空") String id
    ) {
        log.info("[PROD Controller] Received request for preliminary diagnosis for case ID: {}", id);
        
        try {
            Map<String, Object> result = messageService.getPreliminaryDiagnosisProd(id);

            if (result.containsKey("error")) {
                log.warn("[PROD Controller] Preliminary diagnosis failed for case ID {}: {}", id, result.get("error"));
                HttpStatus status = HttpStatus.INTERNAL_SERVER_ERROR; // Default to 500
                String errorMsg = result.get("error").toString().toLowerCase();
                if (errorMsg.contains("未找到案例记录")) {
                    status = HttpStatus.NOT_FOUND; // 404
                } else if (errorMsg.contains("缺少案例 id") || errorMsg.contains("结构无效")) {
                    status = HttpStatus.BAD_REQUEST; // 400
                }
                return ResponseEntity.status(status).body(result);
            } else {
                log.info("[PROD Controller] Preliminary diagnosis successful for case ID: {}", id);
                return ResponseEntity.ok(result);
            }
        } catch (Exception e) {
            log.error("[PROD Controller] Error processing preliminary diagnosis request for case ID {}: {}", id, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("error", "处理拟诊请求时发生服务器内部错误"));
        }
    }

    @Operation(
        summary = "创建新的案例条目 (Prod)",
        description = "根据提供的ID创建一个新的CaseItem条目，interaction_history和profile默认为空对象。"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "案例创建成功", content = @io.swagger.v3.oas.annotations.media.Content(mediaType = "application/json")),
        @ApiResponse(responseCode = "400", description = "请求参数无效 (如缺少ID或ID已存在)"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @PostMapping("/create_case_item")
    public ResponseEntity<Map<String, Object>> createCaseItem(
        @Parameter(description = "新案例的唯一标识符", required = true, example = "case_new_uuid_001")
        @RequestParam @NotBlank(message = "案例ID不能为空") String id
    ) {
        log.info("[PROD Controller] Received request to create new case with ID: {}", id);
        Map<String, Object> result = messageService.createCaseItem(id);

        boolean success = (boolean) result.getOrDefault("success", false);
        if (success) {
            log.info("[PROD Controller] CaseItem created successfully with ID: {}", id);
            return ResponseEntity.status(HttpStatus.CREATED).body(result);
        } else {
            String errorMsg = (String) result.getOrDefault("error", "未知错误");
            log.warn("[PROD Controller] Failed to create CaseItem with ID {}: {}", id, errorMsg);
            if (errorMsg.contains("案例ID已存在")) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result);
            } else if (errorMsg.contains("案例ID不能为空")) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result);
            }
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
        }
    }

    @Operation(
        summary = "获取案例交互历史 (Prod)",
        description = "根据案例ID获取其interaction_history字段内容"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "成功获取交互历史"),
        @ApiResponse(responseCode = "400", description = "请求参数无效 (如缺少ID)"),
        @ApiResponse(responseCode = "404", description = "未找到指定的CaseItem ID"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/get_interaction_history")
    public ResponseEntity<Response<Object>> getInteractionHistory(
        @Parameter(description = "CaseItem的唯一标识符", required = true, example = "case_uuid_123")
        @RequestParam @NotBlank(message = "案例ID不能为空") String id
    ) {
        log.info("[PROD Controller] Received request for interaction_history for case ID: {}", id);
        
        try {
            Map<String, Object> result = messageService.getInteractionHistory(id);
            
            if (result.containsKey("error")) {
                String errorMsg = (String) result.get("error");
                log.warn("[PROD Controller] Error getting interaction_history for case ID {}: {}", id, errorMsg);
                
                Integer statusCode = 500; // 默认状态码
                if (errorMsg.contains("未找到案例记录")) {
                    statusCode = 404;
                } else if (errorMsg.contains("案例ID不能为空")) {
                    statusCode = 400;
                }
                
                return ResponseEntity.status(statusCode)
                        .body(Response.error(statusCode, errorMsg));
            } else {
                log.info("[PROD Controller] Successfully retrieved interaction_history for case ID: {}", id);
                // 将整个包含interaction_history键的对象作为content返回
                // 而不是直接返回interaction_history的内容
                Map<String, Object> contentMap = new HashMap<>();
                contentMap.put("interaction_history", result.get("interaction_history"));
                return ResponseEntity.ok(Response.success(contentMap));
            }
        } catch (Exception e) {
            log.error("[PROD Controller] Unexpected error handling get_interaction_history request for case ID {}: {}", 
                    id, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Response.fail("处理获取交互历史请求时发生服务器内部错误"));
        }
    }

    @Operation(
        summary = "获取所有案例交互历史 (Prod)",
        description = "获取所有案例的interaction_history字段内容，可根据时间范围过滤"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "成功获取所有交互历史"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    @GetMapping("/get_all_interaction_histories")
    public ResponseEntity<Response<PageInfo<Map<String, Object>>>> getAllInteractionHistories(
            @Parameter(description = "搜索开始日期 (ISO 8601 格式, e.g., 2023-01-01T00:00:00Z)", required = false)
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) ZonedDateTime startDate,
            @Parameter(description = "搜索结束日期 (ISO 8601 格式, e.g., 2023-01-31T23:59:59Z)", required = false)
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) ZonedDateTime endDate,
            @Parameter(description = "页码", required = false, example = "1")
            @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小", required = false, example = "10")
            @RequestParam(defaultValue = "10") int size
    ) {
        log.info("[PROD Controller] Received request for all interaction histories with pagination: page={}, size={}, startDate={}, endDate={}",
                page, size, startDate, endDate);
        try {
            PageInfo<Map<String, Object>> historiesPage = messageService.getAllInteractionHistories(startDate, endDate, page, size);
            return ResponseEntity.ok(Response.ok(historiesPage));
        } catch (Exception e) {
            log.error("[PROD Controller] Error getting all interaction histories: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Response.fail("获取交互历史记录时发生服务器内部错误"));
        }
    }

    // ... 这里可以添加 MessageController 的其他业务逻辑接口 ...
}
