] INFO  c.m.a.c.OcrQueueStatsController - [PROD OCR Stats] 定期统计 - 总请求: 77, 成功: 65, 失败: 12, 当前队列: 0, 峰值队列: 3
2025-07-16 16:17:09.249 [ai-doctor-prod-dev,,] [scheduling-1] WARN  c.m.a.utils.ConnectionMonitor - Detected large number of CLOSE_WAIT connections: 40 (change: -1)
2025-07-16 16:17:09.249 [ai-doctor-prod-dev,,] [scheduling-1] INFO  c.m.a.utils.ConnectionMonitor - TCP connection state ESTABLISHED: 53 (change: 9)
2025-07-16 16:17:09.249 [ai-doctor-prod-dev,,] [scheduling-1] INFO  c.m.a.utils.ConnectionMonitor - TCP connection state TIME_WAIT: 488 (change: 147)
2025-07-16 16:17:09.249 [ai-doctor-prod-dev,,] [scheduling-1] INFO  c.m.a.utils.ConnectionMonitor - TCP connection state LAST_ACK: 2 (change: -3)
2025-07-16 16:17:09.264 [ai-doctor-prod-dev,,] [scheduling-1] INFO  c.m.a.c.ConnectionMonitorConfig$ConnectionHealthIndicator - Connection Status - ESTABLISHED: 54, CLOSE_WAIT: 40, TIME_WAIT: 488
2025-07-16 16:17:09.264 [ai-doctor-prod-dev,,] [scheduling-1] WARN  c.m.a.c.ConnectionMonitorConfig$ConnectionHealthIndicator - High number of CLOSE_WAIT connections detected: 40. This may indicate connection leaks.
2025-07-16 16:17:46.827 [ai-doctor-prod-dev,,] [http-nio-9999-exec-8] INFO  c.m.a.controller.OcrController - [PROD OCR Controller] Received OCR request. Image: .h088GEqqf_hOXsxwt7f-x
2025-07-16 16:17:46.828 [ai-doctor-prod-dev,,] [http-nio-9999-exec-8] INFO  c.m.a.service.OcrQueueService - [PROD OCR Queue] OCR请求已加入队列，当前队列长度: 1, 图片大小: 955797 字节
2025-07-16 16:17:46.828 [ai-doctor-prod-dev,,] [ocr-queue-processor-prod] INFO  c.m.a.service.OcrQueueService - [PROD OCR Queue] 开始处理OCR请求，案例ID: 无ID, 剩余队列长度: 0
2025-07-16 16:17:46.829 [ai-doctor-prod-dev,,] [ocr-queue-processor-prod] INFO  c.m.a.service.OcrService - [PROD OCR] Image saved: /home/<USER>/images/c4b8bd9e-049d-402a-bf45-87db203cdae8.h088GEqqf_hOXsxwt7f-x
2025-07-16 16:17:46.836 [ai-doctor-prod-dev,,] [ocr-queue-processor-prod] INFO  c.m.a.service.ImageService - [PROD ImageService] 图片已存储到GridFS，ID: 6877602a9c1fab3a9db06208, 案例ID: null, 分类: OCR
2025-07-16 16:17:46.836 [ai-doctor-prod-dev,,] [ocr-queue-processor-prod] INFO  c.m.a.service.OcrService - [PROD OCR] Image stored in GridFS with ID: 6877602a9c1fab3a9db06208
2025-07-16 16:17:46.836 [ai-doctor-prod-dev,,] [ocr-queue-processor-prod] INFO  c.m.a.service.OcrService - [PROD OCR] Calling Python OCR service URL: http://localhost:5555/ocr_api ，尝试次数: 1/3
2025-07-16 16:18:07.645 [ai-doctor-prod-dev,,] [ocr-queue-processor-prod] INFO  c.m.a.service.OcrService - [PROD OCR] OCR result saved with ID: 6877603f9c1fab3a9db0620d
2025-07-16 16:18:07.645 [ai-doctor-prod-dev,,] [ocr-queue-processor-prod] WARN  c.m.a.service.OcrService - [PROD OCR] Temporary OCR image could not be deleted (or was already gone): /home/<USER>/images/c4b8bd9e-049d-402a-bf45-87db203cdae8.h088GEqqf_hOXsxwt7f-x
2025-07-16 16:18:07.645 [ai-doctor-prod-dev,,] [ocr-queue-processor-prod] INFO  c.m.a.service.OcrQueueService - [PROD OCR Queue] 完成OCR请求处理，案例ID: 无ID
2025-07-16 16:18:07.645 [ai-doctor-prod-dev,,] [http-nio-9999-exec-8] INFO  c.m.a.controller.OcrController - [PROD OCR Controller] OCR request processed successfully. Processing time: 20818ms
2025-07-16 16:18:09.252 [ai-doctor-prod-dev,,] [scheduling-1] INFO  c.m.a.c.ConnectionMonitorConfig$ConnectionHealthIndicator - Connection Status - ESTABLISHED: 55, CLOSE_WAIT: 40, TIME_WAIT: 472
2025-07-16 16:18:09.252 [ai-doctor-prod-dev,,] [scheduling-1] WARN  c.m.a.c.ConnectionMonitorConfig$ConnectionHealthIndicator - High number of CLOSE_WAIT connections detected: 40. This may indicate connection leaks.
2025-07-16 16:18:12.081 [ai-doctor-prod-dev,,] [http-nio-9999-exec-14] INFO  c.m.a.controller.MessageController - [PROD Controller] Received request to create new case with ID: O26SFFu_HjoFpxRXJgQ95
2025-07-16 16:18:12.081 [ai-doctor-prod-dev,,] [http-nio-9999-exec-14] INFO  c.m.a.service.MessageService - [PROD Create CaseItem] Attempting to create CaseItem with ID: O26SFFu_HjoFpxRXJgQ95
2025-07-16 16:18:12.082 [ai-doctor-prod-dev,,] [http-nio-9999-exec-14] DEBUG c.m.a.mapper.CaseItemMapper.findById - ==>  Preparing: select id, created_at, updated_at, profile, interaction_history from "case_item" where id = ?
2025-07-16 16:18:12.082 [ai-doctor-prod-dev,,] [http-nio-9999-exec-14] DEBUG c.m.a.mapper.CaseItemMapper.findById - ==> Parameters: O26SFFu_HjoFpxRXJgQ95(String)
2025-07-16 16:18:12.083 [ai-doctor-prod-dev,,] [http-nio-9999-exec-14] DEBUG c.m.a.mapper.CaseItemMapper.findById - <==      Total: 0
2025-07-16 16:18:12.084 [ai-doctor-prod-dev,,] [http-nio-9999-exec-14] INFO  c.m.a.service.MessageService - [PROD Create CaseItem] Successfully created CaseItem with ID: O26SFFu_HjoFpxRXJgQ95 using direct SQL
2025-07-16 16:18:12.084 [ai-doctor-prod-dev,,] [http-nio-9999-exec-14] INFO  c.m.a.controller.MessageController - [PROD Controller] CaseItem created successfully with ID: O26SFFu_HjoFpxRXJgQ95
2025-07-16 16:18:12.204 [ai-doctor-prod-dev,,] [http-nio-9999-exec-4] INFO  c.m.a.controller.MessageController - [PROD Controller] Received request to add test recommendation for case ID: O26SFFu_HjoFpxRXJgQ95
2025-07-16 16:18:12.204 [ai-doctor-prod-dev,,] [http-nio-9999-exec-4] INFO  c.m.a.service.MessageService - [PROD Add TestRecommendation] Attempting to add 1 test recommendations for case ID: O26SFFu_HjoFpxRXJgQ95
2025-07-16 16:18:12.204 [ai-doctor-prod-dev,,] [http-nio-9999-exec-4] DEBUG c.m.a.mapper.CaseItemMapper.findById - ==>  Preparing: select id, created_at, updated_at, profile, interaction_history from "case_item" where id = ?
2025-07-16 16:18:12.204 [ai-doctor-prod-dev,,] [http-nio-9999-exec-4] DEBUG c.m.a.mapper.CaseItemMapper.findById - ==> Parameters: O26SFFu_HjoFpxRXJgQ95(String)
2025-07-16 16:18:12.206 [ai-doctor-prod-dev,,] [http-nio-9999-exec-4] DEBUG c.m.a.mapper.CaseItemMapper.findById - <==      Total: 1
2025-07-16 16:18:12.206 [ai-doctor-prod-dev,,] [http-nio-9999-exec-4] INFO  c.m.a.service.MessageService - [PROD Add TestRecommendation] Added 1 new test recommendations. New list size: 1 for case ID: O26SFFu_HjoFpxRXJgQ95
2025-07-16 16:18:12.206 [ai-doctor-prod-dev,,] [http-nio-9999-exec-4] DEBUG c.m.a.m.C.updateInteractionHistoryById - ==>  Preparing: update "case_item" set interaction_history = ? where id = ?
2025-07-16 16:18:12.206 [ai-doctor-prod-dev,,] [http-nio-9999-exec-4] DEBUG c.m.a.m.C.updateInteractionHistoryById - ==> Parameters: {"interaction_history":{"cot_entries":[{"dialogue_history":[{"content":"您好，我是本次的医生，请问孩子的年龄，性别，主要的症状是什么？","role":"doctor"}],"feedback":"","observation":"","reasoning":"主诉是问诊的切入点，通过了解患者的基本信息和主要症状，可以快速建立初步印象。进一步询问发病时间、起病情况、症状特点、伴随症状、病情演变和诊疗经过，有助于全面了解现病史，为后续诊断提供关键信息。这种系统性的问诊策略能够帮助我们准确把握患者的病情，避免遗漏重要信息。","strategy":"询问患者的基本信息（性别、年龄）和主诉症状，了解发病时间、起病情况、症状特点（性质、程度、持续时间、频率）、伴随症状、病情演变以及之前的诊疗经过。"}],"preliminary_diagnosis":null,"test_recommendation":[{"content":[{"基础信息":{"姓名":"","年龄":"","性别":"男","科室":"小儿内科","检查部位":"全血"}},{"检查结果":[{"其他":"","单位":"10^9/L","结果":"9.77","诊断":"","参考范围":"4.90~12.70","处理建议":"","异常标记":"","检查描述":"","检测方法":"","结果状态":"正常","项目名称":"★白细胞","样本采集时间":"2025-05-22 09:48"},{"其他":"","单位":"10^12/L","结果":"5.22","诊断":"","参考范围":"4.10~5.50","处理建议":"","异常标记":"","检查描述":"","检测方法":"","结果状态":"正常","项目名称":"★红细胞","样本采集时间":"2025-05-22 09:48"},{"其他":"","单位":"g/L","结果":"147","诊断":"","参考范围":"115~150","处理建议":"","异常标记":"","检查描述":"","检测方法":"","结果状态":"正常","项目名称":"★血红蛋白","样本采集时间":"2025-05-22 09:48"},{"其他":"","单位":"%","结果":"42.3","诊断":"","参考范围":"35.0~45.0","处理建议":"","异常标记":"","检查描述":"","检测方法":"","结果状态":"正常","项目名称":"★红细胞压积","样本采集时间":"2025-05-22 09:48"},{"其他":"","单位":"fl","结果":"81.0","诊断":"","参考范围":"76.0~88.0","处理建议":"","异常标记":"","检查描述":"","检测方法":"","结果状态":"正常","项目名称":"红细胞平均体积","样本采集时间":"2025-05-22 09:48"},{"其他":"","单位":"pg","结果":"28.2","诊断":"","参考范围":"24.0~30.0","处理建议":"","异常标记":"","检查描述":"","检测方法":"","结果状态":"正常","项目名称":"平均血红蛋白含量","样本采集时间":"2025-05-22 09:48"},{"其他":"","单位":"g/L","结果":"348","诊断":"","参考范围":"309~359","处理建议":"","异常标记":"","检查描述":"","检测方法":"","结果状态":"正常","项目名称":"平均血红蛋白浓度","样本采集时间":"2025-05-22 09:48"},{"其他":"","单位":"10^9/L","结果":"312","诊断":"","参考范围":"187~475","处理建议":"","异常标记":"","检查描述":"","检测方法":"","结果状态":"正常","项目名称":"★血小板","样本采集时间":"2025-05-22 09:48"},{"其他":"","单位":"%","结果":"37.7","诊断":"","参考范围":"37.0~54.0","处理建议":"","异常标记":"","检查描述":"","检测方法":"","结果状态":"正常","项目名称":"红细胞体积分布宽度","样本采集时间":"2025-05-22 09:48"},{"其他":"","单位":"%","结果":"12.9","诊断":"","参考范围":"7.0~17.0","处理建议":"","异常标记":"","检查描述":"","检测方法":"","结果状态":"正常","项目名称":"红细胞体积分布宽度","样本采集时间":"2025-05-22 09:48"},{"其他":"","单位":"%","结果":"9.5","诊断":"","参考范围":"7.0~17.0","处理建议":"","异常标记":"","检查描述":"","检测方法":"","结果状态":"正常","项目名称":"血小板体积分布宽度","样本采集时间":"2025-05-22 09:48"},{"其他":"","单位":"fl","结果":"9.6","诊断":"","参考范围":"8.0~13.0","处理建议":"","异常标记":"","检查描述":"","检测方法":"","结果状态":"正常","项目名称":"血小板平均体积","样本采集时间":"2025-05-22 09:48"},{"其他":"","单位":"%","结果":"20.5","诊断":"","参考范围":"13.0~43.0","处理建议":"","异常标记":"","检查描述":"","检测方法":"","结果状态":"正常","项目名称":"大血小板比率","样本采集时间":"2025-05-22 09:48"},{"其他":"","单位":"%","结果":"0.30","诊断":"","参考范围":"0.150~0.330","处理建议":"","异常标记":"","检查描述":"","检测方法":"","结果状态":"正常","项目名称":"血小板压积","样本采集时间":"2025-05-22 09:48"},{"其他":"","单位":"10^9/L","结果":"4.73","诊断":"","参考范围":"1.30~6.70","处理建议":"","异常标记":"","检查描述":"","检测方法":"","结果状态":"正常","项目名称":"中性粒细胞绝对值","样本采集时间":"2025-05-22 09:48"},{"其他":"","单位":"10^9/L","结果":"4.22","诊断":"","参考范围":"2.00~6.50","处理建议":"","异常标记":"","检查描述":"","检测方法":"","结果状态":"正常","项目名称":"淋巴细胞绝对值","样本采集时间":"2025-05-22 09:48"},{"其他":"","单位":"10^9/L","结果":"0.44","诊断":"","参考范围":"0.16~0.92","处理建议":"","异常标记":"","检查描述":"","检测方法":"","结果状态":"正常","项目名称":"单核细胞绝对值","样本采集时间":"2025-05-22 09:48"},{"其他":"","单位":"10^9/L","结果":"0.35","诊断":"","参考范围":"0.04~0.74","处理建议":"","异常标记":"","检查描述":"","检测方法":"","结果状态":"正常","项目名称":"嗜酸性粒细胞绝对值","样本采集时间":"2025-05-22 09:48"},{"其他":"","单位":"10^9/L","结果":"0.03","诊断":"","参考范围":"0.00~0.10","处理建议":"","异常标记":"","检查描述":"","检测方法":"","结果状态":"正常","项目名称":"嗜碱性粒细胞绝对值","样本采集时间":"2025-05-22 09:48"},{"其他":"","单位":"%","结果":"48.4","诊断":"","参考范围":"23.0~64.0","处理建议":"","异常标记":"","检查描述":"","检测方法":"","结果状态":"正常","项目名称":"中性粒细胞百分比","样本采集时间":"2025-05-22 09:48"},{"其他":"","单位":"%","结果":"43.2","诊断":"","参考范围":"26.0~67.0","处理建议":"","异常标记":"","检查描述":"","检测方法":"","结果状态":"正常","项目名称":"淋巴细胞百分比","样本采集时间":"2025-05-22 09:48"},{"其他":"","单位":"%","结果":"4.5","诊断":"","参考范围":"2.0~11.0","处理建议":"","异常标记":"","检查描述":"","检测方法":"","结果状态":"正常","项目名称":"单核细胞百分比","样本采集时间":"2025-05-22 09:48"},{"其他":"","单位":"%","结果":"3.6","诊断":"","参考范围":"0.5~9.0","处理建议":"","异常标记":"","检查描述":"","检测方法":"","结果状态":"正常","项目名称":"嗜酸性粒细胞百分比","样本采集时间":"2025-05-22 09:48"},{"其他":"","单位":"%","结果":"0.3","诊断":"","参考范围":"0.0~1.0","处理建议":"","异常标记":"","检查描述":"","检测方法":"","结果状态":"正常","项目名称":"嗜碱性粒细胞百分比","样本采集时间":"2025-05-22 09:48"},{"其他":"","单位":"mg/L","结果":"<0.499","诊断":"","参考范围":"0.00~8.00","处理建议":"","异常标记":"","检查描述":"","检测方法":"","结果状态":"正常","项目名称":"C反应蛋白","样本采集时间":"2025-05-22 09:48"},{"其他":"","单位":"mg/L","结果":"<5","诊断":"","参考范围":"0.00~10.00","处理建议":"","异常标记":"","检查描述":"","检测方法":"","结果状态":"正常","项目名称":"血清淀粉样蛋白A","样本采集时间":"2025-05-22 09:48"}]}],"imageId":"6877602a9c1fab3a9db06208","ocrResultId":"6877603f9c1fab3a9db0620d","检查名称":"全血细胞分析+CRP+SAA(6岁以下)","检查时间":"2025-05-22T01:48:00.000Z","processingTimeMs":20818}],"diagnosis":"","treatment_recommendation":[],"doctor_supplementary_info":[]}}(PGobject), O26SFFu_HjoFpxRXJgQ95(String)
2025-07-16 16:18:12.207 [ai-doctor-prod-dev,,] [http-nio-9999-exec-4] DEBUG c.m.a.m.C.updateInteractionHistoryById - <==    Updates: 1
2025-07-16 16:18:12.207 [ai-doctor-prod-dev,,] [http-nio-9999-exec-4] INFO  c.m.a.service.MessageService - [PROD Add TestRecommendation] Successfully updated database for case ID: O26SFFu_HjoFpxRXJgQ95
2025-07-16 16:18:12.208 [ai-doctor-prod-dev,,] [http-nio-9999-exec-4] INFO  c.m.a.controller.MessageController - [PROD Controller] Successfully added test recommendation for case ID: O26SFFu_HjoFpxRXJgQ95
2025-07-16 16:18:13.279 [ai-doctor-prod-dev,,] [http-nio-9999-exec-6] INFO  c.m.a.controller.MessageController - [PROD Controller] Received /inquiry request for case ID: O26SFFu_HjoFpxRXJgQ95, message: 患者姓名: 男，性别: 男，年龄: 6岁0个月, flag: 3
2025-07-16 16:18:13.287 [ai-doctor-prod-dev,,] [inquiry-handler-214] INFO  c.m.a.service.MessageService - [PROD Welcome] Starting default welcome message for case ID: O26SFFu_HjoFpxRXJgQ95
2025-07-16 16:18:13.287 [ai-doctor-prod-dev,,] [inquiry-handler-214] DEBUG c.m.a.mapper.CaseItemMapper.findById - ==>  Preparing: select id, created_at, updated_at, profile, interaction_history from "case_item" where id = ?
2025-07-16 16:18:13.287 [ai-doctor-prod-dev,,] [inquiry-handler-214] DEBUG c.m.a.mapper.CaseItemMapper.findById - ==> Parameters: O26SFFu_HjoFpxRXJgQ95(String)
2025-07-16 16:18:13.288 [ai-doctor-prod-dev,,] [inquiry-handler-214] DEBUG c.m.a.mapper.CaseItemMapper.findById - <==      Total: 1
2025-07-16 16:18:13.943 [ai-doctor-prod-dev,,] [inquiry-handler-214] INFO  c.m.a.service.MessageService - [PROD Welcome] Generated TTS URL for case ID O26SFFu_HjoFpxRXJgQ95: /audio/7bc60fe7-8e25-404d-80e3-c7d32abf4492.mp3
2025-07-16 16:18:13.943 [ai-doctor-prod-dev,,] [inquiry-handler-214] INFO  c.m.a.service.MessageService - [PROD Welcome] Sent message event via SSE for case ID O26SFFu_HjoFpxRXJgQ95
2025-07-16 16:18:13.943 [ai-doctor-prod-dev,,] [inquiry-handler-214] INFO  c.m.a.service.MessageService - [PROD Welcome] Sent [DONE] event
2025-07-16 16:18:13.944 [ai-doctor-prod-dev,,] [inquiry-handler-214] INFO  c.m.a.service.MessageService - [PROD Welcome] Completed request for case ID O26SFFu_HjoFpxRXJgQ95
2025-07-16 16:18:23.109 [ai-doctor-prod-dev,,] [http-nio-9999-exec-7] INFO  c.m.a.controller.MessageController - [PROD Controller] Received /inquiry request for case ID: O26SFFu_HjoFpxRXJgQ95, message: 患者姓名: 男，性别: 男，年龄: 6岁0个月, flag: 1
2025-07-16 16:18:23.117 [ai-doctor-prod-dev,,] [inquiry-handler-215] INFO  c.m.a.service.MessageService - [PROD Initial] Starting initialization for case ID: O26SFFu_HjoFpxRXJgQ95, flag: 1
2025-07-16 16:18:23.117 [ai-doctor-prod-dev,,] [inquiry-handler-215] DEBUG c.m.a.mapper.CaseItemMapper.findById - ==>  Preparing: select id, created_at, updated_at, profile, interaction_history from "case_item" where id = ?
2025-07-16 16:18:23.117 [ai-doctor-prod-dev,,] [inquiry-handler-215] DEBUG c.m.a.mapper.CaseItemMapper.findById - ==> Parameters: O26SFFu_HjoFpxRXJgQ95(String)
2025-07-16 16:18:23.118 [ai-doctor-prod-dev,,] [inquiry-handler-215] DEBUG c.m.a.mapper.CaseItemMapper.findById - <==      Total: 1
2025-07-16 16:18:23.122 [ai-doctor-prod-dev,,] [inquiry-handler-215] DEBUG c.m.a.m.C.updateInteractionHistoryById - ==>  Preparing: update "case_item" set interaction_history = ? where id = ?
2025-07-16 16:18:23.128 [ai-doctor-prod-dev,,] [inquiry-handler-215] DEBUG c.m.a.m.C.updateInteractionHistoryById - ==> Parameters: {"interaction_history":{"cot_entries":[{"dialogue_history":[{"content":"您好，我是本次的医生，请问孩子的年龄，性别，主要的症状是什么？","role":"doctor"},{"content":"患者姓名: 男，性别: 男，年龄: 6岁0个月","role":"patient"},{"content":"孩子现在哪里不舒服呢？孩子或家长可以直接对着我回答，回答完毕请点击下面的\"说完了\"","role":"doctor"}],"feedback":"","observation":"","reasoning":"主诉是问诊的切入点，通过了解患者的基本信息和主要症状，可以快速建立初步印象。进一步询问发病时间、起病情况、症状特点、伴随症状、病情演变和诊疗经过，有助于全面了解现病史，为后续诊断提供关键信息。这种系统性的问诊策略能够帮助我们准确把握患者的病情，避免遗漏重要信息。","strategy":"询问患者的基本信息（性别、年龄）和主诉症状，了解发病时间、起病情况、症状特点（性质、程度、持续时间、频率）、伴随症状、病情演变以及之前的诊疗经过。"}],"preliminary_diagnosis":null,"test_recommendation":[],"diagnosis":"","treatment_recommendation":[],"doctor_supplementary_info":[]}}(PGobject), O26SFFu_HjoFpxRXJgQ95(String)
2025-07-16 16:18:23.130 [ai-doctor-prod-dev,,] [inquiry-handler-215] DEBUG c.m.a.m.C.updateInteractionHistoryById - <==    Updates: 1
2025-07-16 16:18:23.130 [ai-doctor-prod-dev,,] [inquiry-handler-215] INFO  c.m.a.service.MessageService - [PROD Initial] Database updated successfully for case ID O26SFFu_HjoFpxRXJgQ95
2025-07-16 16:18:23.682 [ai-doctor-prod-dev,,] [inquiry-handler-215] INFO  c.m.a.service.MessageService - [PROD Initial] Generated TTS URL for case ID O26SFFu_HjoFpxRXJgQ95: /audio/a9e70f85-b6b9-4180-a44a-f23811fc8a59.mp3
2025-07-16 16:18:23.682 [ai-doctor-prod-dev,,] [inquiry-handler-215] INFO  c.m.a.service.MessageService - [PROD Initial] Sent message event via SSE for case ID O26SFFu_HjoFpxRXJgQ95
2025-07-16 16:18:23.682 [ai-doctor-prod-dev,,] [inquiry-handler-215] INFO  c.m.a.service.MessageService - [PROD Initial] 发送DONE事件
2025-07-16 16:18:23.683 [ai-doctor-prod-dev,,] [inquiry-handler-215] INFO  c.m.a.service.MessageService - [PROD Initial] Completed initialization for case ID O26SFFu_HjoFpxRXJgQ95
2025-07-16 16:18:40.903 [ai-doctor-prod-dev,,] [http-nio-9999-exec-8] INFO  c.m.a.controller.MessageController - [PROD Controller] Received /inquiry request for case ID: O26SFFu_HjoFpxRXJgQ95, message: 恶心。, flag: 0
2025-07-16 16:18:40.912 [ai-doctor-prod-dev,,] [inquiry-handler-216] INFO  c.m.a.service.MessageService - [PROD Inquiry] Starting inquiry processing for case ID: O26SFFu_HjoFpxRXJgQ95
2025-07-16 16:18:40.912 [ai-doctor-prod-dev,,] [inquiry-handler-216] DEBUG c.m.a.mapper.CaseItemMapper.findById - ==>  Preparing: select id, created_at, updated_at, profile, interaction_history from "case_item" where id = ?
2025-07-16 16:18:40.912 [ai-doctor-prod-dev,,] [inquiry-handler-216] DEBUG c.m.a.mapper.CaseItemMapper.findById - ==> Parameters: O26SFFu_HjoFpxRXJgQ95(String)
2025-07-16 16:18:40.914 [ai-doctor-prod-dev,,] [inquiry-handler-216] DEBUG c.m.a.mapper.CaseItemMapper.findById - <==      Total: 1
2025-07-16 16:18:40.914 [ai-doctor-prod-dev,,] [inquiry-handler-216] INFO  c.m.a.service.MessageService - [PROD Inquiry] Extracted inner interaction history map from nested DB object for case ID: O26SFFu_HjoFpxRXJgQ95
2025-07-16 16:18:40.914 [ai-doctor-prod-dev,,] [inquiry-handler-216] INFO  c.m.a.service.MessageService - [PROD Inquiry] Sending request to Python service URL: http://localhost:5555/ai_doctor_v2_inquiry for case ID: O26SFFu_HjoFpxRXJgQ95
2025-07-16 16:18:45.667 [ai-doctor-prod-dev,,] [inquiry-handler-216] INFO  c.m.a.service.MessageService - [PROD Inquiry] Received OK response from Python for case ID O26SFFu_HjoFpxRXJgQ95
2025-07-16 16:18:45.667 [ai-doctor-prod-dev,,] [inquiry-handler-216] DEBUG c.m.a.m.C.updateInteractionHistoryById - ==>  Preparing: update "case_item" set interaction_history = ? where id = ?
2025-07-16 16:18:45.668 [ai-doctor-prod-dev,,] [inquiry-handler-216] DEBUG c.m.a.m.C.updateInteractionHistoryById - ==> Parameters: {"interaction_history":{"cot_entries":[{"dialogue_history":[{"content":"您好，我是本次的医生，请问孩子的年龄，性别，主要的症状是什么？","role":"doctor"},{"content":"患者姓名: 男，性别: 男，年龄: 6岁0个月","role":"patient"},{"content":"孩子现在哪里不舒服呢？孩子或家长可以直接对着我回答，回答完毕请点击下面的\"说完了\"","role":"doctor"},{"content":"恶心。","role":"patient"},{"content":"孩子是从什么时候开始出现恶心的？","role":"doctor"}],"feedback":"","observation":"","reasoning":"主诉是问诊的切入点，通过了解患者的基本信息和主要症状，可以快速建立初步印象。进一步询问发病时间、起病情况、症状特点、伴随症状、病情演变和诊疗经过，有助于全面了解现病史，为后续诊断提供关键信息。这种系统性的问诊策略能够帮助我们准确把握患者的病情，避免遗漏重要信息。","strategy":"询问患者的基本信息（性别、年龄）和主诉症状，了解发病时间、起病情况、症状特点（性质、程度、持续时间、频率）、伴随症状、病情演变以及之前的诊疗经过。"}],"diagnosis":"","doctor_supplementary_info":[],"lastObservation":"","preliminary_diagnosis":null,"test_recommendation":[],"treatment_recommendation":[]}}(PGobject), O26SFFu_HjoFpxRXJgQ95(String)
2025-07-16 16:18:45.669 [ai-doctor-prod-dev,,] [inquiry-handler-216] DEBUG c.m.a.m.C.updateInteractionHistoryById - <==    Updates: 1
2025-07-16 16:18:45.669 [ai-doctor-prod-dev,,] [inquiry-handler-216] INFO  c.m.a.service.MessageService - [PROD Inquiry] Database updated successfully with Python response for case ID O26SFFu_HjoFpxRXJgQ95
2025-07-16 16:18:45.669 [ai-doctor-prod-dev,,] [inquiry-handler-216] INFO  c.m.a.service.MessageService - [PROD Inquiry] Determined target based on strategy for case ID O26SFFu_HjoFpxRXJgQ95: 1
2025-07-16 16:18:46.097 [ai-doctor-prod-dev,,] [inquiry-handler-216] INFO  c.m.a.service.MessageService - [PROD Inquiry] Generated TTS URL for case ID O26SFFu_HjoFpxRXJgQ95: /audio/3814d110-732e-4f0c-a9df-59895f124054.mp3
2025-07-16 16:18:46.097 [ai-doctor-prod-dev,,] [inquiry-handler-216] INFO  c.m.a.service.MessageService - [PROD Inquiry] Sent message event via SSE for case ID O26SFFu_HjoFpxRXJgQ95
2025-07-16 16:18:46.097 [ai-doctor-prod-dev,,] [inquiry-handler-216] INFO  c.m.a.service.MessageService - [PROD SSE] 发送DONE事件
2025-07-16 16:18:46.098 [ai-doctor-prod-dev,,] [inquiry-handler-216] INFO  c.m.a.service.MessageService - [PROD Inquiry] Sent [DONE] and completed emitter for case ID O26SFFu_HjoFpxRXJgQ95
2025-07-16 16:19:09.250 [ai-doctor-prod-dev,,] [scheduling-1] INFO  c.m.a.c.ConnectionMonitorConfig$ConnectionHealthIndicator - Connection Status - ESTABLISHED: 58, CLOSE_WAIT: 41, TIME_WAIT: 512
2025-07-16 16:19:09.250 [ai-doctor-prod-dev,,] [scheduling-1] WARN  c.m.a.c.ConnectionMonitorConfig$ConnectionHealthIndicator - High number of CLOSE_WAIT connections detected: 41. This may indicate connection leaks.
2025-07-16 16:19:28.645 [ai-doctor-prod-dev,,] [http-nio-9999-exec-12] INFO  c.m.a.controller.MessageController - [PROD Controller] Received /inquiry request for case ID: O26SFFu_HjoFpxRXJgQ95, message: 嗯，最近这两个周吧。, flag: 0
2025-07-16 16:19:28.667 [ai-doctor-prod-dev,,] [inquiry-handler-217] INFO  c.m.a.service.MessageService - [PROD Inquiry] Starting inquiry processing for case ID: O26SFFu_HjoFpxRXJgQ95
2025-07-16 16:19:28.667 [ai-doctor-prod-dev,,] [inquiry-handler-217] DEBUG c.m.a.mapper.CaseItemMapper.findById - ==>  Preparing: select id, created_at, updated_at, profile, interaction_history from "case_item" where id = ?
2025-07-16 16:19:28.667 [ai-doctor-prod-dev,,] [inquiry-handler-217] DEBUG c.m.a.mapper.CaseItemMapper.findById - ==> Parameters: O26SFFu_HjoFpxRXJgQ95(String)
2025-07-16 16:19:28.669 [ai-doctor-prod-dev,,] [inquiry-handler-217] DEBUG c.m.a.mapper.CaseItemMapper.findById - <==      Total: 1
2025-07-16 16:19:28.669 [ai-doctor-prod-dev,,] [inquiry-handler-217] INFO  c.m.a.service.MessageService - [PROD Inquiry] Extracted inner interaction history map from nested DB object for case ID: O26SFFu_HjoFpxRXJgQ95
2025-07-16 16:19:28.669 [ai-doctor-prod-dev,,] [inquiry-handler-217] INFO  c.m.a.service.MessageService - [PROD Inquiry] Sending request to Python service URL: http://localhost:5555/ai_doctor_v2_inquiry for case ID: O26SFFu_HjoFpxRXJgQ95
2025-07-16 16:19:32.609 [ai-doctor-prod-dev,,] [inquiry-handler-217] INFO  c.m.a.service.MessageService - [PROD Inquiry] Received OK response from Python for case ID O26SFFu_HjoFpxRXJgQ95
2025-07-16 16:19:32.609 [ai-doctor-prod-dev,,] [inquiry-handler-217] DEBUG c.m.a.m.C.updateInteractionHistoryById - ==>  Preparing: update "case_item" set interaction_history = ? where id = ?
2025-07-16 16:19:32.609 [ai-doctor-prod-dev,,] [inquiry-handler-217] DEBUG c.m.a.m.C.updateInteractionHistoryById - ==> Parameters: {"interaction_history":{"cot_entries":[{"dialogue_history":[{"content":"您好，我是本次的医生，请问孩子的年龄，性别，主要的症状是什么？","role":"doctor"},{"content":"患者姓名: 男，性别: 男，年龄: 6岁0个月","role":"patient"},{"content":"孩子现在哪里不舒服呢？孩子或家长可以直接对着我回答，回答完毕请点击下面的\"说完了\"","role":"doctor"},{"content":"恶心。","role":"patient"},{"content":"孩子是从什么时候开始出现恶心的？","role":"doctor"},{"content":"嗯，最近这两个周吧。","role":"patient"},{"content":"孩子出现恶心之前有没有什么特别的情况，比如吃了不干净的食物、受凉或者情绪波动？","role":"doctor"}],"feedback":"","observation":"","reasoning":"主诉是问诊的切入点，通过了解患者的基本信息和主要症状，可以快速建立初步印象。进一步询问发病时间、起病情况、症状特点、伴随症状、病情演变和诊疗经过，有助于全面了解现病史，为后续诊断提供关键信息。这种系统性的问诊策略能够帮助我们准确把握患者的病情，避免遗漏重要信息。","strategy":"询问患者的基本信息（性别、年龄）和主诉症状，了解发病时间、起病情况、症状特点（性质、程度、持续时间、频率）、伴随症状、病情演变以及之前的诊疗经过。"}],"diagnosis":"","doctor_supplementary_info":[],"lastObservation":"","preliminary_diagnosis":null,"test_recommendation":[],"treatment_recommendation":[]}}(PGobject), O26SFFu_HjoFpxRXJgQ95(String)
2025-07-16 16:19:32.610 [ai-doctor-prod-dev,,] [inquiry-handler-217] DEBUG c.m.a.m.C.updateInteractionHistoryById - <==    Updates: 1
2025-07-16 16:19:32.611 [ai-doctor-prod-dev,,] [inquiry-handler-217] INFO  c.m.a.service.MessageService - [PROD Inquiry] Database updated successfully with Python response for case ID O26SFFu_HjoFpxRXJgQ95
2025-07-16 16:19:32.611 [ai-doctor-prod-dev,,] [inquiry-handler-217] INFO  c.m.a.service.MessageService - [PROD Inquiry] Determined target based on strategy for case ID O26SFFu_HjoFpxRXJgQ95: 1
2025-07-16 16:19:33.180 [ai-doctor-prod-dev,,] [inquiry-handler-217] INFO  c.m.a.service.MessageService - [PROD Inquiry] Generated TTS URL for case ID O26SFFu_HjoFpxRXJgQ95: /audio/66309de7-9f7d-46b6-bf50-6e552e7934fb.mp3
2025-07-16 16:19:33.180 [ai-doctor-prod-dev,,] [inquiry-handler-217] INFO  c.m.a.service.MessageService - [PROD Inquiry] Sent message event via SSE for case ID O26SFFu_HjoFpxRXJgQ95
2025-07-16 16:19:33.181 [ai-doctor-prod-dev,,] [inquiry-handler-217] INFO  c.m.a.service.MessageService - [PROD SSE] 发送DONE事件
2025-07-16 16:19:33.181 [ai-doctor-prod-dev,,] [inquiry-handler-217] INFO  c.m.a.service.MessageService - [PROD Inquiry] Sent [DONE] and completed emitter for case ID O26SFFu_HjoFpxRXJgQ95
2025-07-16 16:19:44.854 [ai-doctor-prod-dev,,] [http-nio-9999-exec-9] INFO  c.m.a.controller.MessageController - [PROD Controller] Received /inquiry request for case ID: O26SFFu_HjoFpxRXJgQ95, message: 没有。, flag: 0
2025-07-16 16:19:44.869 [ai-doctor-prod-dev,,] [inquiry-handler-218] INFO  c.m.a.service.MessageService - [PROD Inquiry] Starting inquiry processing for case ID: O26SFFu_HjoFp