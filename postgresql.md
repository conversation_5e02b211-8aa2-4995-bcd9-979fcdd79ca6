-- Enable the ltree extension if it doesn't exist
CREATE EXTENSION IF NOT EXISTS ltree;

-- Sequences
CREATE SEQUENCE public.captcha__id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.captcha__id_seq OWNER TO postgres;

CREATE SEQUENCE public.error_validation__id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.error_validation__id_seq OWNER TO postgres;

CREATE SEQUENCE public.log__id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.log__id_seq OWNER TO postgres;

CREATE SEQUENCE public.validate_code__id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.validate_code__id_seq OWNER TO postgres;

CREATE TABLE public.access_feature (
    id character varying(32) NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    deleted boolean DEFAULT false,
    valid boolean DEFAULT true,
    profile jsonb DEFAULT '{}'::jsonb,
    path public.ltree,
    index integer,
    logs jsonb DEFAULT '{}'::jsonb,
    deleted_at character varying(32) DEFAULT ''::text
);

ALTER TABLE public.access_feature OWNER TO postgres;

## 表结构详细介绍

以下是数据库中定义的 16 张表的详细介绍：

### 1. `public.access_feature`
*   **用途**: 定义系统中的功能模块或权限点，用于访问控制。
*   **列**:
    *   `id` (varchar(32), 非空): 唯一标识符。
    *   `created_at` (timestamptz, 默认当前时间): 创建时间。
    *   `deleted` (boolean, 默认 false): 软删除标记。
    *   `valid` (boolean, 默认 true): 是否有效标记。
    *   `profile` (jsonb, 默认 '{}'): 存储功能的详细信息（如名称、API 定义等）。
    *   `path` (ltree): 功能在层级结构中的路径（需要 `ltree` 扩展）。
    *   `index` (integer): 排序索引。
    *   `logs` (jsonb, 默认 '{}'): 操作日志。
    *   `deleted_at` (varchar(32), 默认 ''): 软删除时间戳或标记。

```sql
CREATE TABLE public.access_role (
    id character varying(32) NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    deleted boolean DEFAULT false,
    profile jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE public.access_role OWNER TO postgres;
```

### 2. `public.access_role`
*   **用途**: 存储访问控制的角色信息。
*   **列**:
    *   `id` (varchar(32), 非空): 角色的唯一标识符。
    *   `created_at` (timestamptz, 默认当前时间): 创建时间。
    *   `deleted` (boolean, 默认 false): 软删除标记。
    *   `profile` (jsonb, 默认 '{}'): 存储角色的详细配置信息（如角色名称、权限列表等）。

```sql
CREATE TABLE public.case_item (
    id character varying(32) NOT NULL,
    created_at timestamp(6) with time zone DEFAULT now(),
    updated_at timestamp(6) with time zone DEFAULT now(),
    profile jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE public.case_item OWNER TO postgres;
```

### 3. `public.case_item`
*   **用途**: 存储案例或工单项目的信息。
*   **列**:
    *   `id` (varchar(32), 非空): 案例项的唯一标识符。
    *   `created_at` (timestamp(6)tz, 默认当前时间): 创建时间。
    *   `updated_at` (timestamp(6)tz, 默认当前时间): 最后更新时间。
    *   `profile` (jsonb, 默认 '{}'): 存储案例项的详细信息。

```sql
CREATE TABLE public.case_message (
    id character varying(32) NOT NULL,
    created_at timestamp(6) with time zone DEFAULT now(),
    updated_at timestamp(6) with time zone DEFAULT now(),
    profile jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE public.case_message OWNER TO postgres;
```

### 4. `public.case_message`
*   **用途**: 存储与案例或工单相关的消息记录。
*   **列**:
    *   `id` (varchar(32), 非空): 消息的唯一标识符。
    *   `created_at` (timestamp(6)tz, 默认当前时间): 创建时间。
    *   `updated_at` (timestamp(6)tz, 默认当前时间): 最后更新时间。
    *   `profile` (jsonb, 默认 '{}'): 存储消息的详细内容。

```sql
CREATE TABLE public.category (
    id character varying(32) NOT NULL,
    created_at timestamp(6) with time zone DEFAULT now(),
    deleted boolean DEFAULT false,
    deleted_at character varying(32) DEFAULT ''::text,
    profile jsonb DEFAULT '{}'::jsonb,
    path public.ltree,
    index integer DEFAULT 0,
    valid boolean DEFAULT true,
    logs jsonb DEFAULT '{}'::jsonb,
    type character varying(128),
    clasz public.ltree
);

ALTER TABLE public.category OWNER TO postgres;
```

### 5. `public.category`
*   **用途**: 存储类别信息，支持层级结构。
*   **列**:
    *   `id` (varchar(32), 非空): 类别的唯一标识符。
    *   `created_at` (timestamp(6)tz, 默认当前时间): 创建时间。
    *   `deleted` (boolean, 默认 false): 软删除标记。
    *   `deleted_at` (varchar(32), 默认 ''): 软删除时间戳或标记。
    *   `profile` (jsonb, 默认 '{}'): 存储类别的详细信息（如名称、描述等）。
    *   `path` (ltree): 类别在层级结构中的路径（需要 `ltree` 扩展）。
    *   `index` (integer, 默认 0): 排序索引。
    *   `valid` (boolean, 默认 true): 是否有效标记。
    *   `logs` (jsonb, 默认 '{}'): 操作日志。
    *   `type` (varchar(128)): 类别的类型。
    *   `clasz` (ltree): 可能与 `path` 类似，用于层级分类的另一种方式或特定分类体系。

```sql
CREATE TABLE public.label (
    id character varying(32) NOT NULL,
    created_at timestamp(6) with time zone DEFAULT now(),
    deleted boolean DEFAULT false,
    deleted_at character varying(32) DEFAULT ''::text,
    valid boolean DEFAULT true,
    profile jsonb DEFAULT '{}'::jsonb,
    category_id character varying(32),
    logs jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE public.label OWNER TO postgres;
```

### 6. `public.label`
*   **用途**: 存储标签信息，可关联到类别。
*   **列**:
    *   `id` (varchar(32), 非空): 标签的唯一标识符。
    *   `created_at` (timestamp(6)tz, 默认当前时间): 创建时间。
    *   `deleted` (boolean, 默认 false): 软删除标记。
    *   `deleted_at` (varchar(32), 默认 ''): 软删除时间戳或标记。
    *   `valid` (boolean, 默认 true): 是否有效标记。
    *   `profile` (jsonb, 默认 '{}'): 存储标签的详细信息（如名称、颜色等）。
    *   `category_id` (varchar(32)): 关联的类别 ID。
    *   `logs` (jsonb, 默认 '{}'): 操作日志。

```sql
CREATE TABLE public.member (
    id character varying(32) NOT NULL,
    created_at timestamp(6) with time zone DEFAULT now(),
    updated_at timestamp(6) with time zone DEFAULT now(),
    profile jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE public.member OWNER TO postgres;
```

### 7. `public.member`
*   **用途**: 存储成员或团队成员的信息。
*   **列**:
    *   `id` (varchar(32), 非空): 成员的唯一标识符。
    *   `created_at` (timestamp(6)tz, 默认当前时间): 创建时间。
    *   `updated_at` (timestamp(6)tz, 默认当前时间): 最后更新时间。
    *   `profile` (jsonb, 默认 '{}'): 存储成员的详细信息。

```sql
CREATE TABLE public.no_trouble_log_item (
    id character varying(32) NOT NULL,
    created_at timestamp(6) with time zone DEFAULT now(),
    updated_at timestamp(6) with time zone DEFAULT now(),
    profile jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE public.no_trouble_log_item OWNER TO postgres;
```

### 8. `public.no_trouble_log_item`
*   **用途**: 存储"无故障"相关的日志项。
*   **列**:
    *   `id` (varchar(32), 非空): 日志项的唯一标识符。
    *   `created_at` (timestamp(6)tz, 默认当前时间): 创建时间。
    *   `updated_at` (timestamp(6)tz, 默认当前时间): 最后更新时间。
    *   `profile` (jsonb, 默认 '{}'): 存储日志项的详细信息。

```sql
CREATE TABLE public.no_trouble_trash_item (
    id character varying(32) NOT NULL,
    created_at timestamp(6) with time zone DEFAULT now(),
    updated_at timestamp(6) with time zone DEFAULT now(),
    profile jsonb DEFAULT '{}'::jsonb,
    logs jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE public.no_trouble_trash_item OWNER TO postgres;
```

### 9. `public.no_trouble_trash_item`
*   **用途**: 存储"无故障"回收站中的项目。
*   **列**:
    *   `id` (varchar(32), 非空): 回收站项的唯一标识符。
    *   `created_at` (timestamp(6)tz, 默认当前时间): 创建时间。
    *   `updated_at` (timestamp(6)tz, 默认当前时间): 最后更新时间。
    *   `profile` (jsonb, 默认 '{}'): 存储回收站项的详细信息。
    *   `logs` (jsonb, 默认 '{}'): 操作日志。

```sql
CREATE TABLE public.oss_object (
    id character varying(32) NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    deleted boolean DEFAULT false,
    profile jsonb DEFAULT '{}'::jsonb,
    hash character varying(256)
);

ALTER TABLE public.oss_object OWNER TO postgres;
```

### 10. `public.oss_object`
*   **用途**: 存储对象存储服务（OSS）中的对象元数据。
*   **列**:
    *   `id` (varchar(32), 非空): 对象的唯一标识符。
    *   `created_at` (timestamptz, 默认当前时间): 创建时间。
    *   `deleted` (boolean, 默认 false): 软删除标记。
    *   `profile` (jsonb, 默认 '{}'): 存储对象的详细信息（如文件名、大小、类型等）。
    *   `hash` (varchar(256)): 对象内容的哈希值，用于校验或去重。

```sql
CREATE TABLE public.settings (
    id character varying(64) NOT NULL,
    created_at timestamp(6) with time zone DEFAULT now(),
    private boolean DEFAULT false,
    deleted boolean DEFAULT false,
    profile jsonb DEFAULT '{}'::json,
    content text,
    deleted_at character varying(32) DEFAULT ''::text,
    logs jsonb DEFAULT '{}'::jsonb,
    category_id character varying(32)
);

ALTER TABLE public.settings OWNER TO postgres;
```

### 11. `public.settings`
*   **用途**: 存储系统或用户配置项。
*   **列**:
    *   `id` (varchar(64), 非空): 配置项的唯一标识符（键名）。
    *   `created_at` (timestamp(6)tz, 默认当前时间): 创建时间。
    *   `private` (boolean, 默认 false): 是否为私有配置。
    *   `deleted` (boolean, 默认 false): 软删除标记。
    *   `profile` (jsonb, 默认 '{}'): 存储配置项的元数据或复杂结构。
    *   `content` (text): 存储配置项的值（通常为文本或 JSON 字符串）。
    *   `deleted_at` (varchar(32), 默认 ''): 软删除时间戳或标记。
    *   `logs` (jsonb, 默认 '{}'): 操作日志。
    *   `category_id` (varchar(32)): 关联的配置类别 ID。

```sql
CREATE TABLE public.user_account (
    id character varying(32) NOT NULL,
    created_at timestamp(6) with time zone DEFAULT now() NOT NULL,
    updated_at timestamp(6) with time zone DEFAULT now() NOT NULL,
    nickname character varying(255),
    phone character varying(255),
    avatar text,
    profile jsonb DEFAULT '{}'::json,
    deleted boolean DEFAULT false,
    email text,
    valid boolean DEFAULT true NOT NULL,
    device jsonb DEFAULT '{}'::jsonb,
    role jsonb DEFAULT '{}'::jsonb,
    real_data jsonb DEFAULT '{}'::jsonb,
    pwd jsonb DEFAULT '{}'::jsonb,
    "from" jsonb,
    support_ability jsonb DEFAULT '{}'::jsonb,
    deleted_at character varying(32) DEFAULT ''::text NOT NULL,
    phone_country_calling_code character varying(10) DEFAULT '86'::character varying,
    logs jsonb DEFAULT '{}'::jsonb,
    CONSTRAINT user_email_check CHECK ((email ~ '^\w+@[-\w]+\.\w+$'::text)),
    CONSTRAINT user_phone_check CHECK (((phone)::text ~ '^[0-9-]{7,}$'::text))
);

ALTER TABLE public.user_account OWNER TO postgres;

COMMENT ON COLUMN public.user_account.support_ability IS '权限范围';
```

### 12. `public.user_account`
*   **用途**: 存储用户账户信息。
*   **列**:
    *   `id` (varchar(32), 非空): 用户的唯一标识符。
    *   `created_at` (timestamp(6)tz, 非空, 默认当前时间): 创建时间。
    *   `updated_at` (timestamp(6)tz, 非空, 默认当前时间): 最后更新时间。
    *   `nickname` (varchar(255)): 用户昵称。
    *   `phone` (varchar(255)): 用户电话号码 (约束: `^[0-9-]{7,}$`)。
    *   `avatar` (text): 用户头像 URL 或 Base64 数据。
    *   `profile` (jsonb, 默认 '{}'): 存储用户的扩展信息。
    *   `deleted` (boolean, 默认 false): 软删除标记。
    *   `email` (text): 用户邮箱 (约束: `^\w+@[-\w]+\.\w+$`)。
    *   `valid` (boolean, 非空, 默认 true): 账户是否有效。
    *   `device` (jsonb, 默认 '{}'): 用户设备信息。
    *   `role` (jsonb, 默认 '{}'): 用户角色信息。
    *   `real_data` (jsonb, 默认 '{}'): 用户实名认证信息。
    *   `pwd` (jsonb, 默认 '{}'): 存储密码相关信息（通常是加密后的密码和盐）。
    *   `from` (jsonb): 用户来源信息。
    *   `support_ability` (jsonb, 默认 '{}'): 权限范围（根据注释）。
    *   `deleted_at` (varchar(32), 非空, 默认 ''): 软删除时间戳或标记。
    *   `phone_country_calling_code` (varchar(10), 默认 '86'): 电话国家码。
    *   `logs` (jsonb, 默认 '{}'): 操作日志。

```sql
CREATE TABLE public.user_captcha (
    _id integer DEFAULT nextval('public.captcha__id_seq'::regclass) NOT NULL,
    created_at timestamp(6) with time zone DEFAULT now(),
    expired_at timestamp(6) with time zone,
    code text,
    checked boolean DEFAULT false,
    checked_at timestamp(6) with time zone
);

ALTER TABLE public.user_captcha OWNER TO postgres;
```

### 13. `public.user_captcha`
*   **用途**: 存储用户验证码信息（如图形验证码）。
*   **列**:
    *   `_id` (integer, 非空, 默认自增): 内部 ID。
    *   `created_at` (timestamp(6)tz, 默认当前时间): 创建时间。
    *   `expired_at` (timestamp(6)tz): 过期时间。
    *   `code` (text): 验证码内容。
    *   `checked` (boolean, 默认 false): 是否已验证。
    *   `checked_at` (timestamp(6)tz): 验证时间。

```sql
CREATE TABLE public.user_error_validation (
    _id integer DEFAULT nextval('public.error_validation__id_seq'::regclass) NOT NULL,
    created_at timestamp(6) with time zone DEFAULT now(),
    ip character varying(256),
    "user" integer,
    api text,
    data text,
    deleted boolean DEFAULT false
);

ALTER TABLE public.user_error_validation OWNER TO postgres;
```

### 14. `public.user_error_validation`
*   **用途**: 记录用户操作中出现的错误或验证失败信息。
*   **列**:
    *   `_id` (integer, 非空, 默认自增): 内部 ID。
    *   `created_at` (timestamp(6)tz, 默认当前时间): 创建时间。
    *   `ip` (varchar(256)): 用户 IP 地址。
    *   `user` (integer): 关联的用户 ID (可能是 `user_account.id` 的整数形式或另一种用户标识)。
    *   `api` (text): 发生错误的 API 路径。
    *   `data` (text): 相关数据。
    *   `deleted` (boolean, 默认 false): 软删除标记。

```sql
CREATE TABLE public.user_message (
    id character varying(32) NOT NULL,
    created_at timestamp(6) with time zone DEFAULT now(),
    updated_at timestamp(6) with time zone DEFAULT now(),
    profile jsonb DEFAULT '{}'::jsonb
);

ALTER TABLE public.user_message OWNER TO postgres;
```

### 15. `public.user_message`
*   **用途**: 存储发给用户的消息。
*   **列**:
    *   `id` (varchar(32), 非空): 消息的唯一标识符。
    *   `created_at` (timestamp(6)tz, 默认当前时间): 创建时间。
    *   `updated_at` (timestamp(6)tz, 默认当前时间): 最后更新时间。
    *   `profile` (jsonb, 默认 '{}'): 存储消息的详细内容。

```sql
CREATE TABLE public.user_validate_code (
    _id integer DEFAULT nextval('public.validate_code__id_seq'::regclass) NOT NULL,
    dest character varying(128),
    code character varying(32),
    created_at timestamp(6) with time zone,
    api character varying(64),
    valid boolean DEFAULT true,
    checked_times integer DEFAULT 0
);

ALTER TABLE public.user_validate_code OWNER TO postgres;
```

### 16. `public.user_validate_code`
*   **用途**: 存储用于验证目的的代码（如短信验证码、邮箱验证码）。
*   **列**:
    *   `_id` (integer, 非空, 默认自增): 内部 ID。
    *   `dest` (varchar(128)): 验证码发送目标（如手机号或邮箱地址）。
    *   `code` (varchar(32)): 验证码。
    *   `created_at` (timestamp(6)tz): 创建时间。
    *   `api` (varchar(64)): 请求验证码的 API 或场景。
    *   `valid` (boolean, 默认 true): 验证码是否仍然有效（未过期且未被使用）。
    *   `checked_times` (integer, 默认 0): 尝试验证的次数。

---

## 示例数据插入语句

以下是从 `postgres.txt` 文件中提取的部分示例数据插入语句，展示了部分表的典型数据格式：

```sql
-- Data for Name: access_feature; Type: TABLE DATA; Schema: public; Owner: postgres
INSERT INTO public.access_feature VALUES ('498_I73gh6', '2020-07-20 14:06:47.459761+08', false, true, '{"api": [{"R": "* /support/system/settings/*"}], "name": "系统配置", "api_def": "# 接口备注\\n[[api]]\\nR = \\\"* /support/system/settings/*\\\""}', 'XN0BISn1lO.498_I73gh6', 0, '{"logs": [{"date": "2022-08-11T08:33:08.902Z", "action": "更新", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}]}', '');
INSERT INTO public.access_feature VALUES ('hpdgBleP2s', '2020-07-20 14:05:58.319744+08', false, true, '{"api": [{"R": "* /support/user/*"}], "name": "账号管理", "api_def": "# 接口备注\\n[[api]]\\nR = \\\"* /support/user/*\\\"\\n\\n"}', 'VLFNkpToUkaLbpqUisEJm.hpdgBleP2s', 0, '{"logs": [{"date": "2022-08-11T08:36:12.509Z", "action": "更新", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}]}', '');

-- Data for Name: access_role; Type: TABLE DATA; Schema: public; Owner: postgres
INSERT INTO public.access_role VALUES ('wRna4LjkmEaZWfyT908p1', '2024-10-12 09:35:22.422123+08', false, '{"name": "医生", "index": 2, "features": ["5hSJ9ZoFZi8JJRJ0oUHOz", "MiHRSrFkDhWEBfxldyA7N", "gDPV1rCfZvJAi_xNt1fw4", "2EVL9YxSiXvYX6ZQLYTub"], "category_id": "qaCMOpOb5uOp_oX9z1jlh"}');
INSERT INTO public.access_role VALUES ('vvtXrXb1PN', '2020-07-20 13:58:17.118997+08', false, '{"name": "超级管理员", "index": 0, "features": ["yGWay4PwcVfrQDrDYaU2r", "XN0BISn1lO", "VLFNkpToUkaLbpqUisEJm", "fL5pyZb1YbYiZzwD1TJIQ", "AWB1ddZONrsOYgVkPYXj1", "QyjncasOAea5CBgOxoeVC", "5hSJ9ZoFZi8JJRJ0oUHOz", "hpdgBleP2s", "rPjQLIWL3zefYQ6WaaFLw", "ltQCCM__ctmrCdoYUXu_D", "498_I73gh6", "MiHRSrFkDhWEBfxldyA7N", "R5v2v2_0G8", "wECZyZ2ucEAc10qJfxwbW", "gDPV1rCfZvJAi_xNt1fw4", "uVFBmLAXIQAD6_k2sMab1", "2EVL9YxSiXvYX6ZQLYTub", "O_KyGJymdJKcMLAM9xpgd", "hznDlepCSl4BIs5nUyouU", "kOEe9EilTLsJ5FnXLJK7y"], "category_id": "ullbfhn8IIcXUH9p_vYjX"}');

-- Data for Name: category; Type: TABLE DATA; Schema: public; Owner: postgres
INSERT INTO public.category VALUES ('cklK7Hv4jSYyt74VFziAC', '2022-08-18 10:24:50.268426+08', false, '', '{"name": "用户", "type": "user"}', 'cklK7Hv4jSYyt74VFziAC', 0, true, '{"logs": [{"date": "2022-08-18T02:24:50.153Z", "action": "添加", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}]}', 'label', NULL);
INSERT INTO public.category VALUES ('akfLbNhFD09P2TAyghnZk', '2025-03-06 17:19:17.682384+08', false, '', '{"name": "问诊会话"}', 'akfLbNhFD09P2TAyghnZk', 2, true, '{}', 'label', NULL);

-- Data for Name: label; Type: TABLE DATA; Schema: public; Owner: postgres
INSERT INTO public.label VALUES ('K8Jk_pmCe', '2022-07-14 15:03:00.243653+08', false, '', true, '{"name": "示例1", "intro": "", "remark": "", "background_color": "#D10069"}', 'cklK7Hv4jSYyt74VFziAC', '{"logs": [{"date": "2022-08-19T02:45:13.926Z", "action": "更新", "operator": {"id": "rJxGvsO1Ir", "nickname": "零号管理员"}}]}');
INSERT INTO public.label VALUES ('iZjwb81uWy193r2zDiwIj', '2025-04-24 10:00:49.928497+08', false, '', true, '{"name": "查体", "remark": "", "background_color": "#004E00"}', 'akfLbNhFD09P2TAyghnZk', '{}');

-- Data for Name: member; Type: TABLE DATA; Schema: public; Owner: postgres
INSERT INTO public.member VALUES ('lCD3j4dkMZeQffF4_c13o', '2025-04-21 16:22:45.448876+08', '2025-04-21 16:22:46.00335+08', '{"name": "兽医皇甫端", "image": {"file": "-hY8xB3TIejOoLCqa8UiO.jpeg", "name": "6f08d6b778684f2187bceaf45461f377.jpeg", "size": 33335, "type": "image/jpeg"}, "intro": "", "state": {"flow": {"code": "ready", "name": "发布"}}, "owner_id": "YICXLp0eGQ7TwsekyKm3-", "creater_id": "rJxGvsO1Ir"}');

-- Data for Name: case_item; Type: TABLE DATA; Schema: public; Owner: postgres
INSERT INTO public.case_item VALUES ('IHPg54mIIbd1EoBCVehiR', '2025-04-21 16:36:52.296032+08', '2025-04-24 14:13:18.628915+08', '{"state": {"flow": {"code": "inquiring", "name": "问诊中", "clasz": "text-dark"}}, "title": "新问诊", "remark": "hello", "诊断": "病毒性脑炎", "patient": {"name": "吴用", "phone": "***********", "idcard": "11100011000"}, "owner_id": "rJxGvsO1Ir", "creater_id": "rJxGvsO1Ir", "初步诊断": "病毒性脑炎", "患者信息": "【基本信息】男，1岁\n【主诉】发热、哭闹、喷射状呕吐1天\n【现病史】发热1天，体温最高达40°C，使用退烧药无效，持续哭闹，不进食、不睡觉，喷射状呕吐1天，每天3次，呕吐物呈红色，含咖啡渣样物质，伴有腹痛、腹泻、尿少、皮肤干燥等脱水表现。病后未予诊治。患儿自发病以来，精神差，食欲差，睡眠差。\n【既往史】既往体健\n【传染病接触史】无", "检查建议": "检查A\n检查B\n检查C", "治疗方案": "【药物治疗】\n药品A每日1次每次10ml\n药品B每日1次 每次10ml\n药品C每日1次每次10ml【无】，展示链接\n【生活建议】\n1.保持充足水分：确保孩子充分补充水分，可以尝试少量多次饮水，以防脱水。\n2.观察症状变化：密切观察孩子的症状变化，特别是体温、呕吐、腹痛等情况，如有加重应及时就医。\n3.适当休息：让孩子多休息，减少活动量，避免过度疲劳。"}');

-- Data for Name: case_message; Type: TABLE DATA; Schema: public; Owner: postgres
INSERT INTO public.case_message VALUES ('4fbvNd_lzrowxEibH5_V8', '2025-04-24 10:37:36.943135+08', '2025-04-24 10:40:10.575593+08', '{"from": "AI", "text": "您老哪里不舒服", "case_id": "IHPg54mIIbd1EoBCVehiR", "label_ids": ["I7nug0LLzcOBkbnubiR9P"], "creater_id": "rJxGvsO1Ir"}');
INSERT INTO public.case_message VALUES ('_JREnBcKcLymb0c7pJMiR', '2025-04-24 10:41:21.142815+08', '2025-04-24 10:41:21.236049+08', '{"from": "患者", "text": "胃疼", "case_id": "IHPg54mIIbd1EoBCVehiR", "label_ids": ["I7nug0LLzcOBkbnubiR9P"], "creater_id": "rJxGvsO1Ir"}');

-- Data for Name: settings; Type: TABLE DATA; Schema: public; Owner: postgres
INSERT INTO public.settings VALUES ('l4_kjEdKRH7y3mbsRL9w3', '2024-05-27 16:25:36.44417+08', false, false, '{"name": "文件服务", "type": "text/x-toml", "cache_ttl": 0}', '[upload]\nmax_filesize=******** # 50m', '', '{"logs": [{"date": "2024-05-27T08:25:36.446Z", "action": "添加", "operator": {"id": "bgPEw4MBHM", "nickname": "cpx"}}]}', 'TioCwI5G88');

-- Data for Name: user_account; Type: TABLE DATA; Schema: public; Owner: postgres
INSERT INTO public.user_account VALUES ('rJxGvsO1Ir', '2019-09-06 14:23:22.410296+08', '2019-09-06 14:23:22.410296+08', '零号管理员', '***********', '40BnqEroOpn1DwzF0cDDm.jpeg', '{"labels": []}', false, NULL, true, '{"work-assistant": {"name": "work-assistant", "platform": "android", "version_code": 4, "version_name": "1.0.0", "aliyun_device_id": ""}}', '{"support": true}', '{"name": "元始天尊", "car_series_id": "KebDMsTb1FZee79LM160R"}', '{"md5": "5de8d28c1cb91e427fe71d6005560d36", "random": "WSkNGklX7geae8EBuL6IZ"}', '{}', '{"items": ["vvtXrXb1PN"]}', '', '86', '{}');
INSERT INTO public.user_account VALUES ('YICXLp0eGQ7TwsekyKm3-', '2024-11-01 16:10:36.099633+08', '2024-11-01 16:10:36.099633+08', '未乘车没', '***********', 'f-eH-FUsOyuLdfoJ17LI5.jpg', '{}', false, NULL, true, '{"work-assistant": {"name": "work-assistant", "platform": "web", "version_code": 1000, "version_name": "1.1.0", "aliyun_device_id": ""}}', '{"support": true}', '{"name": "未乘车没"}', '{"md5": "f53e434024d0a08ea3b450b55217ff76", "random": "JAkd2esxFRDcBnrW7PvgH"}', '{"ip": "*************", "events": {}, "referer": "/"}', '{"items": ["AwS0BtcuC_LZOJ3C9La9o"]}', '', '86', '{}');

```

**注意:** 这只是数据的一小部分快照，完整的示例数据请参考 `postgres.txt` 文件。
