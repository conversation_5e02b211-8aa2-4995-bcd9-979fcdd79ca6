package com.makiyo.ai_doctor_prod.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class InteractionHistoryUtils {

    private static final Logger log = LoggerFactory.getLogger(InteractionHistoryUtils.class);
    // 复用ObjectMapper实例以提高性能
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 根据特定规则转换交互历史的JSON结构。
     * - 将所有'cot_entries'中的'dialogue_history'列表合并成一个单一列表，并保持原始对话顺序。
     * - 最终输出仅包含一个'cot_entry'。
     * - 这个'cot_entry'中的'feedback'、'observation'、'reasoning'和'strategy'字段均取自输入'cot_entries'中的最后一个条目。
     * - 确保输出的JSON中，单个cot_entry内的字段顺序为：dialogue_history, feedback, observation, reasoning, strategy。
     *
     * @param jsonInput 包含交互历史的原始JSON字符串。
     * @return 根据规则转换后的JSON字符串；如果转换失败或输入无效/格式错误，则返回null。
     */
    public static String transformInteractionHistory(String jsonInput) {
        if (jsonInput == null || jsonInput.trim().isEmpty()) {
            log.warn("用于转换的输入JSON字符串为null或为空。");
            return null;
        }

        try {
            Map<String, Object> rootMap = objectMapper.readValue(jsonInput, new TypeReference<Map<String, Object>>() {});

            if (!rootMap.containsKey("interaction_history")) {
                log.warn("转换: 输入JSON不包含'interaction_history'键。");
                return jsonInput;
            }
            Object historyObj = rootMap.get("interaction_history");
            if (!(historyObj instanceof Map)) {
                log.warn("转换: 'interaction_history'不是一个Map。");
                return jsonInput;
            }
            Map<String, Object> interactionHistory = objectMapper.convertValue(historyObj, new TypeReference<Map<String, Object>>() {});

            if (!interactionHistory.containsKey("cot_entries")) {
                log.warn("转换: 'interaction_history'不包含'cot_entries'键。");
                return jsonInput;
            }
            Object entriesObj = interactionHistory.get("cot_entries");
            if (!(entriesObj instanceof List)) {
                log.warn("转换: 'cot_entries'不是一个列表。");
                return jsonInput;
            }
            List<Map<String, Object>> cotEntries = objectMapper.convertValue(entriesObj, new TypeReference<List<Map<String, Object>>>() {});

            if (cotEntries.isEmpty()) {
                log.info("转换: 'cot_entries'列表为空。");
                return jsonInput;
            }

            List<Map<String, Object>> mergedDialogueHistory = new ArrayList<>();
            String lastStrategy = "";
            String lastFeedback = "";
            String lastObservation = "";
            String lastReasoning = "";

            for (Map<String, Object> entry : cotEntries) {
                if (entry != null && entry.containsKey("dialogue_history")) {
                    Object dialogueHistObj = entry.get("dialogue_history");
                    if (dialogueHistObj instanceof List) {
                        try {
                            // 此处使用convertValue进行合并，因为无论如何都需要副本
                            List<Map<String, Object>> currentDialogues = objectMapper.convertValue(dialogueHistObj, new TypeReference<List<Map<String, Object>>>() {});
                            mergedDialogueHistory.addAll(currentDialogues);
                        } catch (IllegalArgumentException e) {
                            log.warn("转换: 无法转换对话历史元素。", e);
                        }
                    } else {
                         log.warn("转换: 对话历史元素不是一个列表。");
                    }
                }
            }

            Map<String, Object> lastEntry = cotEntries.get(cotEntries.size() - 1);
            if (lastEntry != null) {
                lastFeedback = getStringValue(lastEntry, "feedback");
                lastObservation = getStringValue(lastEntry, "observation");
                lastReasoning = getStringValue(lastEntry, "reasoning");
                lastStrategy = getStringValue(lastEntry, "strategy");
            }

            Map<String, Object> targetEntry = new LinkedHashMap<>();
            targetEntry.put("dialogue_history", mergedDialogueHistory);
            targetEntry.put("feedback", lastFeedback);
            targetEntry.put("observation", lastObservation);
            targetEntry.put("reasoning", lastReasoning);
            targetEntry.put("strategy", lastStrategy);

            List<Map<String, Object>> targetCotEntries = new ArrayList<>();
            targetCotEntries.add(targetEntry);

            Map<String, Object> targetInteractionHistory = new LinkedHashMap<>();
            targetInteractionHistory.put("cot_entries", targetCotEntries);

            Map<String, Object> targetRootMap = new LinkedHashMap<>();
            targetRootMap.put("interaction_history", targetInteractionHistory);

            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(targetRootMap);

        } catch (IOException e) {
            log.error("转换交互历史JSON时出错 (IOException): {}", e.getMessage(), e);
            return null;
        } catch (IllegalArgumentException e) {
            log.error("转换过程中转换JSON结构时出错 (IllegalArgumentException): {}", e.getMessage(), e);
            return null;
        } catch (Exception e) {
            log.error("转换交互历史时发生意外错误: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 将一条新的患者消息添加到所提供交互历史JSON中最后一个cot_entry的对话历史里。
     * 此方法会直接修改传入的结构。
     *
     * @param jsonInput 代表交互历史的当前JSON字符串。
     * @param patientMessageContent 患者新消息的内容。
     * @return 添加了新消息的更新后JSON字符串；如果发生错误或输入无效/格式错误，则返回null。
     */
    public static String addPatientMessage(String jsonInput, String patientMessageContent) {
        if (jsonInput == null || jsonInput.trim().isEmpty()) {
            log.warn("用于添加消息的输入JSON字符串为null或为空。");
            return null;
        }
        if (patientMessageContent == null || patientMessageContent.trim().isEmpty()) {
            log.warn("患者消息内容为null或为空。返回原始JSON。");
            return jsonInput; // 如果消息为空，则返回原始JSON
        }

        try {
            // 解析为通用Map和List，以便直接修改
            Map<String, Object> rootMap = objectMapper.readValue(jsonInput, new TypeReference<Map<String, Object>>() {});

            // --- 结构导航与验证 ---
            if (!rootMap.containsKey("interaction_history") || !(rootMap.get("interaction_history") instanceof Map)) {
                log.error("添加消息: 无效或缺失'interaction_history' map。");
                return null;
            }
            @SuppressWarnings("unchecked") // instanceof检查保证了类型安全
            Map<String, Object> interactionHistory = (Map<String, Object>) rootMap.get("interaction_history");

            if (!interactionHistory.containsKey("cot_entries") || !(interactionHistory.get("cot_entries") instanceof List)) {
                log.error("添加消息: 无效或缺失'cot_entries'列表。");
                return null;
            }
            @SuppressWarnings("unchecked") // instanceof检查保证了类型安全
            List<Map<String, Object>> cotEntries = (List<Map<String, Object>>) interactionHistory.get("cot_entries");

            if (cotEntries.isEmpty()) {
                log.error("添加消息: 'cot_entries'列表为空。无法添加消息。");
                return null;
            }

            // 直接获取最后一个条目Map
            Map<String, Object> lastEntry = cotEntries.get(cotEntries.size() - 1);
            if (lastEntry == null) {
                 log.error("添加消息: 'cot_entries'中的最后一个条目为null。");
                 return null;
            }

            // 直接从lastEntry map中获取dialogue_history列表
            List<Map<String, Object>> dialogueHistory;
            Object currentDialogueHistObj = lastEntry.get("dialogue_history");

            if (currentDialogueHistObj instanceof List) {
                // 如果是列表，尝试直接转换
                 try {
                    @SuppressWarnings("unchecked") // instanceof检查保证了类型安全
                    List<Map<String, Object>> existingList = (List<Map<String, Object>>) currentDialogueHistObj;
                    // 可选：如果需要，添加对列表元素类型的检查
                    dialogueHistory = existingList; // 使用现有的列表引用
                    log.debug("添加消息: 直接使用已有的dialogue_history列表。");
                 } catch (ClassCastException e) {
                      log.warn("添加消息: 'dialogue_history'存在但不是List<Map<String, Object>>类型。正在创建新列表。", e);
                      dialogueHistory = new ArrayList<>();
                      lastEntry.put("dialogue_history", dialogueHistory); // 替换格式错误的列表
                 }
            } else {
                // 如果键不存在或值不是列表，则创建一个新的
                log.warn("添加消息: 最后一个条目不包含有效的'dialogue_history'列表。正在创建一个新的。");
                dialogueHistory = new ArrayList<>();
                lastEntry.put("dialogue_history", dialogueHistory); // 将新列表添加到map中
            }

            // 创建新的患者消息map
            Map<String, Object> newMessage = new LinkedHashMap<>(); // 使用LinkedHashMap以保持顺序
            newMessage.put("content", patientMessageContent);
            newMessage.put("role", "patient");

            // 将新消息添加到列表中（这将修改lastEntry内部的列表）
            dialogueHistory.add(newMessage);
            log.info("已将患者消息添加到最后一个cot_entry的dialogue_history列表中。");

            // 将修改后的rootMap序列化回JSON
            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(rootMap);

        } catch (IOException e) {
            log.error("添加患者消息时出错 (IOException): {}", e.getMessage(), e);
            return null;
        } catch (IndexOutOfBoundsException e) {
             log.error("访问cot_entries时出错 (IndexOutOfBoundsException): {}", e.getMessage(), e);
             return null;
        } catch (Exception e) { // 捕获更广泛的异常
            log.error("添加患者消息时发生意外错误: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 安全地从Map中根据键检索字符串值。
     * 如果Map为null、键为null、键不存在、值不存在或值不是字符串类型，则返回空字符串("")。
     *
     * @param map 从中检索值的Map。
     * @param key 要返回其关联值的键。
     * @return 与键关联的字符串值；如果未找到或不是字符串类型，则返回""。
     */
    private static String getStringValue(Map<String, Object> map, String key) {
        if (map == null || key == null) {
            return "";
        }
        Object value = map.get(key);
        if (value instanceof String) {
            return (String) value;
        }
        return "";
    }

    /**
     * 从给定的交互历史中，提取"cot_entries"列表中倒数第二个条目的"observation"内容。
     * 这对于检索最终AI交互之前的特定信息非常有用。
     *
     * @param interactionHistory 包含完整交互历史的Map。
     * @return 倒数第二个条目的"observation"字符串；如果结构无效、未找到或发生任何错误，则返回空字符串。
     */
    public static String extractSecondLastObservation(Map<String, Object> interactionHistory) {
        if (interactionHistory == null) {
            log.warn("提取倒数第二条观察记录: 输入的interactionHistory map为null。");
            return "";
        }

        try {
            if (!interactionHistory.containsKey("cot_entries") || !(interactionHistory.get("cot_entries") instanceof List)) {
                log.warn("提取倒数第二条观察记录: 无效或缺失'cot_entries'列表。");
                return "";
            }
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> cotEntries = (List<Map<String, Object>>) interactionHistory.get("cot_entries");

            if (cotEntries.size() < 2) {
                log.warn("提取倒数第二条观察记录: 'cot_entries'中的条目数不足（找到{}个），无法获取倒数第二个观察记录。", cotEntries.size());
                return "";
            }

            // 获取倒数第二个条目
            Map<String, Object> secondLastEntry = cotEntries.get(cotEntries.size() - 2);
            if (secondLastEntry == null) {
                log.warn("提取倒数第二条观察记录: 'cot_entries'中的倒数第二个条目为null。");
                return "";
            }

            // 使用辅助方法提取"observation"字段
            String observation = getStringValue(secondLastEntry, "observation");
            log.info("提取倒数第二条观察记录: 成功提取观察记录。"); // 先记录成功信息，使日志更清晰
            log.debug("提取到的观察记录值: {}", observation); // 在调试级别记录具体值
            return observation;

        } catch (IndexOutOfBoundsException e) {
             log.error("提取倒数第二条观察记录: 访问cot_entries时出错 (IndexOutOfBoundsException - 在有大小检查的情况下不应发生): {}", e.getMessage(), e);
             return "";
        } catch (Exception e) { // 捕获更广泛的异常
            log.error("提取倒数第二条观察记录: 发生意外错误: {}", e.getMessage(), e);
            return "";
        }
    }
} 