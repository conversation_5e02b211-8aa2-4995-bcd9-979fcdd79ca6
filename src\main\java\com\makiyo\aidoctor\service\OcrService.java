package com.makiyo.aidoctor.service;

import com.makiyo.aidoctor.entity.ImageMetadata;
import com.makiyo.aidoctor.entity.Message;
import com.makiyo.aidoctor.mapper.MessageMapper;
import com.makiyo.aidoctor.utils.OcrTextToJson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.RequestCallback;
import org.springframework.web.client.ResponseExtractor;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.core.io.ResourceLoader;

@Service
public class OcrService {

    private static final Logger log = LoggerFactory.getLogger(OcrService.class);

    private final RestTemplate restTemplate;

    @Resource
    private MessageMapper messageMapper;

    // 从 application.properties 注入 Python 服务 URL
    @Value("${ocr.python.service.url:http://localhost:5555/ocr_api}")
    private String pythonOcrServiceUrl;
    
    // 临时文件存储路径
    @Value("${ocr.temp.directory:./temp/ocr}")
    private String tempDirectory;
    
    // 重试配置
    @Value("${ocr.retry.max-attempts:3}")
    private int maxRetryAttempts;
    
    @Value("${ocr.retry.initial-delay:1000}")
    private int initialRetryDelay;
    
    @Value("${ocr.retry.multiplier:2}")
    private int retryMultiplier;

    // 新增：后端prod服务地址
    @Value("${prod.service.url:http://localhost:9000}")
    private String prodServiceUrl;

    @Autowired
    private ImageService imageService; // 注入ImageService

    @Autowired
    private ResourceLoader resourceLoader;

    @Autowired
    public OcrService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    /**
     * 从后端的prod服务获取所有OCR结果
     * @return OCR结果列表或包含错误的Map
     */
    public Object fetchAllOcrResultsFromProdService() {
        String url = prodServiceUrl + "/images/all-ocr-results";
        log.info("正在从prod服务获取所有OCR结果，URL: {}", url);
        try {
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
            if (responseEntity.getStatusCode() == HttpStatus.OK) {
                log.info("成功从prod服务获取OCR结果。");
                String responseBody = responseEntity.getBody();

                // 使用Jackson手动解析和重写URL
                ObjectMapper objectMapper = new ObjectMapper();
                List<Map<String, Object>> results = objectMapper.readValue(responseBody, new TypeReference<>() {});

                for (Map<String, Object> result : results) {
                    if (result.containsKey("imageInfo")) {
                        Map<String, Object> imageInfo = (Map<String, Object>) result.get("imageInfo");
                        if (imageInfo != null && imageInfo.containsKey("imageUrl")) {
                            String originalUrl = (String) imageInfo.get("imageUrl");
                            if (originalUrl != null && originalUrl.startsWith("/images/file/")) {
                                String newUrl = originalUrl.replace("/images/file/", "/ocr/image/");
                                imageInfo.put("imageUrl", newUrl);
                            }
                        }
                    }
                }
                return results;
            } else {
                log.error("从prod服务获取OCR结果失败。状态码: {}, 响应体: {}",
                        responseEntity.getStatusCode(), responseEntity.getBody());
                Map<String, Object> errorMap = new HashMap<>();
                errorMap.put("error", "从后端服务获取数据失败。");
                errorMap.put("status", responseEntity.getStatusCodeValue());
                return errorMap;
            }
        } catch (IOException e) {
            log.error("解析从prod服务获取的OCR结果时出错: {}", e.getMessage(), e);
            return Collections.singletonMap("error", "解析后端服务响应失败: " + e.getMessage());
        } catch (RestClientException e) {
            log.error("调用prod服务时出错 (URL: {}): {}", url, e.getMessage());
            return Collections.singletonMap("error", "连接后端服务出错: " + e.getMessage());
        }
    }

    /**
     * 从后端的prod服务获取单个图片
     * @param imageId 图片ID
     * @return 包含图片数据的ResponseEntity
     */
    public ResponseEntity<org.springframework.core.io.Resource> fetchImageFromProdService(String imageId) {
        String url = prodServiceUrl + "/images/file/" + imageId;
        log.info("正在从prod服务获取图片，URL: {}", url);
        try {
            // 使用 execute 方法以获得更精細的控制，確保連接被正確關閉
            return restTemplate.execute(
                url,
                HttpMethod.GET,
                (RequestCallback) null, // No request body
                clientHttpResponse -> { // ResponseExtractor callback
                    if (clientHttpResponse.getStatusCode() == HttpStatus.OK) {
                        byte[] imageBytes = clientHttpResponse.getBody().readAllBytes();
                        org.springframework.core.io.Resource resource = new org.springframework.core.io.ByteArrayResource(imageBytes);

                        // Copy original headers
                        HttpHeaders headers = new HttpHeaders();
                        headers.setContentType(clientHttpResponse.getHeaders().getContentType());
                        headers.setContentLength(clientHttpResponse.getHeaders().getContentLength());
                        
                        log.info("成功从prod服务获取图片: {}, 大小: {} bytes", imageId, imageBytes.length);
                        return new ResponseEntity<>(resource, headers, HttpStatus.OK);
                    } else {
                        log.error("从prod服务获取图片失败。状态码: {}", clientHttpResponse.getStatusCode());
                        return new ResponseEntity<>(clientHttpResponse.getStatusCode());
                    }
                }
            );
        } catch (RestClientException e) {
            log.error("调用prod服务获取图片时出错 (URL: {}): {}", url, e.getMessage());
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 处理 OCR 请求：存储图片到本地和GridFS、调用 Python 服务、解析结果、更新数据库消息
     *
     * @param image 上传的图片文件。
     * @param name  用户名。
     * @param time  时间字符串。
     * @param sessionId 会话ID。
     * @return 解析出的原始 "content" 部分，或者包含错误信息的 Map。
     */
    @Transactional
    public Object processOcr(MultipartFile image, String name, String time, Integer sessionId) {
        if (image == null || image.isEmpty()) {
            log.warn("OCR 请求 (Session ID: {}) 中未包含图片文件。", sessionId);
            return Collections.singletonMap("error", "需要提供图片文件。");
        }

        Object originalOcrContent = null; // 存储OCR内容的变量
        String localFilePath = null;      // 本地文件路径
        ImageMetadata imageMetadata = null; // 图片元数据

        try {
            // 1. 将图片存储到本地临时目录
            localFilePath = saveImageToDesignatedLocation(image, sessionId);
            log.info("图片已保存到本地临时目录: {}, 会话ID: {}", localFilePath, sessionId);
            
            // 2. 将图片存储到GridFS
            imageMetadata = imageService.storeImage(image, sessionId, "OCR");
            log.info("图片已存储到GridFS，ID: {}, 会话ID: {}", imageMetadata.getGridFsId(), sessionId);
            
            // 3. 调用Python OCR服务（使用重试机制）
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("image_path", localFilePath);    // Python服务需要的本地文件路径
            requestBody.put("image_id", imageMetadata.getGridFsId()); // 发送GridFS ID
            requestBody.put("db_name", "aidoctor"); // MongoDB数据库名称
            requestBody.put("name", name);
            
            // 调用带有重试功能的方法
            String responseBody = callPythonOcrServiceWithRetry(requestBody, sessionId);
            
            // 4. 处理响应
            if (responseBody != null) {
                log.debug("收到 OCR 响应 (Session ID: {}): {}", sessionId, 
                        responseBody.substring(0, Math.min(responseBody.length(), 500)));

                // 解析OCR JSON响应
                Map<String, Object> parsedOcrMap = OcrTextToJson.parseJsonToMap(responseBody);
                if (parsedOcrMap.isEmpty()) {
                    log.error("解析 OCR 响应失败或响应为空 (Session ID: {}).", sessionId);
                    return Collections.singletonMap("error", "解析 OCR 响应失败。" + 
                            (responseBody.length() < 200 ? " Body: " + responseBody : ""));
                }

                // 检查Python服务特定错误
                if (parsedOcrMap.containsKey("error")) {
                    log.warn("OCR 服务返回错误 (Session ID: {}): {}", sessionId, parsedOcrMap.get("error"));
                    return parsedOcrMap;
                }
                
                if (parsedOcrMap.containsKey("content") && 
                    ("识别失败".equals(parsedOcrMap.get("content")) || 
                     "姓名错误".equals(parsedOcrMap.get("content")))) {
                    log.warn("OCR 服务返回特定失败内容 (Session ID: {}): {}", sessionId, parsedOcrMap.get("content"));
                    return parsedOcrMap;
                }

                // 提取原始内容
                originalOcrContent = parsedOcrMap.get("content");
                if (originalOcrContent == null) {
                    log.warn("OCR 响应解析成功，但缺少 'content' 字段 (Session ID: {}).", sessionId);
                    return Collections.singletonMap("error", "OCR 响应缺少 'content' 字段。");
                }

                // 5. 提取"检查结果"
                List<Map<String, Object>> testResults = extractTestResults(originalOcrContent);

                if (testResults != null && !testResults.isEmpty()) {
                    log.info("为 Session ID: {} 提取到 {} 条检查结果。准备更新数据库...", sessionId, testResults.size());
                    
                    // 6. 获取最新消息
                    List<Message> messages = messageMapper.selectBySessionId(sessionId);
                    if (messages == null || messages.isEmpty()) {
                        log.error("未找到 Session ID: {} 的任何消息记录，无法更新检查结果。", sessionId);
                        // 返回原始内容，但记录错误
                        return originalOcrContent; 
                    }
                    
                    Message latestMessage = messages.get(messages.size() - 1);
                    log.debug("找到 Session ID: {} 的最新消息 ID: {}", sessionId, latestMessage.getId());

                    // 7. 解析顶层消息JSON结构
                    Map<String, Object> topLevelMessageMap = OcrTextToJson.parseJsonToMap(latestMessage.getMessage());
                    if (topLevelMessageMap.isEmpty()) {
                        log.error("解析消息 ID: {} 的顶层 JSON 结构失败，无法更新检查结果。", latestMessage.getId());
                        return originalOcrContent;
                    }

                    // 8. 获取内部interaction_history映射
                    Object historyObj = topLevelMessageMap.get("interaction_history");
                    if (!(historyObj instanceof Map)) {
                        log.error("消息 ID: {} 的 JSON 结构中未找到有效的 'interaction_history' Map，无法更新检查结果。", latestMessage.getId());
                        return originalOcrContent;
                    }
                    
                    Map<String, Object> interactionHistoryMap = (Map<String, Object>) historyObj;

                    // 9. 更新interaction_history中的test_recommendation
                    Object existingInnerRecommendations = interactionHistoryMap.get("test_recommendation");
                    List<Map<String, Object>> targetRecommendationList;

                    if (existingInnerRecommendations instanceof List) {
                        log.debug("消息 ID: {} 在 interaction_history 中找到 test_recommendation 列表，将添加新结果。", latestMessage.getId());
                        targetRecommendationList = (List<Map<String, Object>>) existingInnerRecommendations;
                    } else {
                        if (existingInnerRecommendations != null) {
                             log.warn("消息 ID: {} 的 interaction_history.test_recommendation 字段不是列表类型 (类型: {})，将创建新列表。", latestMessage.getId(), existingInnerRecommendations.getClass().getName());
                        }
                        log.debug("消息 ID: {} 在 interaction_history 中未找到 test_recommendation 列表或类型不符，将创建新列表。", latestMessage.getId());
                        targetRecommendationList = new ArrayList<>();
                        interactionHistoryMap.put("test_recommendation", targetRecommendationList); // 创建新列表
                    }

                    targetRecommendationList.addAll(testResults); // 添加新结果到目标列表
                    
                    // 10. 添加图片引用信息到交互历史中
                    if (!interactionHistoryMap.containsKey("images")) {
                        interactionHistoryMap.put("images", new ArrayList<Map<String, String>>());
                    }
                    
                    List<Map<String, String>> imagesList = (List<Map<String, String>>) interactionHistoryMap.get("images");
                    
                    Map<String, String> imageInfo = new HashMap<>();
                    imageInfo.put("id", imageMetadata.getGridFsId());
                    imageInfo.put("category", "OCR");
                    imageInfo.put("originalName", imageMetadata.getOriginalFilename());
                    imageInfo.put("uploadTime", imageMetadata.getUploadTime().toString());
                    imageInfo.put("localPath", localFilePath);  // 添加本地路径信息
                    
                    imagesList.add(imageInfo);

                    // 11. 将修改后的顶层映射转换回JSON
                    String updatedMessageJson = OcrTextToJson.convertMapToJson(topLevelMessageMap);
                    if (updatedMessageJson == null) {
                        log.error("将更新后的顶层消息 Map (消息 ID: {}) 转换回 JSON 失败。", latestMessage.getId());
                        return originalOcrContent;
                    }

                    // 12. 更新数据库
                    latestMessage.setMessage(updatedMessageJson);
                    int updateCount = messageMapper.updateMessageById(latestMessage.getId(), updatedMessageJson);
                    if (updateCount > 0) {
                        log.info("成功更新消息 ID: {} 的 interaction_history.test_recommendation 字段。", latestMessage.getId());
                    } else {
                        log.error("更新消息 ID: {} 失败，数据库未返回成功标志。", latestMessage.getId());
                    }
                } else {
                     log.info("Session ID: {} 未从 OCR 结果中提取到有效的 '检查结果' 列表，无需更新数据库消息。", sessionId);
                }
                
                // 13. 添加图片ID和本地路径到返回结果中
                if (originalOcrContent instanceof Map) {
                    ((Map<String, Object>) originalOcrContent).put("imageId", imageMetadata.getGridFsId());
                    ((Map<String, Object>) originalOcrContent).put("localPath", localFilePath);
                } else {
                    // 如果originalOcrContent不是Map类型，创建新的Map包含结果和图片信息
                    Map<String, Object> resultMap = new HashMap<>();
                    resultMap.put("content", originalOcrContent);
                    resultMap.put("imageId", imageMetadata.getGridFsId());
                    resultMap.put("localPath", localFilePath);
                    originalOcrContent = resultMap;
                }
                
                log.info("OCR 及数据库更新处理完成 (Session ID: {}). 返回结果", sessionId);
                return originalOcrContent;
            } else {
                log.error("Python OCR 服务返回为空 (Session ID: {}).", sessionId);
                Map<String, Object> errorMap = new HashMap<>();
                errorMap.put("error", "OCR 服务调用失败");
                errorMap.put("details", "Service returned empty response.");
                return errorMap;
            }
        } catch (IOException e) {
            log.error("处理上传图片时出错 (Session ID: {}): {}", sessionId, e.getMessage(), e);
            return Collections.singletonMap("error", "处理上传图片时出错: " + e.getMessage());
        } catch (IndexOutOfBoundsException e) {
            log.error("获取 Session ID: {} 的最新消息时出错 (可能无消息记录): {}", sessionId, e.getMessage(), e);
            return Collections.singletonMap("error", "无法找到会话消息记录以更新检查结果。"); 
        } catch (Exception e) {
            log.error("OCR 服务处理或数据库更新过程中发生意外错误 (Session ID: {}): {}", sessionId, e.getMessage(), e);
            return Collections.singletonMap("error", "OCR 服务或数据库更新发生意外错误: " + e.getMessage());
        }
    }

    /**
     * 带有重试机制的Python OCR服务调用
     * 
     * @param requestBody 请求体
     * @param sessionId 会话ID
     * @return 响应内容，如果所有重试都失败则返回null
     */
    private String callPythonOcrServiceWithRetry(Map<String, String> requestBody, Integer sessionId) {
        int attempts = 0;
        int currentDelay = initialRetryDelay;
        RestClientException lastException = null;
        
        while (attempts < maxRetryAttempts) {
            try {
                // 准备请求头
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                
                HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);
                
                log.info("调用 Python OCR 服务 URL: {} , Session ID: {}, 姓名: {}, 本地路径: {}, 尝试次数: {}/{}", 
                        pythonOcrServiceUrl, sessionId, requestBody.get("name"), 
                        requestBody.get("image_path"), attempts + 1, maxRetryAttempts);
                
                // 调用Python OCR服务
                ResponseEntity<String> responseEntity = restTemplate.postForEntity(
                    pythonOcrServiceUrl, requestEntity, String.class);
                
                if (responseEntity.getStatusCode() == HttpStatus.OK) {
                    // 成功获取响应
                    if (attempts > 0) {
                        log.info("在第{}次尝试后成功调用OCR服务", attempts + 1);
                    }
                    return responseEntity.getBody();
                } else {
                    log.error("Python OCR 服务返回非 OK 状态: {} (Session ID: {}).", 
                             responseEntity.getStatusCode(), sessionId);
                    
                    // 如果是服务器错误(5xx)，尝试重试
                    if (responseEntity.getStatusCode().is5xxServerError()) {
                        attempts++;
                        if (attempts < maxRetryAttempts) {
                            log.warn("服务器错误，将在{}ms后重试 (尝试 {}/{})", 
                                    currentDelay, attempts + 1, maxRetryAttempts);
                            Thread.sleep(currentDelay);
                            currentDelay *= retryMultiplier; // 指数退避
                        }
                    } else {
                        // 对于非服务器错误(如4xx)，不重试
                        return null;
                    }
                }
            } catch (RestClientException e) {
                lastException = e;
                attempts++;
                if (attempts < maxRetryAttempts) {
                    try {
                        log.warn("调用 Python OCR 服务出错，将在{}ms后重试 (尝试 {}/{}): {}", 
                                currentDelay, attempts + 1, maxRetryAttempts, e.getMessage());
                        Thread.sleep(currentDelay);
                        currentDelay *= retryMultiplier; // 指数退避
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.error("重试等待被中断", ie);
                        break;
                    }
                } else {
                    log.error("调用 Python OCR 服务在{}次尝试后失败: {}", 
                            maxRetryAttempts, e.getMessage(), e);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("重试等待被中断", e);
                break;
            } catch (Exception e) {
                log.error("调用 Python OCR 服务时发生意外错误: {}", e.getMessage(), e);
                break;
            }
        }
        
        if (lastException != null) {
            log.error("所有重试尝试均失败，最后一次异常: {}", lastException.getMessage());
        }
        
        return null; // 所有尝试均失败
    }

    /**
     * 将图片保存到指定位置
     * @param image 上传的图片文件
     * @param sessionId 会话ID
     * @return 保存后的文件路径
     * @throws IOException 如果保存过程中发生IO错误
     */
    private String saveImageToDesignatedLocation(MultipartFile image, Integer sessionId) throws IOException {
        // 确保目录存在
        File directory = new File(tempDirectory);
        if (!directory.exists()) {
            directory.mkdirs();
        }

        // 生成唯一文件名
        String originalFilename = image.getOriginalFilename();
        String extension = originalFilename != null && originalFilename.contains(".") 
                ? originalFilename.substring(originalFilename.lastIndexOf(".")) 
                : ".jpg";
        String uniqueFilename = "ocr_" + sessionId + "_" + System.currentTimeMillis() + extension;
        
        // 保存文件
        Path filePath = Paths.get(directory.getAbsolutePath(), uniqueFilename);
        Files.write(filePath, image.getBytes());
        
        log.info("图片已保存到: {}", filePath);
        return filePath.toString();
    }

    /**
     * 从解析的内容对象中提取"检查结果"列表
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> extractTestResults(Object content) {
        if (!(content instanceof List)) {
            log.debug("Expected content to be a List, but got: {}", content != null ? content.getClass().getName() : "null");
            return null;
        }
        List<?> contentList = (List<?>) content;

        for (Object item : contentList) {
            if (item instanceof Map) {
                Map<String, Object> mapItem = (Map<String, Object>) item;
                if (mapItem.containsKey("检查结果")) {
                    Object resultsObj = mapItem.get("检查结果");
                    if (resultsObj instanceof List) {
                        try {
                            // 基本检查列表项是否为maps
                            if (!((List<?>) resultsObj).isEmpty() && !(((List<?>) resultsObj).get(0) instanceof Map)) {
                                log.warn("'检查结果' list does not seem to contain Maps.");
                                return null;
                            }
                            return (List<Map<String, Object>>) resultsObj;
                        } catch (ClassCastException e) {
                             log.warn("'检查结果' field found, but its structure is not List<Map<String, Object>>.", e);
                             return null;
                        }
                    } else {
                         log.warn("'检查结果' field found, but it's not a List (Type: {}).", resultsObj != null ? resultsObj.getClass().getName() : "null");
                         return null;
                    }
                }
            }
        }
        log.warn("Could not find '检查结果' key within the 'content' list structure.");
        return null;
    }
}