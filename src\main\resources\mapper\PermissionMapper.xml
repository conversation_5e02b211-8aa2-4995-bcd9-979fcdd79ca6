<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.makiyo.aidoctor.mapper.PermissionMapper">
  <resultMap id="BaseResultMap" type="com.makiyo.aidoctor.entity.Permission">
    <id column="permission_id" jdbcType="INTEGER" property="permissionId" />
    <result column="permission_name" jdbcType="VARCHAR" property="permissionName" />
    <result column="description" jdbcType="VARCHAR" property="description" />
  </resultMap>
  <sql id="Base_Column_List">
    permission_id, permission_name, description
  </sql>
  <select id="selectAll" resultMap="BaseResultMap">
    SELECT 
    <include refid="Base_Column_List" />
    FROM permission
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    SELECT 
    <include refid="Base_Column_List" />
    FROM permission
    WHERE permission_id = #{permissionId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    DELETE FROM permission
    WHERE permission_id = #{permissionId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="permission_id" keyProperty="permissionId" parameterType="com.makiyo.aidoctor.entity.Permission" useGeneratedKeys="true">
    INSERT INTO permission (permission_name, description)
    VALUES (#{permissionName,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="permission_id" keyProperty="permissionId" parameterType="com.makiyo.aidoctor.entity.Permission" useGeneratedKeys="true">
    insert into permission
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="permissionName != null">
        permission_name,
      </if>
      <if test="description != null">
        description,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="permissionName != null">
        #{permissionName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.makiyo.aidoctor.entity.Permission">
    update permission
    <set>
      <if test="permissionName != null">
        permission_name = #{permissionName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
    </set>
    where permission_id = #{permissionId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.makiyo.aidoctor.entity.Permission">
    UPDATE permission
    SET permission_name = #{permissionName,jdbcType=VARCHAR},
        description = #{description,jdbcType=VARCHAR}
    WHERE permission_id = #{permissionId,jdbcType=INTEGER}
  </update>
</mapper>