package com.makiyo.aidoctor.mapper;

import com.makiyo.aidoctor.entity.User;
import com.makiyo.aidoctor.entity.Session;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserMapper {
    /**
     * 根据主键删除用户
     */
    int deleteByPrimaryKey(Integer userId);

    /**
     * 插入用户记录
     */
    int insert(User record);

    /**
     * 选择性插入用户记录
     */
    int insertSelective(User record);

    /**
     * 根据主键查询用户
     */
    User selectByPrimaryKey(@Param("userId") Integer userId);

    /**
     * 根据主键选择性更新用户
     */
    int updateByPrimaryKeySelective(User record);

    /**
     * 根据主键更新用户
     */
    int updateByPrimaryKey(User record);

    /**
     * 查询所有用户
     */
    List<User> selectAll();

    /**
     * 根据用户ID查询用户信息
     * @param userId 用户ID
     * @return 用户信息
     */
    User selectByUserId(@Param("userId") Integer userId);

    /**
     * 根据用户名查询用户信息
     * @param username 用户名
     * @return 用户信息
     */
    User selectByUsername(@Param("username") String username);

    /**
     * 根据用户ID查询基本信息（用户名、头像和简介）
     * @param userId 用户ID
     * @return 用户基本信息
     */
    User selectBasicInfoByUserId(@Param("userId") Integer userId);

    /**
     * 获取用户的所有会话列表
     * @param userId 用户ID
     * @return 会话列表
     */
    List<Session> selectSessionsByUserId(@Param("userId") Integer userId);
}