package com.makiyo.aidoctor.service;

import com.makiyo.aidoctor.entity.User;
import com.makiyo.aidoctor.entity.Session;
import com.makiyo.aidoctor.entity.Message;
import com.makiyo.aidoctor.form.UserInfoForm;
import com.makiyo.aidoctor.mapper.MessageMapper;
import com.makiyo.aidoctor.mapper.UserMapper;
import com.makiyo.aidoctor.utils.BeanCopyUtils;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.databind.ObjectMapper;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class UserService {

    @Resource
    private UserMapper userMapper;

    @Resource
    private MessageService messageService;

    /**
     * 查询所有用户
     */
    public List<User> listAll() {
        return userMapper.selectAll();
    }

    /**
     * 获取用户基本信息（用户名、头像和简介）
     * @param userId 用户ID
     * @return 用户基本信息
     */
    public UserInfoForm getUserInfo(Integer userId) {
        System.out.println("正在查询用户信息，userId: " + userId);
        try {
            User user = userMapper.selectByPrimaryKey(userId);
            System.out.println("数据库查询结果: " + (user != null ? "找到用户: " + user.getUsername() : "未找到用户"));
            if (user == null) {
                return null;
            }
            UserInfoForm userInfo = BeanCopyUtils.copyBean(user, UserInfoForm::new);
            System.out.println("转换后的用户信息: " + userInfo);
            return userInfo;
        } catch (Exception e) {
            System.err.println("查询用户信息出错: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 根据用户名获取用户基本信息
     * @param username 用户名
     * @return 用户基本信息
     */
    public UserInfoForm getUserInfoByUsername(String username) {
        System.out.println("正在查询用户信息，username: " + username);
        try {
            User user = userMapper.selectByUsername(username);
            System.out.println("数据库查询结果: " + (user != null ? "找到用户: " + user.getUsername() : "未找到用户"));
            if (user == null) {
                return null;
            }
            UserInfoForm userInfo = BeanCopyUtils.copyBean(user, UserInfoForm::new);
            System.out.println("转换后的用户信息: " + userInfo);
            return userInfo;
        } catch (Exception e) {
            System.err.println("根据用户名查询用户信息出错: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 获取用户的所有会话列表
     * @param userId 用户ID
     * @return 会话列表
     */
    public List<Session> getUserSessions(Integer userId) {
        return userMapper.selectSessionsByUserId(userId);
    }

    /**
     * 获取指定会话的所有消息
     * @param sessionId 会话ID
     * @return 消息列表，按时间升序排序
     */
    public List<Message> getSessionMessages(Integer sessionId) {
        return messageService.getSessionMessages(sessionId);
    }

    /**
     * 登录验证
     * @param userId 用户ID
     * @return 用户信息，不存在返回null
     */
    public User login(Integer userId) {
        System.out.println("UserService.login - 开始查询用户，userId: " + userId);
        try {
            User user = userMapper.selectByPrimaryKey(userId);
            System.out.println("UserService.login - 查询结果: " + (user != null ? "找到用户: " + user.getUsername() : "未找到用户"));
            return user;
        } catch (Exception e) {
            System.err.println("UserService.login - 查询出错: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 通过用户名登录验证
     * @param username 用户名
     * @return 用户信息，不存在返回null
     */
    public User loginByUsername(String username) {
        System.out.println("UserService.loginByUsername - 开始查询用户，username: " + username);
        try {
            User user = userMapper.selectByUsername(username);
            System.out.println("UserService.loginByUsername - 查询结果: " + (user != null ? "找到用户: " + user.getUsername() : "未找到用户"));
            return user;
        } catch (Exception e) {
            System.err.println("UserService.loginByUsername - 查询出错: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 注册新用户
     * @param user 用户信息
     * @return 用户信息，注册失败返回null
     */
    public UserInfoForm register(User user) {
        try {
            // 检查用户是否已存在
            User existingUser = userMapper.selectByPrimaryKey(user.getUserId());
            if (existingUser != null) {
                System.out.println("用户已存在: " + user.getUserId());
                return BeanCopyUtils.copyBean(existingUser, UserInfoForm::new);
            }

            // 设置默认值
            if (user.getUsername() == null) {
                user.setUsername("用户" + user.getUserId());
            }
            if (user.getProfilePicture() == null) {
                user.setProfilePicture("https://api.dicebear.com/7.x/avataaars/svg?seed=" + user.getUserId());
            }
            if (user.getBio() == null) {
                user.setBio("这个用户很懒，还没有填写简介");
            }

            // 插入用户记录
            int result = userMapper.insertSelective(user);
            System.out.println("插入用户结果: " + result + ", userId: " + user.getUserId());
            
            if (result > 0) {
                return BeanCopyUtils.copyBean(user, UserInfoForm::new);
            }
            return null;
        } catch (Exception e) {
            System.err.println("注册用户失败: " + e.getMessage());
            e.printStackTrace();
            throw e;  // 抛出异常，让上层处理
        }
    }

    /**
     * Extracts dialogue history from V2 message JSON for a given session.
     * Assumes V2 history is stored within a single Message record for the session.
     * @param sessionId The ID of the session.
     * @return A list of dialogue entries (Map<String, Object> with "role" and "content").
     * @throws Exception If parsing fails.
     */
    public List<Map<String, Object>> extractV2DialogueHistory(Integer sessionId) throws Exception {
        List<Map<String, Object>> fullDialogue = new ArrayList<>();
        List<Message> messages = getSessionMessages(sessionId);

        if (!messages.isEmpty()) {
            Message v2Message = messages.get(0);
            String jsonContent = v2Message.getMessage();
            
            if (jsonContent != null && !jsonContent.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper();
                try {
                    Map<String, Object> root = objectMapper.readValue(jsonContent, Map.class);
                    Map<String, Object> interactionHistory = (Map<String, Object>) root.get("interaction_history");

                    if (interactionHistory != null && interactionHistory.containsKey("cot_entries")) {
                        List<Map<String, Object>> cotEntries = (List<Map<String, Object>>) interactionHistory.get("cot_entries");
                        if (cotEntries != null) {
                            for (Map<String, Object> entry : cotEntries) {
                                if (entry != null && entry.containsKey("dialogue_history")) {
                                    List<Map<String, Object>> dialogues = (List<Map<String, Object>>) entry.get("dialogue_history");
                                    if (dialogues != null) {
                                        fullDialogue.addAll(dialogues);
                                    }
                                }
                            }
                        }
                    }
                } catch (com.fasterxml.jackson.core.JsonProcessingException e) {
                    System.err.println("解析 V2 JSON 失败 for session " + sessionId + ": " + e.getMessage());
                }
            }
        }
        return fullDialogue;
    }

    /**
     * Extracts dialogue history turns from V2 message JSON for a given session.
     * @param sessionId The ID of the session.
     * @return A list of dialogue turn Maps (with "role" and "content").
     * @throws Exception If parsing fails.
     */
    public List<Map<String, Object>> extractV2DialogueHistoryTurns(Integer sessionId) throws Exception {
        List<Map<String, Object>> fullDialogueTurns = new ArrayList<>();
        List<Message> messages = messageService.getSessionMessages(sessionId);

        if (!messages.isEmpty()) {
            // Assume V2 history is in the first/only message
            Message v2Message = messages.get(0); 
            String jsonContent = v2Message.getMessage();
            
            if (jsonContent != null && !jsonContent.isEmpty()) {
                ObjectMapper objectMapper = new ObjectMapper(); 
                try {
                    Map<String, Object> root = objectMapper.readValue(jsonContent, Map.class);
                    Map<String, Object> interactionHistory = (Map<String, Object>) root.get("interaction_history");

                    if (interactionHistory != null && interactionHistory.containsKey("cot_entries")) {
                        List<Map<String, Object>> cotEntries = (List<Map<String, Object>>) interactionHistory.get("cot_entries");
                        if (cotEntries != null) {
                            for (Map<String, Object> entry : cotEntries) {
                                if (entry != null && entry.containsKey("dialogue_history")) {
                                    List<Map<String, Object>> dialogues = (List<Map<String, Object>>) entry.get("dialogue_history");
                                    if (dialogues != null) {
                                        // Add all dialogue turns from this cot_entry
                                        fullDialogueTurns.addAll(dialogues);
                                    }
                                }
                            }
                        }
                    }
                } catch (com.fasterxml.jackson.core.JsonProcessingException e) {
                    System.err.println("解析 V2 JSON 失败 for session " + sessionId + ": " + e.getMessage());
                    // Depending on requirements, re-throw or return empty list
                    // throw e; 
                }
            }
        }
        return fullDialogueTurns;
    }
}
