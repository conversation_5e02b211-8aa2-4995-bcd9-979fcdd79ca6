# 数据库配置
DB_URL=*********************************************************************************
DB_USERNAME=postgres
DB_PASSWORD=lin171820...
DB_INITIAL_SIZE=8
DB_MAX_ACTIVE=16
DB_MIN_IDLE=8
DB_MAX_WAIT=60000

# Druid监控配置
DRUID_ADMIN_USERNAME=admin
DRUID_ADMIN_PASSWORD=admin123
DRUID_ALLOW_IP=localhost

# TTS 语音合成服务配置
TTS_API_URL=https://openspeech.bytedance.com/api/v1/tts
TTS_APP_ID=4146985820
TTS_ACCESS_TOKEN=z_2JNCRIbNM7Mk-dI24H6M8VAOv1JFeu
TTS_VOICE_TYPE=BV001_streaming
TTS_ENCODING=mp3
TTS_CLUSTER=volcano_tts
TTS_BASE_PATH=audio
TTS_BASE_URL=/audio
TTS_SPEED_RATIO=1.0
TTS_VOLUME_RATIO=1.0
TTS_PITCH_RATIO=1.0

# Python AI 服务配置
PYTHON_PROD_URL=http://localhost:5555/ai_doctor_v2
PYTHON_PROD_DIAGNOSE_URL=http://localhost:5555/ai_doctor_v2_diagnosis
PYTHON_PROD_INQUIRY_URL=http://localhost:5555/ai_doctor_v2_inquiry
PYTHON_PROD_PRELIMINARY_DIAGNOSIS_URL=http://localhost:5555/ai_doctor_v2_preliminary_diagnosis
PYTHON_PROD_OCR_URL=http://localhost:5555/ocr_api

# 语音识别服务配置
ASR_API_URL=wss://openspeech.bytedance.com/api/v3/sauc/bigmodel
ASR_APP_ID=4146985820
ASR_TOKEN=z_2JNCRIbNM7Mk-dI24H6M8VAOv1JFeu
ASR_TEMP_DIR=${user.dir}/audio

# OCR 配置
OCR_PYTHON_PROD_SERVICE_URL=http://localhost:5555/ocr_api
OCR_IMAGE_PROD_SAVE_PATH=${user.dir}/images

# 服务器配置
SERVER_PORT=9000 

# 微信验证配置
WECHAT_VALID_TOKEN=aidoctor 