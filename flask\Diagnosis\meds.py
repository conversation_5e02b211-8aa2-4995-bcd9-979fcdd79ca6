import pandas as pd
import json

class Meds:
    def __init__(self, file_path):
        """
        初始化 Meds 类，读取 Excel 文件并自动处理数据
        
        Args:
            file_path (str): 药品数据 Excel 文件路径
        """
        self.file_path = file_path
        self.medications = {"西药": {}, "中药": {}}
        
        # 读取数据并处理
        self._read_excel()
        self._process_all_meds()

    def _read_excel(self):
        """读取 Excel 文件的西药和中药工作表"""
        try:
            self.df_xiyao = pd.read_excel(self.file_path, sheet_name="西药", header=None)
            self.df_zhongyao = pd.read_excel(self.file_path, sheet_name="中药", header=None)
        except Exception as e:
            raise ValueError(f"读取 Excel 文件失败: {str(e)}")

    def _process_all_meds(self):
        """处理所有药品分类"""
        self._process_one_category(self.df_xiyao, "西药")
        self._process_one_category(self.df_zhongyao, "中药")

    def _process_one_category(self, df, category):
        """
        处理单个药品分类（西药/中药）
        
        Args:
            df (pd.DataFrame): 药品数据框
            category (str): 分类名称（西药/中药）
        """
        current_class = None
        for index, row in df.iterrows():
            cell_value = str(row[0]).strip()
            
            # 分类行判断逻辑：以数字开头
            if cell_value and cell_value[0].isdigit():
                current_class = cell_value
                self.medications[category][current_class] = []
            elif current_class:  # 药品行处理
                med_info = {
                    "名称": cell_value,
                    "规格": self._get_cell_value(row, 1),
                    "单位": self._get_cell_value(row, 2)
                }
                self.medications[category][current_class].append(med_info)

    def _get_cell_value(self, row, col_idx):
        """
        安全获取单元格内容
        
        Args:
            row (pd.Series): 数据行
            col_idx (int): 列索引
        """
        return str(row[col_idx]).strip() if len(row) > col_idx else ""

    def to_json(self, output_path):
        """
        导出为 JSON 文件
        
        Args:
            output_path (str): 输出文件路径
        """
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(self.medications, f, ensure_ascii=False, indent=4)

    def print_summary(self):
        """打印分类摘要"""
        print("\n药品分类摘要:")
        for category, classes in self.medications.items():
            print(f"[{category}]")
            for class_name, meds in classes.items():
                print(f"  {class_name}: {len(meds)}种药品")

    def print_examples(self, n=2):
        """打印示例数据"""
        print("\n示例数据:")
        for category, classes in self.medications.items():
            print(f"[{category}]")
            for class_name, meds in classes.items():
                print(f"  {class_name}:")
                for med in meds[:n]:
                    print(f"    - {med['名称']} ({med['规格']}{med['单位']})")

# 使用示例
if __name__ == "__main__":
    # 初始化并处理数据
    meds = Meds("../data/meds.xlsx")
    
    # 打印摘要信息
    meds.print_summary()
    
    # 打印前2条示例
    meds.print_examples()
    
    # 导出为JSON
    meds.to_json("medications.json")
    print("\nJSON 文件已保存至 medications.json")
