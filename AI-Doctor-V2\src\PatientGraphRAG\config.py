""" 此处定义项目所需的全部配置 """
import os
from pathlib import Path

# File
# patient_dirs = [
#     r"D:\internship\data\patient_case"
# ]

# Embedding
huggingface_embedding_model_name = r"D:\model\embed\bge_large_zh_v1.5"
modelscope_embedding_model_name = r"D:\model\embed\emb_CoROM"
api_embedding_url = "http://***************:8000"
api_vector_dim = 1024

# LLM
default_client_type = "openai"

# 使用环境变量管理敏感配置
openai_base_url = "http://***************:40000/v1"
openai_llm_key = "sk-deepseek-123456"
openai_llm_model_name = "deepseek-r1"

volcano_llm_key = os.getenv("VOLCANO_API_KEY", "sk-44a94d67b9a9437885fb9424fcf07a50")
volcano_model_name = os.getenv("VOLCANO_MODEL_NAME", "deepseek-v3")

# PDFParser
llama_api_key = os.getenv("LLAMA_API_KEY", "llx-1P0zhpjZyq9t5YfG0Sn83FFzpyP6GV24f3Oj5hKwLJrEirZq")

# Vector Store
# qdrant_db_url = os.getenv("QDRANT_URL", "http://************:6333/")
qdrant_db_url = os.getenv("QDRANT_URL", "https://e498113c-c4e9-4c60-82be-64ca09c7f23d.eu-central-1-0.aws.cloud.qdrant.io:6333")

qdrant_db_api_key = os.getenv("QDRANT_API_KEY", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3MiOiJtIn0.NSPh1-18mfh_UMgNOz9yGRYrfKFQmmhgjw4zMV1q91g")
qdrant_db_collection_name = os.getenv("QDRANT_COLLECTION", "patient_graph")

# Graph
# medical_graph_save_dir = os.getenv("GRAPH_SAVE_DIR", r"D:\internship\data\patient_graph")  #
medical_graph_save_dir = os.getenv("GRAPH_SAVE_DIR", r"src/PatientGraphRAG/patient_graph")  ## Test
test_save_dir = os.getenv("TEST_SAVE_DIR", r"D:\internship\data\patient_graph")

# backup
back_up_dir = os.getenv("BACKUP_DIR", r"D:\internship\data\backup")

# Embedding API 并发控制配置
EMBEDDING_CONFIG = {
    "max_concurrency": 5,  # 最大并发数
    "batch_size": 2,      # 批处理大小
    "retry_times": 3,      # 重试次数
    "retry_delay": 1.0,    # 重试延迟（秒）
    "batch_delay": 0.1,    # 批次间延迟（秒）
    "timeout": 30.0,       # 超时时间（秒）
    "client_type": "api"
}

# 向量搜索并发控制配置
VECTOR_SEARCH_CONFIG = {
    "max_concurrency": 50,   # 最大并发数 - 降低到1避免并发问题
    "retry_times": 3,       # 重试次数 - 减少到3次
    "retry_delay": 2.0,     # 重试延迟（秒）- 增加延迟
    "timeout": 120.0,       # 超时时间（秒）- 增加超时时间
    "backoff_factor": 2.0   # 退避因子
}

# 向量缓存配置
VECTOR_CACHE_CONFIG = {
    "max_cache_size": 1000,  # 最大缓存症状数量
    "cache_threshold": 50,   # 使用缓存策略的阈值
    "enable_lru": True,      # 启用LRU缓存策略
    "cache_stats_interval": 100,  # 缓存统计间隔
}

# 验证必要的环境变量
def validate_config():
    """验证配置的完整性"""
    required_vars = [
        "OPENAI_API_KEY",
        "QDRANT_API_KEY"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"警告: 以下环境变量未设置: {missing_vars}")
        print("将使用默认值，这可能影响系统功能")

# 在模块加载时验证配置
validate_config()