spring:
  data:
    mongodb:
      host: localhost
      port: 27017
      database: aiDoctor
      username: admin
      password: abc123456
      authentication-database: admin  # Add this line

  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}  # 默认使用生产环境配置
  devtools:
    livereload:
      enabled: false # Disable LiveReload
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ***************************************************************************************************************************************************
      username: root
      password: lin171820...
      initial-size: 8
      max-active: 16
      min-idle: 8
      max-wait: 60000
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
  banner:
    charset: utf-8
    location: classpath:banner.txt
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

# 后端服务端口
server:
  port: 8080
  servlet:
    context-path: /
python:
  v2:
    quick:
      inquiry:
        url: http://localhost:5555/ai_doctor_v2_quick_inquiry
# 增加数据库链接
mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.makiyo.aidoctor.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

logging:
  level:
    com.makiyo.aidoctor.mapper: debug

# Springdoc OpenAPI Configuration (for Swagger UI)
springdoc:
  api-docs:
    version: openapi_3_0 # Specifies the OpenAPI version
  info:
    title: AI Doctor API
    version: 1.0.0 # Your API version
    description: AI Doctor 应用程序的 API 文档
  # You can uncomment and customize paths if needed
  # swagger-ui:
  #   path: /swagger-ui.html
  # api-docs:
  #   path: /v3/api-docs

# 语音识别服务配置
asr:
  api:
    url: wss://openspeech.bytedance.com/api/v3/sauc/bigmodel
    appId: 4146985820
    token: z_2JNCRIbNM7Mk-dI24H6M8VAOv1JFeu
  temp:
    dir: ${java.io.tmpdir}/asr

# TTS 语音合成服务配置
tts:
  api:
    url: https://openspeech.bytedance.com/api/v1/tts  # 使用标准接口
    appId: 4146985820
    accessToken: z_2JNCRIbNM7Mk-dI24H6M8VAOv1JFeu
  audio:
    voiceType: BV001_streaming
    encoding: mp3
    cluster: volcano_tts
    basePath: audio
    baseUrl: /audio
    speedRatio: 1.5
    volumeRatio: 1.0
    pitchRatio: 1.0

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
  datasource:
    url: **********************************************************************************************************
    username: root
    password: lin171820...
    driver-class-name: com.mysql.cj.jdbc.Driver
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

server:
  port: 8080
# 配置 OCR 上传图片临时保存的目录 (请根据实际情况修改)
# 使用正斜杠 '/' 通常更兼容
ocr: # 无前导空格
  image: # 2个前导空格
    save: # 4个前导空格
      path: D:/aiDoctor/AI-Doctor-V2/images # 6个前导空格
# 开发环境 TTS 配置
tts:
  audio:
    basePath: D:/aiDoctor/audio/  # 开发环境使用绝对路径
    baseUrl: /audio

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
  datasource:
    url: **************************************************************************************************************************
    username: root
    password: lin171820...
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 生产环境数据库连接池配置
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

server:
  port: 8080  # 后端服务仍然使用8080端口，由nginx处理SSL
  # 生产环境服务器配置
  tomcat:
    max-threads: 200
    min-spare-threads: 20
    max-connections: 10000
    accept-count: 100
    remoteip:
      remote-ip-header: X-Forwarded-For
      protocol-header: X-Forwarded-Proto
      protocol-header-https-value: https

# 生产环境 TTS 配置
tts:
  audio:
    basePath: /usr/local/aidoctor/audio/  # 生产环境使用绝对路径
    baseUrl: /audio

# 生产环境 OCR 配置
ocr:
  image:
    save:
      path: /home/<USER>/images  # 生产环境OCR图片存储路径
  python:
    service:
      url: http://localhost:5555/ocr_api  # 生产环境OCR Python服务URL

# 生产环境 Python AI 服务配置
python:
  v2:
    diagnosis:
      url: http://localhost:5555/ai_doctor_v2_diagnosis
    inquiry:
      url: http://localhost:5555/ai_doctor_v2
    quick:
      inquiry:
        url: http://localhost:5555/ai_doctor_v2_quick_inquiry
    preliminary:
      diagnosis:
        url: http://localhost:5555/ai_doctor_v2_preliminary_diagnosis
