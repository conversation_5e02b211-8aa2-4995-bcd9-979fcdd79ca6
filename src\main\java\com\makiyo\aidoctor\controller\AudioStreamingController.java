package com.makiyo.aidoctor.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;

@Tag(name = "音频流控制器", description = "处理音频流相关的接口")
@SpringBootApplication
@RestController
public class AudioStreamingController {

    @Operation(summary = "获取音频流", description = "返回音频文件流")
    @GetMapping("/audio")
    public void streamAudio(HttpServletResponse response) throws IOException {
        response.setContentType("audio/mpeg"); // 设置音频类型
        FileInputStream audioFile = new FileInputStream("D:\\aiDoctor\\src\\main\\resources\\static\\test.mp3");
        OutputStream out = response.getOutputStream();

        byte[] buffer = new byte[4096];
        int bytesRead;

        while ((bytesRead = audioFile.read(buffer)) != -1) {
            out.write(buffer, 0, bytesRead);
            out.flush(); // 刷新流
        }

        audioFile.close();
    }
}