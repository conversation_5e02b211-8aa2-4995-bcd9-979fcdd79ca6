package com.makiyo.ai_doctor_prod.utils;

/**
 * 日志辅助工具。
 */
public class LogUtils {

    private LogUtils() {}

    /**
     * 将字符串安全截断到指定长度。
     * @param origin 原始字符串
     * @param maxLen 最大长度
     * @return 截断后的字符串；若 origin 为 null，则返回 "null"
     */
    public static String truncate(String origin, int maxLen) {
        if (origin == null) {
            return "null";
        }
        return origin.length() <= maxLen ? origin : origin.substring(0, maxLen) + "...";
    }
} 