<template>
  <div class="all-cases">
    <h1 class="page-title">APP数据</h1>

    <div class="filter-controls">
      <a-space>
        <a-range-picker
          :show-time="{ format: 'HH:mm:ss' }"
          format="YYYY-MM-DD HH:mm:ss"
          v-model:value="searchDateRange"
          placeholder="['开始时间', '结束时间']"
        />
        <a-button type="primary" @click="search" :loading="loading">搜索</a-button>
        <a-button @click="clearSearch" :disabled="loading">清空</a-button>
      </a-space>
    </div>
    
    <div class="cases-container">
      <a-spin :spinning="loading">
        <div v-if="!loading && caseItems.length > 0">
          <a-table
            :columns="columns"
            :data-source="caseItems"
            :pagination="pagination"
            @change="handleTableChange"
            :scroll="{ x: '800px' }" 
            :row-key="record => record.id"
          >
            <template #bodyCell="{ column, record }">
              <!-- 案例ID列 -->
              <template v-if="column.key === 'id'">
                <span class="case-id">{{ record.id }}</span>
              </template>
              
              <!-- 创建时间列 -->
              <template v-if="column.key === 'createdAt'">
                <span>{{ formatDateTime(record.created_at) }}</span>
              </template>
              
              <!-- 更新时间列 -->
              <template v-if="column.key === 'updatedAt'">
                <span>{{ formatDateTime(record.updated_at) }}</span>
              </template>
              
              <!-- 诊断结果列 -->
              <template v-if="column.key === 'diagnosis'">
                <span v-if="record.interaction_history && record.interaction_history.interaction_history && record.interaction_history.interaction_history.diagnosis">
                  {{ record.interaction_history.interaction_history.diagnosis || '无' }}
                </span>
                <span v-else>无</span>
              </template>
              
              <!-- 对话轮次 -->
              <template v-if="column.key === 'cotCount'">
                <a-tag color="blue" v-if="record.interaction_history && 
                  record.interaction_history.interaction_history && 
                  record.interaction_history.interaction_history.cot_entries">
                  {{ record.interaction_history.interaction_history.cot_entries.length }}
                </a-tag>
                <a-tag v-else color="gray">0</a-tag>
              </template>
              
              <!-- 案例状态 -->
              <template v-if="column.key === 'caseStatus'">
                <a-tag color="green" v-if="record.interaction_history && 
                    record.interaction_history.interaction_history && 
                    record.interaction_history.interaction_history.diagnosis">已完成</a-tag>
                <a-tag color="blue" v-else-if="record.interaction_history && 
                    record.interaction_history.interaction_history && 
                    record.interaction_history.interaction_history.inspection_suggestions">检查建议</a-tag>
                <a-tag color="purple" v-else-if="record.interaction_history && 
                    record.interaction_history.interaction_history && 
                    record.interaction_history.interaction_history.preliminary_diagnosis">初步诊断</a-tag>
                <a-tag color="orange" v-else-if="record.interaction_history && 
                    record.interaction_history.interaction_history && 
                    record.interaction_history.interaction_history.cot_entries && 
                    record.interaction_history.interaction_history.cot_entries.length > 0">问诊阶段</a-tag>
                <a-tag color="red" v-else>未开始</a-tag>
              </template>
              
              <!-- 操作列 -->
              <template v-if="column.key === 'action'">
                <a-button type="primary" size="small" @click="showCaseDetail(record)">查看详情</a-button>
              </template>
            </template>
          </a-table>
        </div>
        <div v-else-if="!loading && caseItems.length === 0">
          <a-empty description="暂无案例数据" />
        </div>
      </a-spin>
    </div>
    
    <!-- 案例详情抽屉 -->
    <a-drawer
      title="案例交互历史详情"
      v-model:open="drawerVisible"
      :width="'100%'" 
      @after-open-change="onDrawerVisibleChange" 
      placement="right"
      :mask-closable="true"
      :destroy-on-close="true"
    >
      <div v-if="selectedCase && renderDrawerContent">
        <div class="drawer-header">
          <div class="drawer-header-top">
            <h3>案例ID: {{ selectedCase.id }}</h3>
            <a-button type="primary" @click="drawerVisible = false">关闭详情</a-button>
          </div>
          <p>创建时间: {{ formatDateTime(selectedCase.created_at) }}</p>
          <p>更新时间: {{ formatDateTime(selectedCase.updated_at) }}</p>
          <div class="case-meta-tags">
            <a-tag color="blue" v-if="selectedCase.interaction_history && selectedCase.interaction_history.interaction_history && selectedCase.interaction_history.interaction_history.cot_entries">
              对话轮次: {{ selectedCase.interaction_history.interaction_history.cot_entries.length }}
            </a-tag>
            <a-tag color="green" v-if="selectedCase.interaction_history && selectedCase.interaction_history.interaction_history && selectedCase.interaction_history.interaction_history.diagnosis">
              已诊断
            </a-tag>
            <a-tag color="orange" v-if="selectedCase.interaction_history && selectedCase.interaction_history.interaction_history && selectedCase.interaction_history.interaction_history.preliminary_diagnosis">
              初步诊断
            </a-tag>
            <a-tag color="cyan" v-if="selectedCase.interaction_history && selectedCase.interaction_history.interaction_history && selectedCase.interaction_history.interaction_history.inspection_suggestions">
              检查建议
            </a-tag>
            <a-tag color="orange" v-if="selectedCase.interaction_history && selectedCase.interaction_history.interaction_history && selectedCase.interaction_history.interaction_history.test_recommendation && selectedCase.interaction_history.interaction_history.test_recommendation.length > 0">
              检查报告: {{ selectedCase.interaction_history.interaction_history.test_recommendation.length }}
            </a-tag>
            <a-tag color="purple" v-if="selectedCase.interaction_history && selectedCase.interaction_history.interaction_history && (selectedCase.interaction_history.interaction_history.treatment_guide_ref || selectedCase.interaction_history.interaction_history.guidelines_content_ref)">
              指南引用
            </a-tag>
          </div>
        </div>
        
        <a-divider />
        
        <div class="history-container" v-if="selectedCase.interaction_history && selectedCase.interaction_history.interaction_history">
          <h3>交互历史记录</h3>
          
          <!-- COT 条目 -->
          <div v-if="selectedCase.interaction_history.interaction_history.cot_entries && selectedCase.interaction_history.interaction_history.cot_entries.length > 0" class="cot-entries-section">
            <h4>问诊与推理过程</h4>
            <div v-for="(entry, entryIndex) in selectedCase.interaction_history.interaction_history.cot_entries" :key="`entry-${entryIndex}`" class="cot-entry-section">
              <h5>
                <span v-if="entryIndex === 0">初始问诊</span>
                <span v-else-if="entryIndex === 1">查体评估</span>
                <span v-else-if="entryIndex === 2">诊断评估</span>
                <span v-else>交互轮次 {{ entryIndex + 1 }}</span>
              </h5>
              <a-tabs size="small" :animated="false">
                <a-tab-pane key="dialogue" tab="对话历史">
                  <div class="dialogue-history-log" v-if="entry.dialogue_history && entry.dialogue_history.length > 0">
                    <div v-for="(dialogue, diaIdx) in entry.dialogue_history" :key="`diag-${diaIdx}`"
                        :class="['dialogue-entry', dialogue.role === 'doctor' ? 'doctor-entry' : 'patient-entry']">
                      <div class="dialogue-speaker">
                        <a-avatar size="small" :style="{ backgroundColor: dialogue.role === 'doctor' ? '#1890ff' : '#52c41a', color: '#fff', marginRight: '8px' }">
                          {{ dialogue.role === 'doctor' ? '医' : '患' }}
                        </a-avatar>
                        <span class="dialogue-timestamp" v-if="dialogue.timestamp">
                          {{ formatDateTime(dialogue.timestamp) }}
                        </span>
                      </div>
                      <div class="dialogue-text markdown-body" v-html="renderMarkdown(dialogue.content || '')"></div>
                    </div>
                  </div>
                  <a-empty v-else description="无对话记录" />
                </a-tab-pane>
                <a-tab-pane key="reasoning" tab="推理过程" v-if="entry.reasoning && entry.reasoning.trim() !== ''">
                  <div class="section-content markdown-body" v-html="renderMarkdown(entry.reasoning)"></div>
                </a-tab-pane>
                <a-tab-pane key="strategy" tab="问诊策略" v-if="entry.strategy && entry.strategy.trim() !== ''">
                  <div class="section-content markdown-body" v-html="renderMarkdown(entry.strategy)"></div>
                </a-tab-pane>
                <a-tab-pane key="observation" tab="观察记录" v-if="entry.observation && entry.observation.trim() !== ''">
                  <div class="section-content markdown-body" v-html="renderMarkdown(entry.observation)"></div>
                </a-tab-pane>
                <a-tab-pane key="feedback" tab="AI反馈" v-if="entry.feedback && entry.feedback.trim() !== ''">
                  <div class="section-content markdown-body" v-html="renderMarkdown(entry.feedback)"></div>
                </a-tab-pane>
              </a-tabs>
            </div>
          </div>
          
          <!-- 推理过程 -->
          <div v-if="selectedCase.interaction_history.interaction_history.reasoning_process" class="section">
            <h4>推理过程</h4>
            <div class="section-content markdown-body" v-html="renderMarkdown(selectedCase.interaction_history.interaction_history.reasoning_process)"></div>
          </div>
          
          <!-- 疾病情况 -->
          <div v-if="selectedCase.interaction_history.interaction_history.condition" class="section">
            <h4>患者病情描述</h4>
            <div class="section-content markdown-body" v-html="renderMarkdown(selectedCase.interaction_history.interaction_history.condition)"></div>
          </div>
          
          <!-- 诊断结果 -->
          <div v-if="selectedCase.interaction_history.interaction_history.diagnosis" class="section">
            <h4>诊断结果</h4>
            <div class="section-content markdown-body" v-html="renderMarkdown(selectedCase.interaction_history.interaction_history.diagnosis)"></div>
          </div>
          
          <!-- 初步诊断 -->
          <div v-if="selectedCase.interaction_history.interaction_history.preliminary_diagnosis" class="section">
            <h4>初步诊断</h4>
            <div class="section-content markdown-body" v-html="renderMarkdown(selectedCase.interaction_history.interaction_history.preliminary_diagnosis)"></div>
          </div>
          
          <!-- 检查建议 -->
          <div class="section-wrapper" v-if="selectedCase && selectedCase.interaction_history && selectedCase.interaction_history.interaction_history && selectedCase.interaction_history.interaction_history.inspection_suggestions">
            <h4>检查建议</h4>
            <div class="section-content markdown-body">
              <div v-if="typeof selectedCase.interaction_history.interaction_history.inspection_suggestions === 'string'">
                <div v-html="renderMarkdown(selectedCase.interaction_history.interaction_history.inspection_suggestions)"></div>
              </div>
              <div v-else-if="Array.isArray(selectedCase.interaction_history.interaction_history.inspection_suggestions)">
                <ul class="inspection-list">
                  <li v-for="(item, index) in selectedCase.interaction_history.interaction_history.inspection_suggestions" :key="`insp-${index}`">
                    <div v-if="typeof item === 'string'" v-html="renderMarkdown(item)"></div>
                    <div v-else-if="typeof item === 'object'" class="inspection-item-object">
                      <div v-for="(value, key) in item" :key="key" class="inspection-detail">
                        <span class="inspection-label">{{ key }}:</span>
                        <span class="inspection-value">{{ value }}</span>
                      </div>
                    </div>
                  </li>
                </ul>
              </div>
              <div v-else>
                <pre>{{ JSON.stringify(selectedCase.interaction_history.interaction_history.inspection_suggestions, null, 2) }}</pre>
              </div>
            </div>
          </div>
          
          <!-- 检查结果 -->
          <div v-if="selectedCase.interaction_history.interaction_history.test_recommendation && selectedCase.interaction_history.interaction_history.test_recommendation.length > 0" class="section">
            <h4>检查结果</h4>
            <!-- 处理结构化的检查报告数据 -->
            <div v-for="(test, testIdx) in selectedCase.interaction_history.interaction_history.test_recommendation" :key="`test-${testIdx}`" class="test-report">
              <a-collapse :key="`ocr-case-collapse-${testIdx}`" accordion>
                <a-collapse-panel :key="1" :header="getItemName(test) || '检查报告详情'">
                  <a-tabs size="small">
                    <a-tab-pane key="parsed" tab="解析数据">
                      <template v-if="typeof test === 'object' && !test.content">
                        <!-- 基础信息部分 -->
                        <div v-if="test.基础信息 || test['基础信息']" class="test-info">
                          <h5>基础信息</h5>
                          <div class="info-table">
                            <div v-for="(value, key) in (test.基础信息 || test['基础信息'])" :key="key" class="info-row">
                              <span class="info-label">{{ key }}:</span>
                              <span class="info-value">{{ value || '-' }}</span>
                            </div>
                          </div>
                        </div>
                        <!-- 检查结果部分 - 改为分组表格布局 -->
                        <div v-if="test.检查结果 || test['检查结果']" class="test-results">
                          <h5>检查结果</h5>
                          
                          <!-- 结果过滤和搜索 -->
                          <div class="result-controls">
                            <a-radio-group v-model:value="resultFilter" button-style="solid" size="small">
                              <a-radio-button value="all">全部</a-radio-button>
                              <a-radio-button value="abnormal">异常</a-radio-button>
                              <a-radio-button value="normal">正常</a-radio-button>
                            </a-radio-group>
                            
                            <a-input-search 
                              placeholder="搜索检查项目" 
                              style="width: 200px; margin-left: 16px"
                              size="small"
                              v-model:value="searchQuery"
                              @change="onSearchChange"
                              allow-clear
                            />
                            
                            <a-button
                              type="primary"
                              size="small"
                              style="margin-left: 16px"
                              @click="showAllResults(test.检查结果 || test['检查结果'])"
                            >
                              全部查看
                            </a-button>
                          </div>
                          
                          <!-- 检查结果分组表格 -->
                          <div class="result-grouped-table">
                            <a-empty 
                              v-if="filteredAndSearchedResults(test.检查结果 || test['检查结果']).length === 0"
                              description="无匹配结果" 
                              :image-style="{ height: '40px' }" 
                            />
                            
                            <template v-else>
                              <a-collapse 
                                :bordered="false" 
                                expand-icon-position="right"
                                v-model:activeKey="activeCollapseKeys"
                              >
                                <!-- 标准检验项目组 -->
                                <a-collapse-panel v-if="getResultsByCategory(filteredAndSearchedResults(test.检查结果 || test['检查结果']), '标准检验').length > 0" 
                                  key="standard" 
                                  header="常规检验项目"
                                  :style="customPanelStyle"
                                >
                                  <a-table
                                    :columns="resultTableColumns"
                                    :data-source="getResultsByCategory(filteredAndSearchedResults(test.检查结果 || test['检查结果']), '标准检验')"
                                    size="small"
                                    :pagination="false"
                                    :row-class-name="getRowClassName"
                                  >
                                    <template #bodyCell="{ column, record }">
                                      <template v-if="column.key === 'itemName'">
                                        {{ getItemName(record) }}
                                      </template>
                                      <template v-if="column.key === 'result'">
                                        <span :class="{'abnormal-result': record.异常标记 || record.结果状态 === '异常' || record.结果状态 === '偏高' || record.结果状态 === '偏低'}">
                                          {{ record.结果 }} {{ record.单位 || '' }}
                                        </span>
                                        <span v-if="record.异常标记" class="abnormal-marker">{{ record.异常标记 }}</span>
                                      </template>
                                      <template v-if="column.key === 'status'">
                                        <a-tag size="small" :color="getResultStatusColor(record.结果状态)">
                                          {{ record.结果状态 || '未知' }}
                                        </a-tag>
                                      </template>
                                      <template v-if="column.key === 'action'">
                                        <a-button 
                                          v-if="record.检查描述 || record.诊断 || record.处理建议"
                                          type="link" 
                                          size="small"
                                          @click="showResultDetail(record)"
                                        >
                                          详情
                                        </a-button>
                                      </template>
                                    </template>
                                  </a-table>
                                </a-collapse-panel>
                                
                                <!-- 影像检查组 -->
                                <a-collapse-panel v-if="getResultsByCategory(filteredAndSearchedResults(test.检查结果 || test['检查结果']), '影像检查').length > 0" 
                                  key="imaging" 
                                  header="影像检查"
                                  :style="customPanelStyle"
                                >
                                  <a-table
                                    :columns="resultTableColumns"
                                    :data-source="getResultsByCategory(filteredAndSearchedResults(test.检查结果 || test['检查结果']), '影像检查')"
                                    size="small"
                                    :pagination="false"
                                    :row-class-name="getRowClassName"
                                  >
                                    <template #bodyCell="{ column, record }">
                                      <template v-if="column.key === 'itemName'">
                                        {{ getItemName(record) }}
                                      </template>
                                      <template v-if="column.key === 'result'">
                                        <span :class="{'abnormal-result': record.异常标记 || record.结果状态 === '异常' || record.结果状态 === '偏高' || record.结果状态 === '偏低'}">
                                          {{ record.结果 }} {{ record.单位 || '' }}
                                        </span>
                                        <span v-if="record.异常标记" class="abnormal-marker">{{ record.异常标记 }}</span>
                                      </template>
                                      <template v-if="column.key === 'status'">
                                        <a-tag size="small" :color="getResultStatusColor(record.结果状态)">
                                          {{ record.结果状态 || '未知' }}
                                        </a-tag>
                                      </template>
                                      <template v-if="column.key === 'action'">
                                        <a-button 
                                          v-if="record.检查描述 || record.诊断 || record.处理建议"
                                          type="link" 
                                          size="small"
                                          @click="showResultDetail(record)"
                                        >
                                          详情
                                        </a-button>
                                      </template>
                                    </template>
                                  </a-table>
                                </a-collapse-panel>
                                
                                <!-- 特殊检查组 -->
                                <a-collapse-panel v-if="getResultsByCategory(filteredAndSearchedResults(test.检查结果 || test['检查结果']), '特殊检查').length > 0" 
                                  key="special" 
                                  header="特殊检查"
                                  :style="customPanelStyle"
                                >
                                  <a-table
                                    :columns="resultTableColumns"
                                    :data-source="getResultsByCategory(filteredAndSearchedResults(test.检查结果 || test['检查结果']), '特殊检查')"
                                    size="small"
                                    :pagination="false"
                                    :row-class-name="getRowClassName"
                                  >
                                    <template #bodyCell="{ column, record }">
                                      <template v-if="column.key === 'itemName'">
                                        {{ getItemName(record) }}
                                      </template>
                                      <template v-if="column.key === 'result'">
                                        <span :class="{'abnormal-result': record.异常标记 || record.结果状态 === '异常' || record.结果状态 === '偏高' || record.结果状态 === '偏低'}">
                                          {{ record.结果 }} {{ record.单位 || '' }}
                                        </span>
                                        <span v-if="record.异常标记" class="abnormal-marker">{{ record.异常标记 }}</span>
                                      </template>
                                      <template v-if="column.key === 'status'">
                                        <a-tag size="small" :color="getResultStatusColor(record.结果状态)">
                                          {{ record.结果状态 || '未知' }}
                                        </a-tag>
                                      </template>
                                      <template v-if="column.key === 'action'">
                                        <a-button 
                                          v-if="record.检查描述 || record.诊断 || record.处理建议"
                                          type="link" 
                                          size="small"
                                          @click="showResultDetail(record)"
                                        >
                                          详情
                                        </a-button>
                                      </template>
                                    </template>
                                  </a-table>
                                </a-collapse-panel>
                                
                                <!-- 其他检查组 -->
                                <a-collapse-panel v-if="getResultsByCategory(filteredAndSearchedResults(test.检查结果 || test['检查结果']), '其他').length > 0" 
                                  key="other" 
                                  header="其他检查项目"
                                  :style="customPanelStyle"
                                >
                                  <a-table
                                    :columns="resultTableColumns"
                                    :data-source="getResultsByCategory(filteredAndSearchedResults(test.检查结果 || test['检查结果']), '其他')"
                                    size="small"
                                    :pagination="false"
                                    :row-class-name="getRowClassName"
                                  >
                                    <template #bodyCell="{ column, record }">
                                      <template v-if="column.key === 'itemName'">
                                        {{ getItemName(record) }}
                                      </template>
                                      <template v-if="column.key === 'result'">
                                        <span :class="{'abnormal-result': record.异常标记 || record.结果状态 === '异常' || record.结果状态 === '偏高' || record.结果状态 === '偏低'}">
                                          {{ record.结果 }} {{ record.单位 || '' }}
                                        </span>
                                        <span v-if="record.异常标记" class="abnormal-marker">{{ record.异常标记 }}</span>
                                      </template>
                                      <template v-if="column.key === 'status'">
                                        <a-tag size="small" :color="getResultStatusColor(record.结果状态)">
                                          {{ record.结果状态 || '未知' }}
                                        </a-tag>
                                      </template>
                                      <template v-if="column.key === 'action'">
                                        <a-button 
                                          v-if="record.检查描述 || record.诊断 || record.处理建议"
                                          type="link" 
                                          size="small"
                                          @click="showResultDetail(record)"
                                        >
                                          详情
                                        </a-button>
                                      </template>
                                    </template>
                                  </a-table>
                                </a-collapse-panel>
                              </a-collapse>
                            </template>
                          </div>
                        </div>
                        <!-- 其他字段 -->
                        <div v-for="(value, key) in test" :key="key" v-if="key !== '基础信息' && key !== '检查结果' && key !== 'content'">
                          <h5>{{ key }}</h5>
                          <div class="section-content markdown-body" v-html="renderMarkdown(JSON.stringify(value, null, 2))"></div>
                        </div>
                      </template>
                      <!-- 处理普通文本或其他格式 -->
                      <div v-else class="section-content markdown-body" v-html="renderMarkdown(typeof test === 'object' ? (test.content || JSON.stringify(test)) : (test || ''))"></div>
                    </a-tab-pane>
                    <a-tab-pane key="raw" tab="原始数据">
                      <pre style="white-space: pre-wrap; word-wrap: break-word; max-height: 400px; overflow-y: auto;">{{ JSON.stringify(test, null, 2) }}</pre>
                    </a-tab-pane>
                  </a-tabs>
                </a-collapse-panel>
              </a-collapse>
            </div>
          </div>
          
          <!-- 治疗建议 -->
          <div v-if="selectedCase.interaction_history.interaction_history.treatment_recommendation" class="section">
            <h4>治疗建议</h4>
            <div v-if="typeof selectedCase.interaction_history.interaction_history.treatment_recommendation === 'string'" class="section-content markdown-body" v-html="renderMarkdown(selectedCase.interaction_history.interaction_history.treatment_recommendation)"></div>
            <ul v-else-if="Array.isArray(selectedCase.interaction_history.interaction_history.treatment_recommendation)" class="recommendation-list markdown-body">
              <li v-for="(treat, treatIdx) in selectedCase.interaction_history.interaction_history.treatment_recommendation" :key="`treat-${treatIdx}`" v-html="renderMarkdown(typeof treat === 'object' ? (treat.content || JSON.stringify(treat)) : (treat || ''))"></li>
            </ul>
          </div>
          
          <!-- 医生补充信息 -->
          <div v-if="selectedCase.interaction_history.interaction_history.doctor_supplementary_info && selectedCase.interaction_history.interaction_history.doctor_supplementary_info.length > 0" class="section">
            <h4>医生补充信息</h4>
            <ul class="recommendation-list markdown-body">
              <li v-for="(info, infoIdx) in selectedCase.interaction_history.interaction_history.doctor_supplementary_info" :key="`info-${infoIdx}`" v-html="renderMarkdown(typeof info === 'object' ? (info.content || JSON.stringify(info)) : (info || ''))"></li>
            </ul>
          </div>
          
          <!-- 治疗指导 -->
          <div v-if="selectedCase.interaction_history.interaction_history.treatment_guide" class="section">
            <h4>治疗指导参考</h4>
            <div class="section-content markdown-body">
              <div v-if="selectedCase.interaction_history.interaction_history.treatment_guide.query">
                <h5>查询参数</h5>
                <pre>{{ JSON.stringify(selectedCase.interaction_history.interaction_history.treatment_guide.query, null, 2) }}</pre>
              </div>
              <div v-if="selectedCase.interaction_history.interaction_history.treatment_guide.results && selectedCase.interaction_history.interaction_history.treatment_guide.results.length > 0">
                <h5>参考结果</h5>
                <div v-for="(result, rIdx) in selectedCase.interaction_history.interaction_history.treatment_guide.results" :key="`result-${rIdx}`" class="guide-result">
                  <div><strong>标题:</strong> {{ result.payload.title }}</div>
                  <div><strong>主题:</strong> {{ result.payload.topic }}</div>
                  <div><strong>主要疾病:</strong> {{ result.payload.main_disease }}</div>
                  <div><strong>诊断症状:</strong> {{ result.payload.diagnostic_symptoms }}</div>
                  <a-collapse v-if="result.payload.relevant_content && result.payload.relevant_content.length > 0">
                    <a-collapse-panel key="1" header="相关内容">
                      <div v-for="(content, cIdx) in result.payload.relevant_content" :key="`content-${cIdx}`" class="relevant-content">
                        <h6>{{ content.title }}</h6>
                        <div class="markdown-body" v-html="renderMarkdown(content.content)"></div>
                      </div>
                    </a-collapse-panel>
                  </a-collapse>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else>
          <a-empty description="案例中无交互历史数据" />
        </div>
        <!-- 添加底部操作栏 -->
        <div class="drawer-footer">
          <a-button type="primary" size="large" @click="drawerVisible = false">返回列表</a-button>
        </div>
      </div>
      <div v-else>
        <a-skeleton active :paragraph="{ rows: 10 }" />
      </div>
    </a-drawer>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted, nextTick, h } from 'vue'
import request from '@/utils/request'
import { message } from 'ant-design-vue'
import { marked } from 'marked'
import { Modal, Empty } from 'ant-design-vue'
import { resolveComponent } from 'vue'
import { DownOutlined, UpOutlined } from '@ant-design/icons-vue'

export default defineComponent({
  name: 'AllCaseHistory',
  components: {
    DownOutlined,
    UpOutlined
  },
  setup() {
    const loading = ref(false)
    const caseItems = ref([])
    const total = ref(0)
    const drawerVisible = ref(false)
    const selectedCase = ref(null)
    const renderDrawerContent = ref(false)
    const searchDateRange = ref([])
    
    const pagination = ref({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      pageSizeOptions: ['10', '20', '50', '100'],
    })
    
    const columns = [
      { title: '案例ID', dataIndex: 'id', key: 'id' },
      { title: '创建时间', dataIndex: 'createdAt', key: 'createdAt' },
      { title: '更新时间', dataIndex: 'updatedAt', key: 'updatedAt' },
      { title: '诊断结果', dataIndex: 'diagnosis', key: 'diagnosis', ellipsis: true },
      { title: '对话轮次', key: 'cotCount' },
      { title: '案例状态', key: 'caseStatus' },
      { title: '操作', key: 'action' }
    ]
    
    const fetchData = async (page = 1, size = 10) => {
      loading.value = true
      try {
        const params = {
          page,
          size,
          startDate: searchDateRange.value?.[0]?.toISOString(),
          endDate: searchDateRange.value?.[1]?.toISOString()
        }

        const res = await request({ 
          url: '/case-api/message/get_all_interaction_histories', 
          method: 'get',
          params
        })
        if (res && res.content) {
          caseItems.value = res.content.list || []
          pagination.value.total = res.content.total || 0
          pagination.value.current = res.content.pageNum || 1
          pagination.value.pageSize = res.content.pageSize || 10
        } else {
          caseItems.value = []
          pagination.value.total = 0
        }
      } catch (error) {
        console.error("获取案例数据失败:", error)
        caseItems.value = []
        pagination.value.total = 0
      } finally {
        loading.value = false
      }
    }

    const handleTableChange = (pager) => {
      pagination.value = pager
      fetchData(pager.current, pager.pageSize)
    }

    const search = () => {
      pagination.value.current = 1
      fetchData(pagination.value.current, pagination.value.pageSize)
    }

    const clearSearch = () => {
      searchDateRange.value = []
      search()
    }
    
    const showCaseDetail = (caseItem) => {
      selectedCase.value = caseItem
      drawerVisible.value = true
      renderDrawerContent.value = false
    }
    
    const onDrawerVisibleChange = (open) => {
      if (open && selectedCase.value) {
        nextTick(() => { renderDrawerContent.value = true })
      } else if (!open) {
        renderDrawerContent.value = false
        selectedCase.value = null
      }
    }
    
    const renderMarkdown = (markdownText) => {
      if (!markdownText) return ''
      try {
        // 添加类型检查，确保传入的是字符串类型
        if (typeof markdownText !== 'string') {
          markdownText = JSON.stringify(markdownText, null, 2)
        }
        return marked.parse(markdownText)
      } catch (e) {
        console.error("Error rendering Markdown:", e)
        return `<p style="color:red;">渲染Markdown出错</p><pre>${String(markdownText).substr(0, 500)}</pre>`
      }
    }
    
    const formatDate = (dateStr) => {
      if (!dateStr) return '-'
      try {
        const date = new Date(dateStr)
        return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())} ${padZero(date.getHours())}:${padZero(date.getMinutes())}`
      } catch (e) { return dateStr }
    }
    
    const formatDateTime = (dateStr) => {
      if (!dateStr) return '-'
      try {
        const date = new Date(dateStr)
        return `${date.getFullYear()}-${padZero(date.getMonth() + 1)}-${padZero(date.getDate())} ${padZero(date.getHours())}:${padZero(date.getMinutes())}:${padZero(date.getSeconds())}`
      } catch (e) { return dateStr }
    }
    
    const padZero = (num) => (num < 10 ? `0${num}` : num)
    
    const getResultStatusColor = (status) => {
      if (!status) return 'default';
      switch(status) {
        case '正常': return 'green';
        case '异常': return 'red';
        case '偏高': return 'orange';
        case '偏低': return 'blue';
        default: return 'default';
      }
    };
    
    const resultFilter = ref('all');
    
    const collapsedCards = ref({});
    
    const toggleCardCollapse = (index) => {
      collapsedCards.value[index] = !collapsedCards.value[index];
    };
    
    const getItemName = (result) => {
      // 优先使用"项目名称"字段
      if (result.项目名称 && result.项目名称.trim() !== '') {
        return result.项目名称;
      }
      // 其次使用"检查"字段
      if (result.检查 && result.检查.trim() !== '') {
        return result.检查;
      }
      // 最后返回默认值
      return '未知项目';
    };
    
    const filteredResults = (results) => {
      if (!results) return [];
      
      if (resultFilter.value === 'all') {
        return results;
      } else if (resultFilter.value === 'abnormal') {
        return results.filter(r => 
          r.异常标记 || 
          r.结果状态 === '异常' || 
          r.结果状态 === '偏高' || 
          r.结果状态 === '偏低'
        );
      } else if (resultFilter.value === 'normal') {
        return results.filter(r => 
          !r.异常标记 && 
          r.结果状态 !== '异常' && 
          r.结果状态 !== '偏高' && 
          r.结果状态 !== '偏低'
        );
      }
      return results;
    };
    
    const showResultDetail = (result) => {
      // 构建详情内容
      let detailContent = '';
      if (result.检查描述) {
        detailContent += `<div><strong>检查描述:</strong> ${result.检查描述}</div>`;
      }
      if (result.诊断) {
        detailContent += `<div><strong>诊断:</strong> ${result.诊断}</div>`;
      }
      if (result.处理建议) {
        detailContent += `<div><strong>处理建议:</strong> ${result.处理建议}</div>`;
      }
      
      // 显示详情弹窗
      if (detailContent) {
        // 使用Modal或其他方式展示详情
        const modal = Modal.info({
          title: `${result.项目名称 || '检查项'} 详细信息`,
          content: h('div', {
            innerHTML: detailContent,
            style: {
              maxHeight: '400px',
              overflow: 'auto'
            }
          }),
          width: '600px'
        });
      }
    };
    
    const showAllResults = (results) => {
      if (!results || results.length === 0) {
        message.info('没有检查结果可显示');
        return;
      }
      
      // 创建表格列定义
      const columns = [
        {
          title: '检查项目',
          dataIndex: 'displayName',
          width: '30%',
          sorter: (a, b) => (a.displayName || '').localeCompare(b.displayName || '')
        },
        {
          title: '结果值',
          dataIndex: 'result_display',
          width: '20%',
          customRender: ({ record }) => {
            const isAbnormal = record.异常标记 || 
                              record.结果状态 === '异常' || 
                              record.结果状态 === '偏高' || 
                              record.结果状态 === '偏低';
            
            return h('div', [
              h('span', {
                class: isAbnormal ? 'abnormal-result' : '',
                style: { marginRight: '4px' }
              }, `${record.结果 || '-'} ${record.单位 || ''}`),
              record.异常标记 ? h('span', { class: 'abnormal-marker' }, record.异常标记) : null
            ]);
          }
        },
        {
          title: '参考范围',
          dataIndex: '参考范围',
          width: '25%'
        },
        {
          title: '状态',
          dataIndex: '结果状态',
          width: '15%',
          customRender: ({ text }) => {
            return h(resolveComponent('a-tag'), {
              color: getResultStatusColor(text)
            }, () => text || '未知');
          },
          filters: [
            { text: '正常', value: '正常' },
            { text: '异常', value: '异常' },
            { text: '偏高', value: '偏高' },
            { text: '偏低', value: '偏低' }
          ],
          onFilter: (value, record) => record.结果状态 === value
        },
        {
          title: '详情',
          width: '10%',
          customRender: ({ record }) => {
            if (!record.检查描述 && !record.诊断 && !record.处理建议) {
              return null;
            }
            
            return h(resolveComponent('a-button'), {
              type: 'link',
              size: 'small',
              onClick: (e) => {
                e.stopPropagation();
                showResultDetail(record);
              }
            }, () => '查看');
          }
        }
      ];
      
      // 创建数据源，添加key属性和显示名称
      const dataSource = results.map((item, index) => ({
        ...item,
        key: `result-${index}`,
        displayName: getItemName(item)
      }));
      
      // 创建模态框
      Modal.info({
        title: '检查结果汇总',
        content: h('div', { style: { width: '100%', maxHeight: '70vh', overflow: 'auto' } }, [
          h('div', { class: 'search-section', style: { marginBottom: '16px' } }, [
            h(resolveComponent('a-input-search'), {
              placeholder: '搜索检查项目',
              allowClear: true,
              style: { width: '300px' },
              onChange: (e) => {
                // 直接搜索功能，不需要更新表格数据的ref
                const value = e.target.value.toLowerCase();
                const tableElement = document.querySelector('.search-result-table');
                if (tableElement) {
                  const rows = tableElement.querySelectorAll('tbody tr');
                  rows.forEach(row => {
                    const nameCell = row.querySelector('td:first-child');
                    if (nameCell) {
                      const name = nameCell.textContent.toLowerCase();
                      row.style.display = name.includes(value) ? '' : 'none';
                    }
                  });
                }
              }
            })
          ]),
          h(resolveComponent('a-table'), {
            columns,
            dataSource,
            size: 'small',
            pagination: { pageSize: 10, showSizeChanger: true },
            rowClassName: (record) => {
              if (record.结果状态 === '异常') return 'result-row-abnormal';
              if (record.结果状态 === '偏高') return 'result-row-high';
              if (record.结果状态 === '偏低') return 'result-row-low';
              return '';
            },
            scroll: { y: 400 },
            class: 'search-result-table'
          })
        ]),
        width: 900,
        maskClosable: true,
        okText: '关闭'
      });
    };
    
    const searchQuery = ref('')
    const activeCollapseKeys = ref(['standard', 'imaging', 'special', 'other'])
    const customPanelStyle = {
      background: '#f7f7f7',
      borderRadius: '4px',
      marginBottom: '16px',
      border: '0px'
    }
    
    const resultTableColumns = [
      { title: '检查项目', key: 'itemName', width: '30%' },
      { title: '结果', key: 'result', width: '30%' },
      { title: '参考范围', dataIndex: '参考范围', width: '25%' },
      { title: '状态', key: 'status', width: '10%' },
      { title: '', key: 'action', width: '5%' }
    ]
    
    // 搜索筛选函数
    const onSearchChange = (e) => {
      searchQuery.value = e.target.value;
    };
    
    // 添加搜索过滤逻辑
    const filteredAndSearchedResults = (results) => {
      if (!results) return [];
      
      let filtered = results;
      
      // 先按状态过滤
      if (resultFilter.value === 'abnormal') {
        filtered = filtered.filter(r => 
          r.异常标记 || 
          r.结果状态 === '异常' || 
          r.结果状态 === '偏高' || 
          r.结果状态 === '偏低'
        );
      } else if (resultFilter.value === 'normal') {
        filtered = filtered.filter(r => 
          !r.异常标记 && 
          r.结果状态 !== '异常' && 
          r.结果状态 !== '偏高' && 
          r.结果状态 !== '偏低'
        );
      }
      
      // 再按搜索词过滤
      if (searchQuery.value && searchQuery.value.trim() !== '') {
        const query = searchQuery.value.toLowerCase().trim();
        filtered = filtered.filter(r => {
          const name = getItemName(r).toLowerCase();
          return name.includes(query);
        });
      }
      
      return filtered;
    };
    
    // 按检查类别分组
    const getResultsByCategory = (results, category) => {
      if (!results) return [];
      
      // 为每个结果添加key
      results = results.map((item, index) => ({
        ...item,
        key: `result-${index}`
      }));
      
      // 根据检查项目名称或特征将结果分类
      if (category === '标准检验') {
        return results.filter(r => {
          const name = getItemName(r).toLowerCase();
          return name.includes('血') || 
                 name.includes('尿') || 
                 name.includes('常规') || 
                 name.includes('生化') || 
                 name.includes('细胞') ||
                 (r.检查类型 && r.检查类型.includes('常规检验'));
        });
      } else if (category === '影像检查') {
        return results.filter(r => {
          const name = getItemName(r).toLowerCase();
          return name.includes('ct') || 
                 name.includes('mri') || 
                 name.includes('超声') || 
                 name.includes('x线') || 
                 name.includes('造影') ||
                 name.includes('影像') ||
                 (r.检查类型 && r.检查类型.includes('影像'));
        });
      } else if (category === '特殊检查') {
        return results.filter(r => {
          const name = getItemName(r).toLowerCase();
          return name.includes('病理') || 
                 name.includes('免疫') || 
                 name.includes('基因') || 
                 name.includes('微生物') ||
                 (r.检查类型 && r.检查类型.includes('特殊'));
        });
      } else {
        // 其他所有未分类的项目
        return results.filter(r => {
          const name = getItemName(r).toLowerCase();
          return !name.includes('血') && 
                 !name.includes('尿') && 
                 !name.includes('常规') && 
                 !name.includes('生化') && 
                 !name.includes('细胞') &&
                 !name.includes('ct') && 
                 !name.includes('mri') && 
                 !name.includes('超声') && 
                 !name.includes('x线') && 
                 !name.includes('造影') &&
                 !name.includes('影像') &&
                 !name.includes('病理') && 
                 !name.includes('免疫') && 
                 !name.includes('基因') && 
                 !name.includes('微生物') &&
                 !(r.检查类型 && (
                   r.检查类型.includes('常规检验') || 
                   r.检查类型.includes('影像') || 
                   r.检查类型.includes('特殊')
                 ));
        });
      }
    };
    
    // 获取表格行类名
    const getRowClassName = (record) => {
      if (record.结果状态 === '异常') return 'result-row-abnormal';
      if (record.结果状态 === '偏高') return 'result-row-high';
      if (record.结果状态 === '偏低') return 'result-row-low';
      return '';
    };
    
    onMounted(() => {
      fetchData()
    })
    
    return {
      loading, caseItems, columns, pagination, drawerVisible, selectedCase, renderDrawerContent,
      searchDateRange, search, clearSearch,
      handleTableChange, showCaseDetail, onDrawerVisibleChange, 
      renderMarkdown, formatDate, formatDateTime, getResultStatusColor, showResultDetail,
      resultFilter, filteredResults, showAllResults, collapsedCards, toggleCardCollapse, getItemName,
      searchQuery, onSearchChange, filteredAndSearchedResults, getResultsByCategory,
      resultTableColumns, activeCollapseKeys, customPanelStyle, getRowClassName,
      fetchData
    }
  }
})
</script>

<style scoped>
.all-cases {
  padding: 24px;
}

.page-title {
  font-size: 24px;
  margin-bottom: 24px;
  color: #333;
}

.filter-controls {
  margin-bottom: 24px;
}

.cases-container {
  background: #fff;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.case-id {
  font-weight: bold;
  color: #1890ff;
}

.drawer-header {
  margin-bottom: 20px;
  background-color: #f9f9f9;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.drawer-header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.drawer-header-top h3 {
  margin-bottom: 0;
  color: #1890ff;
  font-size: 18px;
}

.drawer-header p {
  margin-bottom: 4px;
  color: #666;
}

.case-meta-tags {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.history-container {
  margin-top: 16px;
  max-height: calc(100vh - 280px); /* 调整全屏模式下的高度 */
  overflow-y: auto;
  padding-right: 10px; /* 为滚动条留出空间 */
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  padding: 20px;
}

.history-container h3 {
  margin-bottom: 20px;
  font-size: 18px;
  color: #1890ff;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.history-container h4 {
  font-size: 16px;
  margin-top: 0;
  margin-bottom: 16px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
  color: #1890ff;
}

.section {
  margin-bottom: 24px;
  background-color: transparent;
  border: none;
  border-radius: 0;
  padding: 0;
  box-shadow: none;
}

.section h4 {
  font-size: 16px;
  margin-top: 24px;
  margin-bottom: 16px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
  color: #1890ff;
}

.section:first-of-type h4 {
  margin-top: 0;
}

.cot-entries-section {
  margin-bottom: 24px;
  background-color: transparent;
  border: none;
  border-radius: 0;
  padding: 0;
  box-shadow: none;
}

.cot-entry-section {
  margin-bottom: 16px;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  background-color: #fafafa;
  transition: none;
}

.cot-entry-section:hover {
  box-shadow: 0 3px 6px rgba(0,0,0,0.1);
}

.cot-entry-section h5 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 15px;
  color: #555;
  padding-bottom: 8px;
  border-bottom: 1px dashed #eee;
}

.dialogue-history-log {
  max-height: 350px; /* 增加高度 */
  overflow-y: auto;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  background: #fff;
  box-shadow: inset 0 0 5px rgba(0,0,0,0.05);
}

.dialogue-entry {
  display: flex;
  margin-bottom: 10px;
  align-items: flex-start;
}

.dialogue-speaker {
  margin-right: 8px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.dialogue-text {
  flex-grow: 1;
  padding: 8px 10px;
  border-radius: 6px;
  word-break: break-word;
}

.dialogue-entry.doctor-entry .dialogue-text {
  background-color: #e6f7ff; 
}

.dialogue-entry.patient-entry .dialogue-text {
  background-color: #f6ffed; 
}

.section-content {
  padding: 16px;
  background-color: #fafafa;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-top: 8px;
  line-height: 1.6;
}

.recommendation-list {
  list-style-type: disc;
  padding-left: 20px;
  margin-top: 5px;
}

.recommendation-list li {
  margin-bottom: 8px;
}

.guide-result {
  padding: 12px;
  margin-bottom: 16px;
  background-color: #f5f5f5;
  border-radius: 6px;
}

.guide-result div {
  margin-bottom: 6px;
}

.relevant-content {
  margin-top: 10px;
  padding: 10px;
  background-color: #fff;
  border: 1px solid #eee;
  border-radius: 4px;
}

.relevant-content h6 {
  font-weight: 600;
  margin-bottom: 8px;
  color: #1890ff;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-x: auto;
  max-height: 400px;
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 13px;
}

/* Markdown 样式 */
.markdown-body :deep(h1),
.markdown-body :deep(h2),
.markdown-body :deep(h3),
.markdown-body :deep(h4),
.markdown-body :deep(h5),
.markdown-body :deep(h6) {
  margin-top: 0.8em;
  margin-bottom: 0.4em;
  font-weight: 600;
}
.markdown-body :deep(h1) { font-size: 1.6em; }
.markdown-body :deep(h2) { font-size: 1.4em; }
.markdown-body :deep(h3) { font-size: 1.2em; }

.markdown-body :deep(p) {
  margin-bottom: 0.8em;
  line-height: 1.6;
}

.markdown-body :deep(ul),
.markdown-body :deep(ol) {
  padding-left: 2em;
  margin-bottom: 0.8em;
}

.markdown-body :deep(li) > :deep(p) {
   margin-bottom: 0.2em;
}

.markdown-body :deep(blockquote) {
  margin: 0 0 0.8em 0;
  padding: 0.2em 1em;
  color: #6a737d;
  border-left: 0.25em solid #dfe2e5;
}

.markdown-body :deep(pre) {
  background-color: #f3f3f3;
  border-radius: 4px;
  font-size: 0.9em;
  line-height: 1.45;
  overflow: auto;
  padding: 1em;
  margin-bottom: 0.8em;
}

.markdown-body :deep(code) {
  padding: 0.2em 0.4em;
  margin: 0;
  font-size: 0.9em;
  background-color: #f3f3f3;
  border-radius: 3px;
  font-family: SFMono-Regular, Consolas, 'Liberation Mono', Menlo, Courier, monospace;
}

.markdown-body :deep(table) {
  border-collapse: collapse;
  margin-bottom: 0.8em;
  width: auto;
  display: block;
  overflow-x: auto;
}

.markdown-body :deep(th),
.markdown-body :deep(td) {
  border: 1px solid #ccc;
  padding: 0.4em 0.6em;
}

.markdown-body :deep(tr:nth-child(2n)) {
  background-color: #f6f8fa;
}

.markdown-body :deep(img) {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 0.8em 0;
}

/* 检查建议样式 */
.test-report {
  margin-bottom: 20px;
  padding: 15px;
  border-radius: 6px;
  background-color: #fafafa;
  border: 1px solid #eee;
}

.test-info {
  margin-bottom: 15px;
}

.info-table {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;
}

.info-row {
  width: 50%;
  padding: 4px 0;
  display: flex;
}

.info-label {
  font-weight: 600;
  margin-right: 8px;
  color: #555;
  min-width: 80px;
}

.info-value {
  color: #333;
}

.test-results {
  margin-bottom: 15px;
}

.result-controls {
  margin-bottom: 10px;
}

.result-grouped-table {
  margin-bottom: 15px;
}

.search-section {
  margin-bottom: 16px;
}

.result-row-abnormal {
  background-color: #fff1f0;
}

.result-row-high {
  background-color: #fff7e6;
}

.result-row-low {
  background-color: #e6f7ff;
}

.abnormal-result {
  color: #f5222d;
  font-weight: 600;
}

.abnormal-marker {
  margin-left: 6px;
  color: #f5222d;
}

.result-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding-bottom: 8px;
}

.result-card-collapsed {
  padding: 10px;
  min-height: 42px;
}

.result-card-collapsed .result-card-header {
  padding-bottom: 0;
}

.card-collapse-icon {
  color: #999;
}

.result-status-tag-inline {
  margin-left: 8px;
  vertical-align: middle;
}

.result-card-content {
  margin-top: 8px;
}

@media (max-width: 1200px) {
  .result-compact-card {
    width: calc(50% - 10px);
  }
}

@media (max-width: 768px) {
  .result-compact-card {
    width: 100%;
  }
}

/* 新增样式 */
.result-controls {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.result-grouped-table {
  margin-bottom: 15px;
}

.result-grouped-table :deep(.ant-collapse-header) {
  font-weight: 600;
  color: #1890ff;
  font-size: 15px;
}

.result-grouped-table :deep(.ant-table-thead > tr > th) {
  background-color: #f0f5ff;
  font-weight: 600;
  color: #444;
}

.result-grouped-table :deep(.result-row-abnormal) {
  background-color: #fff1f0;
}

.result-grouped-table :deep(.result-row-high) {
  background-color: #fff7e6;
}

.result-grouped-table :deep(.result-row-low) {
  background-color: #e6f7ff;
}

@media (max-width: 768px) {
  .result-controls {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .result-controls > *:not(:first-child) {
    margin-top: 8px;
    margin-left: 0 !important;
  }
}

.result-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding-bottom: 8px;
}

.dialogue-timestamp {
  font-size: 12px;
  color: #999;
  margin-left: 8px;
}

.dialogue-speaker {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.drawer-footer {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #eee;
  text-align: center;
}

.drawer-footer button {
  min-width: 120px;
}

.all-cases :deep(.ant-drawer-header) {
  padding: 16px 24px;
  background-color: #f0f2f5;
  border-bottom: 1px solid #e8e8e8;
}

.all-cases :deep(.ant-drawer-title) {
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

.all-cases :deep(.ant-drawer-body) {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.inspection-list {
  list-style-type: disc;
  margin-left: 0;
  padding-left: 20px;
}

.inspection-item-object {
  background-color: #f9f9f9;
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 8px;
  border: 1px solid #eee;
}

.inspection-detail {
  margin-bottom: 6px;
  display: flex;
}

.inspection-label {
  font-weight: 600;
  color: #555;
  margin-right: 8px;
  min-width: 100px;
  flex-shrink: 0;
}

.inspection-value {
  color: #333;
  flex-grow: 1;
}
</style> 