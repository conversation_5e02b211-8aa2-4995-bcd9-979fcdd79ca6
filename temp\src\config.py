"""
Configuration settings for the AI Doctor system.
"""
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# OpenAI API configuration
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
OPENAI_MODEL = os.getenv("OPENAI_MODEL", "gpt-4")
OPENAI_TEMPERATURE = float(os.getenv("OPENAI_TEMPERATURE", "0.7"))

# Volcano API configuration (using OpenAI interface)
VOLCANO_API_KEY = os.getenv("VOLCANO_API_KEY")
VOLCANO_API_BASE = os.getenv("VOLCANO_API_BASE", "https://api.volcengine.com/openai/v2")
VOLCANO_MODEL = os.getenv("VOLCANO_MODEL", "deepseek-r1")
VOLCANO_TEMPERATURE_EXECUTOR = float(os.getenv("VOLCANO_TEMPERATURE_EXECUTOR", "0.2"))
VOLCANO_TEMPERATURE_PLANNER = float(os.getenv("VOLCANO_TEMPERATURE_PLANNER", "0.7"))
VOLCANO_TEMPERATURE = float(os.getenv("VOLCANO_TEMPERATURE", "0.7"))

# Claude API configuration
CLAUDE_API_KEY = os.getenv("CLAUDE_API_KEY")
CLAUDE_MODEL = os.getenv("CLAUDE_MODEL", "claude-3-opus-20240229")
CLAUDE_TEMPERATURE = float(os.getenv("CLAUDE_TEMPERATURE", "0.7"))

# Qwen API configuration (using OpenAI interface)
QWEN_API_KEY = os.getenv("QWEN_API_KEY")
QWEN_API_BASE = os.getenv("QWEN_API_BASE", "https://dashscope.aliyuncs.com/compatible-mode/v1")
QWEN_MODEL = os.getenv("QWEN_MODEL", "qwen2.5-72b-instruct")
QWEN_TEMPERATURE = float(os.getenv("QWEN_TEMPERATURE", "1"))
QWEN_STREAM = os.getenv("QWEN_STREAM", "qwen3-235b-a22b")

# Jiuzhang API configuration
JIUZHANG_API_KEY = os.getenv("JIUZHANG_API_KEY")
JIUZHANG_API_BASE = os.getenv("JIUZHANG_API_BASE", "https://api.jiuzhang.com/v1")
JIUZHANG_MODEL = os.getenv("JIUZHANG_MODEL", "deepseek-reasoner")
JIUZHANG_TEMPERATURE = float(os.getenv("JIUZHANG_TEMPERATURE", "1"))

# Together API configuration
TOGETHER_API_KEY = os.getenv("TOGETHER_API_KEY")
TOGETHER_MODEL = os.getenv("TOGETHER_MODEL", "DUO123/deepseek-ai/DeepSeek-R1-8c48fcde")
TOGETHER_TEMPERATURE = float(os.getenv("TOGETHER_TEMPERATURE", "0.7"))

# System settings
MAX_ROUNDS = int(os.getenv("MAX_ROUNDS", "10"))
DEBUG_MODE = os.getenv("DEBUG_MODE", "False").lower() == "true"

# PPIO API configuration
PPIO_API_KEY = os.getenv("PPIO_API_KEY")
PPIO_API_BASE = os.getenv("PPIO_API_BASE", "https://api.ppinfra.com/v3/openai")
PPIO_MODEL = os.getenv("PPIO_MODEL", "deepseek/deepseek-r1-0528")
PPIO_TEMPERATURE = float(os.getenv("PPIO_TEMPERATURE", "0.7"))
