<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语音合成测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #333;
        }
        textarea, input[type="number"], select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        .button-group {
            margin-top: 20px;
            display: flex;
            gap: 10px;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        .convert-btn {
            background-color: #4CAF50;
            color: white;
        }
        .convert-btn:hover {
            background-color: #45a049;
        }
        .advanced-btn {
            background-color: #2196F3;
            color: white;
        }
        .advanced-btn:hover {
            background-color: #1e87dc;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
        }
        .success {
            background-color: #e8f5e9;
            border: 1px solid #c8e6c9;
            color: #2e7d32;
        }
        .error {
            background-color: #ffebee;
            border: 1px solid #ffcdd2;
            color: #c62828;
        }
        .advanced-options {
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            margin-top: 15px;
        }
        audio {
            width: 100%;
            margin-top: 10px;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 10px 0;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>语音合成测试</h1>
        
        <div class="form-group">
            <label for="text">输入文本：</label>
            <textarea id="text" placeholder="请输入要转换的文本"></textarea>
        </div>

        <div class="advanced-options">
            <h3>高级选项</h3>
            <div class="form-group">
                <label for="speedRatio">语速（0.5-2.0）：</label>
                <input type="number" id="speedRatio" value="1.0" min="0.5" max="2.0" step="0.1">
            </div>
            <div class="form-group">
                <label for="volumeRatio">音量（0.5-2.0）：</label>
                <input type="number" id="volumeRatio" value="1.0" min="0.5" max="2.0" step="0.1">
            </div>
            <div class="form-group">
                <label for="pitchRatio">音高（0.5-2.0）：</label>
                <input type="number" id="pitchRatio" value="1.0" min="0.5" max="2.0" step="0.1">
            </div>
            <div class="form-group">
                <label for="voiceType">发音人：</label>
                <select id="voiceType">
                    <option value="BV001_streaming">女声</option>
                    <option value="BV002_streaming">男声</option>
                </select>
            </div>
        </div>

        <div class="button-group">
            <button class="convert-btn" onclick="convertToSpeech(false)">基础转换</button>
            <button class="advanced-btn" onclick="convertToSpeech(true)">高级转换</button>
        </div>

        <div id="loading" class="loading">
            正在转换中，请稍候...
        </div>

        <div id="result" class="result" style="display: none;">
            <div id="resultMessage"></div>
            <audio id="audio" controls style="display: none;">
                您的浏览器不支持 audio 元素。
            </audio>
        </div>
    </div>

    <script>
        async function convertToSpeech(isAdvanced) {
            const text = document.getElementById('text').value.trim();
            if (!text) {
                alert('请输入要转换的文本');
                return;
            }

            const loading = document.getElementById('loading');
            const result = document.getElementById('result');
            const resultMessage = document.getElementById('resultMessage');
            const audio = document.getElementById('audio');

            loading.style.display = 'block';
            result.style.display = 'none';
            audio.style.display = 'none';

            try {
                let response;
                if (isAdvanced) {
                    const data = {
                        text: text,
                        speedRatio: parseFloat(document.getElementById('speedRatio').value),
                        volumeRatio: parseFloat(document.getElementById('volumeRatio').value),
                        pitchRatio: parseFloat(document.getElementById('pitchRatio').value),
                        voiceType: document.getElementById('voiceType').value
                    };

                    response = await fetch('/api/tts/convert/advanced', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(data)
                    });
                } else {
                    response = await fetch(`/api/tts/convert?text=${encodeURIComponent(text)}`, {
                        method: 'POST'
                    });
                }

                const data = await response.json();
                
                result.style.display = 'block';
                if (data.success) {
                    result.className = 'result success';
                    resultMessage.textContent = data.message;
                    audio.src = data.data;
                    audio.style.display = 'block';
                    // 自动播放
                    audio.play();
                } else {
                    result.className = 'result error';
                    resultMessage.textContent = data.message;
                }
            } catch (error) {
                result.style.display = 'block';
                result.className = 'result error';
                resultMessage.textContent = '转换失败：' + error.message;
            } finally {
                loading.style.display = 'none';
            }
        }
    </script>
</body>
</html>