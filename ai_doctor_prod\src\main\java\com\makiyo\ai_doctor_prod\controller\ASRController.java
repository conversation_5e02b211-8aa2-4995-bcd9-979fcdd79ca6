package com.makiyo.ai_doctor_prod.controller;

import com.makiyo.ai_doctor_prod.service.ASRService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.CompletableFuture;

/**
 * 语音识别控制器 (Prod)
 * 提供接口接收前端音频并流式返回识别结果
 */
@Tag(name = "语音识别 (Prod)", description = "语音转文字相关的 API 接口")
@RestController
@RequestMapping("/api/prod/asr")
public class ASRController {
    
    private static final Logger logger = LoggerFactory.getLogger(ASRController.class);

    @Autowired
    private ASRService asrService;
    
    // 从配置文件读取临时目录，与 Service 保持一致
    @Value("${asr.temp.dir:${user.dir}/audio}") 
    private String configuredTempDir;

    private Path tempDirPath;
    
    // 存储 SseEmitter，Key 为 audioId
    private final Map<String, SseEmitter> emitters = new ConcurrentHashMap<>();

    @PostConstruct
    public void initialize() {
        tempDirPath = Paths.get(configuredTempDir);
        try {
            if (!Files.exists(tempDirPath)) {
                Files.createDirectories(tempDirPath);
                logger.info("ASR 临时目录已创建: {}", tempDirPath.toAbsolutePath());
            } else {
                 logger.info("ASR 临时目录已存在: {}", tempDirPath.toAbsolutePath());
            }
        } catch (IOException e) {
            logger.error("无法创建 ASR 临时目录: {}", tempDirPath.toAbsolutePath(), e);
            // 根据需要决定是否抛出异常阻止应用启动
            // throw new RuntimeException("Failed to create ASR temp directory", e);
        }
    }

    /**
     * 接收音频文件，保存并立即开始异步处理
     * 
     * @param audioFile 前端上传的音频文件
     * @return 包含 audioId 的 JSON 响应
     */
    @Operation(summary = "上传音频并获取识别任务ID", description = "上传音频文件，服务器保存文件后立即开始后台识别，并返回用于后续建立 SSE 连接的 audioId。")
    @PostMapping(value = "/upload_and_recognize", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<String> uploadAndRecognizeAudio(@RequestParam("audio") MultipartFile audioFile) {
        if (audioFile == null || audioFile.isEmpty()) {
             // 返回标准的 JSON 错误响应
            return ResponseEntity.badRequest().body("{\"error\": \"音频文件不能为空\"}");
        }

        String originalFilename = audioFile.getOriginalFilename();
        logger.info("收到音频上传请求, 文件名: {}, 大小: {} bytes", originalFilename, audioFile.getSize());

        // 生成唯一 ID 作为文件名基础
        String audioId = UUID.randomUUID().toString();
        String fileExtension = originalFilename != null && originalFilename.contains(".") ? 
                originalFilename.substring(originalFilename.lastIndexOf(".")) : ".tmp"; // 使用 .tmp 或其他默认扩展名
        String savedFileName = audioId + fileExtension;
        Path savedFilePath = tempDirPath.resolve(savedFileName);
        File savedFile = savedFilePath.toFile();

        try {
            // 保存上传的文件到临时目录
            Files.copy(audioFile.getInputStream(), savedFilePath, StandardCopyOption.REPLACE_EXISTING);
            logger.info("音频文件已保存至: {}", savedFilePath.toAbsolutePath());
            
            // 创建 SSE Emitter (设置较长的超时时间，依赖业务逻辑或 WebSocket 超时)
            SseEmitter emitter = new SseEmitter(TimeUnit.MINUTES.toMillis(15)); 
            String emitterId = audioId; // 使用 audioId 作为 emitter 的标识
            
            // 在调用异步服务前先注册 emitter，防止异步处理过快完成而 emitter 未注册
            emitters.put(emitterId, emitter);
            logger.info("为 audioId '{}' 创建并注册了 SSE Emitter", emitterId);

            // 定义清理回调
             // 这个回调会在 onCompletion, onTimeout, onError 时执行
             Runnable cleanupCallback = () -> {
                 logger.info("SSE Emitter (ID: {}) 完成/超时/错误，执行清理回调", emitterId);
                 emitters.remove(emitterId);
                 deleteSafely(savedFile); // 删除临时文件
             };

             // 设置 Emitter 的回调
             emitter.onCompletion(cleanupCallback);
             emitter.onTimeout(cleanupCallback);
             emitter.onError(e -> { // onError 通常在 onCompletion 之前
                 logger.error("SSE Emitter (ID: {}) 发生错误: {}", emitterId, e.getMessage());
                 // cleanupCallback 会被 onCompletion 调用，这里只记录日志
             });

            // 调用异步 ASR 服务处理
            // 确保 ASRService.processAudioFile 是 @Async 或者内部异步执行
             asrService.processAudioFile(savedFile, emitter);
             logger.info("已为 audioId '{}' 提交异步 ASR 处理任务", audioId);

            // 返回 audioId，客户端稍后用此 ID 建立 SSE 连接
             // 返回标准 JSON 格式
            return ResponseEntity.ok("{\"audioId\": \"" + audioId + "\"}");

        } catch (IOException e) {
            logger.error("保存或处理音频文件 '{}' 失败", originalFilename, e);
            deleteSafely(savedFile); // 尝试删除可能已创建的文件
            return ResponseEntity.internalServerError().body("{\"error\": \"处理音频文件失败: " + e.getMessage() + "\"}"); // 返回 JSON
        } catch (Exception e) {
             logger.error("处理音频上传 '{}' 时发生意外错误", originalFilename, e);
             deleteSafely(savedFile); 
            return ResponseEntity.internalServerError().body("{\"error\": \"语音识别请求处理失败: " + e.getMessage() + "\"}"); // 返回 JSON
        }
    }
    
    /**
     * 建立 SSE 连接以获取语音识别结果
     * 
     * @param audioId 之前上传时返回的音频 ID
     * @return SSE 事件发射器
     */
    @Operation(summary = "获取语音识别结果的 SSE 流", description = "使用上传时获取的 audioId 建立 Server-Sent Events 连接，实时接收语音识别结果。")
    @GetMapping(value = "/recognize/stream/{audioId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter getRecognitionResultsStream(@PathVariable String audioId) {
        SseEmitter emitter = emitters.get(audioId);
        
        if (emitter != null) {
            logger.info("为 audioId '{}' 找到并返回现有 SSE Emitter", audioId);
             // 发送一个确认事件，表示 SSE 连接已建立
             try {
                emitter.send(SseEmitter.event().name("sse_connected").data("SSE stream established for " + audioId));
             } catch (IOException e) {
                 // 如果发送失败，可能 emitter 刚刚完成，这是正常的
                logger.warn("尝试发送 SSE 连接确认消息失败 (可能 Emitter 已完成) for audioId '{}'", audioId, e);
             } catch (IllegalStateException e) {
                  // Emitter 可能已经完成了
                  logger.warn("尝试向已完成的 Emitter 发送确认消息 for audioId '{}'", audioId, e);
                 // 返回一个新的、立即完成的 Emitter 可能是更好的选择
                 return createCompletedErrorEmitter("无法连接识别流：任务可能已结束。");
             }
            return emitter;
        } else {
            logger.warn("请求 SSE 流，但未找到 audioId '{}' 对应的 Emitter (可能已完成、超时或无效 ID)", audioId);
            // 返回一个立即完成的 Emitter 并附带错误信息
             return createCompletedErrorEmitter("无法连接识别流：无效的 audioId 或任务已结束。");
        }
    }

    // 创建一个已完成并带有错误信息的 Emitter
    private SseEmitter createCompletedErrorEmitter(String errorMessage) {
        SseEmitter errorEmitter = new SseEmitter(0L); // 0 超时
         try {
             errorEmitter.send(SseEmitter.event()
                     .name("error")
                     .data(errorMessage));
             errorEmitter.complete();
         } catch (Exception e) { // Catch broader exceptions just in case
             logger.error("发送 SSE 错误消息并完成 Emitter 失败", e);
             // Even if sending fails, try to complete
             try { errorEmitter.complete(); } catch (Exception ignored) {}
         }
         return errorEmitter;
    }
    
    // 安全删除文件的方法
    private void deleteSafely(File file) {
        if (file != null) { // 检查 null
            // 在异步线程中删除，避免阻塞当前请求线程
             CompletableFuture.runAsync(() -> { // 使用 CompletableFuture
                 try {
                     if (file.exists()) { // 再次检查存在性
                         boolean deleted = Files.deleteIfExists(file.toPath());
                         if (deleted) {
                             logger.info("[Async Delete] 已删除临时文件: {}", file.getAbsolutePath());
                         } else {
                             logger.warn("[Async Delete] 尝试删除文件但文件不存在或删除失败: {}", file.getAbsolutePath());
                         }
                     } else {
                          logger.debug("[Async Delete] 文件不存在，无需删除: {}", file.getAbsolutePath());
                     }
                 } catch (IOException e) {
                     logger.error("[Async Delete] 删除临时文件时发生 IO 错误: {}", file.getAbsolutePath(), e);
                 } catch (SecurityException e) {
                     logger.error("[Async Delete] 删除临时文件时出现安全异常: {}", file.getAbsolutePath(), e);
                 } catch (Exception e) {
                     logger.error("[Async Delete] 删除临时文件时发生未知错误: {}", file.getAbsolutePath(), e);
                 }
             });
        }
    }
    
    // --- 保留健康检查和 favicon --- 
    @Operation(summary = "健康检查", description = "检查语音识别服务是否正常运行")
    @GetMapping("/health")
    public ResponseEntity<String> healthCheck() {
        // 可以增加更复杂的健康检查逻辑，例如检查 ASRService 依赖状态
        return ResponseEntity.ok("Prod ASR Controller is running.");
    }
    
    @GetMapping("/favicon.ico")
    @ResponseBody // 确保 Spring MVC 知道这是一个响应体
    public void favicon() {
        // 返回空响应体，状态码为 204 No Content (由框架处理)
    }
} 