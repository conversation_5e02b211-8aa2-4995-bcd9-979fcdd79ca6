package com.makiyo.aidoctor.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class InteractionHistoryUtils {

    private static final Logger log = LoggerFactory.getLogger(InteractionHistoryUtils.class);
    // Initialize ObjectMapper once and reuse it
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Transforms the interaction history JSON structure according to specific rules:
     * - Merges all 'dialogue_history' lists from all 'cot_entries' into a single list,
     *   preserving the original order of dialogues.
     * - The final output contains only one 'cot_entry'.
     * - This single 'cot_entry' uses 'feedback', 'observation', 'reasoning', and 'strategy'
     *   all taken from the *last* entry in the input 'cot_entries'.
     * - Ensures the output JSON for the single cot_entry has fields in the order:
     *   dialogue_history, feedback, observation, reasoning, strategy.
     *
     * @param jsonInput The original JSON string containing interaction history.
     * @return The transformed JSON string according to the rules,
     *         or null if transformation fails or input is invalid/malformed.
     */
    public static String transformInteractionHistory(String jsonInput) {
        if (jsonInput == null || jsonInput.trim().isEmpty()) {
            log.warn("Input JSON string is null or empty for transformation.");
            return null;
        }

        try {
            Map<String, Object> rootMap = objectMapper.readValue(jsonInput, new TypeReference<Map<String, Object>>() {});

            if (!rootMap.containsKey("interaction_history")) {
                log.warn("Transform: Input JSON does not contain 'interaction_history' key.");
                return jsonInput;
            }
            Object historyObj = rootMap.get("interaction_history");
            if (!(historyObj instanceof Map)) {
                log.warn("Transform: 'interaction_history' is not a map.");
                return jsonInput;
            }
            Map<String, Object> interactionHistory = objectMapper.convertValue(historyObj, new TypeReference<Map<String, Object>>() {});

            if (!interactionHistory.containsKey("cot_entries")) {
                log.warn("Transform: 'interaction_history' does not contain 'cot_entries' key.");
                return jsonInput;
            }
            Object entriesObj = interactionHistory.get("cot_entries");
            if (!(entriesObj instanceof List)) {
                log.warn("Transform: 'cot_entries' is not a list.");
                return jsonInput;
            }
            List<Map<String, Object>> cotEntries = objectMapper.convertValue(entriesObj, new TypeReference<List<Map<String, Object>>>() {});

            if (cotEntries.isEmpty()) {
                log.info("Transform: 'cot_entries' list is empty.");
                return jsonInput;
            }

            List<Map<String, Object>> mergedDialogueHistory = new ArrayList<>();
            String lastStrategy = "";
            String lastFeedback = "";
            String lastObservation = "";
            String lastReasoning = "";

            for (Map<String, Object> entry : cotEntries) {
                if (entry != null && entry.containsKey("dialogue_history")) {
                    Object dialogueHistObj = entry.get("dialogue_history");
                    if (dialogueHistObj instanceof List) {
                        try {
                            // Use convertValue here for merging as we need copies anyway
                            List<Map<String, Object>> currentDialogues = objectMapper.convertValue(dialogueHistObj, new TypeReference<List<Map<String, Object>>>() {});
                            mergedDialogueHistory.addAll(currentDialogues);
                        } catch (IllegalArgumentException e) {
                            log.warn("Transform: Could not convert dialogue history element.", e);
                        }
                    } else {
                         log.warn("Transform: Dialogue history element is not a List.");
                    }
                }
            }

            Map<String, Object> lastEntry = cotEntries.get(cotEntries.size() - 1);
            if (lastEntry != null) {
                lastFeedback = getStringValue(lastEntry, "feedback");
                lastObservation = getStringValue(lastEntry, "observation");
                lastReasoning = getStringValue(lastEntry, "reasoning");
                lastStrategy = getStringValue(lastEntry, "strategy");
            }

            Map<String, Object> targetEntry = new LinkedHashMap<>();
            targetEntry.put("dialogue_history", mergedDialogueHistory);
            targetEntry.put("feedback", lastFeedback);
            targetEntry.put("observation", lastObservation);
            targetEntry.put("reasoning", lastReasoning);
            targetEntry.put("strategy", lastStrategy);

            List<Map<String, Object>> targetCotEntries = new ArrayList<>();
            targetCotEntries.add(targetEntry);

            Map<String, Object> targetInteractionHistory = new LinkedHashMap<>();
            targetInteractionHistory.put("cot_entries", targetCotEntries);

            Map<String, Object> targetRootMap = new LinkedHashMap<>();
            targetRootMap.put("interaction_history", targetInteractionHistory);

            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(targetRootMap);

        } catch (IOException e) {
            log.error("Error transforming interaction history JSON (IOException): {}", e.getMessage(), e);
            return null;
        } catch (IllegalArgumentException e) {
            log.error("Error converting JSON structure during transformation (IllegalArgumentException): {}", e.getMessage(), e);
            return null;
        } catch (Exception e) {
            log.error("Unexpected error during interaction history transformation: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Adds a new patient message to the dialogue history of the *last* cot_entry
     * within the provided interaction history JSON. Modifies the structure in place.
     *
     * @param jsonInput The current JSON string representing the interaction history.
     * @param patientMessageContent The content of the new message from the patient.
     * @return The updated JSON string with the new message added, or null if an error occurs
     *         or the input is invalid/malformed.
     */
    public static String addPatientMessage(String jsonInput, String patientMessageContent) {
        if (jsonInput == null || jsonInput.trim().isEmpty()) {
            log.warn("Input JSON string is null or empty for adding message.");
            return null;
        }
        if (patientMessageContent == null || patientMessageContent.trim().isEmpty()) {
            log.warn("Patient message content is null or empty. Returning original JSON.");
            return jsonInput; // Return original if message is empty
        }

        try {
            // Parse into generic Maps and Lists to allow direct modification
            Map<String, Object> rootMap = objectMapper.readValue(jsonInput, new TypeReference<Map<String, Object>>() {});

            // --- Navigation and Validation with direct access ---
            if (!rootMap.containsKey("interaction_history") || !(rootMap.get("interaction_history") instanceof Map)) {
                log.error("AddMessage: Invalid or missing 'interaction_history' map.");
                return null;
            }
            @SuppressWarnings("unchecked") // Safe due to instanceof check
            Map<String, Object> interactionHistory = (Map<String, Object>) rootMap.get("interaction_history");

            if (!interactionHistory.containsKey("cot_entries") || !(interactionHistory.get("cot_entries") instanceof List)) {
                log.error("AddMessage: Invalid or missing 'cot_entries' list.");
                return null;
            }
            @SuppressWarnings("unchecked") // Safe due to instanceof check
            List<Map<String, Object>> cotEntries = (List<Map<String, Object>>) interactionHistory.get("cot_entries");

            if (cotEntries.isEmpty()) {
                log.error("AddMessage: 'cot_entries' list is empty. Cannot add message.");
                return null;
            }

            // Get the *last* entry Map directly
            Map<String, Object> lastEntry = cotEntries.get(cotEntries.size() - 1);
            if (lastEntry == null) {
                 log.error("AddMessage: Last entry in 'cot_entries' is null.");
                 return null;
            }

            // Get the dialogue_history list *directly* from the lastEntry map
            List<Map<String, Object>> dialogueHistory;
            Object currentDialogueHistObj = lastEntry.get("dialogue_history");

            if (currentDialogueHistObj instanceof List) {
                // Attempt direct cast if it's a list
                 try {
                    @SuppressWarnings("unchecked") // Safe due to instanceof check
                    List<Map<String, Object>> existingList = (List<Map<String, Object>>) currentDialogueHistObj;
                    // Optional: Add check for list element types if needed
                    dialogueHistory = existingList; // Use the existing list reference
                    log.debug("AddMessage: Using existing dialogue_history list directly.");
                 } catch (ClassCastException e) {
                      log.warn("AddMessage: 'dialogue_history' exists but is not List<Map<String, Object>>. Creating new list.", e);
                      dialogueHistory = new ArrayList<>();
                      lastEntry.put("dialogue_history", dialogueHistory); // Replace malformed list
                 }
            } else {
                // If key doesn't exist or value is not a list, create a new one
                log.warn("AddMessage: Last entry does not contain a valid 'dialogue_history' list. Creating a new one.");
                dialogueHistory = new ArrayList<>();
                lastEntry.put("dialogue_history", dialogueHistory); // Add the new list to the map
            }

            // Create the new patient message map
            Map<String, Object> newMessage = new LinkedHashMap<>(); // Use LinkedHashMap for order
            newMessage.put("content", patientMessageContent);
            newMessage.put("role", "patient");

            // Add the new message to the list (this modifies the list *inside* lastEntry)
            dialogueHistory.add(newMessage);
            log.info("Added patient message to the dialogue_history list in the last cot_entry.");

            // Serialize the *modified* rootMap back to JSON
            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(rootMap);

        } catch (IOException e) {
            log.error("Error adding patient message (IOException): {}", e.getMessage(), e);
            return null;
        } catch (IndexOutOfBoundsException e) {
             log.error("Error accessing cot_entries (IndexOutOfBoundsException): {}", e.getMessage(), e);
             return null;
        } catch (Exception e) { // Catch broader exceptions
            log.error("Unexpected error while adding patient message: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Safely retrieves a String value associated with the given key from the map.
     * Returns an empty string ("") if the map is null, the key is null,
     * the key is not present, the value is null, or the value is not a String.
     *
     * @param map The map from which to retrieve the value.
     * @param key The key whose associated value is to be returned.
     * @return The String value associated with the key, or "" if not found or not a String.
     */
    private static String getStringValue(Map<String, Object> map, String key) {
        if (map == null || key == null) {
            return "";
        }
        Object value = map.get(key);
        if (value instanceof String) {
            return (String) value;
        }
        return "";
    }

    /**
     * Extracts the "observation" content from the second to last entry in the "cot_entries" list
     * within the interaction history JSON.
     *
     * @param jsonInput The JSON string representing the interaction history.
     * @return The content of the second last observation as a String,
     *         or an empty string ("") if not found, if there are fewer than two entries,
     *         or if an error occurs during parsing.
     */
    public static String extractSecondLastObservation(String jsonInput) {
        if (jsonInput == null || jsonInput.trim().isEmpty()) {
            log.warn("extractSecondLastObservation: Input JSON string is null or empty.");
            return "";
        }

        // --- Add JSON Pre-Check ---
        String trimmedJsonInput = jsonInput.trim();
        if (!trimmedJsonInput.startsWith("{") || !trimmedJsonInput.endsWith("}")) {
            log.warn("extractSecondLastObservation: Input string does not appear to be a JSON object. Input: {}", trimmedJsonInput.substring(0, Math.min(trimmedJsonInput.length(), 100)));
            return ""; // Don't attempt to parse non-JSON
        }
        // --- End JSON Pre-Check ---

        try {
            // Now it's likely safe to parse
            Map<String, Object> rootMap = objectMapper.readValue(trimmedJsonInput, new TypeReference<Map<String, Object>>() {});

            if (!rootMap.containsKey("interaction_history") || !(rootMap.get("interaction_history") instanceof Map)) {
                log.warn("extractSecondLastObservation: Invalid or missing 'interaction_history' map.");
                return "";
            }
            @SuppressWarnings("unchecked")
            Map<String, Object> interactionHistory = (Map<String, Object>) rootMap.get("interaction_history");

            if (!interactionHistory.containsKey("cot_entries") || !(interactionHistory.get("cot_entries") instanceof List)) {
                log.warn("extractSecondLastObservation: Invalid or missing 'cot_entries' list.");
                return "";
            }
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> cotEntries = (List<Map<String, Object>>) interactionHistory.get("cot_entries");

            if (cotEntries.size() < 2) {
                log.warn("extractSecondLastObservation: Not enough entries in 'cot_entries' to get the second last observation (found {}).", cotEntries.size());
                return "";
            }

            // Get the second to last entry
            Map<String, Object> secondLastEntry = cotEntries.get(cotEntries.size() - 2);
            if (secondLastEntry == null) {
                log.warn("extractSecondLastObservation: The second last entry in 'cot_entries' is null.");
                return "";
            }

            // Extract the "observation" field using the helper method
            String observation = getStringValue(secondLastEntry, "observation");
            log.info("extractSecondLastObservation: Successfully extracted observation."); // Log without value first for cleaner logs
            log.debug("Extracted observation value: {}", observation); // Log value at debug level
            return observation;

        } catch (IOException e) {
            log.error("extractSecondLastObservation: Error parsing JSON (IOException): {}", e.getMessage(), e);
            return "";
        } catch (IndexOutOfBoundsException e) {
             log.error("extractSecondLastObservation: Error accessing cot_entries (IndexOutOfBoundsException - should not happen with size check): {}", e.getMessage(), e);
             return "";
        } catch (Exception e) { // Catch broader exceptions
            log.error("extractSecondLastObservation: Unexpected error: {}", e.getMessage(), e);
            return "";
        }
    }

    // Example usage - main method updated to demonstrate addPatientMessage
    public static void main(String[] args) {
         String originalJson = """
                          {
                                "interaction_history": {
                                    "cot_entries": [
                                        {
                                            "dialogue_history": [
                                                { "content": "医生: 你好", "role": "doctor" },
                                                { "content": "患者: 我发烧了", "role": "patient" }
                                            ],
                                            "feedback": "FIRST feedback",
                                            "observation": "FIRST observation",
                                            "reasoning": "FIRST reasoning",
                                            "strategy": "FIRST strategy"
                                        },
                                        {
                                            "dialogue_history": [
                                                { "content": "医生: 发烧多久了?", "role": "doctor" },
                                                { "content": "患者: 三天了", "role": "patient" }
                                            ],
                                            "feedback": "LAST feedback",
                                            "observation": "LAST observation",
                                            "reasoning": "LAST reasoning",
                                            "strategy": "LAST strategy"
                                        }
                                    ]
                                }
                            }
                 """;

        System.out.println("Original JSON:");
        System.out.println(originalJson);
        System.out.println("--------------------");

        String newMessageContent = "感觉有点头痛";
        String updatedJson = addPatientMessage(originalJson, newMessageContent);

        if (updatedJson != null) {
            System.out.println("JSON after adding message '" + newMessageContent + "':");
            System.out.println(updatedJson); // Should show the message added to the *second* entry's history
        } else {
            System.out.println("Failed to add patient message.");
        }

        System.out.println("--------------------");
        System.out.println("Testing transformInteractionHistory on the *updated* JSON:");

        // Pass the JSON *after* adding the message to the transform method
        String transformedJson = transformInteractionHistory(updatedJson);

        if (transformedJson != null) {
            System.out.println("Transformed JSON (All metadata from last entry, ordered):");
            System.out.println(transformedJson); // Should show merged history and metadata from last entry
        } else {
            System.out.println("Transformation failed.");
        }

        System.out.println("--------------------");
        System.out.println("Testing extractSecondLastObservation with provided JSON:");
        // Corrected Java String literal with escaped newlines (\n) and quotes (\")
        String testJsonFromUser = "{\"interaction_history\":{\"cot_entries\":[{\"dialogue_history\":[{\"content\":\"您好，我是本次的医生，请问孩子的年龄，性别，主要的症状是什么？\",\"role\":\"doctor\"},{\"content\":\"男，三岁，有点发烧\",\"role\":\"patient\"},{\"content\":\"孩子发烧持续多久了？体温大概是多少度呢？\",\"role\":\"doctor\"},{\"content\":\"持续三天，大概39度\",\"role\":\"patient\"}],\"feedback\":\"根据当前策略，已收集到以下信息：\\n1. 性别年龄：男，3岁\\n2. 主要症状：发热\\n3. 持续时间：3天\\n4. 严重程度：体温达39℃（高热）\\n当前问诊策略已完成，建议更新病史信息后进入下一阶段评估。\",\"observation\":\"患者观察记录（按\\\"一诉五史\\\"格式整理）：\\n\\n1. 主诉\\n3岁男童，发热3天，最高体温39℃\\n\\n2. 现病史\\n患儿于3天前无明显诱因出现发热症状，呈持续性高热，最高体温达39℃。未提供具体热型特征（稽留热/弛张热），未提及伴随寒战、抽搐、皮疹、咳嗽、呕吐、腹泻等其他症状。尚未提供治疗经过及退热药物使用情况。\\n\\n3. 既往史\\n未提供早产史、新生儿疾病史、手术外伤史、过敏史及传染病史\\n\\n4. 个人史\\n未提供喂养史、疫苗接种史、生长发育史及生活环境相关信息\\n\\n5. 家族史\\n未提供家族遗传病史及传染病接触史\\n\\n6. 系统回顾\\n• 呼吸系统：未报告咳嗽、气促、鼻塞\\n• 消化系统：未报告呕吐、腹泻、腹痛\\n• 神经系统：未报告抽搐、意识障碍\\n• 泌尿系统：未报告尿频、尿急、尿痛\\n• 皮肤黏膜：未报告皮疹、出血点\\n• 其他系统：未提供相关信息\\n\\n补充说明：当前病史信息尚不完整，需进一步采集伴随症状、治疗经过、流行病学史及全面系统症状回顾。建议后续重点评估感染相关体征（咽部充血、肺部听诊等）及危重征象（精神反应、脱水征等）。\",\"reasoning\":\"这个策略可以帮助我们了解患者的基本情况和症状的严重程度\",\"strategy\":\"询问患者的性别年龄，症状，持续时间和严重程度\"},{\"dialogue_history\":[{\"content\":\"请检查孩子的咽部是否有充血或扁桃体肿大的情况？\",\"role\":\"doctor\"},{\"content\":\"没有这种情况\",\"role\":\"patient\"},{\"content\":\"请进行肺部听诊，是否有异常呼吸音，如啰音或呼吸音减弱？\",\"role\":\"doctor\"},{\"content\":\"没有异常呼吸音\",\"role\":\"patient\"},{\"content\":\"请检查孩子的耳道是否有红肿或分泌物？\",\"role\":\"doctor\"},{\"content\":\"没有\",\"role\":\"patient\"},{\"content\":\"请进行腹部触诊，检查是否有压痛、包块或肝脾肿大的情况？\",\"role\":\"doctor\"},{\"content\":\"没有\",\"role\":\"patient\"}],\"feedback\":\"根据查体策略评估结果如下：\\n1. 咽部检查：未见充血及扁桃体肿大\\n2. 肺部听诊：呼吸音清晰，未闻及啰音或呼吸音减弱\\n3. 耳道检查：无红肿及异常分泌物\\n4. 腹部触诊：全腹软，无压痛、包块及肝脾肿大\\n5. 皮肤黏膜：经观察未见皮疹、出血点等异常\\n6. 精神反应：意识清醒，应答反应正常\\n\\n当前查体策略已完成，相关阴性体征已补充至病史记录。\",\"observation\":\"一诉五史：\\n\\n1. 主诉：\\n3岁男童发热3天，体温最高39℃\\n\\n2. 现病史：\\n患儿3天前出现发热，体温波动于39℃左右，未报告伴随咳嗽、呕吐、腹泻等伴随症状。系统查体显示：\\n- 咽部无充血及扁桃体肿大\\n- 双肺呼吸音清晰，未闻及啰音\\n- 耳道无红肿及异常分泌物\\n- 腹部软，无压痛/包块/肝脾肿大\\n- 全身皮肤黏膜无皮疹/出血点\\n- 精神反应状态正常\\n\\n3. 既往史：\\n无特殊疾病史及手术史记载\\n\\n4. 个人史：\\n无特殊生活史及过敏史记载\\n\\n5. 家族史：\\n无家族遗传性疾病及传染病史记载\\n\\n6. 系统回顾：\\n• 呼吸系统：无咳嗽/气促/胸痛\\n• 循环系统：无心悸/发绀\\n• 消化系统：无腹痛/呕吐/腹泻\\n• 泌尿系统：无尿频/尿急/血尿\\n• 神经系统：无惊厥/意识障碍\\n• 耳鼻喉：无流涕/耳痛/咽痛\\n• 皮肤系统：无皮疹/瘙痒/脱屑\\n• 血液系统：无出血倾向/苍白\",\"reasoning\":\"\",\"strategy\":\"[查体策略:] 评估咽部充血情况、肺部听诊、耳道检查、腹部触诊、皮肤黏膜观察及精神反应状态\"},{\"dialogue_history\":[],\"feedback\":\"\",\"observation\":\"\",\"reasoning\":\"\",\"strategy\":\"[诊断策略:] 诊断：急性上呼吸道感染（发热待查），检查：[血常规、C反应蛋白(CRP)、降钙素原(PCT)、尿常规]\"}],\"diagnosis\":\"[急性上呼吸道感染（病毒性）]\",\"treatment_recommendation\":\"无法解析诊疗计划: 推理过程:\\n好的，我现在需要为这个3岁男童制定诊疗计划。首先，主诉是发热3天，最高体温39℃，没有其他明显症状如咳嗽、呕吐或腹泻。根据现病史和系统回顾，患儿咽部无充血，双肺呼吸音清晰，其他系统检查也没有异常。诊断为急性上呼吸道感染（病毒性）。\\n\\n接下来，我需要查看药品清单中的西药和中药，选择适合病毒性上感的药物。病毒性感染通常不需要抗生素，但要注意是否有继发细菌感染的迹象。不过根据提供的检查结果，没有明确细菌感染的证据，所以应避免不必要的抗生素使用。\\n\\n在退热方面，布洛芬混悬滴剂适用于婴幼儿退热，符合年龄要求。剂量方面，根据体重计算，3岁儿童大约15kg，每次5-10mg/kg，即75-150mg。药品规格是15ml:0.6g，即每ml含40mg。每次剂量约1.875-3.75ml，但药品清单中的用法是参照年龄体重表，可能直接推荐每次1.5ml（约60mg）或按需使用，但需要具体看剂量表。不过根据示例，可能需要调整为每6-8小时一次，不超过4次/天。\\n\\n中药方面，小儿柴桂退热颗粒有退热作用，适用于上感，且剂量适合3岁儿童，每次1袋，一日4次。但需确认是否与布洛芬同时使用是否合适，通常可以配合，但需注意不要过量退热。\\n\\n其他药物如头孢类、阿奇霉素等都是抗生素，针对细菌感染，而当前诊断是病毒性，所以不应使用。盐酸西替利嗪用于过敏症状，但患儿没有皮疹或过敏表现，故不需要。\\n\\n生活建议方面，保持水分，观察体温，物理降温，饮食清淡，避免交叉感染，都是合理的。住院方面，患儿一般状况良好，无严重症状，无需住院。\\n\\n最后，确保所有药物都来自清单，剂量准确，符合年龄要求，不重复用药，对症治疗。\\n\\n\\n最终回答:\\n{\\n    \\\"药品\\\": [\\n        {\\n            \\\"药品名\\\": \\\"布洛芬混悬滴剂\\\",\\n            \\\"规格\\\": \\\"15ml:0.6g\\\",\\n            \\\"服用方法\\\": \\\"口服\\\",\\n            \\\"剂量安排\\\": \\\"按需每6-8小时1次，每次5mg/kg（以15kg体重计每次1.25ml）\\\",\\n            \\\"使用目的\\\": \\\"退热及缓解不适感\\\"\\n        },\\n        {\\n            \\\"药品名\\\": \\\"小儿柴桂退热颗粒\\\",\\n            \\\"规格\\\": \\\"5g\\\",\\n            \\\"服用方法\\\": \\\"开水冲服\\\",\\n            \\\"剂量安排\\\": \\\"一次1袋，一日4次\\\",\\n            \\\"使用目的\\\": \\\"疏风解表，清里退热\\\"\\n        }\n    ],\\n    \\\"生活建议\\\": [\\n        \\\"保持室内空气流通，维持适宜温湿度\\\",\\n        \\\"少量多次补充温水防止脱水\\\",\\n        \\\"采用温水擦浴等物理降温方式辅助退热\\\",\\n        \\\"饮食清淡易消化，避免高糖高脂食物\\\",\\n        \\\"密切观察体温波动及精神状态变化\\\",\\n        \\\"避免前往人群密集场所防止交叉感染\\\"\\n    ],\\n    \\\"是否住院\\\": \\\"否\\\",\\n    \\\"住院理由\\\": \\\"\\\"\\n}\",\"doctor_supplementary_info\":[\"test\"]}}";

        String secondLastObs = extractSecondLastObservation(testJsonFromUser);
        System.out.println("Extracted Second Last Observation:");
        System.out.println(secondLastObs);
        System.out.println("--------------------");
    }
}