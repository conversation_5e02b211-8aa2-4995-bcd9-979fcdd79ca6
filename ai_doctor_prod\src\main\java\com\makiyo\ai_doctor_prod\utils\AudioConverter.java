package com.makiyo.ai_doctor_prod.utils;

import org.bytedeco.javacv.*;
import org.bytedeco.ffmpeg.global.avcodec;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import javax.sound.sampled.*;

/**
 * 音频转换工具类
 * 支持将音频转换为16kHz采样率、16位深度、单声道的WAV格式
 */
@Component
public class AudioConverter {

    private static final Logger logger = LoggerFactory.getLogger(AudioConverter.class);

    // 目标音频格式：16kHz采样率、16位深度、单声道
    private static final AudioFormat TARGET_FORMAT = new AudioFormat(
            16000,  // 采样率 16kHz
            16,     // 采样位数 16bit
            1,      // 声道数 单声道
            true,   // 有符号
            false   // 小端字节序
    );

    /**
     * 将音频文件转换为16kHz采样率、16位深度、单声道的WAV格式
     *
     * @param inputFile 输入音频文件
     * @param outputFile 输出WAV文件
     * @throws Exception 转换过程中的异常
     */
    public static void convertToStandardWav(File inputFile, File outputFile) throws Exception {
        logger.info("开始转换音频文件: {}", inputFile.getAbsolutePath());

        try {
            // 尝试使用JavaSound API转换
            convertUsingJavaSound(inputFile, outputFile);
        } catch (Exception e) {
            logger.warn("JavaSound API转换失败，尝试使用FFmpeg: {}", e.getMessage());

            // 如果JavaSound API失败，尝试使用FFmpeg
            convertUsingFFmpeg(inputFile, outputFile);
        }

        logger.info("音频格式转换完成: {}", outputFile.getAbsolutePath());
    }

    /**
     * 使用JavaSound API转换音频
     */
    private static void convertUsingJavaSound(File inputFile, File outputFile) throws Exception {
        try (AudioInputStream sourceStream = AudioSystem.getAudioInputStream(inputFile)) {
            AudioFormat sourceFormat = sourceStream.getFormat();

            // 检查是否需要转换
            if (isTargetFormat(sourceFormat)) {
                logger.info("音频已经是目标格式，无需转换");
                if (!inputFile.equals(outputFile)) {
                    java.nio.file.Files.copy(inputFile.toPath(), outputFile.toPath(),
                            java.nio.file.StandardCopyOption.REPLACE_EXISTING);
                }
                return;
            }

            logger.info("原始格式: {}", sourceFormat);
            logger.info("目标格式: {}", TARGET_FORMAT);

            // 转换到目标格式
            AudioInputStream convertedStream = convertToPCM(sourceStream);
            AudioInputStream targetStream = AudioSystem.getAudioInputStream(TARGET_FORMAT, convertedStream);

            // 写入新文件
            AudioSystem.write(targetStream, AudioFileFormat.Type.WAVE, outputFile);
        } catch (Exception e) {
            throw new Exception("JavaSound API音频转换失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用FFmpeg转换音频
     */
    private static void convertUsingFFmpeg(File inputFile, File outputFile) throws Exception {
        try {
            logger.info("使用FFmpeg转换音频: {} -> {}", inputFile.getAbsolutePath(), outputFile.getAbsolutePath());

            // 创建FFmpeg帧抓取器
            FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(inputFile);
            grabber.start();

            // 设置音频参数
            grabber.setSampleRate(16000);
            grabber.setAudioChannels(1);

            // 创建FFmpeg帧录制器
            FFmpegFrameRecorder recorder = new FFmpegFrameRecorder(outputFile, 1);
            recorder.setSampleRate(16000);
            recorder.setAudioChannels(1);
            recorder.setAudioCodec(avcodec.AV_CODEC_ID_PCM_S16LE); // 16位PCM
            recorder.setFormat("wav");
            recorder.start();

            // 转换音频帧
            Frame frame;
            while ((frame = grabber.grab()) != null) {
                if (frame.samples != null) {
                    recorder.record(frame);
                }
            }

            // 关闭资源
            recorder.stop();
            recorder.release();
            grabber.stop();
            grabber.release();

            logger.info("FFmpeg转换完成");
        } catch (Exception e) {
            throw new Exception("FFmpeg音频转换失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将音频流转换为PCM格式
     */
    private static AudioInputStream convertToPCM(AudioInputStream audioInputStream) {
        AudioFormat sourceFormat = audioInputStream.getFormat();

        // 如果已经是PCM格式，直接返回
        if (sourceFormat.getEncoding() == AudioFormat.Encoding.PCM_SIGNED ||
                sourceFormat.getEncoding() == AudioFormat.Encoding.PCM_UNSIGNED) {
            return audioInputStream;
        }

        // 转换为PCM格式
        AudioFormat pcmFormat = new AudioFormat(
                AudioFormat.Encoding.PCM_SIGNED,
                sourceFormat.getSampleRate(),
                16,
                sourceFormat.getChannels(),
                sourceFormat.getChannels() * 2,
                sourceFormat.getSampleRate(),
                false);

        return AudioSystem.getAudioInputStream(pcmFormat, audioInputStream);
    }

    /**
     * 检查音频格式是否符合目标格式
     */
    private static boolean isTargetFormat(AudioFormat format) {
        return format.getSampleRate() == TARGET_FORMAT.getSampleRate() &&
                format.getSampleSizeInBits() == TARGET_FORMAT.getSampleSizeInBits() &&
                format.getChannels() == TARGET_FORMAT.getChannels() &&
                format.isBigEndian() == TARGET_FORMAT.isBigEndian();
    }

    /**
     * 获取音频文件的格式信息
     */
    public static String getAudioInfo(File audioFile) throws Exception {
        try {
            // 尝试使用JavaSound API获取信息
            return getAudioInfoUsingJavaSound(audioFile);
        } catch (Exception e) {
            logger.warn("JavaSound API获取音频信息失败，尝试使用FFmpeg: {}", e.getMessage());

            // 如果JavaSound API失败，尝试使用FFmpeg
            return getAudioInfoUsingFFmpeg(audioFile);
        }
    }

    /**
     * 使用JavaSound API获取音频信息
     */
    private static String getAudioInfoUsingJavaSound(File audioFile) throws Exception {
        try (AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(audioFile)) {
            AudioFormat format = audioInputStream.getFormat();

            return String.format(
                    "采样率: %.0f Hz%n声道数: %d%n采样位数: %d bits%n帧大小: %d bytes%n帧率: %.0f fps%n字节序: %s%n编码: %s",
                    format.getSampleRate(),
                    format.getChannels(),
                    format.getSampleSizeInBits(),
                    format.getFrameSize(),
                    format.getFrameRate(),
                    format.isBigEndian() ? "大端" : "小端",
                    format.getEncoding()
            );
        } catch (Exception e) {
            throw new Exception("JavaSound API获取音频信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用FFmpeg获取音频信息
     */
    private static String getAudioInfoUsingFFmpeg(File audioFile) throws Exception {
        try {
            FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(audioFile);
            grabber.start();

            String info = String.format(
                    "采样率: %d Hz%n声道数: %d%n采样位数: %d bits%n格式: %s%n编码: %s",
                    grabber.getSampleRate(),
                    grabber.getAudioChannels(),
                    grabber.getSampleFormat(),
                    grabber.getFormat(),
                    grabber.getAudioCodecName()
            );

            grabber.stop();
            grabber.release();

            return info;
        } catch (Exception e) {
            throw new Exception("FFmpeg获取音频信息失败: " + e.getMessage(), e);
        }
    }
} 