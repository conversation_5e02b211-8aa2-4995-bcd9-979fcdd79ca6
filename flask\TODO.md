# TODO LIST

## 推理模型做诊断

1. 推理模型裁判，分为三档
2. claude 裁判 分为三档

## 治疗方案

1. 无限资源
2. 有限资源，需要考虑成本
3. 综合判断， 根据病情的严重程度，给出推荐的治疗方案  

## 药品名

1. 功能性
2. 化学名
3. 品牌 商品名  


## 诊断

1. 先不打通 使用完全数据
2. 根据药品分类进行开药，使用R1
3. 使用claude进行打分


输入类似
 {'西药': {'category_0': '07全身用抗感染药 物', 'category_1': '12呼吸系统药物'}, '中药': {'category_0': '01解表剂', 'category_1': '02清热剂', 'category_2': '04化痰、止咳、平喘剂', 'category_3': '17耳鼻喉科用药'}}

从以下json中提取对应的信息

{"西药":
     [{'名称': '磷酸铝凝胶', '规格': '16g(130mg/g)×10袋', '单位': '盒'}, {'名称': '碳酸氢钠片', '规格': '0.5g×100片', '单位': '瓶'} ... ]

"中药":
     [{'名称': '柴黄颗粒', '规格': '4g×12袋', '单位': '盒'}, {'名称': '芩香清解 口服液', '规格': '10ml×6支', '单位': '盒'}...]
    ...}
    


1. 诊断并给出类别
2. 根据药名选药
3. 根据选的药品的具体信息，病情，诊断 给出最后的治疗方案

1. 诊断给出类别
2. 根据药品名和规格，病情，诊断 三个信息 来给出治疗方案

1. 根据病情和真实诊断来评估两个药物方案，分三档