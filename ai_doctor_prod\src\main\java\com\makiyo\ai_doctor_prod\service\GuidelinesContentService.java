package com.makiyo.ai_doctor_prod.service;


import com.makiyo.ai_doctor_prod.entity.GuidelinesContent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 指南内容服务类
 */
@Service
public class GuidelinesContentService {

    private static final Logger log = LoggerFactory.getLogger(GuidelinesContentService.class);

    @Resource
    private MongoTemplate mongoTemplate;

    /**
     * 保存指南内容
     *
     * @param caseId 案例ID
     * @param content 指南内容
     * @return 保存后的引用ID
     */
    public String saveContent(String caseId, Object content) {
        try {
            // 创建指南内容对象
            GuidelinesContent guidelines = new GuidelinesContent();
            guidelines.setId(UUID.randomUUID().toString());
            guidelines.setCaseId(caseId);
            guidelines.setContent(content);
            guidelines.setCreatedAt(LocalDateTime.now());

            // 使用MongoTemplate直接保存到MongoDB
            GuidelinesContent saved = mongoTemplate.save(guidelines, "guidelines_content");

            log.info("成功保存指南内容，案例ID: {}, 引用ID: {}", caseId, saved.getId());
            return saved.getId();
        } catch (Exception e) {
            log.error("保存指南内容失败，案例ID: {}, 错误: {}", caseId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据ID获取指南内容
     *
     * @param id 指南内容引用ID
     * @return 指南内容对象
     */
    public Object getContentById(String id) {
        try {
            GuidelinesContent guidelines = mongoTemplate.findById(id, GuidelinesContent.class, "guidelines_content");
            if (guidelines != null) {
                return guidelines.getContent();
            } else {
                log.warn("未找到ID为{}的指南内容", id);
                return null;
            }
        } catch (Exception e) {
            log.error("获取指南内容失败，ID: {}, 错误: {}", id, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据案例ID获取指南内容
     *
     * @param caseId 案例ID
     * @return 指南内容对象
     */
    public Object getContentByCaseId(String caseId) {
        if (caseId == null) {
            log.warn("尝试使用null案例ID获取指南内容");
            return null;
        }
        
        try {
            // 创建查询条件
            org.springframework.data.mongodb.core.query.Query query =
                    new org.springframework.data.mongodb.core.query.Query(
                            org.springframework.data.mongodb.core.query.Criteria.where("caseId").is(caseId)
                    );
            
            // 按创建时间降序排序，确保获取最新的内容
            query.with(org.springframework.data.domain.Sort.by(
                    org.springframework.data.domain.Sort.Direction.DESC, "createdAt"));

            GuidelinesContent guidelines = mongoTemplate.findOne(
                    query, GuidelinesContent.class, "guidelines_content"
            );

            if (guidelines != null) {
                log.info("成功获取案例ID为{}的指南内容，内容ID: {}", caseId, guidelines.getId());
                return guidelines.getContent();
            } else {
                log.warn("未找到案例ID为{}的指南内容", caseId);
                return null;
            }
        } catch (Exception e) {
            log.error("获取指南内容失败，案例ID: {}, 错误类型: {}, 错误信息: {}", 
                    caseId, e.getClass().getName(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据案例ID更新指南内容
     *
     * @param caseId 案例ID
     * @param content 新的指南内容
     * @return 是否更新成功
     */
    public boolean updateContentByCaseId(String caseId, Object content) {
        try {
            // 创建查询条件
            org.springframework.data.mongodb.core.query.Query query =
                    new org.springframework.data.mongodb.core.query.Query(
                            org.springframework.data.mongodb.core.query.Criteria.where("caseId").is(caseId)
                    );

            GuidelinesContent guidelines = mongoTemplate.findOne(
                    query, GuidelinesContent.class, "guidelines_content"
            );

            if (guidelines != null) {
                guidelines.setContent(content);
                guidelines.setCreatedAt(LocalDateTime.now());
                mongoTemplate.save(guidelines, "guidelines_content");
                log.info("成功更新案例ID为{}的指南内容", caseId);
                return true;
            } else {
                // 如果不存在，则创建新记录
                saveContent(caseId, content);
                return true;
            }
        } catch (Exception e) {
            log.error("更新指南内容失败，案例ID: {}, 错误: {}", caseId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 删除指南内容
     *
     * @param id 指南内容引用ID
     * @return 是否删除成功
     */
    public boolean deleteContent(String id) {
        try {
            org.springframework.data.mongodb.core.query.Query query =
                    new org.springframework.data.mongodb.core.query.Query(
                            org.springframework.data.mongodb.core.query.Criteria.where("_id").is(id)
                    );

            mongoTemplate.remove(query, GuidelinesContent.class, "guidelines_content");
            log.info("成功删除ID为{}的指南内容", id);
            return true;
        } catch (Exception e) {
            log.error("删除指南内容失败，ID: {}, 错误: {}", id, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 根据案例ID删除指南内容
     *
     * @param caseId 案例ID
     * @return 是否删除成功
     */
    public boolean deleteContentByCaseId(String caseId) {
        try {
            org.springframework.data.mongodb.core.query.Query query =
                    new org.springframework.data.mongodb.core.query.Query(
                            org.springframework.data.mongodb.core.query.Criteria.where("caseId").is(caseId)
                    );

            mongoTemplate.remove(query, GuidelinesContent.class, "guidelines_content");
            log.info("成功删除案例ID为{}的指南内容", caseId);
            return true;
        } catch (Exception e) {
            log.error("删除指南内容失败，案例ID: {}, 错误: {}", caseId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 根据案例ID获取完整的指南内容对象（包含元数据）
     *
     * @param caseId 案例ID
     * @return 完整的指南内容对象，包含ID、创建时间等元数据
     */
    public GuidelinesContent getFullContentByCaseId(String caseId) {
        if (caseId == null) {
            log.warn("尝试使用null案例ID获取完整指南内容对象");
            return null;
        }
        
        try {
            // 创建查询条件
            org.springframework.data.mongodb.core.query.Query query =
                    new org.springframework.data.mongodb.core.query.Query(
                            org.springframework.data.mongodb.core.query.Criteria.where("caseId").is(caseId)
                    );
            
            // 按创建时间降序排序，确保获取最新的内容
            query.with(org.springframework.data.domain.Sort.by(
                    org.springframework.data.domain.Sort.Direction.DESC, "createdAt"));

            GuidelinesContent guidelines = mongoTemplate.findOne(
                    query, GuidelinesContent.class, "guidelines_content"
            );

            if (guidelines != null) {
                log.info("成功获取案例ID为{}的完整指南内容对象，内容ID: {}", caseId, guidelines.getId());
                return guidelines;
            } else {
                log.warn("未找到案例ID为{}的指南内容对象", caseId);
                return null;
            }
        } catch (Exception e) {
            log.error("获取完整指南内容对象失败，案例ID: {}, 错误类型: {}, 错误信息: {}", 
                    caseId, e.getClass().getName(), e.getMessage(), e);
            return null;
        }
    }
}