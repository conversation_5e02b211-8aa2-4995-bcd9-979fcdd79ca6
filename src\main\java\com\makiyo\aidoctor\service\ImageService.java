package com.makiyo.aidoctor.service;

import com.makiyo.aidoctor.entity.ImageMetadata;
import com.mongodb.client.gridfs.model.GridFSFile;
import org.apache.commons.io.IOUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.gridfs.GridFsOperations;
import org.springframework.data.mongodb.gridfs.GridFsResource;
import org.springframework.data.mongodb.gridfs.GridFsTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Service
public class ImageService {
    private static final Logger log = LoggerFactory.getLogger(ImageService.class);

    @Autowired
    private GridFsTemplate gridFsTemplate;

    @Autowired
    private GridFsOperations gridFsOperations;

    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * 存储图片到GridFS并保存元数据
     */
    public ImageMetadata storeImage(MultipartFile file, Integer sessionId, String category) throws IOException {
        if (file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        // 创建元数据
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("sessionId", sessionId.toString());
        metadata.put("originalFilename", file.getOriginalFilename());
        metadata.put("contentType", file.getContentType());
        metadata.put("category", category);
        metadata.put("uploadTime", LocalDateTime.now().toString());
        metadata.put("size", file.getSize());

        // 生成唯一文件名
        String uniqueFilename = UUID.randomUUID().toString() + getFileExtension(file.getOriginalFilename());

        // 存储文件到GridFS
        ObjectId gridFsId = gridFsTemplate.store(
                file.getInputStream(),
                uniqueFilename,
                file.getContentType(),
                metadata
        );

        // 保存元数据到MongoDB
        ImageMetadata imageMetadata = new ImageMetadata();
        imageMetadata.setSessionId(sessionId);
        imageMetadata.setOriginalFilename(file.getOriginalFilename());
        imageMetadata.setStoredFilename(uniqueFilename);
        imageMetadata.setContentType(file.getContentType());
        imageMetadata.setSize(file.getSize());
        imageMetadata.setGridFsId(gridFsId.toString());
        imageMetadata.setUploadTime(LocalDateTime.now());
        imageMetadata.setCategory(category);

        mongoTemplate.save(imageMetadata);

        log.info("图片已存储到GridFS，ID: {}, 会话ID: {}", gridFsId.toString(), sessionId);
        return imageMetadata;
    }

    /**
     * 根据ID获取图片
     */
    public GridFSFile getImageById(String id) {
        Query query = new Query(Criteria.where("_id").is(new ObjectId(id)));
        return gridFsTemplate.findOne(query);
    }

    /**
     * 根据ID获取图片数据
     */
    public byte[] getImageData(String id) throws IOException {
        GridFSFile gridFSFile = getImageById(id);
        if (gridFSFile == null) {
            return null;
        }
        GridFsResource resource = gridFsOperations.getResource(gridFSFile);
        return IOUtils.toByteArray(resource.getInputStream());
    }

    /**
     * 根据会话ID获取所有图片元数据
     */
    public List<ImageMetadata> getImagesBySessionId(Integer sessionId) {
        Query query = new Query(Criteria.where("sessionId").is(sessionId));
        return mongoTemplate.find(query, ImageMetadata.class);
    }

    /**
     * 根据会话ID和分类获取图片元数据
     */
    public List<ImageMetadata> getImagesBySessionIdAndCategory(Integer sessionId, String category) {
        Query query = new Query(
                Criteria.where("sessionId").is(sessionId)
                        .and("category").is(category)
        );
        return mongoTemplate.find(query, ImageMetadata.class);
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null) return "";
        int lastDotIndex = filename.lastIndexOf(".");
        return (lastDotIndex == -1) ? "" : filename.substring(lastDotIndex);
    }

    /**
     * 删除图片
     */
    public void deleteImage(String id) {
        try {
            // 删除GridFS文件
            gridFsTemplate.delete(new Query(Criteria.where("_id").is(new ObjectId(id))));

            // 删除元数据
            mongoTemplate.remove(new Query(Criteria.where("gridFsId").is(id)), ImageMetadata.class);

            log.info("成功删除图片，ID: {}", id);
        } catch (Exception e) {
            log.error("删除图片失败，ID: {}: {}", id, e.getMessage(), e);
            throw e;
        }
    }
}