import pandas as pd

# 创建API接口文档数据
api_data = [
    # OCR API
    {"接口名称": "/ocr_api", 
     "请求方法": "POST",
     "功能描述": "处理医疗报告图像并提取结构化数据",
     "请求参数": '{"image_path": "path/to/image.jpg"}',
     "响应格式": '包含检查时间、检查名称、基础信息和检查结果的JSON',
     "状态码": "200: 成功, 400: 参数错误, 404: 文件不存在, 500: 服务器错误",
     "开发状态": "待完成",
     "对接说明": "需确保图片路径可访问，支持JPEG/PNG格式"
    },
    
    # 初步诊断
    {"接口名称": "/ai_doctor_preliminary_diagnosis", 
     "请求方法": "POST",
     "功能描述": "基于交互历史生成初步诊断结果",
     "请求参数": '{"interaction_history": {...}, "supplementary_info": "string"}',
     "响应格式": '{"preliminary_diagnosis": "string", "inspection_suggestions": "string", "reasoning": "string", "guidelines_content": "string", "observation": "string"}',
     "状态码": "200: 成功, 400: 参数错误, 500: 服务器错误",
     "开发状态": "待完成",
     "对接说明": "interaction_history结构详见文档，supplementary_info为可选"
    },
    
    # 最终诊断
    {"接口名称": "/ai_doctor_diagnose", 
     "请求方法": "POST",
     "功能描述": "生成最终诊断结果和治疗方案",
     "请求参数": '{"interaction_history": {...}}',
     "响应格式": '{"diagnosis": "string", "condition": "string", "treatment_recommendation": "string", "treatment_guide": "string"}',
     "状态码": "200: 成功, 400: 参数错误, 500: 服务器错误",
     "开发状态": "待完成",
     "对接说明": "需先完成问诊流程后调用"
    },
    
    # 问诊
    {"接口名称": "/ai_doctor_v2_inquiry", 
     "请求方法": "POST",
     "功能描述": "在医疗对话中生成下一个问题",
     "请求参数": '{"interaction_history": {...}}',
     "响应格式": '返回更新后的交互历史，包含AI医生的下一个问题',
     "状态码": "200: 成功, 400: 参数错误, 500: 服务器错误",
     "开发状态": "待完成",
     "对接说明": "主要用于常规问诊流程"
    },
    
    # 快速问诊
    {"接口名称": "/ai_doctor_v2_quick_inquiry", 
     "请求方法": "POST",
     "功能描述": "在快速演示模式下生成下一个问题",
     "请求参数": '{"interaction_history": {...}}',
     "响应格式": '返回更新后的交互历史，包含AI医生在快速模式下的下一个问题',
     "状态码": "200: 成功, 400: 参数错误, 500: 服务器错误",
     "开发状态": "待完成",
     "对接说明": "用于演示场景，问诊流程简化"
    },
    
    # V2初步诊断
    {"接口名称": "/ai_doctor_v2_preliminary_diagnosis", 
     "请求方法": "POST",
     "功能描述": "V2版本初步诊断API",
     "请求参数": '{"interaction_history": {...}, "supplementary_info": "string"}',
     "响应格式": '{"preliminary_diagnosis": "string", "inspection_suggestions": "string", "reasoning": "string", "guidelines_content": "string"}',
     "状态码": "200: 成功, 400: 参数错误, 500: 服务器错误",
     "开发状态": "待完成",
     "对接说明": "V2版本功能，与v1版本相比移除了observation字段"
    },
    
    # V2最终诊断
    {"接口名称": "/ai_doctor_v2_diagnosis", 
     "请求方法": "POST",
     "功能描述": "V2版本最终诊断API",
     "请求参数": '{"interaction_history": {...}}',
     "响应格式": '{"diagnosis": "string", "condition": "string", "treatment_recommendation": "string", "treatment_guide": "string"}',
     "状态码": "200: 成功, 400: 参数错误, 500: 服务器错误",
     "开发状态": "待完成",
     "对接说明": "V2版本功能，处理逻辑有所优化"
    },
    
    # AI医生主接口
    {"接口名称": "/ai_doctor_v2", 
     "请求方法": "POST",
     "功能描述": "AI医生系统的主要接口，处理完整的交互流程",
     "请求参数": '{"interaction_history": {...}}',
     "响应格式": '{"interaction_history": {...}}',
     "状态码": "200: 成功, 400: 参数错误, 500: 服务器错误",
     "开发状态": "待完成",
     "对接说明": "主入口点，集成了问诊和诊断功能，支持完整流程"
    },
]

# 创建DataFrame
df = pd.DataFrame(api_data)

# 创建Excel写入对象
with pd.ExcelWriter('AI_Doctor_API接口对接文档.xlsx', engine='xlsxwriter') as writer:
    # 将DataFrame写入Excel
    df.to_excel(writer, sheet_name='API接口文档', index=False)
    
    # 获取xlsxwriter对象
    workbook = writer.book
    worksheet = writer.sheets['API接口文档']
    
    # 定义格式
    header_format = workbook.add_format({
        'bold': True,
        'bg_color': '#4472C4',
        'font_color': 'white',
        'border': 1,
        'align': 'center',
        'valign': 'vcenter'
    })
    
    cell_format = workbook.add_format({
        'border': 1,
        'text_wrap': True,
        'valign': 'top'
    })
    
    code_format = workbook.add_format({
        'border': 1,
        'font_name': 'Consolas',
        'text_wrap': True,
        'valign': 'top',
        'bg_color': '#F2F2F2'
    })
    
    status_format = workbook.add_format({
        'border': 1,
        'bg_color': '#FFC7CE',
        'font_color': '#9C0006',
        'align': 'center',
        'valign': 'vcenter',
        'bold': True
    })
    
    # 应用格式到表头
    for col_num, value in enumerate(df.columns.values):
        worksheet.write(0, col_num, value, header_format)
    
    # 设置列宽
    worksheet.set_column('A:A', 30)  # 接口名称
    worksheet.set_column('B:B', 10)  # 请求方法
    worksheet.set_column('C:C', 40)  # 功能描述
    worksheet.set_column('D:D', 40)  # 请求参数
    worksheet.set_column('E:E', 40)  # 响应格式
    worksheet.set_column('F:F', 30)  # 状态码
    worksheet.set_column('G:G', 15)  # 开发状态
    worksheet.set_column('H:H', 30)  # 对接说明
    
    # 应用格式到每个单元格
    for row_num, row in enumerate(df.values):
        for col_num, value in enumerate(row):
            # 使用代码格式的列
            if col_num in [3, 4]:  # 请求参数和响应格式
                worksheet.write(row_num + 1, col_num, value, code_format)
            # 开发状态列使用特殊格式
            elif col_num == 6:  # 开发状态
                worksheet.write(row_num + 1, col_num, value, status_format)
            # 其他列使用普通格式
            else:
                worksheet.write(row_num + 1, col_num, value, cell_format)
    
    # 设置行高
    for i in range(1, len(df) + 1):
        worksheet.set_row(i, 100)  # 设置适当的高度以展示完整内容
    
    # 冻结表头
    worksheet.freeze_panes(1, 0)
    
    # 添加筛选器
    worksheet.autofilter(0, 0, len(df), len(df.columns) - 1)

    # 为请求参数和响应格式创建详细文档
    worksheet_details = workbook.add_worksheet('数据结构详情')
    
    # 设置详情页格式
    detail_header_format = workbook.add_format({
        'bold': True,
        'bg_color': '#4472C4',
        'font_color': 'white',
        'border': 1,
        'align': 'center',
        'valign': 'vcenter'
    })
    
    # 添加详情页内容
    details_headers = ['接口名称', '数据结构', '字段名', '类型', '必填', '描述']
    for col_num, header in enumerate(details_headers):
        worksheet_details.write(0, col_num, header, detail_header_format)
    
    # 设置详情页列宽
    worksheet_details.set_column('A:A', 30)  # 接口名称
    worksheet_details.set_column('B:B', 20)  # 数据结构
    worksheet_details.set_column('C:C', 20)  # 字段名
    worksheet_details.set_column('D:D', 15)  # 类型
    worksheet_details.set_column('E:E', 10)  # 必填
    worksheet_details.set_column('F:F', 40)  # 描述
    
    # 添加interaction_history结构示例
    interaction_history_fields = [
        {'接口名称': '所有接口', '数据结构': 'interaction_history', '字段名': 'cot_entries', '类型': 'array', '必填': '是', '描述': '思维链记录数组，包含对话历史和推理过程'},
        {'接口名称': '所有接口', '数据结构': 'interaction_history', '字段名': 'diagnosis', '类型': 'string', '必填': '否', '描述': '已有诊断结果'},
        {'接口名称': '所有接口', '数据结构': 'interaction_history', '字段名': 'test_recommendation', '类型': 'array', '必填': '否', '描述': '检查建议列表'},
        {'接口名称': '所有接口', '数据结构': 'interaction_history', '字段名': 'treatment_recommendation', '类型': 'string', '必填': '否', '描述': '治疗建议'},
        {'接口名称': '所有接口', '数据结构': 'interaction_history', '字段名': 'preliminary_diagnosis', '类型': 'string', '必填': '否', '描述': '初步诊断'},
        {'接口名称': '所有接口', '数据结构': 'interaction_history', '字段名': 'doctor_supplementary_info', '类型': 'array', '必填': '否', '描述': '医生补充信息列表'},
        {'接口名称': '所有接口', '数据结构': 'interaction_history', '字段名': 'lastObservation', '类型': 'string', '必填': '否', '描述': '最后一次观察结果'},
    ]
    
    row_num = 1
    for field in interaction_history_fields:
        for col_num, key in enumerate(details_headers):
            worksheet_details.write(row_num, col_num, field.get(key, ''), cell_format)
        row_num += 1
    
    # 添加认证信息页
    worksheet_auth = workbook.add_worksheet('认证与限制')
    auth_data = [
        ['认证方式', '无需认证'],
        ['速率限制', '无限制'],
        ['服务器地址', 'http://localhost:5555'],
        ['注意事项', '本API为内部服务，不建议公开暴露到公网']
    ]
    
    for row_num, row_data in enumerate(auth_data):
        for col_num, value in enumerate(row_data):
            if row_num == 0 or col_num == 0:
                worksheet_auth.write(row_num, col_num, value, header_format)
            else:
                worksheet_auth.write(row_num, col_num, value, cell_format)
    
    worksheet_auth.set_column('A:A', 20)
    worksheet_auth.set_column('B:B', 40)

print("Excel文件 'AI_Doctor_API接口对接文档.xlsx' 已成功生成！")