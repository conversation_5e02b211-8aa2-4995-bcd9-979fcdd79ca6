import logging
import re
import json
import os
import json
from datetime import datetime
import httpx
import pandas as pd
from dotenv import load_dotenv


from volcenginesdkarkruntime import Ark

from prompts import _sys_prompt_doctor, _sys_prompt_patient, _ai_check_doctor

# 初始化日志配置
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),  # 输出到控制台
        logging.FileHandler("Consultation.log", mode="a", encoding="utf-8")  # 输出到文件
    ]
)
logger = logging.getLogger(__name__)


load_dotenv('../.env')  # 加载环境变量


# os.environ["ARK_API_KEY"] = "64b51f82-9bf5-4aca-ac6e-5f84fa727737"



client = Ark(api_key=os.getenv("ARK_API_KEY"), timeout=httpx.Timeout(timeout=1800))
model_r1 = "deepseek-r1-250120"
model_v3 = "deepseek-v3-241226"

# ------------------------------------------------
# 基本函数
# ------------------------------------------------



def chat_with_ai(sysprompt: str, message: str, model: str):
    """与AI模型进行对话交互
    
    Args:
        sysprompt: 系统提示语
        message: 用户输入信息
        model: 使用的模型名称
    
    Returns:
        str: AI生成的响应内容
    """
    logger.info(f"正在调用{model}模型进行交互...")
    completion = client.chat.completions.create(
        model=model,
        messages=[
            {"role": "system", "content": sysprompt},
            {"role": "user", "content": message},
        ],
    )
    return completion.choices[0].message.content

def extract_text_tag(response: str) -> str:
    """使用正则表达式提取<text>标签内容
    
    Args:
        response: 包含标签的原始文本
    
    Returns:
        str: 提取后的纯净文本内容
    """
     
    match = re.search(r"<text>(.*?)</text>", response, re.DOTALL)
    return match.group(1).strip() if match else response.strip()

def append_to_json_file(json_filename, new_data):
    """将新数据追加到JSON文件中
    
    Args:
        json_filename: 目标JSON文件名
        new_data: 需要追加的新数据
    """
    logger.info(f"开始写入数据到{json_filename}")
    # 如果 JSON 文件已存在，则读取旧数据
    if os.path.exists(json_filename):
        with open(json_filename, "r", encoding="utf-8") as json_file:
            try:
                existing_data = json.load(json_file)  
                if not isinstance(existing_data, list):
                    existing_data = [existing_data]
            except json.JSONDecodeError:
                logger.warning("JSON文件解析失败，初始化新数据")
                existing_data = []
    else:
        existing_data = []
    existing_data.append(new_data)

    with open(json_filename, "w", encoding="utf-8") as json_file:
        json.dump(existing_data, json_file, ensure_ascii=False, indent=4)

    logger.info(f"\n 对话记录已追加至 {json_filename}")
    logger.info(f"成功追加数据，当前记录数：{len(existing_data)}")

def convert_history(history_doc):
    history_pat = []
    for i in range(0, len(history_doc), 1):  # 以2步长遍历
        # 对应 'text' 标签内容转为 'observation' 内容
        if history_doc[i].startswith('<text>'):
            history_pat.append('<observation>' + history_doc[i][6:-7] + '</observation>')
        # 对应 'observation' 标签内容转为 'text' 内容
        elif history_doc[i].startswith('<observation>'):
            history_pat.append('<text>' + history_doc[i][13:-14] + '</text>')
    return history_pat

def read_prompt_from_file(filename: str) -> str:
    with open(filename, "r", encoding="utf-8") as file:
            return file.read().strip()

def generate_system_prompt(tag_type):

    """根据标签类型生成系统提示语
    
    Args:
        tag_type: 检查类型标签
    
    Returns:
        str: 包含示例的系统提示语
    """

    logger.debug(f"正在为{tag_type}类型生成系统提示语")

    file_path = '..\data\检查和药品0224_shot.xlsx' 
    df = pd.read_excel(file_path)
    filtered_df = df[df['tag'] == tag_type]

    # 从中随机抽取3条 '是否开检查' 列为 '开检查' 的记录
    open_check_samples = filtered_df[filtered_df['是否开检查'] == '开检查'].sample(n=3, replace=False)

    # 从中随机抽取5条 '是否开检查' 列为 '不开检查' 的记录
    no_check_samples = filtered_df[filtered_df['是否开检查'] == '不开检查'].sample(n=5, replace=False)

    # 获取抽取记录的 'shot' 列内容，并按 '\n' 拼接
    shots = list(open_check_samples['shot']) + list(no_check_samples['shot'])
    numbered_shots = [f"示例{i+1}:\n{shot}" for i, shot in enumerate(shots)]
    shots_content = '\n\n'.join(numbered_shots)

    # # 读取指定路径下的文本文件内容
    # prompt_path = r'..\prompt\AI check doctor'
    # with open(prompt_path, 'r', encoding='utf-8') as file:
    #     prompt_content = file.read()

    prompt_content = _ai_check_doctor
    # 构建 system_prompt
    system_prompt = f"{prompt_content}\n{shots_content}"
    return system_prompt

# ------------------------------------------------
# 问诊部分
# ------------------------------------------------

def format_input_for_role(role, history_doc, history_pat, doctor_info_clean=None, round_num=1):
    """格式化不同角色的输入内容
    
    Args:
        role: 角色类型（doctor/patient）
        history_doc: 医生对话历史
        history_pat: 患者对话历史
        doctor_info_clean: 清洗后的医生信息
        round_num: 当前对话轮次
    
    Returns:
        str: 格式化后的输入内容
    """

    if role == "doctor":
        formatted_input = "\n".join(history_doc)
    elif role == "patient":
        formatted_input = "\n".join(history_pat)
    
    if role == "patient" and round_num == 1:
        formatted_input = f"<info>\n{doctor_info_clean}</info>\n" + formatted_input

    return formatted_input.strip()

def doctor_turn(sysprompt_doctor, history_doc, history_pat, initial_message, model_v3, round_num):
    """处理医生回合的对话逻辑
    
    Args:
        sysprompt_doctor: 医生系统提示语
        history_doc: 医生对话历史
        history_pat: 患者对话历史
        initial_message: 初始提示信息
        model_v3: 使用的模型版本
        round_num: 当前轮次
    
    Returns:
        tuple: (医生响应内容, 解决方案文本)
    """
    logger.debug(f"医生第{round_num}回合开始")

    message_doc = format_input_for_role("doctor", history_doc, history_pat)
    
    if round_num == 1:
        message_doc = initial_message
    response_doc = chat_with_ai(sysprompt_doctor, message_doc, model_v3)

    logger.info(f"医生响应：{response_doc}")  


    # 检查 solution 终止对话
    solution_match = re.search(r"<text>(.*?)</text solution='True'>", response_doc, re.DOTALL)
    solution_text = solution_match.group(1).strip() if solution_match else None

    return response_doc, solution_text

def patient_turn(sysprompt_patient, history_doc, history_pat, doctor_info_clean, model_v3, round_num):
    """处理患者回合的对话逻辑
    
    Args:
        sysprompt_patient: 患者系统提示语
        history_doc: 医生对话历史
        history_pat: 患者对话历史
        doctor_info_clean: 清洗后的医生信息
        model_v3: 使用的模型版本
        round_num: 当前轮次
    
    Returns:
        str: 患者响应内容
    """
    logger.debug(f"患者第{round_num}回合开始")

    message_pat = format_input_for_role("patient", history_doc, history_pat, doctor_info_clean, round_num)
    response_pat = chat_with_ai(sysprompt_patient, message_pat, model_v3)

    logger.info(f"患者响应：{response_pat}")

    return response_pat

# **单轮对话**
def dialogue_round(
    sysprompt_doctor, sysprompt_patient, history_doc, history_pat, initial_message, 
    doctor_info_clean, model_v3, round_num, true_patient=None
):
    """
    通过传入true_patient json文件发生对话

    比如：
    true_patient_responses = {
    2: "医生，我最近有点头晕，感觉不太舒服。",
    4: "没有，我最近没接触过生病的人。",
}
    """
    """执行单轮对话
    
    Args:
        true_patient: 预设的患者回答（测试用）
    
    Returns:
        tuple: (医生响应, 患者响应, 解决方案)
    """
    logger.info(f"【第{round_num}轮对话开始】")

    # **医生生成问诊**
    response_doc, solution_text = doctor_turn(
        sysprompt_doctor, history_doc, history_pat, initial_message, model_v3, round_num
    )
    history_doc.append(response_doc)

    if solution_text:
        logger.info("医生提出解决方案，终止对话")
        return response_doc, None, solution_text

    extracted_doc = extract_text_tag(response_doc)
    message_pat = f"<observation>\n{extracted_doc}\n</observation>"
    history_pat.append(message_pat)

    # # 从命令行读取患者回答到true_patient
    # if not true_patient:
    #     true_patient = input("请输入患者回答：") 

    # **如果提供 `true_patient`，直接使用它作为患者回答**
    if true_patient:
        logger.debug("使用预设患者回答")
        response_pat = f"<text>\n{true_patient}\n</text>"
    else:
        response_pat = patient_turn(
            sysprompt_patient, history_doc, history_pat, doctor_info_clean, model_v3, round_num
        )

    history_pat.append(response_pat)
    # **提取患者的回答**
    extracted_pat = extract_text_tag(response_pat)


    # **更新医生的输入**
    history_doc.append(f"<observation>\n{extracted_pat}\n</observation>")

    logger.info(f"history_doc: {history_doc}")

    logger.info(f"【第{round_num}轮对话完成】")
    return response_doc, response_pat, solution_text

def run_dialogue_loop(
    sysprompt_doctor, sysprompt_patient, initial_message, index, doctor_info_clean=None, doctor_info=None, 
    rounds=15, true_patient_responses=None
):
    """运行完整的多轮对话循环
    
    Args:
        true_patient_responses: 预设的患者回答字典 {轮次: 回答}
    
    Returns:
        tuple: (医生历史, 患者历史, 解决方案)
    """
    logger.info(f"开始处理案例 {index}")
    

    history_doc = []
    history_pat = []
    solution_text = None


    logger.info("\n --- 对话开始 ---\n")
    logger.info(f"doctor_info_clean: {doctor_info}...")
    logger.info(f"doctor_info: {doctor_info}...")


    true_patient_responses = true_patient_responses or {}


    for round_num in range(1, rounds + 1):
        # print(f"\n【第 {round_num} 轮】\n")
        logger.info(f"【第 {round_num} 轮】")

        true_patient = true_patient_responses.get(round_num)     
        logger.info(f"history_doc: {history_doc}")
        logger.info(f"history_pat: {history_pat}")  

        response_doc, response_pat, solution_text = dialogue_round(
            sysprompt_doctor, sysprompt_patient, history_doc, history_pat,
            initial_message, doctor_info_clean, model_v3, round_num, true_patient
        )

        logger.info(f"response_doc: {response_doc}")
        logger.info(f"response_pat: {response_pat}")

        # **如果医生返回 solution，结束对话**
        if solution_text:
            print(f"\nSolution: {solution_text}\n")
            logger.info(f"在第{round_num}轮提前终止对话")
            break

    # print("\n--- 对话结束 ---\n")
    logger.info("\n --- 对话结束 ---\n")
    logger.info(f"案例 {index} 处理完成，共进行{round_num}轮对话")
    return history_doc, history_pat, solution_text

# **对话数据存储**
def save_conversation(index, history_doc, history_pat, doctor_info, doctor_info_clean, solution_text):
    """
    构造对话数据字典
    """
    conversation_data = {
        "index": index,
        "doctor_history": history_doc,
        "patient_history": history_pat,
        "doctor_info": doctor_info,
        "doctor_info_clean": doctor_info_clean,
        "AI Ask Solution": solution_text,
    }
    return conversation_data 

# ------------------------------------------------------
# 开检查部分
# ------------------------------------------------------

def AI_check(conversation_data):
    """根据对话数据生成检查建议
    
    Args:
        conversation_data: 包含对话历史的数据字典
    
    Returns:
        dict: 更新后的对话数据
    """
    logger.info(f"开始检查建议生成：{conversation_data['index']}")

    solution_text = conversation_data.get("AI Ask Solution", "")
    doctor_info = conversation_data.get("doctor_info", "")
    doctor_info_clean = conversation_data.get("doctor_info_clean", "")
    # **从 solution_text 提取 TAG**
    tag_match = re.search(r'【tag】\s*(\S+)', solution_text)
    tag = tag_match.group(1).strip() if tag_match else None

    if not tag:
        print(f"未找到 tag 信息，无法生成 system_prompt")
        logger.warning("未检测到有效tag，跳过检查建议生成")
        return conversation_data  # 直接返回原始数据

    system_prompt = generate_system_prompt(tag)
    ai_check_result_1 = chat_with_ai(system_prompt, solution_text, model_v3)

    check_match_1 = re.search(r"<check.*?>(.*?)</check>", ai_check_result_1, re.DOTALL)
    AI_ask_AI_check = check_match_1.group(1).strip() if check_match_1 else ""

    ai_check_result_2 = chat_with_ai(system_prompt, doctor_info_clean, model_v3)
    check_match_2 = re.search(r"<check.*?>(.*?)</check>", ai_check_result_2, re.DOTALL)
    Doctor_ask_ai_check_clean = check_match_2.group(1).strip() if check_match_2 else ""

    ai_check_result_3 = chat_with_ai(system_prompt, doctor_info, model_v3)
    check_match_3 = re.search(r"<check.*?>(.*?)</check>", ai_check_result_3, re.DOTALL)
    Doctor_ask_ai_check = check_match_3.group(1).strip() if check_match_3 else ""

    # **更新 conversation_data**
    conversation_data.update({
        "AI_ask_AI_check_ori": ai_check_result_1, 
        "Doctor_clean_AI_Check_ori": ai_check_result_2,
        "Doctor_AI_check_ori":ai_check_result_3,
        "AI_ask_AI_check": AI_ask_AI_check,  
        "Doctor_clean_AI_Check": Doctor_ask_ai_check_clean,
        "Doctor_AI_check":Doctor_ask_ai_check
    })

    print(f"\n已经处理案例编号: {conversation_data['index']}")

    logger.info(f"案例{conversation_data['index']}检查建议生成完成")
    return conversation_data


def process_xlsx(xlsx_file):
    logger.info("程序启动")

    sysprompt_doctor = _sys_prompt_doctor
    sysprompt_patient = _sys_prompt_patient


    logger.info("加载测试数据集")
    df = pd.read_excel(xlsx_file)

    filtered_df = df.dropna(subset=["病例合并"])
    case_list = filtered_df.to_dict(orient="records")
    json_output_file = r"..\data\text.json"

    for idx, case in enumerate(case_list, start=1):


        if idx > 2:
            break

        logger.info(f"正在处理第{idx}/{len(case_list)}个病例")
        messege = case["病例合并"]
        doctor_info = messege.replace("&#xA;", "\n")
        case_index = case["案例编号"]
        
        # 初始输入
        initial_message = "请您开始问诊"
        doctor_info_clean = re.sub(r"【体格检查】.*?(\n|$)|【专科查体】.*?(\n|$)", "", doctor_info, flags=re.DOTALL)
        history_doc, history_pat, solution_text = run_dialogue_loop(
            sysprompt_doctor, sysprompt_patient, initial_message, case_index, doctor_info_clean, doctor_info, 
            rounds=10, true_patient_responses=None
        )
        conversation_data = save_conversation(case_index, history_doc, history_pat, doctor_info, doctor_info_clean, solution_text)
        # 运行交互
        data_check = AI_check(conversation_data)
        append_to_json_file(json_output_file, data_check)

    logger.info("所有病例处理完成")


def process_xlsx_to_json(xlsx_file):
    logger.info("程序启动")

    sysprompt_doctor = _sys_prompt_doctor
    sysprompt_patient = _sys_prompt_patient


    logger.info("加载测试数据集")
    df = pd.read_excel(xlsx_file)

    filtered_df = df.dropna(subset=["病例合并"])
    case_list = filtered_df.to_dict(orient="records")
    json_output_file = r"..\data\text.json"

    results_json = []

    for idx, case in enumerate(case_list, start=1):


        if idx > 2:
            break

        logger.info(f"正在处理第{idx}/{len(case_list)}个病例")
        messege = case["病例合并"]
        doctor_info = messege.replace("&#xA;", "\n")
        case_index = case["案例编号"]


        preliminary_exam = case["诊断标准"]
        treatment = case["治疗药物"]

        doctor_examination = case["初步检查"]
        has_examination = case["是否开检查"]
        
        # 初始输入
        initial_message = "请您开始问诊"
        doctor_info_clean = re.sub(r"【体格检查】.*?(\n|$)|【专科查体】.*?(\n|$)", "", doctor_info, flags=re.DOTALL)
        history_doc, history_pat, solution_text = run_dialogue_loop(
            sysprompt_doctor, sysprompt_patient, initial_message, case_index, doctor_info_clean, doctor_info, 
            rounds=10, true_patient_responses=None
        )
        conversation_data = save_conversation(case_index, history_doc, history_pat, doctor_info, doctor_info_clean, solution_text)

        conversation_data.update({
            "preliminary_exam": preliminary_exam,
            "treatment": treatment,
            "doctor_examination": doctor_examination,
            "has_examination": has_examination
        })
        # 运行交互
        data_check = AI_check(conversation_data)
        # append_to_json_file(json_output_file, data_check)
        results_json.append(data_check)

    with open(json_output_file, "w", encoding="utf-8") as json_file:
        json.dump(results_json, json_file, ensure_ascii=False, indent=4)
    logger.info("所有病例处理完成")

    return results_json



def main():
    xlsx_file = r"..\data\检查和药品0224_测试.xlsx"
    # process_xlsx(xlsx_file)

    process_xlsx_to_json(xlsx_file)

# --------------------------------------------------------
# 主函数
# --------------------------------------------------------

if __name__ == "__main__":

    main()
