"""
Test script for the AI Doctor system with batch processing capability.
"""
import asyncio
import json
import os
import pandas as pd
import threading
from typing import List, Dict, Optional
from src.config import MAX_ROUNDS
from src.interaction_history import InteractionHistory
from src.logger import get_logger
from src.planner import Planner
from src.executor import Executor
import re
import time

from src.external_context import ExternalContextManager

# 全局共享结果池
class SharedResultPool:
    def __init__(self):
        self._results = []
        self._lock = asyncio.Lock()
    
    async def add_result(self, result):
        """添加结果到池子"""
        async with self._lock:
            self._results.append(result)
    
    async def get_and_clear_results(self):
        """获取并清空所有结果"""
        async with self._lock:
            results = self._results.copy()
            self._results.clear()
            return results
    
    async def has_results(self):
        """检查是否有结果"""
        async with self._lock:
            return len(self._results) > 0

# 全局共享结果池实例
shared_result_pool = SharedResultPool()

async def async_simplify_medical_pipeline(external_manager: ExternalContextManager, patient_content: str):
    """异步执行simplify_medical_pipeline并将结果放入共享池"""
    try:
        logger.info(f"开始异步执行simplify_medical_pipeline，患者内容：{patient_content}")
        retrieval_results = await external_manager.simplify_medical_pipeline(patient_content)
        if retrieval_results and hasattr(retrieval_results, 'all_patients'):
            recalled_info = ""
            for patient_node in retrieval_results.all_patients:
                patient = patient_node.patient
                # 构建患者信息字符串
                patient_info = f"患者ID: {patient.get_id()}"
                if patient.age:
                    patient_info += f", 年龄: {patient.age}岁"
                if patient.gender:
                    patient_info += f", 性别: {patient.gender}"
                if patient.basic_info:
                    patient_info += f", 基本信息: {patient.basic_info}"
                
                # 构建诊断信息
                diagnosis_info = ""
                if patient.disease:
                    diagnosis_info = f"诊断: {patient.disease.name}"
                    if patient.disease.description:
                        diagnosis_info += f" - {patient.disease.description}"
                else:
                    diagnosis_info = "诊断: 未提供"
                
                recalled_info += f"患者信息：{patient_info}\n"
                recalled_info += f"诊断：{diagnosis_info}\n\n"

                logger.info(f"patient_content: {patient_content}")
                logger.info(f"recalled_info: {recalled_info}")
            
            if recalled_info:
                await shared_result_pool.add_result(recalled_info)
                logger.info("异步检索完成，结果已添加到共享池")
            else:
                logger.warning("异步检索完成但未生成有效结果")
        else:
            logger.warning("异步检索未返回结果")
    except Exception as e:
        logger.error(f"异步检索过程中出错: {e}")


logger = get_logger("test")


def final_diagnosis_parser(final_diagnosis):
    """
    解析final_diagnosis，提取诊断结果
    输入: final_diagnosis 类似"诊断：上呼吸道感染，检查：血常规+C反应蛋白"
    输出: 诊断结果，类似"上呼吸道感染" 和 检查建议，类似"血常规+C反应蛋白"
    """
    diagnosis = ""
    examination = ""
    
    # 处理不同的分隔符情况
    if "诊断：" in final_diagnosis:
        parts = final_diagnosis.split("诊断：")
    elif "诊断:" in final_diagnosis:
        parts = final_diagnosis.split("诊断:")
    else:
        return diagnosis, examination
    
    if len(parts) > 1:
        # 提取诊断部分
        diagnosis_part = parts[1]
        
        # 检查是否包含检查建议
        if "检查：" in diagnosis_part:
            diagnosis = diagnosis_part.split("检查：")[0].strip()
            examination = diagnosis_part.split("检查：")[1].strip()
        elif "检查:" in diagnosis_part:
            diagnosis = diagnosis_part.split("检查:")[0].strip()
            examination = diagnosis_part.split("检查:")[1].strip()
        else:
            diagnosis = diagnosis_part.strip()
    
    return diagnosis, examination

async def api_next_response(interaction_history):
    """
    输入: interaction_history
    输出: 下一个问题或者最后的诊断；以及更新后的interaction_history
    注意：同时要更新 interaction_history

    """
    # 检查共享池中是否有可用的检索结果
    if await shared_result_pool.has_results():
        results = await shared_result_pool.get_and_clear_results()
        # 合并所有结果
        combined_recalled_info = "\n".join(results)
        interaction_history.add_recalled_info(combined_recalled_info)
        logger.info("从共享池中获取到检索结果并添加到interaction_history")
    else:
        logger.info("没有检索结果")


    
    # 创建 Planner 和 Executor
    planner = Planner(max_rounds=9, interaction_history=interaction_history, enable_cot_retrieval=False)
    executor = Executor(max_rounds=12, interaction_history=interaction_history)

    while True:
        while True:
            feedback, question, end_of_inquiry = await executor.next_question()
            if question or end_of_inquiry:
                break
            logger.warning("Generated question and end_of_inquiry are both False. Regenerating question.")

        if question:
            # 这里可以添加对问题的处理逻辑
            logger.info(f"Generated question: {question}")
            interaction_history.cot_entries[-1]["dialogue_history"].append({"role": "doctor", "content": question})
            return question, interaction_history
        elif end_of_inquiry:
            interaction_history.cot_entries[-1]["feedback"] = feedback  # 更新反馈
            await executor.update_observation()
            # 本轮strategy对应的问诊结束，生成新的strategy，进行下一轮问诊
            new_strategy, reasoning, should_terminate = await planner.generate_strategy()
            # Print round summary
            round_num = interaction_history.get_round_num()
            logger.info(f"TEST ROUND {round_num} SUMMARY")
            logger.info("="*50)
            logger.info(f"Strategy: {new_strategy}")
            logger.info("-"*50)
            logger.info(f"Reasoning: {reasoning}")
            logger.info("="*50 + "\n")
            # Check if we should terminate
            if should_terminate or round_num >= MAX_ROUNDS:
                logger.info("Terminating test diagnosis process")
                final_diagnosis = planner.get_final_diagnosis()
                logger.info(f"Final diagnosis: {final_diagnosis}")
                logger.info("="*50 + "\n")
                return final_diagnosis, interaction_history
            else:
                # 继续进行下一轮问诊
                continue

async def test_api():
    """
    测试api_next_response函数
    """
    print("\n" + "="*50)
    print("TESTING AI DOCTOR SYSTEM")
    print("="*50)
    print("This is a test run of the AI Doctor system.")
    print("Please respond as if you were a patient with symptoms.")
    print("="*50 + "\n")

    # Create a mock initial strategy for testing
    initial_strategy = "询问患者的基本信息（性别、年龄）和主诉症状，了解发病时间、起病情况、症状特点（性质、程度、持续时间、频率）、伴随症状、病情演变以及之前的诊疗经过。"
    initial_reasoning = "主诉是问诊的切入点，通过了解患者的基本信息和主要症状，可以快速建立初步印象。进一步询问发病时间、起病情况、症状特点、伴随症状、病情演变和诊疗经过，有助于全面了解现病史，为后续诊断提供关键信息。这种系统性的问诊策略能够帮助我们准确把握患者的病情，避免遗漏重要信息。"

    initial_question = "您好，我是本次的医生，请问孩子的年龄，性别，主要的症状是什么？"

#     doctor_info = """
#     女，12岁0月3天，就诊日期2024/3/20
# 【主诉】4月前反复咳嗽，初夜咳嗽，运动加重。1月前，外院诊断肺炎？，无发热，有啰音。28/2胸片：右下较左侧斑点影。20/3肺功能（-）；心电图：窦性心动过速。口服仙特明+孟鲁司特1周，眼睛刚开始痒。既往常年清嗓子
# 【现病史】
# 【既往史】
# 【传染病接触史】无
# 【个人史】
# 【家族史】无
# 【体格检查】见专科查体
# 【专科查体】精神反应好，咽部无异常，未见呼吸困难，双肺呼吸音清，心腹（-）。
# 【辅助检查】20/3血常规+CRP：（-）；
    
#     """

#     doctor_info = """
# 男，1岁0月1天，就诊日期2024/6/18
# 【主诉】发热1天
# 【现病史】发热1天，无咳嗽，无呕吐、腹泻。
# 【既往史】
# 【传染病接触史】否认流行病学史
# 【个人史】
# 【家族史】否认家族遗传病史
# 【体格检查】
# 【专科查体】一般可，呼吸平稳，咽轻度充血，双扁桃体无肿大，颈部淋巴结无肿大，双肺呼吸音清，未闻罗音，心音有力，律齐，未闻杂音，腹软，未及包块，右下腹压痛阴性，皮肤无皮疹，手足口无皮疹。
# 【辅助检查】

#     """

#     doctor_info = """
#     女，4岁2月13天，就诊日期2024/12/31
# 【主诉】1周前间断发热，咳嗽，夜咳嗽，有痰，性状不详。31/12胸片：左肺斑片影；呼吸道病原13项核酸：RSV阳性
# 【现病史】
# 【既往史】
# 【传染病接触史】无
# 【个人史】
# 【家族史】无
# 【体格检查】见专科查体
# 【专科查体】精神反应好，咽部无异常，未见呼吸困难，左肺少许干鸣音，心腹（-）。
# 【辅助检查】
    
#     """

    doctor_info = """
    女，4岁1月21天，就诊日期2024/1/12
【主诉】发热2天
【现病史】发热，体温最高39°C，有咳嗽，有鼻塞，有流涕，无腹泻，无呕吐，无抽搐。
【既往史】
【传染病接触史】否认流行病学史
【个人史】
【家族史】否认家族遗传病史
【体格检查】
【专科查体】一般情况好，咽充血，双侧扁桃体1度肿大，咽后壁有分泌物，未见脓苔，双肺呼吸音粗，未闻及干湿性啰音，心音有力，律齐，腹软，神经系统查体未见异常。
【辅助检查】
    """

    # Create InteractionHistory
    interaction_history = InteractionHistory()

    external_manager = ExternalContextManager()

    interaction_history.add_cot_entry(
        strategy=initial_strategy,
        reasoning=initial_reasoning,
        dialogue_history=[{"role": "doctor", "content": initial_question}],
        feedback="",
    )

    # 创建Planner实例
    planner = Planner(max_rounds=9, interaction_history=interaction_history)
    

    print(f"Initial question: {initial_question}")

    #拟诊 = ""
    final_diagnosis = ""

    # 循环调用api_next_response函数
    while True:

        # 获得患者的回答
        executor_ai_patient = Executor(max_rounds=12, interaction_history=interaction_history, doctor_info=doctor_info)

        answer = await executor_ai_patient._AI_patient_response()
        print(f"AI Patient: {answer}")

        # 更新interaction_history
        interaction_history.cot_entries[-1]["dialogue_history"].append({"role": "patient", "content": answer})

        # 将对话记录设置成patinet_content
        patient_content = ""
        for dialogue in interaction_history.cot_entries[-1]["dialogue_history"]:
            if dialogue["role"] == "doctor":
                patient_content += f"AI医生: {dialogue['content']}\n"
            elif dialogue["role"] == "patient":
                patient_content += f"患者: {dialogue['content']}\n"


        # 异步执行simplify_medical_pipeline并将结果放入共享池
        retrieval_task = asyncio.create_task(async_simplify_medical_pipeline(external_manager, patient_content))
        
        # 等待异步检索任务完成，最多等待7秒，超时后任务继续在后台运行
        try:
            logger.info("等待异步检索任务完成...")
            done, pending = await asyncio.wait({retrieval_task}, timeout=7.0)
            
            if done:
                # 任务在超时前完成
                logger.info("异步检索任务已完成")
                # 获取任务结果（如果需要）
                for task in done:
                    try:
                        result = await task
                    except Exception as e:
                        logger.error(f"异步检索任务执行过程中出错: {e}")
            else:
                # 任务超时，但继续在后台运行
                logger.warning("异步检索任务超时（7秒），但任务继续在后台运行")
                # 任务会继续在后台执行，不会被取消
                
        except Exception as e:
            logger.error(f"等待异步检索任务时出错: {e}")

        # 调用api_next_response函数
        response, interaction_history = await api_next_response(interaction_history)

        # 打印问题
        print(f"Generated question: {response}")

        # 如果question是一个诊断结果，则结束循环
        if "诊断：" in response or "诊断:" in response:
            print(f"Final diagnosis: {response}")
            final_diagnosis = response
            
            # # 进入医生补充信息流程
            # print("\n" + "="*50)
            # print("出拟诊卡片前医生补充信息")
            # print("="*50)
            # print("请输入补充信息：")
            
            # supplementary_info = input()
            # if supplementary_info:
            #     interaction_history.doctor_supplementary_info.append(supplementary_info)
            
            # # 获取当前观察信息
            current_observation = interaction_history.cot_entries[-2]["observation"] if interaction_history.cot_entries else ""
            
            # 调用verify_diagnosis_with_guidelines进行再次判断
            print("\n" + "="*50)
            print("正在进行拟诊核实...")
            diagnosis_parsed, examination_parsed = final_diagnosis_parser(final_diagnosis)
            # 会新增医生补充信息，无需单独update observation
            diagnosis_content, additional_tests, reasoning, guidelines_content = await planner.generate_preliminary_diagnosis(
                diagnosis_parsed, 
                examination_parsed, 
                current_observation
            )
            
            # 打印核实结果
            print("\n" + "="*50)
            print("拟诊结果")
            print("="*50)
            print(f"初步拟诊：{final_diagnosis}")
            print(f"核实拟诊：{diagnosis_content}")
            print(f"建议检查：{additional_tests}")
            print("\n推理过程：")
            print(reasoning)
            print("="*50 + "\n")
            print("出拟诊卡片")
            break

    # # 第一次补充信息
    # print("\n" + "="*50)
    # print("出拟诊卡片后 医生补充信息（如无请直接回车）：")
    # doctor_supplementary_info = input()
    # has_input = False
    # if doctor_supplementary_info:
    #     planner.interaction_history.add_doctor_supplementary_info(doctor_supplementary_info)
    #     has_input = True

    # # 获取检查内容
    # print("\n" + "="*50)
    # print("出拟诊卡片后 输入检查名称（如无请直接回车）：")
    # test_name = input()
    # if test_name:
    #     print("请输入检查结果：")
    #     test_result = input()
    #     test_info = {"项目名称": test_name, "结果": test_result}
    #     planner.interaction_history.add_test_recommendation(test_info)
    #     has_input = True
    
    # 确诊
    logger.info("\n" + "="*50)
    logger.info("医生补充信息和ocr检查后确诊")
    diagnosis, condition, reasoning = await planner.final_diagnosis_and_condition(has_input=False)
    logger.info("\n" + "="*50)
    logger.info("确诊结果")
    logger.info("="*50)
    logger.info(f"确诊: {diagnosis}")
    logger.info(f"病情描述: {condition}")
    logger.info("="*50 + "\n")

    # 获取治疗方案
    logger.info("获取治疗方案...")

    # 使用try-except块捕获可能的错误
    try:
        treatment_plan = await planner.get_treatments(planner.interaction_history.diagnosis, condition)
    except Exception as e:
        logger.error(f"获取治疗方案时出错: {e}")
        treatment_plan = "无法获取治疗方案"

    # 保存交互历史
    planner.interaction_history.to_jsonl("data\\batchdata_0414_claude.jsonl")

    # 打印治疗方案
    logger.info("\n" + "="*50)
    logger.info("治疗方案")
    logger.info("="*50)
    logger.info(treatment_plan)
    logger.info("="*50 + "\n")

    #将对话记录，最终诊断，治疗方案，interaction_history保存到xlsx和jsonl文件
    # 文件名加上时间戳
    interaction_history.to_jsonl(f"data\\graphrag_test_{time.strftime('%Y%m%d_%H%M%S')}.jsonl")

    interaction_history.to_xlsx(f"data\\graphrag_test_{time.strftime('%Y%m%d_%H%M%S')}.xlsx")





async def test_medical_diagnosis():
    """
    Test the medical diagnosis pipeline with a mock initial strategy.
    """
    print("\n" + "="*50)
    print("TESTING AI DOCTOR SYSTEM")
    print("="*50)
    print("This is a test run of the AI Doctor system.")
    print("Please respond as if you were a patient with symptoms.")
    print("="*50 + "\n")

    # Create a mock initial strategy for testing
    initial_strategy = "询问患者的性别年龄，症状，持续时间和严重程度。"
    initial_reasoning = "这个策略可以帮助我们了解患者的基本情况和症状的严重程度。"

    # Create InteractionHistory
    interaction_history = InteractionHistory()

    # Create Planner and Executor
    planner = Planner(max_rounds=9, init_strategy=initial_strategy, init_reasoning=initial_reasoning, interaction_history=interaction_history, enable_cot_retrieval=False)
    executor = Executor(max_rounds=12, interaction_history=interaction_history)


    # Main diagnosis loop
    round_num = 1
    while True:
        logger.info(f"Starting test round {round_num}")

        # Execute inquiry based on current strategy
        feedback, end_of_inquiry = await executor.inquire()

        # Update observation
        await executor.update_observation()

        # Generate new strategy
        new_strategy, reasoning, should_terminate = await planner.generate_strategy()

        # Print round summary
        print("\n" + "="*50)
        print(f"TEST ROUND {round_num} SUMMARY")
        print("="*50)
        print(f"Strategy: {new_strategy}")
        print("-"*50)
        print(f"Reasoning: {reasoning}")
        print("="*50 + "\n")

        # logger.info(f"interaction_history: \n {planner.interaction_history.format_cot_entries()}")

        # Check if we should terminate
        if should_terminate or round_num >= 10:
            logger.info("Terminating test diagnosis process")
            break


        round_num += 1

    # Get final diagnosis
    final_diagnosis = planner.get_final_diagnosis()

    logger.info(f"interaction_history: \n {planner.interaction_history.format_cot_entries()}")

    # Print final diagnosis
    print("\n" + "="*50)
    print("TEST FINAL DIAGNOSIS")
    print("="*50)
    print(final_diagnosis)
    print("="*50 + "\n")

    return final_diagnosis, planner.interaction_history.format_cot_output()

async def batch_test_patient(doctor_info, auxiliary_exam, enable_cot_retrieval=False):
    """
    Test the medical diagnosis pipeline with a mock initial strategy.
    
    参数:
        doctor_info (str): 医生提供的病例信息
        auxiliary_exam (str): 辅助检查信息
        enable_cot_retrieval (bool): 是否启用COT召回功能，默认为False
    """
    print("\n" + "="*50)
    print("TESTING AI DOCTOR SYSTEM")
    print("="*50)
    print("This is a test run of the AI Doctor system.")
    print("Please respond as if you were a patient with symptoms.")
    print("="*50 + "\n")

    # Create a mock initial strategy for testing
    initial_strategy = "询问患者的基本信息（性别、年龄）和主诉症状，了解发病时间、起病情况、症状特点（性质、程度、持续时间、频率）、伴随症状、病情演变以及之前的诊疗经过。"
    initial_reasoning = "主诉是问诊的切入点，通过了解患者的基本信息和主要症状，可以快速建立初步印象。进一步询问发病时间、起病情况、症状特点、伴随症状、病情演变和诊疗经过，有助于全面了解现病史，为后续诊断提供关键信息。这种系统性的问诊策略能够帮助我们准确把握患者的病情，避免遗漏重要信息。"

    # Create Planner and Executor
    # Create InteractionHistory
    interaction_history = InteractionHistory()
    interaction_history.add_test_recommendation({
        "检查": "之前检查信息",
        "结果": auxiliary_exam,
    })
    planner = Planner(max_rounds=12, init_strategy=initial_strategy, init_reasoning=initial_reasoning, interaction_history=interaction_history, enable_cot_retrieval=enable_cot_retrieval)
    executor = Executor(max_rounds=9, doctor_info=doctor_info, interaction_history=interaction_history)

    # Set initial strategy for Executor
    # executor.set_strategy(initial_strategy)

    # Main diagnosis loop
    round_num = 1
    while True:
        logger.info(f"Starting test round {round_num}")

        # Execute inquiry based on current strategy
        feedback, end_of_inquiry = await executor.inquire()

        # Update observation
        await executor.update_observation()
        observation = executor.get_observation()

        # Generate new strategy
        new_strategy, reasoning, should_terminate = await planner.generate_strategy()

        # Print round summary
        print("\n" + "="*50)
        print(f"TEST ROUND {round_num} SUMMARY")
        print("="*50)
        print(f"Strategy: {new_strategy}")
        print("-"*50)
        print(f"Reasoning: {reasoning}")
        print("="*50 + "\n")

        logger.info(f"interaction_history: \n {planner.interaction_history.format_cot_entries()}")

        # Check if we should terminate
        if should_terminate or round_num >= 10:
            logger.info("Terminating test diagnosis process")
            break

        # Update strategy for next round
        # executor.set_strategy(new_strategy)
        round_num += 1
    final_diagnosis = planner.get_final_diagnosis()

    # 获取当前观察信息
    current_observation = interaction_history.cot_entries[-2]["observation"] if interaction_history.cot_entries else ""
    
    # 调用verify_diagnosis_with_guidelines进行再次判断
    print("\n" + "="*50)
    print("正在进行拟诊核实...")
    diagnosis_parsed, examination_parsed = final_diagnosis_parser(final_diagnosis)
    logger.info(f"初步诊断结果: {diagnosis_parsed}")
    logger.info(f"初步检查建议: {examination_parsed}")
    # 会新增医生补充信息，无需单独update observation
    diagnosis_content, additional_tests, reasoning, guidelines_content = await planner.generate_preliminary_diagnosis(
        diagnosis_parsed, 
        examination_parsed, 
        current_observation
    )

    # 根据案例编号的奇偶性决定是否添加补充信息
    try:
        case_number_text = doctor_info.split("案例编号：")[1].split("\n")[0].strip()
        # 提取数字部分
        numeric_part = re.search(r'\d+', case_number_text)
        if numeric_part:
            case_number = int(numeric_part.group())
        else:
            # 如果没有数字部分，使用哈希值的奇偶性
            case_number = hash(case_number_text) % 100  # 使用哈希值的最后两位
        has_supplementary_info = case_number % 2 == 0
        logger.info(f"案例编号: {case_number_text}, 提取数字: {case_number}, 是否添加补充信息: {has_supplementary_info}")
    except Exception as e:
        # 默认为偶数，添加补充信息
        logger.warning(f"提取案例编号时出错: {str(e)}，默认添加补充信息")
        has_supplementary_info = True
    
    if has_supplementary_info:
        # 添加医生补充信息
        supplementary_info = "患者症状参照上述对话"
        planner.interaction_history.add_doctor_supplementary_info(supplementary_info)
    
    # 确诊
    print("\n" + "="*50)
    print("医生补充信息和ocr检查后确诊")
    diagnosis, condition, diagnosis_reasoning = await planner.final_diagnosis_and_condition(has_supplementary_info)
    
    # 获取治疗方案
    print("\n" + "="*50)
    print("获取治疗方案...")
    
    # 使用try-except块捕获可能的错误
    try:
        # 使用交互历史中的诊断结果
        final_diagnosis_from_history = planner.interaction_history.diagnosis
        treatment_plan = await planner.get_treatments(final_diagnosis_from_history, condition)
    except Exception as e:
        print(f"获取治疗方案时出错: {e}")
        treatment_plan = "无法获取治疗方案"

    # 构建诊断指南对应关系
    diagnosis_info = {
        "初步拟诊": final_diagnosis,
        "二次拟诊": diagnosis_content,
        "二次检查": additional_tests,
        "拟诊核实思考": reasoning,
        "拟诊后是否补充信息": "是" if has_supplementary_info else "否",
        "确诊病名": diagnosis,
        "确诊思考": diagnosis_reasoning,
    }

    return (
        final_diagnosis,
        planner.interaction_history.format_cot_output(),
        current_observation,
        executor.format_dialogue_history(),
        treatment_plan,
        diagnosis_info,
        planner.interaction_history.diagnosis_guidelines  # 添加诊断指南历史
    )


async def batch_process_jsonl(enable_cot_retrieval=False):
    """
    Batch process patient data from Excel file and save results to a JSONL file.
    
    参数:
        enable_cot_retrieval (bool): 是否启用COT召回功能，默认为False
    """
    # Read the Excel file
    # df = pd.read_excel('data/0417RI评价.xlsx')
    # df = pd.read_excel('data/merged_cases.xlsx')
    df = pd.read_excel('data/base_cases.xlsx')

    # 定义需要跳过的案例编号列表 
    # skip_case_ids = ['C06', 'C22', 'C24', 'C36', 'C51', 'C52', 'C04', 'C10', 'C13', 'C54']

    # 过滤掉需要跳过的案例
    # filtered_df = df[~df['案例编号'].isin(skip_case_ids)]
    
    # 输出文件路径
    output_path = f'data/batch_process_0609_1616{"_with_cot" if enable_cot_retrieval else ""}.jsonl'
    
    logger.info(f"启动批处理，COT检索功能：{'启用' if enable_cot_retrieval else '禁用'}")
    logger.info(f"结果将保存到: {output_path}")

    # 检查输出文件是否存在，如果不存在则创建
    if not os.path.exists(output_path):
        with open(output_path, 'w', encoding='utf-8') as f:
            pass

    # 打开 JSONL 文件准备写入（使用 append 模式）
    with open(output_path, 'a', encoding='utf-8') as f:
        # 取过滤后的前10个案例
        for index, row in df.iloc[0:15].iterrows():
            # logger.info(f"处理患者 {index + 1}，医生信息: {row['病例合并']}")
            doctor_info = row['病例合并'].replace("&#xA;", "\n")
            # 提取辅助检查信息
            auxiliary_exam = ""
            if "【辅助检查】" in doctor_info:
                auxiliary_exam_parts = doctor_info.split("【辅助检查】")
                if len(auxiliary_exam_parts) > 1:
                    auxiliary_exam = auxiliary_exam_parts[1].strip()
                    doctor_info_clean = auxiliary_exam_parts[0].strip()
            
            index_for_xlsx = row["案例编号"]
            # doctor_check = row['初步检查']
            doctor_diagnosis = row['诊断标准']
            doctor_treatment = row['治疗药物']
            # print(f"doctor：{doctor_info}")
            logger.info(f"处理患者 {index + 1}，医生信息: {doctor_info_clean}")
            logger.info(f"提取的辅助检查信息: {auxiliary_exam}")

            # 调用异步处理函数，传入辅助检查信息和COT启用标志
            batch_test_patient_result = await batch_test_patient(doctor_info, auxiliary_exam, enable_cot_retrieval)
            
            # 构建结果字典
            result = {
                "案例编号": index_for_xlsx,
                '病例合并': doctor_info,
                '初步拟诊': batch_test_patient_result[5]['初步拟诊'],
                '二次拟诊': batch_test_patient_result[5]['二次拟诊'],
                '二次检查': batch_test_patient_result[5]['二次检查'],
                '拟诊核实思考': batch_test_patient_result[5]['拟诊核实思考'],
                '拟诊后是否补充信息': batch_test_patient_result[5]['拟诊后是否补充信息'],
                '确诊病名': batch_test_patient_result[5]['确诊病名'],
                '确诊思考': batch_test_patient_result[5]['确诊思考'],
                '标准诊断': doctor_diagnosis,
                '对话历史': batch_test_patient_result[3],
                '诊断历史': batch_test_patient_result[1],
                '观察': batch_test_patient_result[2],
                '治疗方案': batch_test_patient_result[4],
                '治疗药物': doctor_treatment
            }

            # 添加诊断指南历史，每个字典作为独立字段
            for i, d in enumerate(batch_test_patient_result[6], 1):
                result[f'指南历史{i}'] = f"召回病名：{d['diagnosis']}\n召回指南：{d['guidelines']}\n时间：{d['timestamp']}"

            # 写入一行 JSON（ensure_ascii=False 可保持中文）
            f.write(json.dumps(result, ensure_ascii=False) + '\n')
            f.flush()  # 确保数据立即写入文件
            os.fsync(f.fileno())  # 确保操作系统将数据写入磁盘
            logger.info(f"成功写入案例编号: {index_for_xlsx}")

    print(f"Batch processing completed. Results saved to '{output_path}'.")



async def batch_process(save_format='xlsx'):
    """
    Batch process patient data from Excel file and save results to a new file in the specified format.
    """
    # Read the Excel file
    df = pd.read_excel('data/检查和药品0224.xlsx')

    # Prepare a list to store results
    results = []

    # Process each patient
    for index, row in df.iloc[1:].iterrows():
        doctor_info = row['病例合并']
        index_for_xlsx = row["案例编号"]
        print(f"doctor：{doctor_info}")
        print(f"Processing patient {index + 1} with doctor info: {doctor_info}")
        # Call the batch test function
        batch_test_patient_result = await batch_test_patient(doctor_info)
        # Store the result
        result = {
            "案例编号": index_for_xlsx,
            '病例合并': doctor_info,
            '最终诊断': batch_test_patient_result[0],
            '对话历史': batch_test_patient_result[1],
            '观察': batch_test_patient_result[2],
            'CoT': batch_test_patient_result[3],
            '治疗方案': batch_test_patient_result[4]
        }
        results.append(result)

    # Save results based on the specified format
    if save_format == 'xlsx':
        with pd.ExcelWriter('data/batch_process.xlsx', mode='a', engine='openpyxl', if_sheet_exists='overlay') as writer:
            sheet_name = 'Results'
            if sheet_name in writer.sheets:
                startrow = writer.sheets[sheet_name].max_row
            else:
                startrow = 0
            result_df = pd.DataFrame(results)
            result_df.to_excel(
                writer,
                index=False,
                header=(startrow == 0),
                startrow=startrow + 1 if startrow !=0 else startrow)
    elif save_format == 'json':
        if os.path.exists('data/batch_process.json'):
            with open('data/batch_process.json', 'r+', encoding='utf-8') as file:
                existing_data = pd.read_json(file)
                result_df = pd.DataFrame(results)
                combined_data = pd.concat([existing_data, result_df], ignore_index=True)
                file.seek(0)
                combined_data.to_json(file, force_ascii=False, orient='records', lines=True)
        else:
            result_df = pd.DataFrame(results)
            result_df.to_json('data/batch_process.json', force_ascii=False, orient='records', lines=True)
    print(f"Batch processing completed. Results saved to 'data/batch_process.{save_format}'.")

if __name__ == "__main__":
    # Create logs directory if it doesn't exist
    os.makedirs("logs", exist_ok=True)

    # 可以通过命令行参数或环境变量控制是否启用COT召回
    import sys
    enable_cot = False
    if len(sys.argv) > 1 and sys.argv[1].lower() in ['true', '1', 'yes', 'y']:
        enable_cot = True
    
    # 打印是否启用COT召回的信息
    print(f"COT检索功能：{'启用' if enable_cot else '禁用'}")
    
    # Run the batch process function with the COT retrieval setting
    # asyncio.run(batch_process_jsonl(enable_cot_retrieval=enable_cot))
    asyncio.run(test_api())

