package com.makiyo.aidoctor.controller;

import com.makiyo.aidoctor.entity.Message;
import com.makiyo.aidoctor.entity.Session;
import com.makiyo.aidoctor.entity.User;
import com.makiyo.aidoctor.form.LoginOrRegisterForm;
import com.makiyo.aidoctor.form.LoginRequest;
import com.makiyo.aidoctor.form.UserInfoForm;
import com.makiyo.aidoctor.response.Response;
import com.makiyo.aidoctor.service.MessageService;
import com.makiyo.aidoctor.service.SessionService;
import com.makiyo.aidoctor.service.UserService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PathVariable;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.time.LocalDateTime;

@Tag(name = "用户管理", description = "用户相关的 API 接口")
@Validated
@RestController
@RequestMapping("/user")
public class UserController {

    @Resource
    private UserService userService;

    @Resource
    private SessionService sessionService;

    @Resource
    private MessageService messageService;

    @Operation(summary = "获取用户列表", description = "获取系统中所有用户的列表")
    @GetMapping("/list")
    public Response<List<User>> list() {
        return Response.ok(userService.listAll());
    }

    @Operation(summary = "用户登录", description = "用户登录认证接口")
    @PostMapping("/login")
    public Response<LoginOrRegisterForm> login(@RequestBody @Valid LoginRequest loginRequest) {
        try {
            System.out.println("收到登录请求，用户名: " + loginRequest.getUsername());
            
            // 1. 获取用户信息
            UserInfoForm userInfo = userService.getUserInfoByUsername(loginRequest.getUsername());
            if (userInfo == null) {
                return Response.error(404, "用户不存在");
            }

            // 2. 获取用户的所有会话
            List<Session> sessions = sessionService.getUserSessions(userInfo.getUserId());

            // 3. 如果没有会话，创建一个新会话
            if (sessions.isEmpty()) {
                Session session = sessionService.createSession(userInfo.getUserId());
                sessions = Collections.singletonList(session);

                // 创建欢迎消息
                Message welcomeMessage = new Message();
                welcomeMessage.setSessionId(session.getId());
                welcomeMessage.setSpeaker("doctor");
                welcomeMessage.setMessage("您好，我是本次您的接诊医生。请问您的性别和年龄？");
                messageService.createMessage(welcomeMessage);
            }

            // 4. 获取每个会话的消息列表
            Map<Integer, List<Message>> sessionMessages = new HashMap<>();
            for (Session session : sessions) {
                List<Message> messages = messageService.getSessionMessages(session.getId());
                sessionMessages.put(session.getId(), messages);
            }

            // 5. 组装返回数据
            LoginOrRegisterForm form = new LoginOrRegisterForm();
            form.setUserId(userInfo.getUserId());
            form.setSessions(sessions);
            form.setSessionMessages(sessionMessages);
            form.setUserInfo(userInfo);

            return Response.ok(form);
        } catch (Exception e) {
            System.err.println("登录异常: " + e.getMessage());
            e.printStackTrace();
            return Response.error(500, "登录失败：" + e.getMessage());
        }
    }

    @Operation(summary = "用户注册", description = "新用户注册接口")
    @PostMapping("/register")
    public Response<String> register(@RequestBody @Valid User user) {
        try {
            System.out.println("收到注册请求: " + user.getUserId());
            
            // 1. 注册新用户
            UserInfoForm userInfo = userService.register(user);
            if (userInfo == null) {
                System.err.println("注册失败: userInfo 为空");
                return Response.fail("注册失败：用户创建失败");
            }

            // 2. 创建一个初始会话
            Session session = sessionService.createSession(user.getUserId());
            if (session == null) {
                System.err.println("创建会话失败");
                return Response.fail("注册失败：创建会话失败");
            }
            
            List<Session> sessions = Collections.singletonList(session);

            // 3. 创建初始消息
            Message welcomeMessage = new Message();
            welcomeMessage.setSessionId(session.getId());
            welcomeMessage.setSpeaker("doctor");
            welcomeMessage.setMessage("您好，我是本次您的接诊医生。请问您的性别和年龄？");
            Message createdMessage = messageService.createMessage(welcomeMessage);
            
            if (createdMessage == null) {
                System.err.println("创建欢迎消息失败");
                return Response.fail("注册失败：创建欢迎消息失败");
            }

            System.out.println("注册成功: " + user.getUserId());
            return Response.ok("注册成功！");

        } catch (Exception e) {
            System.err.println("注册异常: " + e.getMessage());
            e.printStackTrace();
            return Response.fail("注册失败：" + e.getMessage());
        }
    }

    @Operation(summary = "用户聊天", description = "用户与AI医生进行对话 (Original V1 logic)")
    @PostMapping("/chat")
    public Response<LoginOrRegisterForm> userChat(
            @RequestParam @NotNull(message = "用户ID不能为空") 
            @Positive(message = "用户ID必须为正整数") Integer userId) {
        try {
            UserInfoForm userInfo = userService.getUserInfo(userId);
            if (userInfo == null) return Response.fail("用户不存在");
            List<Session> sessions = sessionService.getUserSessions(userId);
            if (sessions.isEmpty()) {
                Session session = sessionService.createSession(userId);
                sessions = Collections.singletonList(session);
                Message welcomeMessage = new Message();
                welcomeMessage.setSessionId(session.getId());
                welcomeMessage.setSpeaker("doctor");
                welcomeMessage.setMessage("您好，我是本次您的接诊医生。请问您的性别和年龄？");
                messageService.createMessage(welcomeMessage);
            }
            Map<Integer, List<Message>> sessionMessagesMap = new HashMap<>();
            for (Session session : sessions) {
                List<Message> messages = messageService.getSessionMessages(session.getId());
                sessionMessagesMap.put(session.getId(), messages);
            }
            LoginOrRegisterForm form = new LoginOrRegisterForm();
            form.setUserId(userId);
            form.setSessions(sessions);
            form.setSessionMessages(sessionMessagesMap);
            form.setUserInfo(userInfo);
            return Response.ok(form);
        } catch (Exception e) {
             System.err.println("Chat 异常: " + e.getMessage());
             e.printStackTrace();
             return Response.fail("获取聊天信息失败：" + e.getMessage());
        }
    }

    @Operation(summary = "用户聊天V2", description = "解析V2对话历史并返回V1兼容格式")
    @PostMapping("/chatV2")
    public Response<LoginOrRegisterForm> userChatV2(
            @RequestParam @NotNull(message = "用户ID不能为空") 
            @Positive(message = "用户ID必须为正整数") Integer userId) {
         try {
             UserInfoForm userInfo = userService.getUserInfo(userId);
             if (userInfo == null) return Response.fail("用户不存在");
             List<Session> sessions = sessionService.getUserSessions(userId);
             if (sessions.isEmpty()) {
                 Session session = sessionService.createSession(userId);
                 sessions = Collections.singletonList(session);
                 // No initial message needed here for V2
             }
 
             // Assume V2 session is the latest one
             Session v2Session = null; 
             if (!sessions.isEmpty()) {
                 v2Session = sessions.get(sessions.size() - 1); 
             }

             Map<Integer, List<Message>> sessionMessagesMap = new HashMap<>();
             for (Session session : sessions) {
                 List<Message> messageListForSession = new ArrayList<>();
                 if (v2Session != null && session.getId().equals(v2Session.getId())) {
                     // This is the V2 session, parse its history
                     System.out.println("Processing V2 session: " + session.getId());
                     List<Map<String, Object>> dialogueTurns = userService.extractV2DialogueHistoryTurns(session.getId());
                     int messageIdCounter = 1; // Simple counter for synthesized IDs
                     for (Map<String, Object> turn : dialogueTurns) {
                         Message synthMessage = new Message();
                         synthMessage.setId(messageIdCounter++); // Assign temp ID
                         synthMessage.setSessionId(session.getId());
                         synthMessage.setSpeaker((String) turn.get("role"));
                         synthMessage.setMessage((String) turn.get("content"));
                         // Set a default timestamp or leave null if not crucial
                         // synthMessage.setCreatedAt(LocalDateTime.now()); 
                         messageListForSession.add(synthMessage);
                     }
                     System.out.println("Synthesized " + messageListForSession.size() + " messages for V2 session.");
                 } else {
                     // Assume this is a V1 session, fetch normally
                     System.out.println("Processing V1 session: " + session.getId());
                     messageListForSession = messageService.getSessionMessages(session.getId());
                     System.out.println("Fetched " + messageListForSession.size() + " messages for V1 session.");
                 }
                 sessionMessagesMap.put(session.getId(), messageListForSession);
             }
 
             LoginOrRegisterForm form = new LoginOrRegisterForm();
             form.setUserId(userId);
             form.setSessions(sessions);
             form.setSessionMessages(sessionMessagesMap); // Use the map containing synthesized V2 messages
             form.setUserInfo(userInfo);
 
             return Response.ok(form);
         } catch (Exception e) {
              System.err.println("ChatV2 异常: " + e.getMessage());
              e.printStackTrace();
              return Response.fail("获取 V2 聊天信息失败：" + e.getMessage());
         }
     }

    @Operation(summary = "获取用户信息", description = "根据用户ID获取用户详细信息")
    @GetMapping("/{userId}")
    public Response<User> getUserById(@PathVariable Integer userId) {
        try {
            User user = userService.login(userId);
            if (user == null) {
                return Response.error(404, "用户不存在");
            }
            return Response.success(user);
        } catch (Exception e) {
            return Response.error(500, e.getMessage());
        }
    }
}
