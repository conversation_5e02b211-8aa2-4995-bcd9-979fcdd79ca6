"""
Main module for the AI Doctor system.
"""
import asyncio
import os
from src.config import MAX_ROUNDS
from src.interaction_history import InteractionHistory
from src.logger import get_logger
from src.planner import Planner
from src.executor import Executor

logger = get_logger("main")

async def medical_diagnosis_pipeline(initial_strategy=None, max_rounds=MAX_ROUNDS):
    """
    Main pipeline for the medical diagnosis process.
    
    Args:
        initial_strategy (str, optional): Initial diagnostic strategy.
        max_rounds (int, optional): Maximum number of rounds for the diagnosis.
        
    Returns:
        str: Final diagnosis.
    """
    logger.info("Starting medical diagnosis pipeline")

    initial_strategy = "询问患者的性别年龄，症状，持续时间和严重程度。"
    initial_reasoning = "这个策略可以帮助我们了解患者的基本情况和症状的严重程度。"

    # Create InteractionHistory
    interaction_history = InteractionHistory()
    
    # Create Planner and Executor
    planner = Planner(max_rounds, init_strategy=initial_strategy, init_reasoning=initial_reasoning, interaction_history=interaction_history)
    executor = Executor(max_rounds=7, interaction_history=interaction_history)  
    # Main diagnosis loop
    round_num = 1
    while True:
        logger.info(f"Starting round {round_num}")
        
        # Execute inquiry based on current strategy
        feedback, end_of_inquiry = await executor.inquire()
        
        # Update observation
        await executor.update_observation()
        observation = executor.get_observation()
        
        # Generate new strategy
        new_strategy, reasoning, should_terminate = await planner.generate_strategy()
        
        # Print round summary
        print("\n" + "="*50)
        print(f"第 {round_num} 轮总结")
        print("="*50)
        print(f"策略: {new_strategy}")
        print("-"*50)
        print(f"推理: {reasoning}")
        print("="*50 + "\n")
        
        # Check if we should terminate
        if should_terminate:
            logger.info("Terminating diagnosis process")
            break
        
        round_num += 1
    
    # Get final diagnosis
    final_diagnosis = planner.get_final_diagnosis()
    
    # Print final diagnosis
    print("\n" + "="*50)
    print("最终诊断")
    print("="*50)
    print(final_diagnosis)
    print("="*50 + "\n")
    
    return final_diagnosis

async def main():
    """
    Main entry point for the AI Doctor system.
    """
    # Print welcome message
    print("\n" + "="*50)
    print("欢迎使用AI医生")
    print("="*50)
    print("本系统将通过对话进行医疗诊断。")
    print("请尽可能准确地回答问题。")
    print("随时输入'exit'结束会话。")
    print("="*50 + "\n")
    
    # Start the diagnosis pipeline
    try:
        await medical_diagnosis_pipeline()
    except KeyboardInterrupt:
        print("\n会话被用户终止。")
    except Exception as e:
        logger.error(f"Error in diagnosis pipeline: {str(e)}")
        print(f"\n发生错误: {str(e)}")
    
    print("\n感谢使用AI医生。祝您健康！")

if __name__ == "__main__":
    # Create logs directory if it doesn't exist
    os.makedirs("logs", exist_ok=True)
    
    # Run the main function
    asyncio.run(main())