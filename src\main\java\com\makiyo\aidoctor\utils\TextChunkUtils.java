package com.makiyo.aidoctor.utils;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

public class TextChunkUtils {
    private static final int MAX_BYTES = 1024; // UTF-8编码下的最大字节数

    /**
     * 将文本按照UTF-8编码的字节长度分块
     * @param text 需要分块的文本
     * @return 分块后的文本列表
     */
    public static List<String> splitTextIntoChunks(String text) {
        List<String> chunks = new ArrayList<>();
        StringBuilder currentChunk = new StringBuilder();
        
        for (char c : text.toCharArray()) {
            String temp = currentChunk.toString() + c;
            byte[] bytes = temp.getBytes(StandardCharsets.UTF_8);
            
            if (bytes.length > MAX_BYTES) {
                // 当前块已满，保存当前块并开始新块
                chunks.add(currentChunk.toString());
                currentChunk = new StringBuilder();
                currentChunk.append(c);
            } else {
                currentChunk.append(c);
            }
        }
        
        // 添加最后一个块
        if (currentChunk.length() > 0) {
            chunks.add(currentChunk.toString());
        }
        
        return chunks;
    }
} 