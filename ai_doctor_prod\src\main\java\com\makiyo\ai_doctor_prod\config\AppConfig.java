package com.makiyo.ai_doctor_prod.config;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 应用程序配置类
 * 启用定时任务和设置组件扫描
 */
@Configuration
@EnableScheduling 
@ComponentScan(basePackages = {"com.makiyo.ai_doctor_prod.utils"})
public class AppConfig {
    // 配置已通过注解完成
} 