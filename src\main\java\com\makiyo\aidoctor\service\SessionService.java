package com.makiyo.aidoctor.service;

import com.makiyo.aidoctor.entity.Session;
import com.makiyo.aidoctor.entity.Message;
import com.makiyo.aidoctor.mapper.SessionMapper;
import com.makiyo.aidoctor.response.SessionWithMessagesDTO;
import org.springframework.stereotype.Service;
import com.makiyo.aidoctor.response.PageInfo;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Map;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;
import java.util.stream.Collectors;
import com.makiyo.aidoctor.mapper.MessageMapper;
import java.nio.charset.StandardCharsets;

@Service
public class SessionService {

    @Resource
    private SessionMapper sessionMapper;
    
    @Resource
    private MessageService messageService;

    @Resource
    private MessageMapper messageMapper;
    
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 获取用户的所有会话
     * @param userId 用户ID
     * @return 会话列表
     */
    public List<Session> getUserSessions(Integer userId) {
        return sessionMapper.selectByUserId(userId);
    }

    /**
     * 创建新会话
     * @param userId 用户ID
     * @return 创建的会话
     */
    public Session createSession(Integer userId) {
        Session session = new Session();
        session.setUserId(userId);
        Date now = new Date();
        session.setCreatedAt(now);
        session.setUpdatedAt(now);
        int result = sessionMapper.insertSelective(session);
        if (result > 0) {
            return session;  // session对象中的id字段会被MyBatis自动填充
        }
        return null;
    }

    /**
     * 获取会话详情
     * @param id 会话ID
     * @return 会话信息
     */
    public Session getSession(Integer id) {
        return sessionMapper.selectByPrimaryKey(id);
    }

    /**
     * 添加新会话
     * @param session 会话信息
     * @return 创建的会话，如果创建失败返回null
     */
    public Session addSession(Session session) {
        try {
            // 设置创建时间和更新时间
            Date now = new Date();
            session.setCreatedAt(now);
            session.setUpdatedAt(now);

            // 插入会话记录
            int result = sessionMapper.insertSelective(session);
            if (result > 0) {
                return session;
            }
            return null;
        } catch (Exception e) {
            System.err.println("创建会话失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 获取所有会话
     * @return 会话列表
     */
    public List<Session> getAllSessions() {
        return sessionMapper.selectAllSessions();
    }
    
    public PageInfo<SessionWithMessagesDTO> getAllSessionsWithPagination(int page, int size) {
        int offset = (page - 1) * size;
        List<Session> sessions = sessionMapper.selectSessionsWithPagination(offset, size);
        if (sessions.isEmpty()) {
            return new PageInfo<>(Collections.emptyList(), page, size, 0);
        }
        long total = sessionMapper.countTotalSessions();

        List<Integer> sessionIds = sessions.stream().map(Session::getId).collect(Collectors.toList());

        List<Map<String, Object>> counts = messageMapper.getMessageCounts(sessionIds);
        Map<Integer, Long> messageCounts = counts.stream()
                .collect(Collectors.toMap(
                        map -> (Integer) map.get("sessionId"),
                        map -> ((Number) map.get("messageCount")).longValue()
                ));

        List<Map<String, Object>> lastMessages = messageMapper.getLastMessagesForSessions(sessionIds);
        Map<Integer, String> lastMessageContentMap = lastMessages.stream()
                .collect(Collectors.toMap(
                        map -> (Integer) map.get("sessionId"),
                        map -> {
                            Object raw = map.get("message");
                            if (raw == null) {
                                return null;
                            } else if (raw instanceof byte[]) {
                                return new String((byte[]) raw, StandardCharsets.UTF_8);
                            } else {
                                return raw.toString();
                            }
                        }
                ));

        List<SessionWithMessagesDTO> dtos = sessions.stream().map(session -> {
            SessionWithMessagesDTO dto = new SessionWithMessagesDTO();
            dto.setId(session.getId());
            dto.setUserId(session.getUserId());
            dto.setCreatedAt(session.getCreatedAt());
            dto.setUpdatedAt(session.getUpdatedAt());
            dto.setMessageCount(messageCounts.getOrDefault(session.getId(), 0L));
            dto.setSessionStatus(determineStatusFromMessage(lastMessageContentMap.get(session.getId())));
            dto.setMessages(Collections.emptyList());
            return dto;
        }).collect(Collectors.toList());

        return new PageInfo<>(dtos, page, size, total);
    }

    private String determineStatusFromMessage(String messageJson) {
        if (messageJson == null || messageJson.trim().isEmpty()) {
            return "问诊阶段";
        }
        try {
            Map<String, Object> msgData = objectMapper.readValue(messageJson, new TypeReference<>() {});
            Map<String, Object> history = (Map<String, Object>) msgData.get("interaction_history");

            if (history != null) {
                if (history.containsKey("diagnosis") && history.get("diagnosis") != null && !history.get("diagnosis").toString().isEmpty()) {
                    return "最终诊断";
                }
                if (history.containsKey("preliminary_diagnosis") && history.get("preliminary_diagnosis") != null && !history.get("preliminary_diagnosis").toString().isEmpty()) {
                    return "初步诊断";
                }
                if (history.containsKey("inspection_suggestions") && history.get("inspection_suggestions") != null) {
                     return "检查建议";
                }
            }
        } catch (Exception e) {
            // Not a valid JSON or structure mismatch, treat as inquiry stage
            return "问诊阶段";
        }
        return "问诊阶段";
    }
    
    /**
     * 获取所有会话及其消息
     * @return 包含会话和消息的DTO列表
     */
    public List<SessionWithMessagesDTO> getAllSessionsWithMessages() {
        List<Session> sessions = sessionMapper.selectAllSessions();
        List<SessionWithMessagesDTO> result = new ArrayList<>();
        
        for (Session session : sessions) {
            List<Message> messages = messageService.getSessionMessages(session.getId());
            SessionWithMessagesDTO dto = SessionWithMessagesDTO.fromSession(session, messages);
            result.add(dto);
        }
        
        return result;
    }
}
