package com.makiyo.aidoctor.utils;

import com.alibaba.fastjson.JSON;
import lombok.Getter;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.framing.CloseFrame;
import org.java_websocket.handshake.ServerHandshake;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.math.BigInteger;
import java.net.URI;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.UUID;

public class TtsWebsocketDemo {
    private static final Logger log = LoggerFactory.getLogger(TtsWebsocketDemo.class);
    public static final String API_URL = "wss://openspeech.bytedance.com/api/v1/tts/ws_binary";

    public static void main(String[] args) throws Exception {
        // set your appid and access_token
        String appid = "4146985820";
        String accessToken = "z_2JNCRIbNM7Mk-dI24H6M8VAOv1JFeu";

        TtsRequest ttsRequest = TtsRequest.builder()
            .app(TtsRequest.App.builder()
                .appid(appid)
                .cluster("volcano_tts")
                .build())
            .user(TtsRequest.User.builder()
                .uid("uid")
                .build())
            .audio(TtsRequest.Audio.builder()
                .encoding("mp3")
                .voiceType("BV001_streaming")
                .build())
            .request(TtsRequest.Request.builder()
                .reqID(UUID.randomUUID().toString())
                .operation("query")
                .text("-----------------【问诊结果】-------------------\n" +
                        "【基本信息】男，4岁3个月\n" +
                        "【主诉】发热4天，热退1天，咳嗽\n" +
                        "【现病史】4天前发热，体温不详，伴咳嗽，无鼻塞、流涕、咽痛、头痛、乏力、食欲不振等症状。发热期间服用药物（具体不详），症状有所改善，热退1天，咳嗽减轻。病初胸片示左下实变明显，25号呼吸道病原13项核酸检测示博卡病毒阳性，24号咽拭子支原体核酸检测阴性。精神状态可，饮食、睡眠、大小便均正常。\n" +
                        "【既往史】否认慢性疾病史及过敏史\n" +
                        "【传染病接触史】否认\n" +
                        "【家族史】否认遗传病史及其他特殊病史\n" +
                        "【辅助检查】\n" +
                        "- 胸片：左下实变明显\n" +
                        "- 呼吸道病原13项核酸检测：博卡病毒阳性\n" +
                        "- 咽拭子支原体核酸检测：阴性\n" +
                        "\n" +
                        "-----------------【AI检查建议】-------------------\n" +
                        "无需额外检查\n" +
                        "\n" +
                        "-----------------【诊断建议】-------------------\n" +
                        "博卡病毒性肺炎\n" +
                        "\n" +
                        "-----------------【治疗方案】-------------------\n" +
                        "药品推荐：\n" +
                        "药品名: 复方福尔可定口服溶液\n" +
                        "规格: 100ml（每5ml含福尔可定5.0mg）\n" +
                        "服用方法: 口服\n" +
                        "剂量安排: 一次5ml，一日3次\n" +
                        "使用目的: 缓解病毒性呼吸道感染引起的咳嗽\n" +
                        "------------------------------\n" +
                        "\n" +
                        "药品名: 头孢地尼颗粒\n" +
                        "规格: 50mg/袋\n" +
                        "服用方法: 口服\n" +
                        "剂量安排: 按15kg体重计算：每日225mg（4.5袋），分3次服用，每次1.5袋\n" +
                        "使用目的: 预防或治疗胸片左下实变提示的继发细菌感染\n" +
                        "------------------------------\n" +
                        "\n" +
                        "生活建议：\n" +
                        "- 保持室内空气流通，避免接触冷空气刺激\n" +
                        "- 少量多次饮水维持呼吸道湿润\n" +
                        "- 饮食宜清淡易消化，避免生冷油腻食物\n" +
                        "- 监测体温变化，出现呼吸急促立即就诊\n" +
                        "\n" +
                        "------【本次问诊结束，继续测试请开始新的会话】------")
                .build())
            .build();
        TtsWebsocketClient ttsWebsocketClient = new TtsWebsocketClient(accessToken);
        byte[] audio = ttsWebsocketClient.submit(ttsRequest);
        FileOutputStream fos = new FileOutputStream("D:\\aiDoctor\\src\\main\\resources\\test.mp3");
        fos.write(audio);
        fos.close();
        log.info("TTS done.");
    }

    public static class TtsWebsocketClient extends WebSocketClient {
        private final ByteArrayOutputStream buffer = new ByteArrayOutputStream();

        public TtsWebsocketClient(String accessToken) {
            super(URI.create(API_URL), Collections.singletonMap("Authorization", "Bearer; " + accessToken));
        }

        public byte[] submit(TtsRequest ttsRequest) throws InterruptedException {
            String json = JSON.toJSONString(ttsRequest);
            log.info("request: {}", json);
            byte[] jsonBytes = json.getBytes(StandardCharsets.UTF_8);
            byte[] header = {0x11, 0x10, 0x10, 0x00};
            ByteBuffer requestByte = ByteBuffer.allocate(8 + jsonBytes.length);
            requestByte.put(header).putInt(jsonBytes.length).put(jsonBytes);

            this.connectBlocking();
            synchronized (this) {
                this.send(requestByte.array());
                wait();
                return this.buffer.toByteArray();
            }
        }

        @Override
        public void onMessage(ByteBuffer bytes) {
            int protocolVersion = (bytes.get(0) & 0xff) >> 4;
            int headerSize = bytes.get(0) & 0x0f;
            int messageType = (bytes.get(1) & 0xff) >> 4;
            int messageTypeSpecificFlags = bytes.get(1) & 0x0f;
            int serializationMethod = (bytes.get(2) & 0xff) >> 4;
            int messageCompression = bytes.get(2) & 0x0f;
            int reserved = bytes.get(3) & 0xff;
            bytes.position(headerSize * 4);
            byte[] fourByte = new byte[4];
            if (messageType == 11) {
                // Audio-only server response
                log.info("received audio-only response.");
                if (messageTypeSpecificFlags == 0) {
                    // Ack without audio data
                } else {
                    bytes.get(fourByte, 0, 4);
                    int sequenceNumber = new BigInteger(fourByte).intValue();
                    bytes.get(fourByte, 0, 4);
                    int payloadSize = new BigInteger(fourByte).intValue();
                    byte[] payload = new byte[payloadSize];
                    bytes.get(payload, 0, payloadSize);
                    try {
                        this.buffer.write(payload);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                    if (sequenceNumber < 0) {
                        // received the last segment
                        this.close(CloseFrame.NORMAL, "received all audio data.");
                    }
                }
            } else if (messageType == 15) {
                // Error message from server
                bytes.get(fourByte, 0, 4);
                int code = new BigInteger(fourByte).intValue();
                bytes.get(fourByte, 0, 4);
                int messageSize = new BigInteger(fourByte).intValue();
                byte[] messageBytes = new byte[messageSize];
                bytes.get(messageBytes, 0, messageSize);
                String message = new String(messageBytes, StandardCharsets.UTF_8);
                throw new TtsException(code, message);
            } else {
                log.warn("Received unknown response message type: {}", messageType);
            }
        }

        @Override
        public void onOpen(ServerHandshake serverHandshake) {
            log.info("opened connection");
        }

        @Override
        public void onMessage(String message) {
            log.info("received message: " + message);
        }

        @Override
        public void onClose(int code, String reason, boolean remote) {
            log.info("Connection closed by {}, Code: {}, Reason: {}", (remote ? "remote" : "us"), code, reason);
            synchronized (this) {
                notify();
            }
        }

        @Override
        public void onError(Exception e) {
            close(CloseFrame.NORMAL, e.toString());
        }
    }

    @Getter
    public static class TtsException extends RuntimeException {
        private final int code;
        private final String message;

        public TtsException(int code, String message) {
            super("code=" + code + ", message=" + message);
            this.code = code;
            this.message = message;
        }
    }
}