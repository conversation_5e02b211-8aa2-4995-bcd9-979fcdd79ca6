package com.makiyo.ai_doctor_prod.repository;

import com.makiyo.ai_doctor_prod.entity.OcrResult;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * OCR结果的MongoDB存储库
 */
@Repository
public interface OcrResultRepository extends MongoRepository<OcrResult, String> {
    
    /**
     * 根据案例ID查找OCR结果
     * @param caseId 案例ID
     * @return OCR结果列表
     */
    List<OcrResult> findByCaseId(String caseId);
    
    /**
     * 根据图片ID查找OCR结果
     * @param imageId GridFS中的图片ID
     * @return OCR结果，如果存在
     */
    Optional<OcrResult> findByImageId(String imageId);
    
    /**
     * 根据案例ID和原始文件名查找OCR结果
     * @param caseId 案例ID
     * @param originalFilename 原始文件名
     * @return OCR结果，如果存在
     */
    Optional<OcrResult> findByCaseIdAndOriginalFilename(String caseId, String originalFilename);
    
    /**
     * 查找给定案例ID的最新OCR结果
     * @param caseId 案例ID
     * @return OCR结果列表，按创建时间降序排序
     */
    List<OcrResult> findByCaseIdOrderByCreatedAtDesc(String caseId);
    
    /**
     * 删除指定案例ID的所有OCR结果
     * @param caseId 案例ID
     */
    void deleteByCaseId(String caseId);

    Page<OcrResult> findAll(Pageable pageable);
}