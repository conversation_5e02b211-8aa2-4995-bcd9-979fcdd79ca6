import json
from pathlib import Path
from src.logger import get_logger


class InteractionHistory:
    """
    InteractionHistory 类用于存储和管理策略、推理、反馈、对话历史和观察信息。
    """

    def __init__(self):
        """
        初始化 InteractionHistory 类。
        """
        self.cot_entries = []  # 存储每个 CoT 条目，包括策略、推理、反馈和相关对话历史
        # self.observation = ""  # 存储观察信息
        self.diagnosis = ""  # 存储最终诊断结果
        # 存储查体信息，格式为字典列表，如 [{"查体": "询问是否存在呼吸急促（呼吸频率＞30次/分）", "检查原因": "呼吸急促是WHO定义的小儿肺炎重要诊断标准，呼吸频率＞30次/分提示可能存在下呼吸道感染。"}, ...]
        # self.physical_examination_recommendation = [] # 存储查体推荐信息
        self.test_recommendation = []       # 存储检查信息，格式为字典列表，如 [{"检查": "血常规", "检查原因": "血常规可以帮助判断是否存在感染。"}, ...]
        self.treatment_recommendation = []  # 存储治疗信息，格式暂定
        # 存储拟诊信息
        self.preliminary_diagnosis = None # 存储初步诊断结果，格式为字典, 如 {"初步诊断": "小儿肺炎", "诊断原因": "患者症状...... "}，如果没有初步诊断，则为None
        # 需要检查信息,医生补充信息
        # 存储医生补充信息，格式为字典列表，如 [{"补充内容": "患者有过敏史", "补充时间": "2023-05-01 10:30"}, ...]
        self.doctor_supplementary_info = []

        self.guidance_for_treatment = None # 存储治疗指南

        self.inspection_suggestions = None # 存储检查建议
        
        # 新增：存储诊断和指南的对应关系
        self.diagnosis_guidelines = []  # 存储诊断和指南的对应关系，格式为 [{"diagnosis": "诊断名称", "guidelines": "指南内容", "timestamp": "时间戳"}, ...]
    
    def restore_state(self):
        """
        恢复最近的状态，包括策略、推理、反馈和对话历史。
        
        返回:
            tuple: (strategy, reasoning, feedback, dialogue_history)
        """
        if self.cot_entries:
            last_entry = self.cot_entries[-1]
            return (last_entry["strategy"], last_entry["reasoning"], last_entry["feedback"], last_entry["dialogue_history"])
        return None, None, None, []

    def add_cot_entry(self, strategy, reasoning, feedback, dialogue_history, observation= ""):
        """
        添加一个新的 CoT 条目。

        参数:
            strategy (str): 策略内容。
            reasoning (str): 推理内容。
            feedback (str): 反馈内容。
            dialogue_history (list): 与该 CoT 条目相关的对话历史。
        """
        entry = {
            "strategy": strategy,
            "reasoning": reasoning,
            "feedback": feedback,
            "dialogue_history": dialogue_history,
            "observation" : observation
        }
        self.cot_entries.append(entry)

    def format_cot_entries(self):
        """
        格式化 CoT 条目为字符串。

        返回:
            str: 格式化后的 CoT 字符串。
        """
        formatted_cot = ""
        for entry in self.cot_entries:
            formatted_cot += f"策略: {entry['strategy']}\n"
            formatted_cot += f"推理: {entry['reasoning']}\n"
            formatted_cot += f"反馈: {entry['feedback']}\n"
            formatted_cot += f"对话历史: {entry['dialogue_history']}\n"
            formatted_cot += f"观察: {entry['observation']}\n"
            formatted_cot += "="*50 + "\n"
        return formatted_cot
    
    def format_cot_output(self):
        """
        格式化 CoT 条目为字符串（去掉 observation，优化对话历史显示）。

        返回:
            str: 格式化后的 CoT 字符串。
        """
        formatted_cot = ""
        for idx, entry in enumerate(self.cot_entries, start=1):
            formatted_cot += f"Round {idx}:\n"
            formatted_cot += f"策略: {entry['strategy']}\n\n"
            formatted_cot += f"推理: {entry['reasoning']}\n\n"

            # 美化对话历史
            dialogue_lines = ""
            for dialogue in entry.get("dialogue_history", []):
                role = "AI医生" if dialogue["role"] == "doctor" else "患者"
                dialogue_lines += f"{role}: {dialogue['content']}\n"
            if dialogue_lines:
                formatted_cot += "对话历史:\n" + dialogue_lines

            formatted_cot += f"\n反馈: {entry['feedback']}\n"
            formatted_cot += "=" * 50 + "\n"

        return formatted_cot
    
    def format_cot_example(self):
        """
        格式化 CoT 条目为示例字符串（去掉对话过程和observation，只保留策略、推理和反馈）。
        
        返回:
            str: 格式化后的 CoT 示例字符串。
        """
        formatted_example = ""
        for idx, entry in enumerate(self.cot_entries, start=1):
            formatted_example += f"Round {idx}:\n"
            formatted_example += f"策略: {entry['strategy']}\n\n"
            formatted_example += f"推理: {entry['reasoning']}\n\n"
            
            if entry['feedback']:
                formatted_example += f"反馈: {entry['feedback']}\n"
            
            formatted_example += "=" * 50 + "\n"
            
        return formatted_example
    
    def get_round_num(self):
        """
        获取当前的回合数。

        返回:
            int: 当前的回合数。
        """
        return len(self.cot_entries)
    
    def to_jsonl(self, filepath):
        """
        将 cot_entries 保存为 JSONL 文件。若文件存在，则追加写入。

        参数:
            filepath (str | Path): 要保存的 JSONL 文件路径
        """
        filepath = Path(filepath)
        filepath.parent.mkdir(parents=True, exist_ok=True)  # 确保目录存在

        with open(filepath, "a", encoding="utf-8") as f:  # 使用追加模式
            json.dump(self.cot_entries, f, ensure_ascii=False)  # 整个列表作为一个 JSON
            f.write("\n")
    
    def add_doctor_supplementary_info(self, info: str):
        """
        添加医生的补充信息。

        参数:
            info (str): 医生的补充信息
        """
        if not hasattr(self, 'doctor_supplementary_info'):
            self.doctor_supplementary_info = []
        self.doctor_supplementary_info.append(info)
        print(f"Added doctor supplementary info: {info}")

    def add_test_recommendation(self, test_info: dict):
        """
        添加检查建议。

        参数:
            test_info (dict): 包含检查名称和结果的字典
        """
        if not hasattr(self, 'test_recommendation'):
            self.test_recommendation = []
        self.test_recommendation.append(test_info)
        print(f"Added test recommendation: {test_info}")