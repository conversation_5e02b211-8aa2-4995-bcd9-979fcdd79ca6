<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.makiyo.aidoctor.mapper.RoleMapper">
  <resultMap id="BaseResultMap" type="com.makiyo.aidoctor.entity.Role">
    <id column="role_id" jdbcType="INTEGER" property="roleId" />
    <result column="role_name" jdbcType="VARCHAR" property="roleName" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="permissions" jdbcType="VARCHAR" property="permissions" /> <!-- 修改为 VARCHAR -->
  </resultMap>
  <sql id="Base_Column_List">
    role_id, role_name, description, permissions
  </sql>
  <select id="selectAll" resultMap="BaseResultMap">
    SELECT 
    <include refid="Base_Column_List" />
    FROM role
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    SELECT 
    <include refid="Base_Column_List" />
    FROM role
    WHERE role_id = #{roleId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    DELETE FROM role
    WHERE role_id = #{roleId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="role_id" keyProperty="roleId" parameterType="com.makiyo.aidoctor.entity.Role" useGeneratedKeys="true">
    INSERT INTO role (role_name, description, permissions)
    VALUES (#{roleName,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{permissions,jdbcType=VARCHAR}) <!-- 修改为 VARCHAR -->
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.makiyo.aidoctor.entity.Role">
    UPDATE role
    SET role_name = #{roleName,jdbcType=VARCHAR},
        description = #{description,jdbcType=VARCHAR},
        permissions = #{permissions,jdbcType=VARCHAR} <!-- 修改为 VARCHAR -->
    WHERE role_id = #{roleId,jdbcType=INTEGER}
  </update>
</mapper>