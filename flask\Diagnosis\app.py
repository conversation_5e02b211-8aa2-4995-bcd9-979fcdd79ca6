from flask import Flask, request, jsonify, Response
import logging
from Doctor import Doctor
from DiagnosisAgent import DiagnosisAgent
from api import consultation_doctor_reoponse
import time  # 用于模拟延迟
import json
import re
import os  # 导入 os 模块以处理文件路径

app = Flask(__name__)

# 配置日志记录
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# 定义诊断文件路径
DIAGNOSIS_FILE = 'Diagnosis/diagnosis.txt'

def append_to_diagnosis_file(content):
    with open(DIAGNOSIS_FILE, 'a', encoding='utf-8') as f:
        f.write(content + '\n')  # 每次写入后换行

def read_diagnosis_file():
    if os.path.exists(DIAGNOSIS_FILE):
        with open(DIAGNOSIS_FILE, 'r', encoding='utf-8') as f:
            return f.read()  # 读取文件内容
    return ""  # 如果文件不存在，返回空字符串

@app.route('/diagnosis', methods=['POST'])
def diagnosis():
    data = request.get_json()  # 从请求中获取 JSON 数据
    
    # 输出请求内容
    logger.info(f"Received request data: {data}")  # 记录请求内容
    print(f"Received request data: {data}")  # 打印请求内容（可选）

    model = data.get('model')  # 获取模型名称
    messages = data.get('messages')  # 获取消息列表

    if not messages:
        return jsonify({"error": "messages are required"}), 400  # 返回错误信息

    # 提取最后一条用户消息
    user_message = None
    for msg in reversed(messages):
        if msg['role'] == 'user':
            user_message = msg['content']
            break

    if not user_message:
        return jsonify({"error": "user message is required"}), 400  # 返回错误信息

    # 读取历史消息
    historical_messages = read_diagnosis_file()
    full_message = f"{historical_messages}<observation>\n{user_message}\n</observation>"

    # 传入问诊历史，返回AI医生的回答和解决方案
    response_doc, solution_text = consultation_doctor_reoponse(full_message)

    # 将用户消息和系统回复写入文件
    append_to_diagnosis_file(f"<observation>\n{user_message}\n</observation>")
    if response_doc:
        append_to_diagnosis_file(response_doc)

    # 如果 solution_text 不为 None，返回诊断和治疗方案
    if solution_text:
        doctors = Doctor.load_from_solution(solution_text)
        print(doctors)

        # 开始诊断
        diagnosis_agent = DiagnosisAgent(doctors)
        # 诊断所有患者
        results = diagnosis_agent.diagnose_all()

        # 打印诊断结果
        for result in results:
            print(f"Diagnosis: {result['diagnosis']}")
            print(f"Treatment Plan: {result['treatment_plan']}")
            print("-" * 40)

        # 整合诊断结果和治疗方案
        combined_result = f"Diagnosis: {results[0]['diagnosis']}\nTreatment Plan: {results[0]['treatment_plan']}"

        # 清空诊断文件
        open(DIAGNOSIS_FILE, 'w', encoding='utf-8').close()

        # 返回整合后的结果流式输出
        return generate_streaming_response_from_combined(combined_result)

    # 如果 solution_text 为 None，返回 response_doc 的流式输出
    return generate_streaming_response_from_doc(response_doc)

def generate_streaming_response_from_combined(response_doc):
    logger.info(f"Received response_doc: {response_doc}")  # 添加日志，查看 response_doc 内容
    def generate():
        try:
            # 确保 response_doc 是有效的字符串
            if response_doc is None or not response_doc.strip():
                logger.error("response_doc is None or empty")
                yield "data: {\"error\": \"response_doc is None or empty\"}\n\n"
                return
            
            # 处理 response_doc，确保它是有效的 JSON 格式
            response_data = {}
            lines = response_doc.splitlines()
            for line in lines:
                if line.startswith("Diagnosis:"):
                    response_data['Diagnosis'] = line[len("Diagnosis:"):].strip()
                elif line.startswith("Treatment Plan:"):
                    treatment_plan_str = line[len("Treatment Plan:"):].strip()
                    # 这里假设 treatment_plan_str 是一个有效的字典字符串
                    response_data['Treatment Plan'] = eval(treatment_plan_str)  # 注意：使用 eval 可能存在安全风险，确保输入是可信的

            # 检查是否存在 '生活建议'
            if '生活建议' not in response_data['Treatment Plan']:
                logger.error("'生活建议' not found in response_data['Treatment Plan']")
                yield "data: {\"error\": \"'生活建议' not found in response_data['Treatment Plan']\"}\n\n"
                return
            
            # 格式化输出
            formatted_output = ""
            formatted_output += f"Diagnosis: {response_data['Diagnosis']}\n"
            formatted_output += "Treatment Plan: \n"
            formatted_output += "药品：\n"
            for item in response_data['Treatment Plan']['药品']:
                formatted_output += (
                    f"    药品名：{item['药品名']}\n"
                    f"    剂量：{item['剂量']}\n"
                    f"    服用方法：{item['服用方法']}\n"
                    f"    剂量安排：{item['剂量安排']}\n"
                    f"    使用目的：{item['使用目的']}\n"
                )
            formatted_output += "生活建议：\n"
            for suggestion in response_data['Treatment Plan']['生活建议']:
                formatted_output += f"    - {suggestion}\n"

            # 直接返回格式化后的内容，增加 "role": "assistant"
            yield f"data: {json.dumps({'id': 'chatcmpl-123', 'object': 'chat.completion.chunk', 'created': int(time.time()), 'model': 'gpt-4', 'choices': [{'index': 0, 'delta': {'role': 'assistant', 'content': formatted_output}, 'finish_reason': None}]})}\n\n"
        
        except json.JSONDecodeError as json_err:
            logger.error(f"JSON decode error: {str(json_err)}")  # 记录 JSON 解码错误
            yield "data: {\"error\": \"JSON decode error\"}\n\n"  # 返回 JSON 解码错误信息
        except Exception as e:
            logger.error(f"Error in generate_streaming_response_from_combined: {str(e)}")  # 记录其他错误信息
            yield "data: {\"error\": \"Internal Server Error\"}\n\n"  # 返回错误信息
        
        # 发送结束标志
        yield "data: [DONE]\n\n"
    
    return Response(generate(), content_type='text/event-stream')

def generate_streaming_response_from_doc(response_doc):
    logger.info(f"Received response_doc: {response_doc}")  # 添加日志，查看 response_doc 内容
    def generate():
        # 去除标签
        cleaned_content = response_doc.replace("<text>\n", "").replace("</text>", "")  # 去除标签
        # 将内容每五个字符分割成多个 chunk
        for i in range(0, len(cleaned_content), 5):
            chunk = cleaned_content[i:i+5]  # 每五个字符为一个 chunk
            if chunk.strip():  # 只发送非空内容
                data_chunk = {
                    "id": "chatcmpl-123",
                    "object": "chat.completion.chunk",
                    "created": int(time.time()),  # 当前时间戳
                    "model": "gpt-4",
                    "choices": [
                        {
                            "index": 0,
                            "delta": {
                                "role": "assistant",
                                "content": chunk  # 发送分割后的内容
                            },
                            "finish_reason": None
                        }
                    ]
                }
                yield f"data: {json.dumps(data_chunk)}\n\n"  # 发送数据块
                time.sleep(0.01)  # 增加延迟，模拟流式返回效果

        # 发送结束标志
        yield "data: [DONE]\n\n"

    return Response(generate(), content_type='text/event-stream')

if __name__ == '__main__':
    app.run(debug=True)