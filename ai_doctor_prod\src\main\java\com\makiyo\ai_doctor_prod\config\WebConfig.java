package com.makiyo.ai_doctor_prod.config; // 请确保这个包名与你的项目结构一致

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import java.io.File;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Value("${tts.audio.baseUrl:/audio}") // 从 application.yml 读取，/audio 是默认值
    private String audioBaseUrl;

    @Value("${tts.audio.basePath:audio}") // 从 application.yml 读取，audio 是默认值
    private String audioBasePath;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 确保 audioBaseUrl 以 "/" 开头
        String urlPattern = audioBaseUrl.startsWith("/") ? audioBaseUrl : "/" + audioBaseUrl;
        // 确保 urlPattern 匹配路径下的所有文件, 例如 /audio/**
        if (!urlPattern.endsWith("/**")) {
            if (urlPattern.endsWith("/")) {
                urlPattern += "**";
            } else {
                urlPattern += "/**";
            }
        }

        // 构建文件系统中的实际路径
        // System.getProperty("user.dir") 获取的是应用启动时的当前工作目录
        String fileSystemPath = System.getProperty("user.dir") + File.separator + audioBasePath + File.separator;
        String resourceLocation = "file:" + fileSystemPath;

        // 应用启动时打印配置信息，方便调试
        System.out.println("--------------------------------------------------------------------");
        System.out.println("AI-Doctor Static Resource Configuration:");
        System.out.println("  URL Request Path (tts.audio.baseUrl): " + urlPattern);
        System.out.println("  Serving From (tts.audio.basePath relative to app work dir): " + resourceLocation);
        System.out.println("  Expected absolute audio files location on server: " + new File(fileSystemPath).getAbsolutePath());
        System.out.println("  Ensure your application has write permissions to this directory and that tts.audio.basePath in application.yml matches the directory name.");
        System.out.println("--------------------------------------------------------------------");

        registry.addResourceHandler(urlPattern) // 例如: /audio/**
                .addResourceLocations(resourceLocation); // 例如: file:./audio/ 或 file:/opt/app/audio/
    }
} 