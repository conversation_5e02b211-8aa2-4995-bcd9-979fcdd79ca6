package com.makiyo.ai_doctor_prod.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * case_item
 * <AUTHOR>
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CaseItem implements Serializable {
    private String id;

    private Date createdAt;

    private Date updatedAt;

    private Object profile;

    private Object interactionHistory;

    private static final long serialVersionUID = 1L;
}