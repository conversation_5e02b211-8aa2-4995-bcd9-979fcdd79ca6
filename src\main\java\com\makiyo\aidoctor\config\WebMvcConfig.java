package com.makiyo.aidoctor.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Value("${tts.audio.basePath}")
    private String audioBasePath;

    @Value("${tts.audio.baseUrl}")
    private String audioBaseUrl;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置音频文件的访问路径
        registry.addResourceHandler(audioBaseUrl + "/**")
                .addResourceLocations("file:" + audioBasePath + "/");
    }
    
    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        // 将根路径映射到index.html
        registry.addViewController("/").setViewName("forward:/index.html");
    }
} 