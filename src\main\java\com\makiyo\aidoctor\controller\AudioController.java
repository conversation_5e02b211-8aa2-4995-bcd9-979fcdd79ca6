package com.makiyo.aidoctor.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URI;
import java.net.URL;

@Tag(name = "音频流管理", description = "音频流处理相关的 API 接口")
@RestController
@RequestMapping("/audio")
public class AudioController {
    private static final Logger logger = LoggerFactory.getLogger(AudioController.class);

    @Value("${tts.audio.basePath}")
    private String audioBasePath;

    @Operation(summary = "音频流获取", description = "获取音频流数据")
    @GetMapping("/stream")
    public ResponseEntity<Resource> streamAudio(@RequestParam String audioUrl) {
        File audioFile = null;
        try {
            // 从 URL 中提取文件路径
            String filePath = audioUrl.substring(audioUrl.lastIndexOf("/") + 1);
            // 修改为正确的音频文件路径
            String fullPath = audioBasePath + filePath;
            logger.info("请求音频文件: {}", fullPath);
            
            audioFile = new File(fullPath);
            if (!audioFile.exists()) {
                logger.error("音频文件未找到: {}", fullPath);
                return ResponseEntity.notFound().build();
            }

            // 创建文件资源
            final Resource resource = new FileSystemResource(audioFile);
            final File finalAudioFile = audioFile;

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("audio/mpeg"));
            headers.setContentDispositionFormData("attachment", audioFile.getName());
            headers.setCacheControl("no-cache, no-store, must-revalidate");
            headers.setPragma("no-cache");
            headers.setExpires(0);

            logger.info("开始传输音频文件: {}, 大小: {} 字节", fullPath, audioFile.length());
            
            // 创建一个自定义的ResponseEntity，在传输完成后删除文件
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(audioFile.length())
                    .body(new Resource() {
                        @Override
                        public InputStream getInputStream() throws IOException {
                            return new FilterInputStream(resource.getInputStream()) {
                                @Override
                                public void close() throws IOException {
                                    super.close();
                                    // 在流关闭时（即传输完成时）删除文件
                                    if (finalAudioFile.exists()) {
                                        boolean deleted = finalAudioFile.delete();
                                        if (deleted) {
                                            logger.info("音频文件已删除: {}", finalAudioFile.getAbsolutePath());
                                        } else {
                                            logger.warn("音频文件删除失败: {}", finalAudioFile.getAbsolutePath());
                                        }
                                    }
                                }
                            };
                        }

                        @Override
                        public boolean exists() {
                            return resource.exists();
                        }

                        @Override
                        public boolean isReadable() {
                            return resource.isReadable();
                        }

                        @Override
                        public boolean isOpen() {
                            return resource.isOpen();
                        }

                        @Override
                        public URL getURL() throws IOException {
                            return resource.getURL();
                        }

                        @Override
                        public URI getURI() throws IOException {
                            return resource.getURI();
                        }

                        @Override
                        public File getFile() throws IOException {
                            return resource.getFile();
                        }

                        @Override
                        public long contentLength() throws IOException {
                            return resource.contentLength();
                        }

                        @Override
                        public long lastModified() throws IOException {
                            return resource.lastModified();
                        }

                        @Override
                        public Resource createRelative(String relativePath) throws IOException {
                            return resource.createRelative(relativePath);
                        }

                        @Override
                        public String getFilename() {
                            return resource.getFilename();
                        }

                        @Override
                        public String getDescription() {
                            return resource.getDescription();
                        }
                    });
                    
        } catch (Exception e) {
            logger.error("音频流处理失败", e);
            // 发生错误时也尝试删除文件
            if (audioFile != null && audioFile.exists()) {
                boolean deleted = audioFile.delete();
                if (deleted) {
                    logger.info("发生错误时删除音频文件: {}", audioFile.getAbsolutePath());
                } else {
                    logger.warn("发生错误时删除音频文件失败: {}", audioFile.getAbsolutePath());
                }
            }
            return ResponseEntity.internalServerError().build();
        }
    }
} 