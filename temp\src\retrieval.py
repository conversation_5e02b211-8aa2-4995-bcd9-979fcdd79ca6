# search.py

import torch
from transformers import BertToken<PERSON>, BertModel
from qdrant_client import QdrantClient
from qdrant_client.models import Distance

# 1. 加载预训练的 BERT 模型和分词器
model_path = r"C:\Users\<USER>\.cache\modelscope\hub\models\BAAI\bge-large-zh-v1___5"
tokenizer = BertTokenizer.from_pretrained(model_path)
model = BertModel.from_pretrained(model_path)
model.eval()  # 设置模型为评估模式

# 2. 生成嵌入向量的函数
def generate_embedding(text):
    """将文本转换为 BERT 向量"""
    inputs = tokenizer(text, return_tensors='pt', padding=True, truncation=True, max_length=512)
    with torch.no_grad():
        outputs = model(**inputs)
    return outputs.last_hidden_state[:, 0, :].squeeze().numpy()  # 取 [CLS] 作为向量

# 3. 连接 Qdrant 数据库
import os
QDRANT_URL = "https://4c32b9ec-36a6-4290-9dbe-7e455e14dc5b.eu-west-2-0.aws.cloud.qdrant.io:6333"
QDRANT_API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3MiOiJtIn0.g_G0ko_m5qFdwL6wvxZEcygPftfUak3qZRDEyBGMNbQ"
os.environ['NO_PROXY'] = QDRANT_URL

# 连接Qdrant客户端
qdrant_client = QdrantClient(
    url=QDRANT_URL,
    api_key=QDRANT_API_KEY,
    timeout=600  # 超时时间（秒）
)

# 4. 创建 collection 名称
collection_name = "COT"

# 5. 检索函数
def search_query(query_text, top_k):
    """根据查询文本获取最相似的结果"""
    # 生成查询文本的向量
    query_embedding = generate_embedding(query_text)

    # 在 Qdrant 中执行检索
    search_result = qdrant_client.search(
        collection_name=collection_name,
        query_vector=query_embedding.tolist(),  # 查询向量
        limit=top_k,  # 限制返回的数量
        with_payload=True  # 返回 payload
    )

    # 打印检索结果
    print(f"检索到 {len(search_result)} 条结果：")
    for result in search_result:
        print(f"ID: {result.id}")
        print("Payload: ")
        print(result.payload)  # 显示每个结果的 payload 数据


# 示例：检索与查询文本 "发热4天" 相关的最相似条目
if __name__ == "__main__":
    query_text = "发热4天"
    search_query(query_text, top_k=2)
