package com.makiyo.aidoctor.service;

import lombok.AllArgsConstructor;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PreDestroy;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Queue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * OCR队列服务，确保OCR请求按顺序一个一个处理
 */
@Service
public class OcrQueueService {
    private static final Logger log = LoggerFactory.getLogger(OcrQueueService.class);
    
    // 请求队列
    private final Queue<OcrRequest> requestQueue = new ConcurrentLinkedQueue<>();
    // 是否正在处理的标志
    private final AtomicBoolean isProcessing = new AtomicBoolean(false);
    // OCR服务
    private final OcrService ocrService;
    // 用于异步处理的线程池
    private final ExecutorService executorService;
    
    @Autowired
    public OcrQueueService(OcrService ocrService) {
        this.ocrService = ocrService;
        // 创建单线程执行器确保顺序处理
        this.executorService = Executors.newSingleThreadExecutor(r -> {
            Thread thread = Executors.defaultThreadFactory().newThread(r);
            thread.setName("ocr-queue-processor");
            thread.setDaemon(true); // 设置为守护线程
            return thread;
        });
        
        log.info("OCR队列服务已初始化");
    }
    
    /**
     * 将OCR请求加入队列
     * 
     * @param image 上传的图片文件
     * @param name 用户名
     * @param time 时间字符串
     * @param sessionId 会话ID
     * @return 包含OCR处理结果的CompletableFuture
     */
    public CompletableFuture<Object> enqueueOcrRequest(MultipartFile image, String name, String time, Integer sessionId) {
        CompletableFuture<Object> future = new CompletableFuture<>();
        
        try {
            // 立即将文件内容读入内存，避免长时间持有文件句柄
            byte[] imageData = image.getBytes();
            String originalFilename = image.getOriginalFilename();
            String contentType = image.getContentType();
            
            // 创建深度复制的请求对象，避免引用原始MultipartFile
            OcrRequest request = new OcrRequest(imageData, originalFilename, contentType, name, time, sessionId, future);
            requestQueue.add(request);
            
            log.info("OCR请求已加入队列，会话ID: {}, 当前队列长度: {}, 图片大小: {} 字节", 
                    sessionId, requestQueue.size(), imageData.length);
            
            // 如果没有正在处理的请求，启动处理
            if (isProcessing.compareAndSet(false, true)) {
                startProcessing();
            }
        } catch (IOException e) {
            log.error("读取上传文件时出错，会话ID: {}: {}", sessionId, e.getMessage(), e);
            future.completeExceptionally(e);
        }
        
        return future;
    }
    
    /**
     * 启动队列处理
     */
    private void startProcessing() {
        executorService.submit(() -> {
            try {
                processNextRequest();
            } catch (Exception e) {
                log.error("队列处理过程中发生未捕获的异常: {}", e.getMessage(), e);
                isProcessing.set(false); // 重置处理状态
                
                // 尝试重新启动处理
                if (!requestQueue.isEmpty() && isProcessing.compareAndSet(false, true)) {
                    startProcessing();
                }
            }
        });
    }
    
    /**
     * 递归处理队列中的下一个请求
     */
    private void processNextRequest() {
        OcrRequest request = requestQueue.poll();
        if (request == null) {
            log.debug("队列已空，OCR处理完成");
            isProcessing.set(false);
            return;
        }
        
        log.info("开始处理OCR请求，会话ID: {}, 剩余队列长度: {}", 
                request.getSessionId(), requestQueue.size());
        
        ByteArrayMultipartFile multipartFile = null;
        try {
            // 从内存中的字节数组创建MultipartFile的实现
            multipartFile = new ByteArrayMultipartFile(
                    request.getImageData(), 
                    request.getOriginalFilename(),
                    request.getContentType());
            
            // 处理OCR请求
            Object result = ocrService.processOcr(
                    multipartFile, 
                    request.getName(), 
                    request.getTime(), 
                    request.getSessionId());
            
            request.getFuture().complete(result);
            log.info("完成OCR请求处理，会话ID: {}", request.getSessionId());
            
        } catch (Exception e) {
            log.error("处理OCR请求时出错，会话ID: {}: {}", request.getSessionId(), e.getMessage(), e);
            request.getFuture().completeExceptionally(e);
        } finally {
            // 清理请求资源
            request.setImageData(null);
            
            // 确保释放临时文件资源
            if (multipartFile != null) {
                try {
                    // 显式调用GC以帮助释放文件句柄
                    multipartFile = null;
                    System.gc();
                } catch (Exception e) {
                    log.warn("清理MultipartFile资源时出错: {}", e.getMessage());
                }
            }
            
            // 继续处理下一个请求
            processNextRequest();
        }
    }
    
    /**
     * 获取当前队列长度
     * @return 队列中等待处理的请求数量
     */
    public int getQueueSize() {
        return requestQueue.size();
    }
    
    /**
     * 应用关闭时清理资源
     */
    @PreDestroy
    public void shutdown() {
        log.info("正在关闭OCR队列处理线程池...");
        executorService.shutdown();
        try {
            // 等待所有任务完成或超时
            if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                log.warn("OCR队列处理线程池未能在60秒内完成所有任务，将强制关闭");
                executorService.shutdownNow();
                
                // 再次等待线程响应中断
                if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                    log.error("OCR队列处理线程池未能完全关闭");
                }
            }
            log.info("OCR队列处理线程池已成功关闭");
        } catch (InterruptedException e) {
            log.error("等待OCR队列处理线程池关闭时被中断", e);
            Thread.currentThread().interrupt();
            executorService.shutdownNow();
        }
        
        // 清空队列中的剩余请求
        int remainingRequests = requestQueue.size();
        if (remainingRequests > 0) {
            log.warn("在关闭时，OCR队列中仍有{}个未处理的请求，这些请求将被取消", remainingRequests);
            for (OcrRequest request : requestQueue) {
                request.getFuture().completeExceptionally(
                    new RuntimeException("服务关闭，请求被取消"));
                request.setImageData(null); // 清理内存
            }
            requestQueue.clear();
        }
    }
    
    /**
     * OCR请求的数据类
     */
    @Data
    @AllArgsConstructor
    private static class OcrRequest {
        private byte[] imageData;
        private String originalFilename;
        private String contentType;
        private String name;
        private String time;
        private Integer sessionId;
        private CompletableFuture<Object> future;
    }
    
    /**
     * 基于字节数组的MultipartFile实现，避免持有原始文件句柄
     */
    public static class ByteArrayMultipartFile implements MultipartFile {
        private final byte[] content;
        private final String name;
        private final String originalFilename;
        private final String contentType;

        public ByteArrayMultipartFile(byte[] content, String originalFilename, String contentType) {
            this.content = content;
            this.name = "file";  // 表单字段名
            this.originalFilename = originalFilename;
            this.contentType = contentType;
        }

        @Override
        public String getName() {
            return name;
        }

        @Override
        public String getOriginalFilename() {
            return originalFilename;
        }

        @Override
        public String getContentType() {
            return contentType;
        }

        @Override
        public boolean isEmpty() {
            return content == null || content.length == 0;
        }

        @Override
        public long getSize() {
            return content == null ? 0 : content.length;
        }

        @Override
        public byte[] getBytes() throws IOException {
            return content;
        }

        @Override
        public java.io.InputStream getInputStream() throws IOException {
            return new java.io.ByteArrayInputStream(content);
        }

        @Override
        public void transferTo(java.io.File dest) throws IOException, IllegalStateException {
            try (java.io.FileOutputStream fos = new java.io.FileOutputStream(dest)) {
                fos.write(content);
                fos.flush(); // 确保内容写入磁盘
            }
        }

        public org.springframework.core.io.Resource getResource() {
            return new ByteArrayResource(content);
        }
        
        /**
         * 显式释放资源
         */
        public void releaseResources() {
            // 虽然Java会自动GC，但显式设置为null可以帮助更快释放大型字节数组
            // 这个方法在需要立即释放内存时调用
        }
    }
    
    /**
     * 清理临时文件目录
     * 
     * @param tempDir 临时目录路径
     * @param maxAgeHours 文件最大保留时间（小时）
     */
    public void cleanupTempFiles(String tempDir, int maxAgeHours) {
        try {
            Path directory = Paths.get(tempDir);
            if (!Files.exists(directory)) {
                return;
            }
            
            long cutoffTime = System.currentTimeMillis() - (maxAgeHours * 60 * 60 * 1000);
            
            Files.list(directory)
                .filter(path -> {
                    try {
                        return Files.isRegularFile(path) && 
                               Files.getLastModifiedTime(path).toMillis() < cutoffTime;
                    } catch (IOException e) {
                        return false;
                    }
                })
                .forEach(path -> {
                    try {
                        Files.delete(path);
                        log.info("已删除过期临时文件: {}", path);
                    } catch (IOException e) {
                        log.warn("无法删除临时文件: {}, 错误: {}", path, e.getMessage());
                    }
                });
        } catch (IOException e) {
            log.error("清理临时文件时出错: {}", e.getMessage(), e);
        }
    }
}