package com.makiyo.ai_doctor_prod.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "case_base_info")
public class CaseBaseInfo {
    @Id
    private String id;

    private String caseId;

    private Map<String, Object> info;

    private Date createdAt;
} 