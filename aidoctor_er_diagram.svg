<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 12.2.1 (20241206.2353)
 -->
<!-- Pages: 1 -->
<svg width="4500pt" height="2442pt"
 viewBox="36.00 36.00 1044.00 550.04" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(2.66155 2.66155) rotate(0) translate(60.36 857.09)">
<polygon fill="white" stroke="none" points="-4,4 -4,-800.73 1574.03,-800.73 1574.03,4 -4,4"/>
<!-- cot_entries -->
<g id="node1" class="node">
<title>cot_entries</title>
<polygon fill="#e8f4ff" stroke="black" points="1146,-480.5 1146,-624.75 1528,-624.75 1528,-480.5 1146,-480.5"/>
<text text-anchor="middle" x="1337" y="-609.35" font-family="Arial" font-size="12.00">cot_entries</text>
<polyline fill="none" stroke="black" points="1146,-602.5 1528,-602.5"/>
<text text-anchor="start" x="1154" y="-587.1" font-family="Arial" font-size="12.00">id: int 🔑 NOT NULL</text>
<text text-anchor="start" x="1154" y="-572.85" font-family="Arial" font-size="12.00">interaction_history_id: int 🔗 NOT NULL /* 关联的交互历史 */</text>
<text text-anchor="start" x="1154" y="-558.6" font-family="Arial" font-size="12.00">round_number: int NOT NULL /* 条目在数组中的顺序 (轮次) */</text>
<text text-anchor="start" x="1154" y="-544.35" font-family="Arial" font-size="12.00">strategy: text /* 策略 */</text>
<text text-anchor="start" x="1154" y="-530.1" font-family="Arial" font-size="12.00">reasoning: text /* 推理过程 */</text>
<text text-anchor="start" x="1154" y="-515.85" font-family="Arial" font-size="12.00">observation: text /* 观察记录 */</text>
<text text-anchor="start" x="1154" y="-501.6" font-family="Arial" font-size="12.00">feedback: text /* 反馈 */</text>
<text text-anchor="start" x="1154" y="-487.35" font-family="Arial" font-size="12.00">created_at: timestamp </text>
</g>
<!-- interaction_histories -->
<g id="node4" class="node">
<title>interaction_histories</title>
<polygon fill="#e8f4ff" stroke="black" points="564.75,-315.75 564.75,-431.5 945.25,-431.5 945.25,-315.75 564.75,-315.75"/>
<text text-anchor="middle" x="755" y="-416.1" font-family="Arial" font-size="12.00">interaction_histories</text>
<polyline fill="none" stroke="black" points="564.75,-409.25 945.25,-409.25"/>
<text text-anchor="start" x="572.75" y="-393.85" font-family="Arial" font-size="12.00">id: int 🔑 NOT NULL</text>
<text text-anchor="start" x="572.75" y="-379.6" font-family="Arial" font-size="12.00">session_id: int NOT NULL /* 关联的 V2 会话 (format_version=2) */</text>
<text text-anchor="start" x="572.75" y="-365.35" font-family="Arial" font-size="12.00">final_diagnosis: text /* 最终诊断文本 */</text>
<text text-anchor="start" x="572.75" y="-351.1" font-family="Arial" font-size="12.00">preliminary_diagnosis: text /* 初步诊断文本 */</text>
<text text-anchor="start" x="572.75" y="-336.85" font-family="Arial" font-size="12.00">created_at: timestamp </text>
<text text-anchor="start" x="572.75" y="-322.6" font-family="Arial" font-size="12.00">updated_at: timestamp </text>
</g>
<!-- cot_entries&#45;&gt;interaction_histories -->
<g id="edge1" class="edge">
<title>cot_entries:table&#45;&gt;interaction_histories:table</title>
<path fill="none" stroke="#666666" stroke-width="1.2" d="M1145,-613.62C1137.58,-613.62 1141.95,-486.22 1146,-480 1156.65,-463.67 1175.41,-478.37 1186,-462 1188.9,-457.52 1189.53,-454 1186,-450 1169.75,-431.62 1005.32,-422.09 956.68,-420.59"/>
<polygon fill="#666666" stroke="#666666" stroke-width="1.2" points="956.65,-420.59 946.74,-415.88 951.25,-420.48 947.05,-420.39 947.05,-420.39 947.05,-420.39 951.25,-420.48 946.56,-424.88 956.65,-420.59"/>
<text text-anchor="middle" x="1246.17" y="-452.5" font-family="Arial" font-size="10.00">interaction_history_id → id</text>
</g>
<!-- dialogue_turns -->
<g id="node2" class="node">
<title>dialogue_turns</title>
<polygon fill="#e8f4ff" stroke="black" points="1156.5,-673.75 1156.5,-789.5 1517.5,-789.5 1517.5,-673.75 1156.5,-673.75"/>
<text text-anchor="middle" x="1337" y="-774.1" font-family="Arial" font-size="12.00">dialogue_turns</text>
<polyline fill="none" stroke="black" points="1156.5,-767.25 1517.5,-767.25"/>
<text text-anchor="start" x="1164.5" y="-751.85" font-family="Arial" font-size="12.00">id: int 🔑 NOT NULL</text>
<text text-anchor="start" x="1164.5" y="-737.6" font-family="Arial" font-size="12.00">cot_entry_id: int 🔗 NOT NULL /* 关联的思考链条目 */</text>
<text text-anchor="start" x="1164.5" y="-723.35" font-family="Arial" font-size="12.00">turn_number: int NOT NULL /* 轮次在对话历史中的顺序 */</text>
<text text-anchor="start" x="1164.5" y="-709.1" font-family="Arial" font-size="12.00">role: varchar(10) NOT NULL /* &quot;doctor&quot; 或 &quot;patient&quot; */</text>
<text text-anchor="start" x="1164.5" y="-694.85" font-family="Arial" font-size="12.00">content: text NOT NULL /* 对话内容 */</text>
<text text-anchor="start" x="1164.5" y="-680.6" font-family="Arial" font-size="12.00">created_at: timestamp </text>
</g>
<!-- dialogue_turns&#45;&gt;cot_entries -->
<g id="edge2" class="edge">
<title>dialogue_turns:table&#45;&gt;cot_entries:table</title>
<path fill="none" stroke="#666666" stroke-width="1.2" d="M1337,-790.5C1337,-800.53 1510.43,-796.61 1517.5,-789.5 1553.93,-752.87 1548.99,-714.21 1517.5,-673.25 1469.18,-610.4 1348.51,-699.54 1337.77,-635.36"/>
<polygon fill="#666666" stroke="#666666" stroke-width="1.2" points="1337.79,-635.62 1341.52,-625.31 1337.38,-630.24 1337.06,-626.05 1337.06,-626.05 1337.06,-626.05 1337.38,-630.24 1332.54,-625.99 1337.79,-635.62"/>
<text text-anchor="middle" x="1531.78" y="-645.75" font-family="Arial" font-size="10.00">cot_entry_id → id</text>
</g>
<!-- doctor_supplementary_infos -->
<g id="node3" class="node">
<title>doctor_supplementary_infos</title>
<polygon fill="#e8f4ff" stroke="black" points="0,-509 0,-596.25 364,-596.25 364,-509 0,-509"/>
<text text-anchor="middle" x="182" y="-580.85" font-family="Arial" font-size="12.00">doctor_supplementary_infos</text>
<polyline fill="none" stroke="black" points="0,-574 364,-574"/>
<text text-anchor="start" x="8" y="-558.6" font-family="Arial" font-size="12.00">id: int 🔑 NOT NULL</text>
<text text-anchor="start" x="8" y="-544.35" font-family="Arial" font-size="12.00">interaction_history_id: int 🔗 NOT NULL /* 关联的交互历史 */</text>
<text text-anchor="start" x="8" y="-530.1" font-family="Arial" font-size="12.00">info_text: text NOT NULL /* 补充信息文本 */</text>
<text text-anchor="start" x="8" y="-515.85" font-family="Arial" font-size="12.00">created_at: timestamp </text>
</g>
<!-- doctor_supplementary_infos&#45;&gt;interaction_histories -->
<g id="edge3" class="edge">
<title>doctor_supplementary_infos:table&#45;&gt;interaction_histories:table</title>
<path fill="none" stroke="#666666" stroke-width="1.2" d="M182,-597.25C182,-607.36 356.6,-603.14 364,-596.25 382.97,-578.6 357.03,-500.41 373,-480 398.44,-447.48 503.06,-423.89 553.33,-420.74"/>
<polygon fill="#666666" stroke="#666666" stroke-width="1.2" points="553.35,-420.73 563.5,-424.89 558.75,-420.55 562.95,-420.4 562.95,-420.4 562.95,-420.4 558.75,-420.55 563.19,-415.89 553.35,-420.73"/>
<text text-anchor="middle" x="478.54" y="-452.5" font-family="Arial" font-size="10.00">interaction_history_id → id</text>
</g>
<!-- session -->
<g id="node5" class="node">
<title>session</title>
<polygon fill="#e8f4ff" stroke="black" points="501.75,-165.25 501.75,-266.75 1008.25,-266.75 1008.25,-165.25 501.75,-165.25"/>
<text text-anchor="middle" x="755" y="-251.35" font-family="Arial" font-size="12.00">session</text>
<polyline fill="none" stroke="black" points="501.75,-244.5 1008.25,-244.5"/>
<text text-anchor="start" x="509.75" y="-229.1" font-family="Arial" font-size="12.00">id: int 🔑 NOT NULL</text>
<text text-anchor="start" x="509.75" y="-214.85" font-family="Arial" font-size="12.00">user_id: int 🔗 NOT NULL /* 关联的用户ID */</text>
<text text-anchor="start" x="509.75" y="-200.6" font-family="Arial" font-size="12.00">format_version: int 🔗 NOT NULL /* 1 代表 V1 文本消息, 2 代表 V2 交互历史格式 */</text>
<text text-anchor="start" x="509.75" y="-186.35" font-family="Arial" font-size="12.00">created_at: timestamp </text>
<text text-anchor="start" x="509.75" y="-172.1" font-family="Arial" font-size="12.00">updated_at: timestamp </text>
</g>
<!-- interaction_histories&#45;&gt;session -->
<g id="edge4" class="edge">
<title>interaction_histories:table&#45;&gt;session:table</title>
<path fill="none" stroke="#666666" stroke-width="1.2" d="M755,-432.5C755,-443.07 937.8,-438.99 945.25,-431.5 981.69,-394.87 977,-356.01 945.25,-315.25 893.96,-249.41 766.59,-345.5 755.74,-277.44"/>
<polygon fill="#666666" stroke="#666666" stroke-width="1.2" points="755.75,-277.62 759.52,-267.32 755.36,-272.24 755.06,-268.05 755.06,-268.05 755.06,-268.05 755.36,-272.24 750.54,-267.98 755.75,-277.62"/>
<text text-anchor="middle" x="954.78" y="-287.75" font-family="Arial" font-size="10.00">session_id → id</text>
</g>
<!-- user -->
<g id="node8" class="node">
<title>user</title>
<polygon fill="#e8f4ff" stroke="black" points="620.62,-0.5 620.62,-116.25 889.38,-116.25 889.38,-0.5 620.62,-0.5"/>
<text text-anchor="middle" x="755" y="-100.85" font-family="Arial" font-size="12.00">user</text>
<polyline fill="none" stroke="black" points="620.62,-94 889.38,-94"/>
<text text-anchor="start" x="628.62" y="-78.6" font-family="Arial" font-size="12.00">id: int 🔑 NOT NULL</text>
<text text-anchor="start" x="628.62" y="-64.35" font-family="Arial" font-size="12.00">username: varchar(50) NOT NULL /* 用户名 */</text>
<text text-anchor="start" x="628.62" y="-50.1" font-family="Arial" font-size="12.00">password: varchar(255) NOT NULL /* 密码 */</text>
<text text-anchor="start" x="628.62" y="-35.85" font-family="Arial" font-size="12.00">email: varchar(100) /* 电子邮箱 */</text>
<text text-anchor="start" x="628.62" y="-21.6" font-family="Arial" font-size="12.00">created_at: timestamp </text>
<text text-anchor="start" x="628.62" y="-7.35" font-family="Arial" font-size="12.00">updated_at: timestamp </text>
</g>
<!-- session&#45;&gt;user -->
<g id="edge5" class="edge">
<title>session:table&#45;&gt;user:table</title>
<path fill="none" stroke="#666666" stroke-width="1.2" d="M755,-267.75C755,-274.78 1003.29,-271.73 1008.25,-266.75 1040.24,-234.63 1037.17,-199.66 1008.25,-164.75 937.53,-79.37 766.5,-221.59 755.55,-127"/>
<polygon fill="#666666" stroke="#666666" stroke-width="1.2" points="755.56,-127.14 759.52,-116.91 755.27,-121.74 755.04,-117.55 755.04,-117.55 755.04,-117.55 755.27,-121.74 750.53,-117.39 755.56,-127.14"/>
<text text-anchor="middle" x="1010.78" y="-137.25" font-family="Arial" font-size="10.00">user_id → id</text>
</g>
<!-- test_recommendations -->
<g id="node6" class="node">
<title>test_recommendations</title>
<polygon fill="#e8f4ff" stroke="black" points="382,-509 382,-596.25 746,-596.25 746,-509 382,-509"/>
<text text-anchor="middle" x="564" y="-580.85" font-family="Arial" font-size="12.00">test_recommendations</text>
<polyline fill="none" stroke="black" points="382,-574 746,-574"/>
<text text-anchor="start" x="390" y="-558.6" font-family="Arial" font-size="12.00">id: int 🔑 NOT NULL</text>
<text text-anchor="start" x="390" y="-544.35" font-family="Arial" font-size="12.00">interaction_history_id: int 🔗 NOT NULL /* 关联的交互历史 */</text>
<text text-anchor="start" x="390" y="-530.1" font-family="Arial" font-size="12.00">recommendation_text: text NOT NULL /* 检查建议文本 */</text>
<text text-anchor="start" x="390" y="-515.85" font-family="Arial" font-size="12.00">created_at: timestamp </text>
</g>
<!-- test_recommendations&#45;&gt;interaction_histories -->
<g id="edge6" class="edge">
<title>test_recommendations:table&#45;&gt;interaction_histories:table</title>
<path fill="none" stroke="#666666" stroke-width="1.2" d="M564,-597.25C564,-607.36 738.87,-603.42 746,-596.25 759.75,-582.42 756.78,-524.75 746,-508.5 697.67,-435.64 616.16,-502.72 564.75,-432 564.46,-431.61 564.18,-431.19 563.9,-430.74"/>
<polygon fill="#666666" stroke="#666666" stroke-width="1.2" points="563.9,-430.78 568.26,-420.71 563.82,-425.37 563.76,-421.18 563.76,-421.18 563.76,-421.18 563.82,-425.37 559.26,-420.84 563.9,-430.78"/>
<text text-anchor="middle" x="659.07" y="-452.5" font-family="Arial" font-size="10.00">interaction_history_id → id</text>
</g>
<!-- treatment_recommendations -->
<g id="node7" class="node">
<title>treatment_recommendations</title>
<polygon fill="#e8f4ff" stroke="black" points="764,-509 764,-596.25 1128,-596.25 1128,-509 764,-509"/>
<text text-anchor="middle" x="946" y="-580.85" font-family="Arial" font-size="12.00">treatment_recommendations</text>
<polyline fill="none" stroke="black" points="764,-574 1128,-574"/>
<text text-anchor="start" x="772" y="-558.6" font-family="Arial" font-size="12.00">id: int 🔑 NOT NULL</text>
<text text-anchor="start" x="772" y="-544.35" font-family="Arial" font-size="12.00">interaction_history_id: int 🔗 NOT NULL /* 关联的交互历史 */</text>
<text text-anchor="start" x="772" y="-530.1" font-family="Arial" font-size="12.00">recommendation_text: text NOT NULL /* 治疗建议文本 */</text>
<text text-anchor="start" x="772" y="-515.85" font-family="Arial" font-size="12.00">created_at: timestamp </text>
</g>
<!-- treatment_recommendations&#45;&gt;interaction_histories -->
<g id="edge7" class="edge">
<title>treatment_recommendations:table&#45;&gt;interaction_histories:table</title>
<path fill="none" stroke="#666666" stroke-width="1.2" d="M946,-597.25C946,-607.36 1120.87,-603.42 1128,-596.25 1141.75,-582.42 1138.35,-525.02 1128,-508.5 1105.85,-473.14 1006.59,-427.36 956.67,-421.1"/>
<polygon fill="#666666" stroke="#666666" stroke-width="1.2" points="956.63,-421.09 946.96,-415.91 951.24,-420.72 947.05,-420.43 947.05,-420.43 947.05,-420.43 951.24,-420.72 946.34,-424.89 956.63,-421.09"/>
<text text-anchor="middle" x="1124.41" y="-452.5" font-family="Arial" font-size="10.00">interaction_history_id → id</text>
</g>
</g>
</svg>
