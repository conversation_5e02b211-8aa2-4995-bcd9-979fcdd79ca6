package com.makiyo.aidoctor.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.Contact;
import org.springdoc.core.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SwaggerConfig {
    
    @Bean
    public OpenAPI aiDoctorOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("AI医生问诊系统(基层版)接口文档")
                        .description("AI医生问诊系统(基层版)的RESTful APIs文档")
                        .version("1.0")
                        .contact(new Contact()
                                .name("Makiyo")
                                .url("http://************:8080")
                                .email("<EMAIL>")));
    }

    @Bean
    public GroupedOpenApi publicApi() {
        return GroupedOpenApi.builder()
                .group("public")
                .pathsToMatch("/**")
                .build();
    }
} 