import json
import pandas as pd
import httpx
import requests
from volcenginesdkarkruntime import Ark
from anthropic import Anthropic, DefaultHttpxClient

from openai import OpenAI


from dotenv import load_dotenv
import os
import re

from prompts import _user_prompt, _sys_prompt, _user_prompt_category, _sys_prompt_category, _user_prompt_category_treatment, _sys_prompt_category_treatment, _user_prompt_diagnosis_category, _sys_prompt_diagnosis_category, _user_prompt_treatment_specification, _sys_prompt_treatment_specification, _user_prompt_diagnosis_symptoms, _sys_prompt_diagnosis_symptoms, _user_prompt_treatment_rag, _sys_prompt_treatment_rag

from Doctor import Doctor

from logger import logger



pattern = r'(\{.*?\})'

load_dotenv('../.env')  # 加载环境变量

def format_medical_advice(data):
    # 提取药品信息
    medicine_list = []
    for medicine in data.get('药品', []):  # 如果 '药品' 不存在，则默认空列表，避免 KeyError
        medicine_details = [
            f"药品名: {medicine.get('药品名', '未知')}",
            f"规格: {medicine.get('规格', '未提供')}",
            f"服用方法: {medicine.get('服用方法', '未提供')}",
            f"剂量安排: {medicine.get('剂量安排', '未提供')}",
            f"使用目的: {medicine.get('使用目的', '未提供')}",
            "-" * 30
        ]
        medicine_list.append("\n".join(medicine_details))

    medicine_text = "药品推荐：\n" + "\n\n".join(medicine_list) if medicine_list else "药品推荐：暂无推荐药品\n"

    # 提取生活建议
    life_advice_list = [f"- {advice}" for advice in data.get('生活建议', [])]  # 如果 '生活建议' 不存在，默认为空列表
    life_advice_text = "生活建议：\n" + "\n".join(life_advice_list) if life_advice_list else "\n生活建议：暂无生活建议"

    # 读取“是否住院”字段，如果是“是” 则仅输出“住院理由”字段
    if data.get("是否住院") == "是":
        return f"{medicine_text}\n{life_advice_text}\n住院理由：{data.get('住院理由', '未提供')}"

    # 合并药品信息和生活建议
    return f"{medicine_text}\n{life_advice_text}"



class DiagnosisAgent:
    def __init__(self, doctors, model="deepseek_r1"):
        self.doctors = doctors  # 患者信息列表
        self.model = model  # 诊断模型
        self.meds = self.load_meds()  # 药品分类信息
        logger.info(f"DiagnosisAgent initialized with model: {model}")

    def load_meds(self):
        """
        读取药品分类信息
        """
        # 读取药品分类信息
        with open("../data/medications.json", "r", encoding="utf-8") as json_file:
            meds = json.load(json_file)
        logger.info("Medications loaded successfully.")
        return meds
    
    # 提取信息的函数
    def extract_medications(self, input_data, data):
        result = {}

        if not input_data:
            return result
        
        # 遍历输入的分类数据
        for category_group, categories in input_data.items():
            medications = []
            for category_name in categories.values():
                # 查找西药类别
                if category_name in data.get(category_group, {}):
                    medications.extend(data[category_group].get(category_name, []))
            result[category_group] = medications
        
        return result
    
    # 提取药品名称的函数
    def extract_medication_names(self, data):
        names = []
        if not data:
            return names
        for category, medications in data.items():
            for medication in medications:
                names.append(medication["名称"])
        return names

    def _create_api_client(self, model):
        """
        根据不同模型创建相应的API客户端
        """
        logger.info(f"Creating API client for model: {model}")

        if model == "deepseek_r1":
            api_key = os.getenv(f"{model.upper()}_API_KEY")
            api_key_jiuzhang = os.getenv("DEEPSEEK_R1_API_KEY_JIUZHANG")
            logger.debug(f"Using API key for model {model}: {api_key_jiuzhang}")
            # return Ark(
            #     base_url="https://ark.cn-beijing.volces.com/api/v3",
            #     api_key=api_key,
            #     timeout=httpx.Timeout(timeout=1800)
            # )
            return OpenAI(
                api_key=api_key_jiuzhang,
                base_url="https://deepseek.alayanew.com/v1"
            )
        
        elif model == "deepseek_v3":
            api_key = os.getenv(f"{model.upper()}_API_KEY")
            logger.debug(f"Using API key for model {model}: {api_key}")
            return Ark(
                base_url="https://ark.cn-beijing.volces.com/api/v3",
                api_key=api_key,
                timeout=httpx.Timeout(timeout=1800)
            )
        
        
        elif model == "claude":
            api_key = os.getenv("CLAUDE_API_KEY")
            logger.debug(f"Using API key for Claude model: {api_key}")
            return Anthropic(
                api_key=api_key,
                http_client=DefaultHttpxClient(
                    proxy="http://localhost:7890",  # Clash HTTP 代理地址
                    transport=httpx.HTTPTransport(local_address="0.0.0.0"),
                ),
            )
        elif model == "qwq-32b":
            api_key = os.getenv("ALI_API_KEY")
            logger.debug(f"Using API key for model {model}: {api_key}")
            return OpenAI(
                api_key=api_key,
                base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
            )

        else:
            logger.error(f"Model {model} not supported")
            raise ValueError(f"Model {model} not supported")

    def process_result(self, result, type):
        if type == "diagnosis":
            # 处理结果 加入异常处理
            try :
                result_dict = json.loads(result)
                logger.info("API response successfully parsed.")
                logger.info(f"API response: {result_dict}")
                return result_dict["病名"], result_dict["诊疗计划"], result_dict["种类"]
            except json.JSONDecodeError as e:
                logger.error(f"JSON Decode Error: {e} in diagnosis")
                match = re.search(pattern, result)
                if match:
                    json_data = json.loads(match.group(1))
                    return json_data["病名"], json_data["诊疗计划"], json_data["种类"]
                if result.startswith("```"):
                    result_new = result.replace("```", "")
                    result_dict = json.loads(result_new)
                    logger.info("API response parsed by ``` ``` .")
                    return result_dict["病名"], result_dict["诊疗计划"], result_dict["种类"]
                
                return None, None, None
        elif type == "category":
            # 处理结果 加入异常处理
            try :
                result_dict = json.loads(result)
                logger.info("API response successfully parsed.")
                logger.info(f"API response: {result_dict}")
                return result_dict
            except json.JSONDecodeError as e:
                logger.error(f"JSON Decode Error: {e}")
                match = re.search(pattern, result)
                if match:
                    json_data = json.loads(match.group(1))
                    return json_data
                return None
        elif type == "new_treatment":
            # 处理结果 加入异常处理
            try :
                result_dict = json.loads(result)
                logger.info("API response successfully parsed.")
                logger.info(f"API response: {result_dict}")
                return result_dict["诊疗计划"]
            except json.JSONDecodeError as e:
                logger.error(f"JSON Decode Error: {e}  in new treatment")
                match = re.search(pattern, result)
                if match:
                    json_data = json.loads(match.group(1))
                    return json_data["诊疗计划"]
                return None
        elif type == "diagnosis_category":
            try:
                result_dict = json.loads(result)
                logger.info("API response successfully parsed.")
                logger.info(f"API response: {result_dict}")
                return result_dict["病名"], result_dict["种类"]
            except json.JSONDecodeError as e:
                logger.error(f"JSON Decode Error: {e}  in diagnosis category")
                # match = re.search(pattern, result)
                # if match:
                #     json_data = json.loads(match.group(1))
                #     return json_data["病名"], json_data["种类"]
                # return None, None

                # 使用正则表达式提取 JSON 部分
                json_string = re.search(r'({.*})', result).group(0)

                # 解析 JSON 字符串为 Python 字典
        elif type == "diagnosis_sysptoms":
            try:
                result_dict = json.loads(result)
                logger.info("API response successfully parsed.")
                logger.info(f"API response: {result_dict}")
                return result_dict["病名"], result_dict["病例特点"]
            except json.JSONDecodeError as e:
                logger.error(f"JSON Decode Error: {e}  in diagnosis category")
                # match = re.search(pattern, result)
                # if match:
                #     json_data = json.loads(match.group(1))
                #     return json_data["病名"], json_data["种类"]
                # return None, None

                # 使用正则表达式提取 JSON 部分
                json_string = re.search(r'({.*})', result).group(0)
                # 解析 JSON 字符串为 Python 字典
                json_data = json.loads(json_string)
                return json_data["病名"], json_data["病例特点"]
            
        elif type == "treatment_specification" or type == "treatment_rag":
            try:
                result_dict = json.loads(result)
                logger.info("API response successfully parsed.")
                logger.info(f"API response: {result_dict}")
                return result_dict
            except json.JSONDecodeError as e:
                logger.error(f"JSON Decode Error: {e}  in treatment specification")
                match = re.search(pattern, result)
                if match:
                    json_data = json.loads(match.group(1))
                    return json_data
                return None    
        
        else:
            logger.error(f"Type {type} not supported")
            return result

    def _get_result_from_api(self, client, user_prompt, sys_prompt, model, type= "diagnosis"):
        """
        通用API调用逻辑
        """
        logger.info(f"Making API call with model: {model}")
        logger.info(f"User prompt: {user_prompt}")
        logger.info(f"System prompt: {sys_prompt}")
        result = ""
        try:
            if model == "claude":
                message = client.messages.create(
                    model="claude-3-5-sonnet-20241022",
                    max_tokens=1024,
                    messages=[{"role": "user", "content": user_prompt}],
                    system=sys_prompt
                )
                result = message.content[0].text
            if model == "qwq-32b":
                completion = client.chat.completions.create(
                    model="qwq-32b",
                    messages=[{"role": "system", "content": sys_prompt}, {"role": "user", "content": user_prompt}],
                    # messages=[
                    #     {"role": "user", "content": "say hello"}
                    # ],
                    stream=True,
                    # Uncomment the following line to return token usage in the last chunk
                    # stream_options={
                    #     "include_usage": True
                    # }
                )
                content = ""
                for chunk in completion:
                    if chunk.choices:
                        for choice in chunk.choices:
                            # 优先提取delta中的content字段
                            if choice.delta.content:
                                content += choice.delta.content
                            
                            # 如果模型使用reasoning_content字段（根据实际API设计调整）
                            # if getattr(choice.delta, 'reasoning_content', None):
                            #     content += choice.delta.reasoning_content
                result = content
            
            if model == "deepseek_r1":
                completion = client.chat.completions.create(
                    model="deepseek-r1",
                    messages=[{"role": "system", "content": sys_prompt}, {"role": "user", "content": user_prompt}]
                )
                result = completion.choices[0].message.content
            
            if model == "deepseek_v3":
                completion = client.chat.completions.create(
                    model="ep-20250218172420-mcwk8",
                    messages=[{"role": "system", "content": sys_prompt}, {"role": "user", "content": user_prompt}]
                )
                result = completion.choices[0].message.content
                
                    
            # else:
            #     completion = client.chat.completions.create(
            #         model="ep-20250218172420-mcwk8" if model == "deepseek_v3" else "deepseek-r1",
            #         messages=[{"role": "system", "content": sys_prompt}, {"role": "user", "content": user_prompt}]
            #     )
            #     result = completion.choices[0].message.content

            logger.info(f"API response: {result}")

            return self.process_result(result, type)

        except (json.JSONDecodeError, Exception) as e:
            print(f"Error during API call or result parsing: {e}")
            
            return None, None
        
    def diagnose_refactor(self, doctor):
        """
        诊断方法，根据患者信息做出简单推断或调用外部 API 获取诊断结果。
        """

        logger.info(f"Diagnosing patient with index: {doctor.index}")

        user_prompt = _user_prompt.format(doctor=doctor, meds_xiyao=list(self.meds['西药'].keys()), meds_zhongyao=list(self.meds['中药'].keys()))
        sys_prompt = _sys_prompt


        # 输入患者信息和药品分类信息，获取诊断结果和对应的几个药品分类
        user_prompt_diagnosis_category = _user_prompt_diagnosis_category.format(doctor=doctor, meds_xiyao=list(self.meds['西药'].keys()), meds_zhongyao=list(self.meds['中药'].keys()))
        sys_prompt_diagnosis_category = _sys_prompt_diagnosis_category
        # 创建API客户端
        client = self._create_api_client(self.model)
        # 获取诊断和分类结果
        diagnosis, meds_category = self._get_result_from_api(client, user_prompt_diagnosis_category, sys_prompt_diagnosis_category, self.model, type = "diagnosis_category")
        # 输出药品分类结果
        logger.info(f"Medications category: {meds_category}")

        # 提取返回的药品分类对应的药品信息meds_extracted 药品名和规格等信息
        meds_extracted = self.extract_medications(meds_category, self.meds)
        # print("meds_extracted: ", meds_extracted)
        medications_names = self.extract_medication_names(meds_extracted)
        # print("medications_names: ", medications_names)

        # 输入患者信息，真实诊断结果，药品信息，输出对应的诊疗计划
        user_prompt_treatment_specification = _user_prompt_treatment_specification.format(doctor=doctor, diagnosis=doctor.preliminary_exam, meds_list=meds_extracted)
        sys_prompt_treatment_specification = _sys_prompt_treatment_specification    
        # 获取诊疗计划
        new_treatment_plan_specification = self._get_result_from_api(client, user_prompt_treatment_specification, sys_prompt_treatment_specification, self.model, type="treatment_specification")
        #对new_treatment_plan_specification进行处理
        new_treatment_plan_specification = format_medical_advice(new_treatment_plan_specification)
        
        # return diagnosis, treatment_plan
        return diagnosis, new_treatment_plan_specification
    
    def diagnose_with_rag(self, doctor):
        logger.info(f"Diagnosing patient with index: {doctor.index}")

        user_prompt_diagnosis_symptoms = _user_prompt_diagnosis_symptoms.format(doctor=doctor)
        sys_prompt_diagnosis_symptoms = _sys_prompt_diagnosis_symptoms
        client = self._create_api_client(self.model)

        # 获取诊断和症状
        diagnosis, symptoms = self._get_result_from_api(
            client, user_prompt_diagnosis_symptoms, sys_prompt_diagnosis_symptoms, self.model, type="diagnosis_sysptoms"
        )
        logger.info(f"Symptoms: {symptoms}")

        # 统一的API请求函数
        def fetch_medicines(medicine_type, top_k):
            url = "http://47.94.171.56:8090/search"
            headers = {"Content-Type": "application/json"}
            data = {
                "text": f"{diagnosis}, {symptoms}",
                "medicine_type": medicine_type,
                "availability": "有",
                "top_k": top_k
            }

            logger.info(f"Requesting {medicine_type} medicines with data: {data}")
            try:
                response = requests.post(url, json=data, headers=headers, timeout=10)
                response.raise_for_status()  # 检查 HTTP 响应状态码
                return response.json()
            except requests.exceptions.RequestException as e:
                logger.error(f"Failed to fetch {medicine_type} medicines: {e}")
                return {"results": []}  # 返回空数据，防止后续代码崩溃
            except requests.exceptions.JSONDecodeError:
                logger.error(f"Invalid JSON response for {medicine_type}: {response.text}")
                return {"results": []}

        # 获取西药信息
        meds_retriveal_xiyao = fetch_medicines("西药", 12)
        meds_retriveal_payloads_xiyao = [med.get("payload", {}) for med in meds_retriveal_xiyao.get("results", [])]
        meds_retriveal_extracted_xiyao = [
            {
                "通用名称": med.get("通用名称", ""),
                "适应症": med.get("适应症", ""),
                "规格": med.get("规格", ""),
                "用法用量": med.get("用法用量", "")
            }
            for med in meds_retriveal_payloads_xiyao
        ]

        # 获取中药信息
        meds_retriveal_zhongyao = fetch_medicines("中药", 10)
        meds_retriveal_payloads_zhongyao = [med.get("payload", {}) for med in meds_retriveal_zhongyao.get("results", [])]
        meds_retriveal_extracted_zhongyao = [
            {
                "通用名称": med.get("通用名称", ""),
                "功能主治": med.get("适应症", ""),
                "规格": med.get("规格", ""),
                "用法用量": med.get("用法用量", "")
            }
            for med in meds_retriveal_payloads_zhongyao
        ]

        # 合并西药和中药的信息
        meds_retriveal_extracted = {
            "西药": meds_retriveal_extracted_xiyao,
            "中药": meds_retriveal_extracted_zhongyao
        }

        # 生成诊疗计划
        user_prompt_treatment_rag = _user_prompt_treatment_rag.format(
            doctor=doctor, diagnosis=doctor.preliminary_exam, meds_list=meds_retriveal_extracted
        )
        sys_prompt_treatment_rag = _sys_prompt_treatment_rag
        new_treatment_plan_rag = self._get_result_from_api(
            client, user_prompt_treatment_rag, sys_prompt_treatment_rag, self.model, type="treatment_rag"
        )


        return diagnosis, format_medical_advice(new_treatment_plan_rag)
    




    def diagnose_all(self):
        """
        对所有患者进行诊断
        """
        results = []
        logger.info(f"Diagnosing {len(self.doctors)} patients...")

        print("doctors length: ", len(self.doctors))
        for index, doctor in enumerate(self.doctors, start=1):
            if index > 20:
                break
            # 只诊断第1，9，14个患者
            # if index != 1 and index != 9 and index != 14:
            #     continue
            # 使用换行符分隔不同患者的诊断结果
            logger.info("\n" + "\n" + "\n"  + "-" * 40)
            logger.info(f"Diagnosing patient {index}/{len(self.doctors)}...")

            logger.info(f"Diagnosing patient {index}/{len(self.doctors)}...")
            # diagnosis, treatment_plan = self.diagnose_refactor(doctor)
            diagnosis, treatment_plan = self.diagnose_with_rag(doctor)

            results.append({
                "diagnosis": diagnosis,
                "treatment_plan": treatment_plan
            })

        logger.info("Diagnosis completed for all patients.")
        return results
    


def main_csv():
    file_path = "../data/cases.csv"
    doctors = Doctor.load_from_csv(file_path)
    
    
    diagnosis_agent = DiagnosisAgent(doctors, model="deepseek_r1")


    # # # 诊断单个患者
    # doctor = doctors[2]
    # diagnosis, treatment_plan = diagnosis_agent.diagnose_refactor(doctor)

    # print(f"Diagnosis: {diagnosis}")
    # print(f"Treatment Plan: {treatment_plan}")

    # 诊断所有患者
    results = diagnosis_agent.diagnose_all()
    # 打印诊断结果
    for result in results:
        # print(f"Patient: {result['patient']}")
        print(f"Diagnosis: {result['diagnosis']}")
        print(f"Treatment Plan: {result['treatment_plan']}")
        print("-" * 40)

    # 将诊断结果保存到CSV文件
    df = pd.DataFrame(results)

    # 将诊断结果保存到xlsx文件
    df = pd.DataFrame(results)

    # df新加两列，分别是doctor.preliminary_exam 和 doctor.treatment
    for i in range(len(doctors)):
        df.loc[i, 'preliminary_exam'] = doctors[i].preliminary_exam
        df.loc[i, 'treatment'] = doctors[i].treatment

    # 将诊断结果写入xlsx文件
    file_path_result = "../data/results_treatment_specification.xlsx"
    df.to_excel(file_path_result, index=False, engine='openpyxl')
    print(f"Diagnosis complete. Results written to {file_path_result}")


def main_json():
    # file_path = "../data/cases_full.json"
    file_path = "../data/0314.json"
    doctors = Doctor.load_from_json_filepath(file_path)
    
    
    diagnosis_agent = DiagnosisAgent(doctors, model="qwq-32b")

    # 诊断所有患者
    results = diagnosis_agent.diagnose_all()
    # 打印诊断结果
    for result in results:
        # print(f"Patient: {result['patient']}")
        print(f"Diagnosis: {result['diagnosis']}")
        print(f"Treatment Plan: {result['treatment_plan']}")
        print("-" * 40)


    # 将诊断结果保存到xlsx文件
    df = pd.DataFrame(results)
    
    # df新加两列，分别是doctor.preliminary_exam 和 doctor.treatment
    for i in range(len(doctors)):
        df.loc[i, 'preliminary_exam'] = doctors[i].preliminary_exam
        df.loc[i, 'treatment'] = doctors[i].treatment
        df.loc[i, "doctor_examination"] = doctors[i].doctor_examination
        df.loc[i, "has_examination"] = doctors[i].has_examination
        df.loc[i, "index"] = doctors[i].index


    # 将诊断结果写入xlsx文件
    file_path_result = "../data/0314_qwq.xlsx"
    df.to_excel(file_path_result, index=False)
    print(f"Diagnosis complete. Results written to {file_path_result}")

def process_json(historys_json : dict , output_file_path = "../data/full_run_3_7.xlsx"):
    # file_path = "../data/cases_full.json"
    doctors = Doctor.load_from_json(historys_json)

    logger.info(f"doctor: {doctors}")
    
    
    diagnosis_agent = DiagnosisAgent(doctors, model="deepseek_r1")

    # 诊断所有患者
    results = diagnosis_agent.diagnose_all()
    # 打印诊断结果
    for result in results:
        # print(f"Patient: {result['patient']}")
        print(f"Diagnosis: {result['diagnosis']}")
        print(f"Treatment Plan: {result['treatment_plan']}")
        print("-" * 40)


    # 将诊断结果保存到xlsx文件
    df = pd.DataFrame(results)
    
    # df新加两列，分别是doctor.preliminary_exam 和 doctor.treatment
    for i in range(len(doctors)):
        df.loc[i, 'AI_ask_AI_check'] = doctors[i].AI_ask_AI_check
        df.loc[i, 'preliminary_exam'] = doctors[i].preliminary_exam
        df.loc[i, 'treatment'] = doctors[i].treatment
        df.loc[i, 'doctor_examination'] = doctors[i].doctor_examination
        df.loc[i, 'has_examination'] = doctors[i].has_examination
        df.loc[i, 'index'] = doctors[i].index


    # 将诊断结果写入xlsx文件
    file_path_result = output_file_path
    df.to_excel(file_path_result, index=False)
    print(f"Diagnosis complete. Results written to {file_path_result}")


def main_xlsx():
    xlsx_file = r"../data/检查和药品0224_shot.xlsx"

    # 输入文件路径，读取数据，初始化Doctor实例列表
    doctors = Doctor.load_from_xlsx(xlsx_file)
    diagnosis_agent = DiagnosisAgent(doctors, model="qwq-32b")
    # results = diagnosis_agent.diagnose_with_rag(doctors[0])
    results = diagnosis_agent.diagnose_all()
    # 打印诊断结果
    for result in results:
        # print(f"Patient: {result['patient']}")
        print(f"Diagnosis: {result['diagnosis']}")
        print(f"Treatment Plan: {result['treatment_plan']}")
        print("-" * 40)

    # 将诊断结果保存到xlsx文件
    df = pd.DataFrame(results)
    # df新加两列，分别是doctor.preliminary_exam 和 doctor.treatment
    for i in range(len(doctors)):
        df.loc[i, 'AI_ask_AI_check'] = doctors[i].AI_ask_AI_check
        df.loc[i, 'preliminary_exam'] = doctors[i].preliminary_exam
        df.loc[i, 'treatment'] = doctors[i].treatment
        df.loc[i, 'doctor_examination'] = doctors[i].doctor_examination
        df.loc[i, 'has_examination'] = doctors[i].has_examination
        df.loc[i, 'index'] = doctors[i].index

    # 将诊断结果写入xlsx文件
    file_path_result = "../data/qwq-32b.xlsx"
    df.to_excel(file_path_result, index=False)
    print(f"Diagnosis complete. Results written to {file_path_result}")


    
    


if __name__ == '__main__':
    main_json()