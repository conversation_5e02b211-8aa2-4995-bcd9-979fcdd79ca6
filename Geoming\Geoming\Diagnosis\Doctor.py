import pandas as pd
import re
from typing import List, Dict, Optional
from logger import logger


class Doctor:
    def __init__(self, age: int, gender: str, chief_complaint: str, history_of_present_illness: str,
                 past_history: str, contact_history_of_infectious_diseases: str,
                  family_history: str, physical_examination: Optional[str] = None,
                 special_examination: Optional[str] = None, auxiliary_examination: Optional[str] = None, preliminary_exam: Optional[str] = None, treatment: Optional[str] = None, doctor_examination: Optional[str] = None, has_examination: Optional[str] = None, index: Optional[str] = None, AI_ask_AI_check: Optional[str] = None, personal_history: Optional[str] = None):
        self.age = age
        self.gender = gender
        self.chief_complaint = chief_complaint
        self.history_of_present_illness = history_of_present_illness
        self.past_history = past_history
        self.contact_history_of_infectious_diseases = contact_history_of_infectious_diseases
        self.personal_history = personal_history
        self.family_history = family_history
        self.physical_examination = physical_examination
        self.special_examination = special_examination
        self.auxiliary_examination = auxiliary_examination


        self.preliminary_exam = preliminary_exam
        self.treatment = treatment

        self.doctor_examination = doctor_examination
        self.has_examination = has_examination
        self.index = index

        self.AI_ask_AI_check = AI_ask_AI_check

    def __repr__(self):
        return (f"Doctor(age={self.age}, gender={self.gender}, \n"
                f"index={self.index}, \n"
                f"chief_complaint={self.chief_complaint}, \n"
                f"history_of_present_illness={self.history_of_present_illness}, \n"
                f"past_history={self.past_history}, \n"
                f"contact_history_of_infectious_diseases={self.contact_history_of_infectious_diseases}, \n"
                f"personal_history={self.personal_history}, \n"
                f"family_history={self.family_history}, \n"
                f"physical_examination={self.physical_examination}, \n"
                f"special_examination={self.special_examination}, \n"
                f"auxiliary_examination={self.auxiliary_examination}), \n"
                f"preliminary_exam={self.preliminary_exam}, \n"
                f"treatment={self.treatment}, \n"
                f"doctor_examination={self.doctor_examination}, \n"
                f"has_examination={self.has_examination}, \n"
                f"AI_ask_AI_check={self.AI_ask_AI_check}, \n"

                )
    
    @classmethod
    def string_zh(self):
        return (f"【基本信息】 {doctor.gender}，{doctor.age}\n"
                f"【主诉】{doctor.chief_complaint}\n"
                f"【现病史】{doctor.history_of_present_illness}\n"
                f"【既往史】{doctor.past_history}\n"
                f"【传染病接触史】{doctor.contact_history_of_infectious_diseases}\n"
                f"【个人史】{doctor.personal_history}\n"
                f"【家族史】{doctor.family_history}\n" 
                
                
                )

    @classmethod
    def load_from_csv(cls, file_path: str) -> List['Doctor']:
        """
        从 CSV 文件加载数据并创建 Doctor 实例列表。
        """
        try:
            df = pd.read_csv(file_path)
            logger.info(f"成功读取 CSV 文件: {file_path}")
        except Exception as e:
            logger.error(f"读取 CSV 文件失败: {e}")
            return []

        doctors = []
        for _, row in df.iterrows():
            try:
                doctor = cls(
                    age=row['就诊时年龄'],
                    gender=row['性别'],
                    chief_complaint=row['主诉'],
                    history_of_present_illness=row['现病史'],
                    past_history=row['既往史'],
                    contact_history_of_infectious_diseases=row['传染病接触史'],
                    personal_history=row['个人史'],
                    family_history=row['家族史'],
                    physical_examination=row.get('体格检查'),
                    special_examination=row.get('专科查体'),
                    auxiliary_examination=row.get('辅助检查'),
                    preliminary_exam=row.get('诊断标准'),
                    treatment=row.get('治疗方案'),
                )
                doctors.append(doctor)
            except KeyError as e:
                logger.warning(f"CSV 文件缺少必要字段: {e}")
            except Exception as e:
                logger.error(f"创建 Doctor 实例失败: {e}")

        return doctors
    
    @classmethod
    def load_from_solution(cls, solution: str) -> List['Doctor']:
        """
        从诊断方案加载数据并创建 Doctor 实例列表。
        """
        try:
            doctors = []
            data = cls.extract_data(solution)
            doctor = cls(**data)
            doctors.append(doctor)
        except Exception as e:
            logger.error(f"创建 Doctor 实例失败: {e}")

        return doctors
    
    @classmethod
    def load_from_xlsx(cls, file_path: str) -> List['Doctor']:
        """
        从 xlsx 文件加载数据并创建 Doctor 实例列表。
        """
        try:
            df = pd.read_excel(file_path)
            logger.info(f"成功读取 xlsx 文件: {file_path}")
        except Exception as e:
            logger.error(f"读取 xlsx 文件失败: {e}")
            return []

        doctors = []
        for _, row in df.iterrows():
            # logger.info(f"index: {row.get('案例编号')}")
            try:
                data = cls.extract_data(row["病例合并"])
                doctor = cls(**data)
                doctor.preliminary_exam = row.get("诊断标准")
                doctor.treatment = row.get("治疗药物")
                doctor.doctor_examination = row.get("初步检查")
                doctor.index = row.get("案例编号")
                doctors.append(doctor)
            except KeyError as e:
                logger.warning(f"xlsx 文件缺少必要字段: {e}")
            except Exception as e:
                logger.error(f"创建 Doctor 实例失败: {e}")
        
        return doctors


    @classmethod
    def load_from_json_filepath(cls, file_path: str) -> List['Doctor']:
        """
        从 JSON 文件加载数据并创建 Doctor 实例列表。
        """
        try:
            df = pd.read_json(file_path)
            logger.info(f"成功读取 JSON 文件: {file_path}")
        except Exception as e:
            logger.error(f"读取 JSON 文件失败: {e}")
            return []

        doctors = []
        for _, row in df.iterrows():
            try:
                text = row["AI Ask Solution"]
                data = cls.extract_data(text)
                logger.info(f"data: {data}")
                doctor = cls(**data)
                doctor.preliminary_exam = row.get("preliminary_exam")
                doctor.treatment = row.get("treatment")

                doctor.doctor_examination = row.get("doctor_examination")
                doctor.has_examination = row.get("has_examination")
                doctor.index = row.get("index")

                data_original = row.get("doctor_info")
                data_original = cls.extract_data(data_original)
                if data_original:
                    doctor.physical_examination = data_original.get("physical_examination")
                    doctor.special_examination = data_original.get("special_examination")
                    # doctor.auxiliary_examination = data_original.get("auxiliary_examination")

                doctors.append(doctor)
            except (KeyError, IndexError) as e:
                logger.warning(f"JSON 文件缺少必要字段或格式错误: {e}")
            except Exception as e:
                logger.error(f"创建 Doctor 实例失败: {e}")

        return doctors
    

    @classmethod
    def load_from_json(cls, historys_json : dict) -> List['Doctor']:
        """
        从 JSON 格式的字符串 加载数据并创建 Doctor 实例列表。
        """
        try:
            df = pd.DataFrame(historys_json)
            logger.info(f"成功读取 JSON 文件:")
        except Exception as e:
            logger.error(f"读取 JSON 文件失败: {e}")
            return []

        doctors = []
        for _, row in df.iterrows():
            try:
                text = row["doctor_history"][-1]
                data = cls.extract_data(text)
                doctor = cls(**data)
                doctor.preliminary_exam = row.get("preliminary_exam")
                doctor.treatment = row.get("treatment")
                doctor.doctor_examination = row.get("doctor_examination")
                doctor.has_examination = row.get("has_examination")
                doctor.index = row.get("index")
                doctor.AI_ask_AI_check = row.get("AI_ask_AI_check")
                doctors.append(doctor)
            except (KeyError, IndexError) as e:
                logger.warning(f"JSON 文件缺少必要字段或格式错误: {e}")
            except Exception as e:
                logger.error(f"创建 Doctor 实例失败: {e}")

        return doctors
    
    @classmethod
    def extract_age(cls, text: str) -> str:
        """
        从文本中提取年龄信息，支持以下格式：
        - "X岁Y月Z天"
        - "X岁Y月"
        - "X岁"
        - "Y月Z天"
        - "Y月"
        - "Z天"
        """
        # 正则表达式
        pattern = r'，([^\n]*)'  # 匹配第一个全角逗号后的所有非换行字符
        match = re.search(pattern, text)

        if match:
            result = match.group(1)
            if "就诊日期" in result:
                # 删除就诊日期以及之后的内容
                result = result.split("就诊日期")[0]
            return result
        else:
            return "未知"

    

    @classmethod
    def extract_data(cls, text: str) -> Dict[str, str]:
        """
        使用正则表达式从文本中提取数据，并在匹配失败时提供详细信息。
        """
        missing_fields = []  # 记录匹配失败的字段

        text = text.replace("&#xA;", "\n")  # 替换 HTML 换行符

        # 如果以<text>开头，就去掉第一行和最后一行
        if text.startswith("<text>"):
            text = text.split("\n")[1:-1]
            text = "\n".join(text)


        try:
            # 提取基本信息（第一行）
            basic_info = text.split("\n")[0]

            gender = "男" if "男" in basic_info else "女"
            # age_match = re.search(r"\d+", basic_info)
            # age = int(age_match.group()) if age_match else None
            age = cls.extract_age(basic_info)

            if age is None:
                missing_fields.append("age")

            # 定义需要匹配的字段
            fields = {
                "chief_complaint": r"【主诉】(.*?)\n",
                "history_of_present_illness": r"【现病史】(.*?)\n",
                "past_history": r"【既往史】(.*?)\n",
                "contact_history_of_infectious_diseases": r"【传染病接触史】(.*?)\n",
                "personal_history": r"【个人史】(.*?)\n",
                "family_history": r"【家族史】(.*?)\n",
                "physical_examination": r"【体格检查】(.*?)\n",
                "special_examination": r"【专科查体】(.*?)\n",
                "auxiliary_examination": r"【辅助检查】(.*?)"
            }

            # 逐个匹配字段
            extracted_data = {"age": age, "gender": gender}
            for field, pattern in fields.items():
                match = re.search(pattern, text)
                if match:
                    extracted_data[field] = match.group(1).strip()
                else:
                    missing_fields.append(field)

            # 如果有未匹配的字段，记录日志并抛出异常
            if missing_fields:
                logger.error(f"以下字段未能匹配: {missing_fields}")
                # raise ValueError(f"文本格式不符合预期，以下字段匹配失败: {', '.join(missing_fields)}")

            return extracted_data

        except Exception as e:
            logger.error(f"提取数据失败: {e}")
            raise ValueError("数据提取过程中发生错误") from e

if __name__ == '__main__':
    # 从 CSV 文件加载数据
    doctors = Doctor.load_from_xlsx('../data/检查和药品0224_shot.xlsx')
    for doctor in doctors:
        print(doctor)

    # # 从 JSON 文件加载数据
    # doctors = Doctor.load_from_json_filepath('../data/Qwen_3_5.json')
    # for doctor in doctors:
    #     print("index: ", doctors.index(doctor))
    #     print(doctor)
    #     print()
    #     print()

    # text = """【基本信息】男，8岁7月2天
    # 【主诉】胸闷1月
    # 【现病史】胸闷1月，无明显诱因，无剧烈运动或情绪激动后发生，无咳嗽、无发热、无呼吸困难、无心慌。7岁以前有3次呼吸道感染时伴有喘息，平时有轻微的过敏性鼻炎。外院肺功能检查提示小气道功能中度减低，一氧化氮呼气测定52ppb，未使用特殊药物。
    # 【既往史】7岁以前有3次呼吸道感染时伴有喘息，平时有轻微的过敏性鼻炎。
    # 【传染病接触史】否认近期传染病接触史
    # 【家族史】否认家族遗传病史
    # 【辅助检查】肺功能：小气道功能中度减低；一氧化氮呼气测定：52ppb
    # 【tag】呼吸类"""

    # # 正则表达式提取年龄
    # pattern = r'【基本信息】[^，]+，\s*(\d+岁)?\s*(\d+月)?\s*(\d+天)?'
    # match = re.search(pattern, text)

    # # 解析结果
    # age = {}
    # if match:
    #     for part in match.groups():
    #         if part:
    #             unit = part[-1]  # 取最后一个字符作为单位（岁/月/天）
    #             value = int(part[:-1])
    #             age[unit] = value

    # # age转化成string 某岁某月某天 如8岁7月2天
    # age_string = ""
    # for unit, value in age.items():
    #     if value:
    #         age_string += f"{value}{unit}"

    # print(age_string)
    # # 输出: {'岁': 8, '月': 7, '天': 2}