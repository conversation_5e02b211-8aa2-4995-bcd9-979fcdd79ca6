import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'
import '@/styles/global.scss'

const app = createApp(App)

// 更健壮的错误处理，使用 Vue 的全局错误处理器
app.config.errorHandler = (err, instance, info) => {
  // 检查是否是 ResizeObserver 错误
  if (err && err.message && err.message.includes('ResizeObserver loop completed with undelivered notifications')) {
    // 阻止这个特定错误被 Vue 的默认处理器和上层捕获
    // 返回 false 表示错误已处理
    return false;
  }
  // 对于所有其他错误，仍然在控制台打印它们
  console.error('Vue anhandled error:', err, instance, info);
};

// 保留原始的 window.onerror 处理器作为备用
window.addEventListener('error', (event) => {
  if (event.message.includes('ResizeObserver loop completed with undelivered notifications')) {
    event.preventDefault();
    event.stopPropagation();
  }
});

const pinia = createPinia()

app.use(pinia)
app.use(router)
app.use(Antd)

app.mount('#app')
