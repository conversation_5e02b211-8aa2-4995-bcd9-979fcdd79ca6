import torch
from transformers import BertTokenizer, BertModel
from qdrant_client import QdrantClient
from qdrant_client.models import PointStruct, VectorParams, Distance
import pandas as pd
import os

# 读取Excel数据
df = pd.read_excel('../data/COT_entry_新增.xlsx')
df = df.dropna(subset=["COT过程", "病史"])
print(f"读取到 {len(df)} 条有效数据")

# 加载预训练模型
model_path = r"C:\Users\<USER>\.cache\modelscope\hub\models\BAAI\bge-large-zh-v1___5"
tokenizer = BertTokenizer.from_pretrained(model_path)
model = BertModel.from_pretrained(model_path)
model.eval()  # 设置模型为评估模式

# 生成嵌入向量的函数
def generate_embedding(text):
    """将文本转换为向量"""
    inputs = tokenizer(text, return_tensors='pt', padding=True, truncation=True, max_length=512)
    with torch.no_grad():
        outputs = model(**inputs)
    return outputs.last_hidden_state[:, 0, :].squeeze().numpy()  # 取 [CLS] 作为向量

# 连接 Qdrant 数据库
# 禁用代理
import os
QDRANT_URL = "https://4c32b9ec-36a6-4290-9dbe-7e455e14dc5b.eu-west-2-0.aws.cloud.qdrant.io:6333"
QDRANT_API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3MiOiJtIn0.g_G0ko_m5qFdwL6wvxZEcygPftfUak3qZRDEyBGMNbQ"
os.environ['NO_PROXY'] = QDRANT_URL

# 连接Qdrant客户端
qdrant_client = QdrantClient(
    url=QDRANT_URL,
    api_key=QDRANT_API_KEY,
    timeout=600  # 超时时间（秒）
)

# 处理集合
collection_name = "COT"

# 如果集合不存在则创建新的集合
if not qdrant_client.collection_exists(collection_name):
    vector_dim = 1024  # bge-large-zh 模型的输出维度
    qdrant_client.create_collection(
        collection_name=collection_name,
        vectors_config=VectorParams(size=vector_dim, distance=Distance.COSINE),
    )
    print(f"新的 collection `{collection_name}` 已创建。")
else:
    print(f"使用已存在的 collection `{collection_name}`。")

# 获取当前集合中最大的ID
existing_points = qdrant_client.scroll(
    collection_name=collection_name,
    limit=1,
    with_payload=False,
    with_vectors=False
)[0]
start_id = max([point.id for point in existing_points]) + 1 if existing_points else 0

# 对于少量数据，可以简单地逐条插入
for idx, row in df.iterrows():
    obs_text = str(row["病史"])
    cot_text = str(row["COT过程"])
    
    # 生成向量嵌入
    try:
        emb = generate_embedding(obs_text)
        
        # 创建数据点，使用新的ID
        point = PointStruct(
            id=start_id + idx,
            vector=emb.tolist(),
            payload={"COT": cot_text, "OBS": obs_text}
        )
        
        # 插入单条数据
        qdrant_client.upsert(
            collection_name=collection_name, 
            points=[point]
        )
        
        print(f"✅ 成功写入第 {idx} 条数据，ID: {start_id + idx}")
    except Exception as e:
        print(f"❌ 写入第 {idx} 条数据失败: {e}")

# 验证数据是否已成功写入
collection_info = qdrant_client.get_collection(collection_name)
print(f"\n🎉 集合信息: {collection_info}")
print(f"🎉 成功写入的数据总数: {collection_info.points_count}")

# 列出所有点
all_points = qdrant_client.scroll(
    collection_name=collection_name,
    limit=10,  # 最多显示10条
    with_payload=True,
    with_vectors=False  # 不返回向量以减少输出
)[0]

print("\n集合中的所有点:")
for point in all_points:
    print(f"ID: {point.id}, Payload: {point.payload}")
