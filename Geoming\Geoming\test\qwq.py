from openai import OpenAI
import os

# Initialize OpenAI client
client = OpenAI(
    # If the environment variable is not configured, replace with your API Key: api_key="sk-xxx"
    # How to get an API Key：https://help.aliyun.com/zh/model-studio/developer-reference/get-api-key
    api_key="sk-44a94d67b9a9437885fb9424fcf07a50",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
)

reasoning_content = ""
content = ""

is_answering = False

completion = client.chat.completions.create(
    model="qwq-32b",
    messages=[
        {"role": "user", "content": "say hello world"}
    ],
    stream=True,
    # Uncomment the following line to return token usage in the last chunk
    # stream_options={
    #     "include_usage": True
    # }
)

content = ""

# 收集所有内容片段
for chunk in completion:
    if chunk.choices:
        delta = chunk.choices[0].delta
        if delta.content:
            content += delta.content

# 最终一次性输出完整结果
print(content)

# print("\n" + "=" * 20 + "reasoning content" + "=" * 20 + "\n")

# for chunk in completion:
#     # If chunk.choices is empty, print usage
#     if not chunk.choices:
#         print("\nUsage:")
#         print(chunk.usage)
#     else:
#         delta = chunk.choices[0].delta
#         # Print reasoning content
#         if hasattr(delta, 'reasoning_content') and delta.reasoning_content is not None:
#             print(delta.reasoning_content, end='', flush=True)
#             reasoning_content += delta.reasoning_content
#         else:
#             if delta.content != "" and is_answering is False:
#                 print("\n" + "=" * 20 + "content" + "=" * 20 + "\n")
#                 is_answering = True
#             # Print content
#             print(delta.content, end='', flush=True)
#             content += delta.content
