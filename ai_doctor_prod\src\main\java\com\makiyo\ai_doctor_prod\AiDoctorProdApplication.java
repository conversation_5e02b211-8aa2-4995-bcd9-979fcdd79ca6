package com.makiyo.ai_doctor_prod;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@MapperScan("com.makiyo.ai_doctor_prod.mapper")
@ComponentScan(basePackages = {
    "com.makiyo.ai_doctor_prod.controller", 
    "com.makiyo.ai_doctor_prod.service", 
    "com.makiyo.ai_doctor_prod.config",
    "com.makiyo.ai_doctor_prod.utils"
})
@EnableScheduling
public class AiDoctorProdApplication {

    public static void main(String[] args) {
        SpringApplication.run(AiDoctorProdApplication.class, args);
    }

}
