package com.makiyo.ai_doctor_prod.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.index.Indexed;
import java.time.LocalDateTime;

/**
 * 指南内容 - MongoDB文档
 * 用于存储诊断相关的指南内容，与案例(CaseItem)相关联
 */
@Data
@Document(collection = "guidelines_content")
public class GuidelinesContent {

    @Id
    private String id;

    /**
     * 案例ID - 关联到CaseItem的id
     */
    @Indexed
    private String caseId;

    /**
     * 指南内容 - 存储为Object类型以支持各种内容格式
     */
    private Object content;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    public GuidelinesContent() {
        this.createdAt = LocalDateTime.now();
    }

    public GuidelinesContent(String caseId, Object content) {
        this.caseId = caseId;
        this.content = content;
        this.createdAt = LocalDateTime.now();
    }
}