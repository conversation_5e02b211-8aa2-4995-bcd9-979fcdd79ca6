import requests
import json

# 设置请求URL
url = 'http://localhost:5555/ai_doctor_v2_inquiry'

# 构造正确的JSON请求体
payload = {
    "interaction_history": {
        "diagnosis": "",
        "cot_entries": [
            {
                "feedback": "",
                "strategy": "询问患者的基本信息（性别、年龄）和主诉症状，了解发病时间、起病情况、症状特点（性质、程度、持续时间、频率）、伴随症状、病情演变以及之前的诊疗经过。",
                "reasoning": "主诉是问诊的切入点，通过了解患者的基本信息和主要症状，可以快速建立初步印象。进一步询问发病时间、起病情况、症状特点、伴随症状、病情演变和诊疗经过，有助于全面了解现病史，为后续诊断提供关键信息。这种系统性的问诊策略能够帮助我们准确把握患者的病情，避免遗漏重要信息。",
                "observation": "",
                "dialogue_history": [
                    {"role": "doctor", "content": "您好，我是本次的医生，请问孩子的年龄，性别，主要的症状是什么？"},
                    {"role": "patient", "content": "男，三岁，有点发烧"},
                    {"role": "doctor", "content": "孩子是从什么时候开始发烧的？"},
                    {"role": "patient", "content": "三天前开始发烧"},
                    {"role": "doctor", "content": "孩子发烧前有没有什么特别的事情，比如着凉、接触生病的人或者剧烈活动？"}
                ]
            }
        ],
        "test_recommendation": [],
        "preliminary_diagnosis": None,
        "treatment_recommendation": [],
        "doctor_supplementary_info": []
    }
}

# 打印请求内容 (仅用于调试)
print("发送请求到:", url)
print("请求体 (Python 对象):")
print(json.dumps(payload, ensure_ascii=False, indent=2))

# 设置请求头
headers = {
    'Content-Type': 'application/json; charset=utf-8'
}

# 发送POST请求
try:
    # 关键：使用json参数而不是data参数，这样requests会自动处理JSON序列化和编码
    response = requests.post(url, json=payload, headers=headers)
    
    # 打印响应信息
    print("\n响应状态码:", response.status_code)
    print("响应内容:")
    try:
        # 尝试解析JSON响应
        json_response = response.json()
        print(json.dumps(json_response, ensure_ascii=False, indent=2))
    except:
        # 如果不是JSON，则打印原始文本
        print(response.text)
except Exception as e:
    print("请求发送失败:", e) 