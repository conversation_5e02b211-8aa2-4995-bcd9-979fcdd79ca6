package com.makiyo.ai_doctor_prod.config;

import com.alibaba.druid.support.http.StatViewServlet;
import com.alibaba.druid.support.http.WebStatFilter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Druid数据源监控配置
 * 
 * 配置后访问路径: http://localhost:端口/druid
 * 生产环境请确保添加IP访问限制和强密码
 */
@Configuration
public class DruidMonitorConfig {

    @Value("${spring.datasource.druid.stat-view-servlet.login-username:admin}")
    private String loginUsername;

    @Value("${spring.datasource.druid.stat-view-servlet.login-password:admin}")
    private String loginPassword;

    @Value("${spring.datasource.druid.stat-view-servlet.allow:}")
    private String allow;

    @Value("${spring.datasource.druid.stat-view-servlet.deny:}")
    private String deny;

    /**
     * 配置监控服务器
     * @return ServletRegistrationBean
     */
    @Bean
    @ConditionalOnProperty(name = "spring.datasource.druid.stat-view-servlet.enabled", havingValue = "true", matchIfMissing = false)
    public ServletRegistrationBean<StatViewServlet> statViewServlet() {
        ServletRegistrationBean<StatViewServlet> servletRegistrationBean = new ServletRegistrationBean<>(
                new StatViewServlet(), "/druid/*");
        
        // 添加监控页面的用户名和密码
        servletRegistrationBean.addInitParameter("loginUsername", loginUsername);
        servletRegistrationBean.addInitParameter("loginPassword", loginPassword);
        
        // IP白名单
        servletRegistrationBean.addInitParameter("allow", allow);
        // IP黑名单(优先于白名单)
        servletRegistrationBean.addInitParameter("deny", deny);
        // 是否能够重置数据
        servletRegistrationBean.addInitParameter("resetEnable", "false");
        return servletRegistrationBean;
    }

    /**
     * 配置监控拦截器
     * @return FilterRegistrationBean
     */
    @Bean
    @ConditionalOnProperty(name = "spring.datasource.druid.web-stat-filter.enabled", havingValue = "true", matchIfMissing = false)
    public FilterRegistrationBean<WebStatFilter> webStatFilter() {
        FilterRegistrationBean<WebStatFilter> filterRegistrationBean = new FilterRegistrationBean<>(
                new WebStatFilter());
        
        // 添加过滤规则
        filterRegistrationBean.addUrlPatterns("/*");
        
        // 添加不需要忽略的格式信息
        filterRegistrationBean.addInitParameter("exclusions", "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*");
        return filterRegistrationBean;
    }
} 