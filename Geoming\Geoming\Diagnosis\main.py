import logging
from Doctor import Doctor
from prompts import _sys_prompt_doctor
from DiagnosisAgent import Diagnosis<PERSON><PERSON>, process_json, format_medical_advice
from Consultation import convert_history, doctor_turn, process_xlsx_to_json, AI_check_solution
from logger import logger


# # 配置日志记录
# logging.basicConfig(
#     level=logging.INFO,  # 设置日志级别
#     format="%(asctime)s - %(levelname)s - %(message)s",  # 日志格式
#     handlers=[
#         # logging.StreamHandler(),  # 输出到控制台
#         logging.FileHandler("main.log", mode="a", encoding="utf-8")  # 输出到文件
#     ]
# )

# logger = logging.getLogger(__name__)


def remove_tagged_lines(text: str) -> str:
    """Remove lines that start with '【tag】' from the input string."""
    return "\n".join(line for line in text.splitlines() if not line.startswith("【tag】"))




# 问诊接口

def consultation_doctor_reoponse(history_doc):
    sysprompt_doctor = _sys_prompt_doctor
    history_pat = convert_history(history_doc)
    initial_message = "请您开始问诊"
    round_num = len(history_doc) // 2 + 1
    model_v3 = "deepseek-v3-241226"
    logger.info(f"history_pat: {history_pat}")
    logger.info(f"round_num: {round_num}")

    response_doc, solution_text = doctor_turn(
        sysprompt_doctor, history_doc, history_pat, initial_message, model_v3, round_num
    )

    logger.info(f"response_doc: {response_doc}")
    logger.info(f"solution_text: {solution_text}")

    return response_doc, solution_text





# def main():
#     xlsx_file = r"..\data\检查和药品0224_测试.xlsx"

#     # 输入文件路径，读取数据， 输出json格式的问诊历史
#     historys_json = process_xlsx_to_json(xlsx_file)

#     # 输入json格式的问诊历史，输出诊断结果和治疗方案，保存到XLSX文件,
#     # 默认为"../data/json_r1_full_new_test_json.xlsx"
#     process_json(historys_json)

    
def doctor_response():
    history_doc = [
        "<text>\n 您好，我是急诊科值班医生。请说明您与患儿的关系以及孩子的基本信息？\n</text>",
        "<observation>\n 医生您好！我是孩子的父亲，我儿子今年 2 岁 8 个月。\n</observation>",
        "<text>\n 请描述孩子的主要症状及持续时间？\n</text>",
        "<observation>\n 孩子持续高热 6 天，体温最高 40.2℃，今天突然出现呼吸急促，嘴唇发绀。\n</observation>",
        "<text>\n 是否伴有其他症状？咳嗽性质如何？有无呕吐、抽搐等情况？\n</text>",
        "<observation>\n 有犬吠样咳嗽，声音嘶哑，无法平卧入睡。今天呕吐 3 次，出现两次意识模糊。\n</observation>",
        "<text>\n 请说明院外诊疗情况，包括检查及用药效果？\n</text>",
        "<observation>\n 在诊所静脉注射头孢三天无效，胸片显示双肺弥漫性浸润影，C 反应蛋白＞160mg/L。\n</observation>",
        "<text>\n 近期有无接触传染病患者？疫苗接种是否完成？\n</text>",
        "<observation>\n 邻居家孩子上周确诊流感，疫苗接种只完成基础免疫。\n</observation>",
        "<text>\n 孩子既往是否有基础疾病？家族过敏史？\n</text>",
        "<observation>\n 有先天性室间隔缺损病史，对青霉素过敏。奶奶有支气管哮喘史。\n</observation>",
        "<text>\n 目前孩子精神状态和进食情况如何？\n</text>",
        "<observation>\n 已经 6 小时无法进食进水，嗜睡但易惊醒，尿量明显减少。\n</observation>",
        "<text>\n 请补充其他重要体征变化？\n</text>",
        "<observation>\n 发现四肢出现瘀斑，指端发凉，血氧饱和度监测只有 88%（未吸氧状态）。\n</observation>",
        "<text>\n 是否进行过血气分析？\n</text>",
        "<observation>\n 外院动脉血气显示 PH7.28，乳酸 5.6mmol/L，氧分压 55mmHg。\n</observation>",
        "<text>\n 请确认最后进食时间及当前体位？\n</text>",
        "<observation>\n 最后一次进食是 14 小时前，现在必须保持端坐呼吸，平卧即出现窒息样咳嗽。\n</observation>"
        ]


    # 传入问诊历史，返回AI医生的回答和解决方案
    response_doc, solution_text = consultation_doctor_reoponse(history_doc)

    return response_doc, solution_text

def diagnose_treatment(solution_text):
    #如果solution_text不为None， 从诊断方案加载数据并创建 Doctor 实例列表
    if solution_text:
        print(solution_text)
        #其实该列表只有一个元素
        doctors = Doctor.load_from_solution(solution_text)
        print(doctors)

        AI_ask_AI_check = AI_check_solution(solution_text)

        print(AI_ask_AI_check)

        # 开始诊断
        diagnosis_agent = DiagnosisAgent(doctors)
        # 诊断所有患者
        results = diagnosis_agent.diagnose_all()
        # 打印诊断结果
        for result in results:
            # print(f"Patient: {result['patient']}")
            print(f"Diagnosis: {result['diagnosis']}")
            print(f"Treatment Plan: {result['treatment_plan']}")
            print(solution_text)
            print("-" * 40)

        return results[0]['diagnosis'], results[0]['treatment_plan']
    return None, None


def api_test():
    history_doc_hospitalization = [
        "<text>\n 您好，我是急诊科值班医生。请说明您与患儿的关系以及孩子的基本信息？\n</text>",
        "<observation>\n 医生您好！我是孩子的父亲，我儿子今年 2 岁 8 个月。\n</observation>",
        "<text>\n 请描述孩子的主要症状及持续时间？\n</text>",
        "<observation>\n 孩子持续高热 6 天，体温最高 40.2℃，今天突然出现呼吸急促，嘴唇发绀。\n</observation>",
        "<text>\n 是否伴有其他症状？咳嗽性质如何？有无呕吐、抽搐等情况？\n</text>",
        "<observation>\n 有犬吠样咳嗽，声音嘶哑，无法平卧入睡。今天呕吐 3 次，出现两次意识模糊。\n</observation>",
        "<text>\n 请说明院外诊疗情况，包括检查及用药效果？\n</text>",
        "<observation>\n 在诊所静脉注射头孢三天无效，胸片显示双肺弥漫性浸润影，C 反应蛋白＞160mg/L。\n</observation>",
        "<text>\n 近期有无接触传染病患者？疫苗接种是否完成？\n</text>",
        "<observation>\n 邻居家孩子上周确诊流感，疫苗接种只完成基础免疫。\n</observation>",
        "<text>\n 孩子既往是否有基础疾病？家族过敏史？\n</text>",
        "<observation>\n 有先天性室间隔缺损病史，对青霉素过敏。奶奶有支气管哮喘史。\n</observation>",
        "<text>\n 目前孩子精神状态和进食情况如何？\n</text>",
        "<observation>\n 已经 6 小时无法进食进水，嗜睡但易惊醒，尿量明显减少。\n</observation>",
        "<text>\n 请补充其他重要体征变化？\n</text>",
        "<observation>\n 发现四肢出现瘀斑，指端发凉，血氧饱和度监测只有 88%（未吸氧状态）。\n</observation>",
        "<text>\n 是否进行过血气分析？\n</text>",
        "<observation>\n 外院动脉血气显示 PH7.28，乳酸 5.6mmol/L，氧分压 55mmHg。\n</observation>",
        "<text>\n 请确认最后进食时间及当前体位？\n</text>",
        "<observation>\n 最后一次进食是 14 小时前，现在必须保持端坐呼吸，平卧即出现窒息样咳嗽。\n</observation>"
        ]
    
    history_doc = [
            "<text>\n您好，我是本次的接诊医生。请问您是孩子什么人，孩子的性别和年龄？\n</text>",
            "<observation>\n您好，医生。我是孩子的母亲，我的女儿今年4岁1个月21天。\n</observation>",
            "<text>\n请问孩子主要哪里不舒服，有多长的时间了\n</text>",
            "<observation>\n孩子主要是发烧，已经两天了。\n</observation>",
            "<text>\n请问孩子发烧前有没有什么诱因，比如受凉、接触感冒的人？\n</text>",
            "<observation>\n没有明显的诱因。\n</observation>",
            "<text>\n孩子体温最高达到多少度？\n</text>",
            "<observation>\n体温最高烧到39度。\n</observation>",
            "<text>\n孩子一天烧几次，用了退烧药能降到正常吗？\n</text>",
            "<observation>\n孩子一天大概烧两三次，用了退烧药之后可以降到正常。\n</observation>",
            "<text>\n孩子有没有咳嗽、鼻塞、流涕，有没有呕吐、腹泻，有没有抽搐？\n</text>",
            "<observation>\n有咳嗽、鼻塞、流涕，没有呕吐、腹泻、抽搐。\n</observation>",
            "<text>\n孩子之前有在哪儿看过吗？做过哪些检查，用了哪些药？\n</text>",
            "<observation>\n没有，这是她第一次出现这样的症状。\n</observation>",
            "<text>\n孩子从生病以来，精神、食欲、睡眠怎么样？\n</text>",
            "<observation>\n精神还行，食欲不太好，睡眠还可以，大小便都正常。\n</observation>",
            "<text>\n孩子平时身体怎么样？有没有什么慢性病或者生过什么大病？\n</text>",
            "<observation>\n没有，孩子平时身体挺好的，没有什么慢性病或者生过什么大病。\n</observation>",
            "<text>\n孩子近期有没有接触过传染病人，比如流感、结核等？\n</text>",
            "<observation>\n没有，我们都没有接触过这样的病人。\n</observation>"
            
        ]

    # 传入问诊历史，返回AI医生的回答和解决方案
    response_doc, solution_text = consultation_doctor_reoponse(history_doc)

    #如果solution_text不为None， 从诊断方案加载数据并创建 Doctor 实例列表
    if solution_text:
        print(solution_text)
        #其实该列表只有一个元素
        doctors = Doctor.load_from_solution(solution_text)
        print(doctors)

        AI_ask_AI_check = AI_check_solution(solution_text)

        print(AI_ask_AI_check)

        # 开始诊断
        diagnosis_agent = DiagnosisAgent(doctors)
        # 诊断所有患者
        results = diagnosis_agent.diagnose_all()
        # 打印诊断结果
        for result in results:
            # print(f"Patient: {result['patient']}")
            print(f"Diagnosis: {result['diagnosis']}")
            print(f"Treatment Plan: {result['treatment_plan']}")
            print(solution_text)
            print("-" * 40)

        return results[0]['diagnosis'], results[0]['treatment_plan'], AI_ask_AI_check, remove_tagged_lines(solution_text)
    return None, None, None, None


def main_one_shot():
    diagnosis, treatment_plan, AI_ask_AI_check, patient_info = api_test()
    
    print("-----------------【问诊结果】-------------------")
    
    print(patient_info)

    print("-----------------【AI检查建议】-------------------")

    print(AI_ask_AI_check)

    print("-----------------【诊断建议】-------------------")

    print(diagnosis)
    
    print("-----------------【治疗方案】-------------------")
    
    print(treatment_plan)

def main_two_shot():
    logger.info("Starting application")
    # main()
    response, solution_text = doctor_response()

    print("-----------------【医生回复】-------------------")

    print(response)

    if not solution_text:
        print("仍需继续问诊")
        exit()
    
    print("-----------------【问诊结果】-------------------")
    
    print(remove_tagged_lines(solution_text))

    print("-----------------【AI检查建议】-------------------")

    AI_ask_AI_check = AI_check_solution(solution_text)

    print(AI_ask_AI_check)

    print("-----------------【诊断建议】-------------------")

    diagnosis, treatment_plan = diagnose_treatment(solution_text)

    print(diagnosis)
    
    print("-----------------【治疗方案】-------------------")
    
    print(treatment_plan)

if __name__ == '__main__':
    main_one_shot()
    


    