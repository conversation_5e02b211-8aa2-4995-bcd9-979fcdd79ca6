import asyncio
import json

import requests

from openai import OpenAI
import httpx

from src.logger import get_logger
from src.llm_client import LLMClient
from src.config import (
    QWEN_API_KEY, QWEN_API_BASE
)

logger = get_logger("planner")


api_url = "http://47.94.171.56:8090/guide_multifield"



async def get_guide(main_disease, diagnostic_symptoms=None, top_k_qdrant=12, top_k_llm=1):
    """
    根据疾病和症状从医学资料库中检索相关指南，并通过LLM筛选最相关的资料。
    
    参数:
    main_disease (str): 患者的主要疾病名称
    diagnostic_symptoms (str): 患者的诊断症状描述
    top_k_qdrant (int): 从向量数据库中检索的资料数量
    top_k_llm (int): 通过LLM筛选后返回的资料数量
    
    返回:
    dict: 包含筛选后的医学指南资料的响应对象
    """

    # 从qdrant中获取指南
    
    data = {
        "main_disease": main_disease,
        "top_k": top_k_qdrant
    }
    


    try:
        response = requests.post(api_url, json=data)
        response.raise_for_status()  # 检查HTTP状态码
        response_qdrant = response.json()
    except requests.exceptions.JSONDecodeError:
        logger.error(f"API返回非JSON响应: {response.text}")
        return None
    except requests.exceptions.RequestException as e:
        logger.error(f"API请求失败: {e}")
        return None

    results = response_qdrant.get("results")

    if not results:
        logger.info("从qdrant中未召回任何指南")
        return None

    guide_source_file = []
    for item in results:
        guide_source_file.append(item.get("payload").get("source_file"))


    guide_source_file_str = ""

    for index, item in enumerate(guide_source_file):
        guide_source_file_str += f"第{index+1}篇资料：{item}\n"

    logger.info(f"召回的指南{guide_source_file_str}")


    client = OpenAI(
        api_key=QWEN_API_KEY,
        base_url=QWEN_API_BASE,
        timeout=httpx.Timeout(timeout=180)
    )

    # 使用llm判断应该使用哪个source_file
    prompt = f"""
    你是一个经验丰富的医生，现在你正准备给一个患者看病，你现在有{len(guide_source_file)}篇关于这个疾病的资料，请根据这些资料和患者的症状，选择其中的{top_k_llm}篇资料,并且最相关的资料放在最前面。如果没有任何一篇资料是相关的，请直接输出"无"。

    以下是患者的情况：
    患者的疾病是：{main_disease}
    患者的症状是：{diagnostic_symptoms}

    以下是资料：
    {guide_source_file_str} 

    请根据这些资料和患者的症状，选择其中的{top_k_llm}篇资料,并且最相关的资料放在最前面。如果没有任何一篇资料是相关的，请直接输出"无"。
    
    最后的输出为一行，每篇资料编号之间用逗号隔开。

    注意：
    1. 请根据患者的症状，选择其中的{top_k_llm}篇资料。
    2. 输出必须是一行，每篇资料编号之间用逗号隔开。
    3. 如果没有任何一篇资料是相关的，请直接输出"无"。
    """

    system_message = f"""
    你是一个经验丰富的医生，现在你正准备给一个患者制定治疗方案，你现在有{len(guide_source_file)}篇关于这个疾病的资料，请根据这些资料和患者的症状，选择其中的{top_k_llm}篇资料,并且最相关的资料放在最前面。

    请根据这些资料和患者的症状，选择其中的{top_k_llm}篇资料,并且最相关的资料放在最前面。
    
    最后的输出为一行，每篇资料编号之间用逗号隔开。

    注意：
    1. 请根据患者的症状，选择其中的{top_k_llm}篇资料。
    2. 输出必须是一行，每篇资料编号之间用逗号隔开。
    3. 如果没有任何一篇资料是相关的，请直接输出"无"。   


    如果选择1篇资料，则输出最符合的资料编号，输出示例：
    1

    如果选择2篇资料，则输出最符合的2篇资料编号，并且最符合的资料编号放在最前面，输出示例：
    5,1

    如果选择3篇资料，则输出最符合的3篇资料编号，并且最符合的资料编号放在最前面，输出示例：
    5,1,2

    如果没有任何一篇资料是相关的，请直接输出"无"。
    """

    

    # response = client.chat.completions.create(
    #     model="deepseek-r1",
    #     messages=[{"role": "system", "content": system_message}, {"role": "user", "content": prompt}],
    #     max_tokens=1000,
    #     temperature=0.7
    # )

    response = await LLMClient.generate_response(provider="jiuzhang", prompt=prompt, system_message=system_message, temperature=0.7, model="deepseek-reasoner", show_reasoning=False)

    logger.info(response)

    response_content = response

    reasoning_content = response

    logger.info(f"llm选择的资料是：{response_content}")
    logger.info(f"llm的推理是：{reasoning_content}")


    # response的格式为：推理过程:\n{reasoning_content}\n\n最终回答:\n{response_content}

    if "无" in response_content or not response_content.strip():
        logger.info("LLM判断没有相关的指南")
        return None

    # 将response_content转换为列表
    response_content_list = response_content.split(",")

    # 将response_content_list转换为整型，去除空格和空字符串
    response_content_list = [int(item.strip()) for item in response_content_list if item.strip()]


    selected_guide_source_file = []
    for item in response_content_list:
        selected_guide_source_file.append(guide_source_file[item-1])

    logger.info(f"llm选择的资料是：{selected_guide_source_file}")
    logger.info(f"llm的推理是：{reasoning_content}")

    # response_qdrant_processed 等于response_qdrant，但results为空
    response_qdrant_processed = response_qdrant.copy()
    response_qdrant_processed["results"] = []

    # 将selected_guide_source_file的payload写入response_qdrant_processed
    for item in selected_guide_source_file:
        for result in response_qdrant.get("results"):
            if result.get("payload").get("source_file") == item:
                response_qdrant_processed["results"].append(result)

    
    
    return response_qdrant_processed



async def main():
    selected_guide_source_file_payload = await get_guide("咳嗽变异性哮喘", "9岁男性，间断干咳2年余，运动及情绪激动时加重；双肺呼吸音清，未闻及干湿啰音，鼻腔黏膜轻度充血；肺功能检查（含支气管激发试验）、呼出气一氧化氮检测、过敏原筛查", 10, 1)

    # 将selected_guide_source_file_payload写入json文件
    with open("selected_guide_source_file_payload.json", "w", encoding="utf-8") as f:
        json.dump(selected_guide_source_file_payload, f, ensure_ascii=False, indent=4)


    
if __name__ == "__main__":
    asyncio.run(main())









