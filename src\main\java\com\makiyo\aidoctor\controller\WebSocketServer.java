package com.makiyo.aidoctor.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;


@Component
@ServerEndpoint("/api/ws/{sid}")
@Slf4j
public class WebSocketServer {

    private String sid;

    private static final ConcurrentHashMap<String, Session> SESSION_MAP = new ConcurrentHashMap<>();

    /**
     * 连接成功
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("sid") String sid) {
        this.sid = sid;
        SESSION_MAP.put(sid, session);
        log.info("有新连接：sid：{}，sessionId：{}，当前连接数：{}", sid, session.getId(), SESSION_MAP.size());
    }

    /**
     * 连接关闭
     */
    @OnClose
    public void onClose(Session session) {
        SESSION_MAP.remove(this.sid);
        log.info("连接关闭，sid：{}，session id：{}！当前连接数：{}", this.sid, session.getId(), SESSION_MAP.size());
    }

    /**
     * 收到消息
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        log.info("收到消息：{}，内容：{}", sid, message);
        if("ping".equals(message)){
            try {
                session.getBasicRemote().sendText("pong");
            } catch (IOException e) {
                log.error("onMessage 推送消息失败：{}，内容：{}", sid, message);
            }
        }else{
            // 排除自己
            // sendMeasureDataInfoExcludeSelf(message, sid);
            // 发给所有客户端包括自己
            sendMeasureDataInfo(message);
        }
    }


    /**
     * 连接错误
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("{} 发生错误", session.getId(), error);
    }

    /**
     * 群发消息
     */
    public void sendMeasureDataInfo(String message) {
        for (String sid : SESSION_MAP.keySet()) {
            Session session = SESSION_MAP.get(sid);
            try {
                session.getBasicRemote().sendText(message);
            } catch (IOException e) {
                log.error("推送消息失败：{}，内容：{}", sid, message);
            }
            log.info("推送消息：{}，内容：{}", sid, message);
        }
    }

    /**
     * 群发消息，排除消息发起者
     * @param message
     * @param sidSelf
     */
    private void sendMeasureDataInfoExcludeSelf(String message, String sidSelf){
        for(String sid : SESSION_MAP.keySet()){
            if(sidSelf.equals(sid)){
                continue;
            }
            Session session = SESSION_MAP.get(sid);
            try {
                session.getBasicRemote().sendText(message);
            } catch (IOException e) {
                log.error("sendMeasureDataInfoExcludeSelf 推送消息失败：{}，内容：{}", sid, message);
            }
            log.info("sendMeasureDataInfoExcludeSelf 推送消息：{}，内容：{}", sid, message);
        }
    }
}

