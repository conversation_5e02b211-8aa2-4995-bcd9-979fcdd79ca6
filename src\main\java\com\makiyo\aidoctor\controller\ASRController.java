package com.makiyo.aidoctor.controller;

import com.makiyo.aidoctor.service.ASRService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 语音识别控制器
 * 提供接口接收前端音频并流式返回识别结果
 */
@Tag(name = "语音识别", description = "语音转文字相关的 API 接口")
@RestController
@RequestMapping("/api/asr")
public class ASRController {
    
    private static final Logger logger = LoggerFactory.getLogger(ASRController.class);

    @Autowired
    private ASRService asrService;
    
    // 存储待处理的音频文件和对应的SSE发射器
    private final Map<String, File> pendingAudioFiles = new ConcurrentHashMap<>();
    private final Map<String, SseEmitter> emitters = new ConcurrentHashMap<>();
    
    // 临时目录
    private final String tempDir = System.getProperty("java.io.tmpdir") + File.separator + "asr_uploads";

    /**
     * 接收音频文件
     * 
     * @param audioFile 前端上传的音频文件
     * @return 音频文件ID，用于后续获取识别结果
     */
    @Operation(summary = "POST方式语音识别", description = "通过POST请求进行语音识别")
    @PostMapping(value = "/recognize", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<String> uploadAudio(@RequestParam("audio") MultipartFile audioFile) {
        try {
            // 确保临时目录存在
            File tempDirFile = new File(tempDir);
            if (!tempDirFile.exists()) {
                tempDirFile.mkdirs();
            }
            
            // 生成唯一ID
            String audioId = UUID.randomUUID().toString();
            
            // 保存上传的文件到自定义临时目录
            String originalFilename = audioFile.getOriginalFilename();
            String fileExtension = originalFilename != null ? 
                    originalFilename.substring(originalFilename.lastIndexOf(".")) : ".wav";
            
            File savedFile = new File(tempDirFile, audioId + fileExtension);
            Files.copy(audioFile.getInputStream(), savedFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            
            logger.info("音频文件已保存: {}", savedFile.getAbsolutePath());
            
            // 存储音频文件
            pendingAudioFiles.put(audioId, savedFile);
            
            return ResponseEntity.ok(audioId);
        } catch (Exception e) {
            logger.error("处理音频文件失败", e);
            return ResponseEntity.badRequest().body("处理音频文件失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取语音识别结果的SSE流
     * 
     * @param audioId 音频文件ID
     * @return SSE事件发射器，用于流式返回识别结果
     */
    @Operation(summary = "GET方式语音识别", description = "通过GET请求进行语音识别")
    @GetMapping(value = "/recognize", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter getRecognitionResults(@RequestParam(value = "audioId", required = false) String audioId) {
        // 创建SSE发射器，超时时间设置为10分钟
        SseEmitter emitter = new SseEmitter(600000L);
        
        try {
            if (audioId != null && pendingAudioFiles.containsKey(audioId)) {
                // 获取对应的音频文件
                File audioFile = pendingAudioFiles.get(audioId);
                
                if (!audioFile.exists()) {
                    logger.error("音频文件不存在: {}", audioFile.getAbsolutePath());
                    emitter.send(SseEmitter.event()
                            .name("error")
                            .data("音频文件不存在或已被删除"));
                    emitter.complete();
                    return emitter;
                }
                
                // 存储发射器
                emitters.put(audioId, emitter);
                
                // 异步处理音频识别
                asrService.processAudioFile(audioFile, emitter);
                
                // 处理完成后移除
                emitter.onCompletion(() -> {
                    cleanup(audioId);
                });
                
                emitter.onTimeout(() -> {
                    logger.warn("SSE连接超时，audioId: {}", audioId);
                    cleanup(audioId);
                });
                
                emitter.onError(e -> {
                    logger.error("SSE连接错误，audioId: {}, error: {}", audioId, e.getMessage());
                    cleanup(audioId);
                });
                
            } else {
                // 如果没有指定audioId或找不到对应的音频文件，返回错误
                logger.error("无效的音频ID: {}", audioId);
                emitter.send(SseEmitter.event()
                        .name("error")
                        .data("无效的音频ID或音频文件已过期"));
                emitter.complete();
            }
        } catch (Exception e) {
            logger.error("处理识别请求失败", e);
            try {
                emitter.send(SseEmitter.event()
                        .name("error")
                        .data("处理音频文件失败: " + e.getMessage()));
                emitter.complete();
            } catch (IOException ex) {
                logger.error("发送错误事件失败", ex);
            }
        }
        
        return emitter;
    }
    
    /**
     * 清理资源
     */
    private void cleanup(String audioId) {
        try {
            // 移除发射器
            emitters.remove(audioId);
            
            // 删除临时文件
            File audioFile = pendingAudioFiles.remove(audioId);
            if (audioFile != null && audioFile.exists()) {
                audioFile.delete();
                logger.info("已删除临时文件: {}", audioFile.getAbsolutePath());
            }
        } catch (Exception e) {
            logger.warn("清理资源失败", e);
        }
    }
    
    /**
     * 健康检查接口
     */
    @Operation(summary = "健康检查", description = "检查语音识别服务是否正常运行")
    @GetMapping("/health")
    public ResponseEntity<String> healthCheck() {
        return ResponseEntity.ok("ASR服务正常运行");
    }
    
    /**
     * 添加网站图标，避免404错误
     */
    @GetMapping("/favicon.ico")
    public ResponseEntity<Void> favicon() {
        return ResponseEntity.noContent().build();
    }
} 