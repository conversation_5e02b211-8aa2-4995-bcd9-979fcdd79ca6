import requests
import json
import time
import threading
import concurrent.futures
from datetime import datetime

# 设置请求URL
url = 'http://localhost:5555/ai_doctor_v2_inquiry'

# 构造正确的JSON请求体
payload = {
    "interaction_history": {
        "diagnosis": "",
        "cot_entries": [
            {
                "feedback": "",
                "strategy": "询问患者的基本信息（性别、年龄）和主诉症状，了解发病时间、起病情况、症状特点（性质、程度、持续时间、频率）、伴随症状、病情演变以及之前的诊疗经过。",
                "reasoning": "主诉是问诊的切入点，通过了解患者的基本信息和主要症状，可以快速建立初步印象。进一步询问发病时间、起病情况、症状特点、伴随症状、病情演变和诊疗经过，有助于全面了解现病史，为后续诊断提供关键信息。这种系统性的问诊策略能够帮助我们准确把握患者的病情，避免遗漏重要信息。",
                "observation": "",
                "dialogue_history": [
                    {"role": "doctor", "content": "您好，我是本次的医生，请问孩子的年龄，性别，主要的症状是什么？"},
                    {"role": "patient", "content": "男，三岁，有点发烧"},
                    {"role": "doctor", "content": "孩子是从什么时候开始发烧的？"},
                    {"role": "patient", "content": "三天前开始发烧"},
                    {"role": "doctor", "content": "孩子发烧前有没有什么特别的事情，比如着凉、接触生病的人或者剧烈活动？"}
                ]
            }
        ],
        "test_recommendation": [],
        "preliminary_diagnosis": None,
        "treatment_recommendation": [],
        "doctor_supplementary_info": []
    }
}

# 设置请求头
headers = {
    'Content-Type': 'application/json; charset=utf-8'
}

# 定义测试参数
CONCURRENT_REQUESTS = 10  # 并发请求的数量
TOTAL_BATCHES = 3         # 总批次数
DELAY_BETWEEN_BATCHES = 2 # 每批次间隔(秒)

# 创建线程锁，用于安全地打印和更新统计数据
print_lock = threading.Lock()
stats_lock = threading.Lock()

# 统计数据
stats = {
    'success': 0,
    'failure': 0,
    'total_time': 0,
    'max_time': 0,
    'min_time': float('inf'),
    'responses': []
}

def send_request(request_id):
    """发送一个HTTP请求并记录结果"""
    start_time = time.time()
    try:
        # 向JSON中添加唯一标识，以区分不同请求
        unique_payload = json.loads(json.dumps(payload))
        unique_payload['request_id'] = request_id
        
        # 发送请求
        response = requests.post(
            url, 
            json=unique_payload,
            headers=headers, 
            timeout=120  # 设置10秒超时
        )
        
        # 计算请求耗时
        end_time = time.time()
        duration = end_time - start_time
        
        # 安全地更新统计信息
        with stats_lock:
            if response.status_code == 200:
                stats['success'] += 1
            else:
                stats['failure'] += 1
                
            stats['total_time'] += duration
            stats['max_time'] = max(stats['max_time'], duration)
            stats['min_time'] = min(stats['min_time'], duration)
            stats['responses'].append({
                'id': request_id,
                'status_code': response.status_code,
                'time': duration,
                'timestamp': datetime.now().strftime('%H:%M:%S.%f')[:-3],
                'content_length': len(response.content)
            })
        
        # 打印单个请求的结果
        with print_lock:
            status = "✓" if response.status_code == 200 else "✗"
            print(f"[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] 请求 {request_id:3d}: {status} 状态={response.status_code} 耗时={duration:.2f}秒")
    
    except Exception as e:
        # 处理请求失败的情况
        end_time = time.time()
        duration = end_time - start_time
        
        with stats_lock:
            stats['failure'] += 1
            stats['total_time'] += duration
            stats['responses'].append({
                'id': request_id,
                'status': 'error',
                'error': str(e),
                'time': duration,
                'timestamp': datetime.now().strftime('%H:%M:%S.%f')[:-3]
            })
            
        with print_lock:
            print(f"[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] 请求 {request_id:3d}: ✗ 错误={str(e)[:50]} 耗时={duration:.2f}秒")

# 主函数 - 使用线程池执行并发请求
def run_concurrent_test():
    request_counter = 0
    
    print(f"开始并发测试: {CONCURRENT_REQUESTS} 个并发请求 x {TOTAL_BATCHES} 批 = {CONCURRENT_REQUESTS * TOTAL_BATCHES} 总请求")
    print(f"批次之间间隔 {DELAY_BETWEEN_BATCHES} 秒")
    print("-" * 70)
    
    for batch in range(TOTAL_BATCHES):
        batch_start = time.time()
        print(f"\n==== 批次 {batch+1}/{TOTAL_BATCHES} 开始 ====")
        
        # 创建线程池，并发执行请求
        with concurrent.futures.ThreadPoolExecutor(max_workers=CONCURRENT_REQUESTS) as executor:
            # 提交CONCURRENT_REQUESTS个任务到线程池
            futures = []
            for i in range(CONCURRENT_REQUESTS):
                request_counter += 1
                future = executor.submit(send_request, request_counter)
                futures.append(future)
                
            # 等待本批次所有请求完成
            concurrent.futures.wait(futures)
            
        batch_duration = time.time() - batch_start
        print(f"==== 批次 {batch+1}/{TOTAL_BATCHES} 完成 (耗时: {batch_duration:.2f}秒) ====")
        
        # 如果不是最后一个批次，则等待指定时间
        if batch < TOTAL_BATCHES - 1:
            print(f"等待 {DELAY_BETWEEN_BATCHES} 秒后发起下一批请求...")
            time.sleep(DELAY_BETWEEN_BATCHES)
    
    # 打印统计结果
    print("\n" + "=" * 50)
    print("测试完成! 结果统计:")
    print("=" * 50)
    
    total_requests = stats['success'] + stats['failure']
    avg_time = stats['total_time'] / total_requests if total_requests > 0 else 0
    
    print(f"总请求: {total_requests}")
    print(f"成功: {stats['success']} ({stats['success']/total_requests*100:.1f}%)")
    print(f"失败: {stats['failure']} ({stats['failure']/total_requests*100:.1f}%)")
    print(f"平均响应时间: {avg_time:.2f}秒")
    print(f"最短响应时间: {stats['min_time']:.2f}秒")
    print(f"最长响应时间: {stats['max_time']:.2f}秒")
    
    # 输出响应时间分布
    print("\n响应时间分布:")
    ranges = [(0,0.5), (0.5,1), (1,2), (2,5), (5,10), (10,float('inf'))]
    for time_range in ranges:
        count = sum(1 for r in stats['responses'] if 'time' in r and time_range[0] <= r['time'] < time_range[1])
        if time_range[1] == float('inf'):
            print(f"{time_range[0]}秒以上: {count}个请求 ({count/total_requests*100:.1f}%)")
        else:
            print(f"{time_range[0]}-{time_range[1]}秒: {count}个请求 ({count/total_requests*100:.1f}%)")

# 运行测试
if __name__ == "__main__":
    run_concurrent_test() 