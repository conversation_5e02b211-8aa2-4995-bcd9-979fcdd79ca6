# 环境变量配置说明

## 概述

本文档说明如何使用环境变量来替代硬编码的配置值，提高应用程序的安全性和灵活性。

## 文件说明

1. `env-template` - 环境变量模板文件，包含所有需要的环境变量和示例值
2. `application-env.yml` - 使用环境变量的Spring配置文件示例

## 使用方法

### 1. 创建环境变量文件

复制`env-template`文件并重命名为`.env`：

```bash
cp env-template .env
```

然后根据您的环境修改`.env`文件中的值。

### 2. 配置Spring应用程序

有多种方式可以让Spring应用程序加载环境变量：

#### 方式一：使用Spring Boot的环境变量支持

Spring Boot默认支持环境变量注入。只需确保您的`application.yml`文件中使用了`${VARIABLE_NAME}`语法引用环境变量，如`application-env.yml`中示例的那样。

#### 方式二：使用spring-dotenv库

1. 在`pom.xml`中添加依赖：

```xml
<dependency>
    <groupId>me.paulschwarz</groupId>
    <artifactId>spring-dotenv</artifactId>
    <version>2.5.4</version>
</dependency>
```

2. 这个库会自动加载项目根目录下的`.env`文件中的变量。

#### 方式三：手动加载

您也可以在应用启动时手动加载`.env`文件：

```java
import java.io.FileInputStream;
import java.util.Properties;

public class EnvLoader {
    public static void loadEnv() {
        try {
            Properties props = new Properties();
            FileInputStream fis = new FileInputStream(".env");
            props.load(fis);
            props.forEach((key, value) -> System.setProperty((String) key, (String) value));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
```

然后在应用主类中调用：

```java
public static void main(String[] args) {
    EnvLoader.loadEnv();
    SpringApplication.run(Application.class, args);
}
```

## 部署到生产环境

在生产环境中，您可以：

1. 直接在服务器上设置环境变量
2. 使用容器化工具（如Docker）时，通过环境变量文件或命令行参数设置环境变量
3. 在云平台上，使用其提供的环境变量配置功能

## 注意事项

1. **不要**将包含敏感信息的`.env`文件提交到版本控制系统
2. 确保在`.gitignore`文件中添加`.env`
3. 保持`env-template`文件的更新，但不要在其中存储实际的敏感信息
4. 定期更新密钥和访问令牌，并确保有安全的方式来分发这些更新 