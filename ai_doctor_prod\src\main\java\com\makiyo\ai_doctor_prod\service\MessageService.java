package com.makiyo.ai_doctor_prod.service;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.makiyo.ai_doctor_prod.config.ExecutorConfig;
import com.makiyo.ai_doctor_prod.entity.CaseItem;
import com.makiyo.ai_doctor_prod.entity.GuidelinesContent;
import com.makiyo.ai_doctor_prod.mapper.CaseItemMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.postgresql.util.PGobject;
import java.sql.SQLException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import javax.sql.DataSource;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.util.ArrayList;
import java.time.ZonedDateTime;
import java.util.stream.Collectors;
import com.makiyo.ai_doctor_prod.response.PageInfo;
import com.makiyo.ai_doctor_prod.utils.TreatmentUtils;
import com.makiyo.ai_doctor_prod.utils.InteractionHistoryUtils;
import com.makiyo.ai_doctor_prod.utils.LogUtils;

/**
 * 生产环境消息服务，处理与 AI 模型的交互和数据库操作。
 * 使用 PostgreSQL 的 JSONB 类型存储交互历史。
 */
@Service
public class MessageService {

    private static final Logger log = LoggerFactory.getLogger(MessageService.class);

    @Resource
    private CaseItemMapper caseItemMapper;

    @Resource
    private TtsService ttsService;
    
    @Resource
    private MongoTemplate mongoTemplate;

    private final RestTemplate restTemplate;
    private final ExecutorService inquiryExecutorService;
    @Autowired
    private ObjectMapper objectMapper;

    @Value("${python.prod.inquiry.url:http://localhost:5555/ai_doctor_v2_inquiry}")
    private String inquiryUrl;

    @Value("${python.prod.diagnosis.url:http://localhost:5555/default_diagnosis_endpoint}")
    private String pythonProdDiagnosisUrl;

    @Value("${python.prod.diagnose.url:http://localhost:5555/ai_doctor_v2_diagnosis}")
    private String diagnoseUrl;

    @Value("${python.prod.preliminary.diagnosis.url:http://localhost:5555/ai_doctor_preliminary_diagnosis}")
    private String pythonProdPreliminaryDiagnosisUrl;

    @Value("${python.prod.quick.inquiry.url:http://localhost:5555/ai_doctor_v2_quick_inquiry}")
    private String pythonProdQuickInquiryUrl;

    @Autowired
    public MessageService(RestTemplate restTemplate, 
                          @Qualifier(ExecutorConfig.INQUIRY_TASK_EXECUTOR_BEAN_NAME) ExecutorService inquiryExecutorService) {
        this.restTemplate = restTemplate;
        this.inquiryExecutorService = inquiryExecutorService;
        log.info("MessageService initialized with injected Inquiry ExecutorService.");
    }

    @Autowired
    private DataSource dataSource;

    /**
     * 测试数据库连接。
     * @return 如果连接成功返回 true，否则返回 false。
     */
    public boolean testDatabaseConnection() {
        log.info("Testing database connection via CaseItemMapper...");
        try {
            int result = caseItemMapper.testConnection();
            if (result == 1) {
                log.info("Database connection test successful via CaseItemMapper.");
                return true;
            } else {
                log.error("Database connection test failed via CaseItemMapper: Query did not return 1. Returned: {}", result);
                return false;
            }
        } catch (Exception e) {
            log.error("Database connection test failed via CaseItemMapper with exception: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 处理 AI 问诊请求 (生产环境)。
     *
     * @param id          CaseItem 的唯一标识符。
     * @param userMessage 用户发送的消息。
     * @return 用于流式传输响应的 SseEmitter。
     */
    public SseEmitter aiDoctorInquiry(String id, String userMessage) {
        SseEmitter emitter = new SseEmitter(300000L);

        inquiryExecutorService.execute(() -> {
            String rawPythonResponseStr = null;
            Map<String, Object> interactionHistory = null;
            Map<String, Object> pythonResponseMap = null;

            try {
                log.info("[PROD Inquiry] Starting inquiry processing for case ID: {}", id);

                // --- 1. 获取并验证 CaseItem ---
                CaseItem caseItem = getCaseItemAndValidate(id, emitter);
                if (caseItem == null) return;

                // --- 2. 获取或初始化内部 interaction_history Map ---
                interactionHistory = getOrInitializeInteractionHistory(caseItem, id);

                // --- 3. 添加用户消息到内部历史 ---
                addPatientMessageProd(interactionHistory, userMessage);
                log.debug("[PROD Inquiry] Added patient message to internal history for case ID: {}", id);

                // --- 4. 准备并调用 Python 服务 ---
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                // 添加Connection: close头，避免CLOSE_WAIT问题
                headers.add("Connection", "close");
                
                // 修改：增加session_id字段，值为id
                Map<String, Object> requestBodyMap = Map.of(
                    "interaction_history", interactionHistory,
                    "session_id", id
                );
                HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBodyMap, headers);

                log.info("[PROD Inquiry] Sending request to Python service URL: {} for case ID: {}", inquiryUrl, id);

                ResponseEntity<String> response = null;
                try {
                    response = restTemplate.exchange(
                        inquiryUrl, HttpMethod.POST, requestEntity, String.class
                    );
                    rawPythonResponseStr = response.getBody();
                    
                    // 确保完全消费响应体，避免连接泄漏
                    if (rawPythonResponseStr != null) {
                        // 已经完全读取了响应体
                        log.debug("[PROD Inquiry] Response fully consumed for case ID: {}", id);
                    }
                } catch (RestClientException e) {
                    log.error("[PROD Inquiry] Error calling Python service for case ID {}: {}", id, e.getMessage(), e);
                    sendSseError(emitter, "{\"error\":\"AI service connection failed: " + escapeJson(e.getMessage()) + "\"}", null);
                    return;
                }

                 // --- 5. 处理 Python 响应 ---
                if (response.getStatusCode() == HttpStatus.OK && rawPythonResponseStr != null && !rawPythonResponseStr.trim().isEmpty()) {
                    log.info("[PROD Inquiry] Received OK response from Python for case ID {}", id);
                    log.debug("[PROD Inquiry] Raw Python Response: {}", LogUtils.truncate(rawPythonResponseStr, 200));

                    // --- 5a. 解析Python响应 ---
                    try {
                        pythonResponseMap = objectMapper.readValue(rawPythonResponseStr, new TypeReference<Map<String, Object>>() {});
                        if (pythonResponseMap == null || pythonResponseMap.isEmpty()) {
                            log.error("[PROD Inquiry] Python response map is null or empty after parsing for case ID {}. Raw response: {}", id, rawPythonResponseStr);
                            sendSseError(emitter, "{\"error\":\"AI service returned empty or invalid response\"}", rawPythonResponseStr);
                            return;
                        }
                    } catch (Exception e) {
                        log.error("[PROD Inquiry] Error parsing Python response for case ID {}: {}", id, e.getMessage(), e);
                        sendSseError(emitter, "{\"error\":\"Failed to parse AI service response: " + escapeJson(e.getMessage()) + "\"}", rawPythonResponseStr);
                        return;
                    }

                    // --- 5b. 更新数据库 ---
                    updateDatabaseWithResponse(id, pythonResponseMap);

                    // --- 5c. 确定target值和内容 ---
                    int target = determineTargetFromResponse(pythonResponseMap, id);
                    String contentForSSE = null;
                    
                    // 对target=3的特殊处理
                    if (target == 3) {
                        contentForSSE = "问诊完成";
                        log.info("[PROD Inquiry] Target is 3, setting content to '问诊完成' for case ID: {}", id);
                        
                        // 再次更新数据库，确保包含解析后的诊断信息
                        updateDatabaseWithEnhancedData(id, pythonResponseMap);
                    }

                    // 如果未设置内容，则从对话历史获取
                    if (contentForSSE == null || contentForSSE.isEmpty()) {
                        contentForSSE = extractLastDoctorMessageProd(pythonResponseMap);
                        if (contentForSSE == null) {
                            log.warn("[PROD Inquiry] Failed to extract content from dialogue history for target {} case ID {}", target, id);
                            sendSseError(emitter, "{\"error\":\"Could not extract doctor message from response\"}", rawPythonResponseStr);
                            return;
                        }
                    }

                    // --- 5d. 准备并发送SSE响应 ---
                    sendSseResponse(emitter, contentForSSE, pythonResponseMap, target, id, rawPythonResponseStr);
                } else {
                    // 处理Python服务返回非OK或空响应的情况
                    log.error("[PROD Inquiry] Python service returned non-OK status: {} or empty body for case ID {}", response.getStatusCode(), id);
                    String errorBody = rawPythonResponseStr != null ? rawPythonResponseStr : "Status: " + response.getStatusCode();
                    sendSseError(emitter, "{\\\"error\\\":\\\"AI service returned " + response.getStatusCode() + "\\\"}", errorBody);
                    return;
                }

                // 完成SSE响应
                emitter.complete();
                log.info("[PROD Inquiry] Sent [DONE] and completed emitter for case ID {}", id);

            } catch (Exception e) {
                handleUnexpectedError(emitter, id, e, rawPythonResponseStr, pythonResponseMap);
            }
        });

        return emitter;
    }

    /**
     * 获取并验证CaseItem
     */
    private CaseItem getCaseItemAndValidate(String id, SseEmitter emitter) {
        CaseItem caseItem = caseItemMapper.findById(id);
        if (caseItem == null) {
            log.error("[PROD Inquiry] CaseItem not found for ID: {}", id);
            sendSseError(emitter, "{\"error\":\"Case record not found\"}", null);
            return null;
        }
        return caseItem;
    }

    /**
     * 获取或初始化交互历史
     */
    private Map<String, Object> getOrInitializeInteractionHistory(CaseItem caseItem, String id) {
        Object historyDbObject = caseItem.getInteractionHistory();
        Map<String, Object> interactionHistory = null;
        
        if (historyDbObject instanceof Map) {
            Map<String, Object> topLevelMapFromDb = (Map<String, Object>) historyDbObject;
            if (topLevelMapFromDb.containsKey("interaction_history") && topLevelMapFromDb.get("interaction_history") instanceof Map) {
                interactionHistory = (Map<String, Object>) topLevelMapFromDb.get("interaction_history");
                log.info("[PROD Inquiry] Extracted inner interaction history map from nested DB object for case ID: {}", id);
            } else if (topLevelMapFromDb.containsKey("cot_entries")) {
                interactionHistory = topLevelMapFromDb;
                log.info("[PROD Inquiry] Extracted inner interaction history map directly from DB object for case ID: {}", id);
                                            } else {
                log.warn("[PROD Inquiry] DB object is a Map but lacks expected inner structure for case ID: {}. Initializing.", id);
            }
        } else if (historyDbObject != null) {
            log.warn("[PROD Inquiry] Unexpected type ({}) returned by TypeHandler for case ID: {}. Initializing.",
                    historyDbObject.getClass().getName(), id);
        }

        // 如果无法从数据库获取或解析，则创建初始结构
        if (interactionHistory == null) {
            log.warn("[PROD Inquiry] Initializing interaction history structure for case ID: {}", id);
            interactionHistory = createInitialInnerInteractionHistoryProd();
        }
        
        return interactionHistory;
    }

    /**
     * 更新数据库
     */
    private void updateDatabaseWithResponse(String id, Map<String, Object> pythonResponseMap) {
        try {
            // 将 Python 返回的内部 Map 包装在顶层键下
            Map<String, Object> mapToSave = Map.of("interaction_history", pythonResponseMap);

            // 将包装后的 Map 传递给 Mapper
            int updateCount = caseItemMapper.updateInteractionHistoryById(id, mapToSave);
            if (updateCount > 0) {
                log.info("[PROD Inquiry] Database updated successfully with Python response for case ID {}", id);
            } else {
                log.warn("[PROD Inquiry] Failed to update database for case ID {}. Update count: {}", id, updateCount);
            }
        } catch (Exception dbEx) {
            log.error("[PROD Inquiry] Error updating database for case ID {}: {}", id, dbEx.getMessage(), dbEx);
        }
    }

    /**
     * 当target=3时，更新数据库以包含增强的数据
     */
    private void updateDatabaseWithEnhancedData(String id, Map<String, Object> pythonResponseMap) {
                        try {
                            if (pythonResponseMap != null && !pythonResponseMap.isEmpty()) {
                                // 将 Python 返回的内部 Map 包装在顶层键下
                                Map<String, Object> mapToSaveUpdated = Map.of("interaction_history", pythonResponseMap);
                                
                                // 将包装后的 Map 传递给 Mapper
                                int updateCount = caseItemMapper.updateInteractionHistoryById(id, mapToSaveUpdated);
                                if (updateCount > 0) {
                    log.info("[PROD Inquiry] Database updated successfully with enhanced response for case ID {}", id);
                                } else {
                                    log.warn("[PROD Inquiry] Failed to update database with enhanced response for case ID {}. Update count: {}", id, updateCount);
                                }
                            }
                        } catch (Exception dbEx) {
                            log.error("[PROD Inquiry] Error updating database with enhanced response for case ID {}: {}", id, dbEx.getMessage(), dbEx);
                        }
                    }

    /**
     * 发送SSE响应
     */
    private void sendSseResponse(SseEmitter emitter, String contentForSSE, Map<String, Object> pythonResponseMap, 
                                int target, String id, String rawPythonResponseStr) {
        try {
            log.debug("[PROD Inquiry] Final content for SSE: {}", contentForSSE);
            
            // 当target=2时，使用固定文本"请医生查体"作为TTS内容
            String ttsContent = (target == 2) ? "请医生查体" : contentForSSE;
            if (target == 2) {
                log.info("[PROD Inquiry] Target=2，使用固定TTS文本：请医生查体");
            }
            
            String audioUrl = null;
            if (target != 3) { // 诊断阶段(target=3)不生成语音
                audioUrl = ttsService.generateTtsUrl(ttsContent);
                log.info("[PROD Inquiry] Generated TTS URL for case ID {}: {}", id, audioUrl);
            } else {
                log.info("[PROD Inquiry] Target=3, skipping TTS generation for diagnosis. Case ID: {}", id);
            }

            Map<String, Object> responseData = new HashMap<>();
            responseData.put("content", contentForSSE);
            if (audioUrl != null) responseData.put("audioUrl", audioUrl);
            // responseData.put("cot", rawPythonResponseStr);
            responseData.put("target", target);
                        
            // 处理target=3的特殊响应字段
            if (target == 3) {
                addTarget3ResponseFields(responseData, pythonResponseMap, id);
            }
            
            String jsonData = objectMapper.writeValueAsString(responseData);
            emitter.send(SseEmitter.event().data(jsonData).id(String.valueOf(System.currentTimeMillis())).name("message"));
            log.info("[PROD Inquiry] Sent message event via SSE for case ID {}", id);
            
            // 发送DONE事件，确保客户端知道流已结束
            try {
                emitter.send(SseEmitter.event().data("[DONE]"));
                log.info("[PROD SSE] 发送DONE事件");
            } catch (Exception doneEx) {
                log.warn("[PROD SSE] 发送DONE事件失败: {}", doneEx.getMessage());
            }
        } catch (Exception e) {
            log.error("[PROD Inquiry] Error sending SSE response for case ID {}: {}", id, e.getMessage(), e);
            try {
                sendSseError(emitter, "{\"error\":\"Error generating response: " + escapeJson(e.getMessage()) + "\"}", rawPythonResponseStr);
            } catch (Exception ignored) {}
        }
    }

    /**
     * 为target=3添加特殊响应字段
     */
    private void addTarget3ResponseFields(Map<String, Object> responseData, Map<String, Object> pythonResponseMap, String id) {
        log.info("[PROD Inquiry] Target=3, adding special fields from pythonResponseMap for case ID: {}", id);
        
        // 添加one_chief_complaint_and_five_histories字段
                                String observation = extractOneChiefComplaintAndFiveHistories(pythonResponseMap, id);
                                if (observation != null) {
                                    responseData.put("one_chief_complaint_and_five_histories", observation);
                                    log.info("[PROD Inquiry] Added one_chief_complaint_and_five_histories for case ID: {}", id);
                                }
                                
        // 添加preliminary_diagnosis (populated by parseAndSetDiagnosisStrategy)
                                Object pd = pythonResponseMap.get("preliminary_diagnosis");
        responseData.put("preliminary_diagnosis", pd);
                                log.debug("[PROD Inquiry] Added preliminary_diagnosis to SSE response for case ID {}: {}", id, pd);
                                
        // 添加inspection_suggestions (populated by parseAndSetDiagnosisStrategy)
        Object is = pythonResponseMap.get("inspection_suggestions");
        responseData.put("inspection_suggestions", is);
        log.debug("[PROD Inquiry] Added inspection_suggestions to SSE response for case ID {}: {}", id, is);
        
        // 更新cot字段
        /* try {
                                    String updatedCot = objectMapper.writeValueAsString(pythonResponseMap);
                                    responseData.put("cot", updatedCot);
            log.info("[PROD Inquiry] Updated cot field with latest pythonResponseMap for case ID: {}", id);
                                } catch (Exception e) {
                                    log.error("[PROD Inquiry] Error serializing updated pythonResponseMap for cot field: {}", e.getMessage(), e);
        } */
    }

    /**
     * 从响应中确定target值
     */
    private int determineTargetFromResponse(Map<String, Object> pythonResponseMap, String id) {
        int target = 1; // 默认目标为病人
        
        try {
            if (pythonResponseMap != null && pythonResponseMap.containsKey("cot_entries")) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> cotEntries = (List<Map<String, Object>>) pythonResponseMap.get("cot_entries");
                if (cotEntries != null && !cotEntries.isEmpty()) {
                    Map<String, Object> lastEntry = cotEntries.get(cotEntries.size() - 1);
                    if (lastEntry != null && lastEntry.containsKey("strategy")) {
                        String strategy = (String) lastEntry.get("strategy");
                        if (strategy != null) {
                            if (strategy.contains("[查体策略:]")) {
                                target = 2; // 目标: 医生
                            } else if (strategy.contains("[诊断策略:]")) {
                                target = 3; // 目标: 拟诊
                                parseAndSetDiagnosisStrategy(pythonResponseMap, strategy, id);
                            }
                            log.info("[PROD Inquiry] Determined target based on strategy for case ID {}: {}", id, target);
                        }
                    }
                }
            }
                        } catch (Exception e) {
            log.warn("[PROD Inquiry] Error determining target from response for case ID {}: {}", id, e.getMessage(), e);
            // 保持 target 为默认值 1
        }
        
        return target;
    }

    /**
     * 解析诊断策略并设置相关字段
     */
    private void parseAndSetDiagnosisStrategy(Map<String, Object> pythonResponseMap, String strategy, String id) {
        log.info("[PROD Inquiry] Target=3, attempting to parse strategy for case ID {}: {}", id, strategy);
        String strategyMarker = "[诊断策略:]";
        int markerIdx = strategy.indexOf(strategyMarker);

        if (markerIdx != -1) {
            String diagnosticContentFromStrategy = strategy.substring(markerIdx + strategyMarker.length()).trim();
            log.debug("[PROD Inquiry] Target=3, diagnosticContentFromStrategy: '{}' for case ID: {}", diagnosticContentFromStrategy, id);

            String finalPreliminaryDiagnosis = "";
            String finalInspectionSuggestions = "";

            String diagnosisPrefix = "诊断：";
            String inspectionPrefix = "检查：";

            int diagPrefixActualIndex = diagnosticContentFromStrategy.indexOf(diagnosisPrefix);
            int inspPrefixActualIndex = diagnosticContentFromStrategy.indexOf(inspectionPrefix);

            if (diagPrefixActualIndex != -1 && inspPrefixActualIndex != -1 && diagPrefixActualIndex < inspPrefixActualIndex) {
                // Case: Both "诊断：" and "检查：" found, "诊断：" is first.
                finalPreliminaryDiagnosis = diagnosticContentFromStrategy.substring(diagPrefixActualIndex + diagnosisPrefix.length(), inspPrefixActualIndex).trim();
                // Remove trailing comma from diagnosis part if it exists before "检查："
                if (finalPreliminaryDiagnosis.endsWith(",")) {
                    finalPreliminaryDiagnosis = finalPreliminaryDiagnosis.substring(0, finalPreliminaryDiagnosis.length() - 1).trim();
                }
                finalInspectionSuggestions = diagnosticContentFromStrategy.substring(inspPrefixActualIndex + inspectionPrefix.length()).trim();
                log.info("[PROD Inquiry] Target=3, Parsed from diagnosticContent: PD='{}', IS='{}' for case ID: {}", finalPreliminaryDiagnosis, finalInspectionSuggestions, id);
            } else if (diagPrefixActualIndex != -1) {
                // Case: Only "诊断：" found, or it appears after "检查：" (treat as full diagnosis part)
                finalPreliminaryDiagnosis = diagnosticContentFromStrategy.substring(diagPrefixActualIndex + diagnosisPrefix.length()).trim();
                // finalInspectionSuggestions remains ""
                log.info("[PROD Inquiry] Target=3, Parsed from diagnosticContent (only diagnosis part or malformed): PD='{}' for case ID: {}", finalPreliminaryDiagnosis, id);
            } else if (inspPrefixActualIndex != -1) {
                // Case: Only "检查：" found (diagnosis part is missing or not prefixed)
                // finalPreliminaryDiagnosis remains ""
                finalInspectionSuggestions = diagnosticContentFromStrategy.substring(inspPrefixActualIndex + inspectionPrefix.length()).trim();
                log.info("[PROD Inquiry] Target=3, Parsed from diagnosticContent (only inspection part or malformed): IS='{}' for case ID: {}", finalInspectionSuggestions, id);
                } else {
                // Case: Neither prefix found. Treat the whole block as preliminary diagnosis.
                finalPreliminaryDiagnosis = diagnosticContentFromStrategy;
                // finalInspectionSuggestions remains ""
                log.warn("[PROD Inquiry] Target=3, DiagnosticContent '{}' did not contain expected '{}' or '{}' prefixes. Entire content set as PD for case ID: {}", diagnosticContentFromStrategy, diagnosisPrefix, inspectionPrefix, id);
            }

            pythonResponseMap.put("preliminary_diagnosis", finalPreliminaryDiagnosis);
            pythonResponseMap.put("inspection_suggestions", finalInspectionSuggestions);
            
            // 记录提取到的检查建议
            if (!finalInspectionSuggestions.isEmpty()) {
                log.info("[PROD Inquiry] Target=3, Successfully extracted inspection_suggestions for case ID: {}: {}", 
                         id, finalInspectionSuggestions);
            }

            // Ensure test_recommendation list exists, even if empty (original logic)
            if (!pythonResponseMap.containsKey("test_recommendation")) {
                pythonResponseMap.put("test_recommendation", new ArrayList<Map<String, Object>>());
                log.info("[PROD Inquiry] No test_recommendation in pythonResponseMap, setting to empty list for case ID: {}", id);
            }
        } else {
            log.warn("[PROD Inquiry] Target=3 but strategy for case ID {} does not contain proper marker '{}': {}", id, strategyMarker, strategy);
            // Fallback: ensure keys exist with empty strings if strategy was malformed but target was 3
            if (!pythonResponseMap.containsKey("preliminary_diagnosis")) {
                pythonResponseMap.put("preliminary_diagnosis", "");
            }
            if (!pythonResponseMap.containsKey("inspection_suggestions")) {
                pythonResponseMap.put("inspection_suggestions", "");
            }
        }
    }

    /**
     * 处理意外错误并向客户端发送错误消息
     * 优化：更详细的错误分类与日志
     */
    private void handleUnexpectedError(SseEmitter emitter, String id, Exception e, 
                                   String rawPythonResponseStr, Map<String, Object> pythonResponseMap) {
        try {
            String errorMessage;
            String errorDetails;
            String logMessage;
            
            // 分类处理不同类型的异常
            if (e instanceof RestClientException) {
                // REST客户端异常，通常是连接或API调用问题
                errorMessage = "AI服务连接失败";
                errorDetails = "REST调用错误: " + e.getMessage();
                logMessage = "Python服务REST调用失败";
            } else if (e instanceof JsonProcessingException) {
                // JSON处理异常
                errorMessage = "JSON处理错误";
                errorDetails = "解析响应时出错: " + e.getMessage();
                logMessage = "解析Python服务JSON响应失败";
            } else if (e instanceof IOException) {
                // IO异常
                errorMessage = "IO操作失败";
                errorDetails = "IO错误: " + e.getMessage();
                logMessage = "与Python服务通信时发生IO错误";
            } else if (e instanceof NullPointerException) {
                // 空指针异常
                errorMessage = "程序遇到空值错误";
                errorDetails = "NullPointerException: " + (e.getMessage() != null ? e.getMessage() : "未知空值引用");
                logMessage = "处理Python响应时遇到空值";
            } else if (e instanceof ClassCastException) {
                // 类型转换异常
                errorMessage = "数据类型错误";
                errorDetails = "类型转换失败: " + e.getMessage();
                logMessage = "处理Python响应时数据类型不匹配";
            } else if (e instanceof IllegalArgumentException) {
                // 参数错误
                errorMessage = "请求参数错误";
                errorDetails = "参数错误: " + e.getMessage();
                logMessage = "调用Python服务时参数无效";
            } else {
                // 其他未知异常
                errorMessage = "AI服务处理错误";
                errorDetails = "未预期的异常: " + e.getClass().getSimpleName() + " - " + e.getMessage();
                logMessage = "处理Python响应时发生未知错误";
            }
            
            // 生成错误消息JSON对象
            Map<String, Object> errorObj = new HashMap<>();
            errorObj.put("error", errorMessage);
            
            // 记录详细的错误日志
            log.error("[PROD Error] 案例ID:{} - {}: {}. {}", id, logMessage, errorDetails, 
                      (e.getMessage() != null ? "原始异常: " + e.getMessage() : "无异常详情"), e);
            
            // 整理COT内容用于调试
            StringBuilder cotBuilder = new StringBuilder();
            cotBuilder.append(errorDetails).append("\n\n");
            
            // 添加原始响应的片段（如果有）
            if (rawPythonResponseStr != null && !rawPythonResponseStr.isEmpty()) {
                int maxResponseLength = 500;
                String responseSnippet = rawPythonResponseStr.length() > maxResponseLength ?
                                        rawPythonResponseStr.substring(0, maxResponseLength) + "..." :
                                        rawPythonResponseStr;
                cotBuilder.append("原始响应片段: ").append(responseSnippet);
            } else {
                cotBuilder.append("无原始响应数据");
            }
            
            // 添加解析后的响应Map片段（如果有）
            if (pythonResponseMap != null && !pythonResponseMap.isEmpty()) {
                try {
                    String mapString = objectMapper.writeValueAsString(pythonResponseMap);
                    int maxMapLength = 500;
                    String mapSnippet = mapString.length() > maxMapLength ?
                                      mapString.substring(0, maxMapLength) + "..." :
                                      mapString;
                    cotBuilder.append("\n\n解析后的响应片段: ").append(mapSnippet);
                } catch (Exception serializeEx) {
                    cotBuilder.append("\n\n无法序列化已解析的响应: ").append(serializeEx.getMessage());
                }
            }
            
            // 发送错误消息
            sendSseError(emitter, objectMapper.writeValueAsString(errorObj), cotBuilder.toString());
            // 注意：sendSseError方法已更新，会发送DONE事件
            
        } catch (Exception handlingError) {
            // 处理错误处理过程中的异常 - 使用最简单的方法发送错误
            log.error("[PROD Error] 案例ID:{} - 处理异常时出错: {}", id, handlingError.getMessage(), handlingError);
            try {
                sendSseError(emitter, "处理错误时发生了意外问题", "错误处理失败: " + handlingError.getMessage());
                // 注意：sendSseError方法已更新，会发送DONE事件
            } catch (Exception ignored) {
                log.error("[PROD Error] 案例ID:{} - 发送最终错误消息也失败", id);
            }
        }
    }

    /**
     * 创建初始的 *内部* Interaction History 结构 (Map)。
     * 包含所有预期的顶级字段。
     * 使用 LinkedHashMap 保持字段顺序（可选）。
     *
     * @return 包含初始值的内部 Map。
     */
    private Map<String, Object> createInitialInnerInteractionHistoryProd() {
        Map<String, Object> innerHistory = new LinkedHashMap<>();
        List<Map<String, Object>> cotEntries = new ArrayList<>();
        Map<String, Object> cotEntry = new LinkedHashMap<>();
        List<Map<String, Object>> dialogueHistory = new ArrayList<>();

        Map<String, Object> initialDoctorMessage = new LinkedHashMap<>();
        initialDoctorMessage.put("content", "您好，我是本次的医生，请问孩子的年龄，性别，主要的症状是什么？");
        initialDoctorMessage.put("role", "doctor");
        dialogueHistory.add(initialDoctorMessage);

        cotEntry.put("dialogue_history", dialogueHistory);
        cotEntry.put("feedback", "");
        cotEntry.put("observation", "");
        cotEntry.put("reasoning", "主诉是问诊的切入点，通过了解患者的基本信息和主要症状，可以快速建立初步印象。进一步询问发病时间、起病情况、症状特点、伴随症状、病情演变和诊疗经过，有助于全面了解现病史，为后续诊断提供关键信息。这种系统性的问诊策略能够帮助我们准确把握患者的病情，避免遗漏重要信息。");
        cotEntry.put("strategy", "询问患者的基本信息（性别、年龄）和主诉症状，了解发病时间、起病情况、症状特点（性质、程度、持续时间、频率）、伴随症状、病情演变以及之前的诊疗经过。");
        cotEntries.add(cotEntry);
        innerHistory.put("cot_entries", cotEntries);

        innerHistory.put("preliminary_diagnosis", null);
        innerHistory.put("test_recommendation", new ArrayList<Map<String, Object>>());
        innerHistory.put("diagnosis", "");
        innerHistory.put("treatment_recommendation", new ArrayList<>());
        innerHistory.put("doctor_supplementary_info", new ArrayList<>());

        log.debug("Created initial inner interaction history structure.");
        return innerHistory;
    }

    /**
     * 将用户消息添加到 *内部* Interaction History Map 的最后一个对话条目中。
     *
     * @param interactionHistory 代表内部历史的 Map (可被修改)。
     * @param userMessage        用户消息。
     */
    private void addPatientMessageProd(Map<String, Object> interactionHistory, String userMessage) {
        if (interactionHistory == null || userMessage == null || userMessage.trim().isEmpty()) {
            log.warn("[PROD Helper] Invalid input for adding patient message. History or message is null/empty.");
            return;
        }
        try {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> cotEntries = (List<Map<String, Object>>) interactionHistory.computeIfAbsent("cot_entries", k -> new ArrayList<>());

            Map<String, Object> lastEntry;
            if (cotEntries.isEmpty()) {
                log.warn("[PROD Helper] 'cot_entries' is empty. Creating a new entry to add patient message.");
                lastEntry = new LinkedHashMap<>();
                cotEntries.add(lastEntry);
            } else {
                lastEntry = cotEntries.get(cotEntries.size() - 1);
                 if (lastEntry == null) {
                    log.warn("[PROD Helper] Last entry in 'cot_entries' was null. Creating a new one.");
                    lastEntry = new LinkedHashMap<>();
                    cotEntries.set(cotEntries.size() - 1, lastEntry);
                }
            }

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> dialogueHistory = (List<Map<String, Object>>) lastEntry.computeIfAbsent("dialogue_history", k -> new ArrayList<>());

            Map<String, Object> patientMsg = new LinkedHashMap<>();
            patientMsg.put("content", userMessage);
            patientMsg.put("role", "patient");
            dialogueHistory.add(patientMsg);

            log.debug("[PROD Helper] Patient message added to the last dialogue history.");
        } catch (ClassCastException | NullPointerException e) {
             log.error("[PROD Helper] Error adding patient message due to invalid history structure: {}", e.getMessage(), e);
        }
    }

    /**
     * 从Python响应中提取最后一个医生消息
     * 优化版：更健壮地处理各种可能的结构和边缘情况
     */
    public String extractLastDoctorMessageProd(Map<String, Object> pythonResponseMap) {
        if (pythonResponseMap == null || pythonResponseMap.isEmpty()) {
            log.warn("pythonResponseMap为空或null");
            return null;
        }

        // 从cot_entries中提取医生消息
        if (pythonResponseMap.containsKey("cot_entries")) {
            List<Map<String, Object>> cotEntries = (List<Map<String, Object>>) pythonResponseMap.get("cot_entries");
            if (cotEntries != null && !cotEntries.isEmpty()) {
                // 获取最后一个cot条目
                Map<String, Object> lastCotEntry = cotEntries.get(cotEntries.size() - 1);
                
                // 获取最后一个cot条目中的dialogue_history
                List<Map<String, String>> dialogueHistory = (List<Map<String, String>>) lastCotEntry.get("dialogue_history");
                
                if (dialogueHistory != null && !dialogueHistory.isEmpty()) {
                    // 从dialogue_history中提取最后一条医生消息
                    for (int i = dialogueHistory.size() - 1; i >= 0; i--) {
                        Map<String, String> message = dialogueHistory.get(i);
                        String role = message.get("role");
                        if ("doctor".equals(role) || "医生".equals(role)) {
                            String content = message.get("content") != null ? message.get("content") : message.get("内容");
                            if (content != null && !content.trim().isEmpty()) {
                                log.debug("从最后一个cot条目的dialogue_history中提取到医生消息: {}", content);
                                return content;
                            }
                        }
                    }
                } else {
                    log.warn("最后一个cot条目中的dialogue_history为空");
                    
                    // 增加对诊断策略的特殊处理
                    String strategy = (String) lastCotEntry.get("strategy");
                    if (strategy != null && (strategy.contains("诊断:") || strategy.contains("诊断："))) {
                        log.info("检测到诊断策略，尝试从strategy中提取诊断内容");
                        
                        // 提取诊断内容
                        int diagnosisIndex = strategy.indexOf("诊断:");
                        if (diagnosisIndex == -1) {
                            diagnosisIndex = strategy.indexOf("诊断：");
                        }
                        
                        if (diagnosisIndex != -1) {
                            String diagnosisContent = strategy.substring(diagnosisIndex).trim();
                            if (!diagnosisContent.isEmpty()) {
                                log.debug("从诊断策略中提取到诊断内容: {}", diagnosisContent);
                                return diagnosisContent;
                            }
                        }
                    }
                    
                    // 尝试从其他cot条目中提取医生消息
                    log.info("尝试从其他cot条目中提取医生消息");
                    for (int i = cotEntries.size() - 2; i >= 0; i--) {
                        Map<String, Object> cotEntry = cotEntries.get(i);
                        List<Map<String, String>> otherDialogueHistory = (List<Map<String, String>>) cotEntry.get("dialogue_history");
                        if (otherDialogueHistory != null && !otherDialogueHistory.isEmpty()) {
                            for (int j = otherDialogueHistory.size() - 1; j >= 0; j--) {
                                Map<String, String> message = otherDialogueHistory.get(j);
                                String role = message.get("role");
                                if ("doctor".equals(role) || "医生".equals(role)) {
                                    String content = message.get("content") != null ? message.get("content") : message.get("内容");
                                    if (content != null && !content.trim().isEmpty()) {
                                        log.debug("从第{}个cot条目的dialogue_history中提取到医生消息: {}", i + 1, content);
                                        return content;
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                log.warn("cot_entries为空");
            }
        }

        // 从interaction_history中提取医生消息
        if (pythonResponseMap.containsKey("interaction_history")) {
            List<Map<String, String>> interactionHistory = (List<Map<String, String>>) pythonResponseMap.get("interaction_history");
            if (interactionHistory != null && !interactionHistory.isEmpty()) {
                for (int i = interactionHistory.size() - 1; i >= 0; i--) {
                    Map<String, String> message = interactionHistory.get(i);
                    String role = message.get("role");
                    if ("doctor".equals(role) || "医生".equals(role)) {
                        String content = message.get("content") != null ? message.get("content") : message.get("内容");
                        if (content != null && !content.trim().isEmpty()) {
                            log.debug("从interaction_history中提取到医生消息: {}", content);
                            return content;
                        }
                    }
                }
            } else {
                log.warn("interaction_history为空");
            }
        }

        // 尝试从其他字段中提取内容
        if (pythonResponseMap.containsKey("diagnosis")) {
            String diagnosis = (String) pythonResponseMap.get("diagnosis");
            if (diagnosis != null && !diagnosis.trim().isEmpty()) {
                log.debug("从diagnosis字段中提取到内容: {}", diagnosis);
                return diagnosis;
            }
        }

        if (pythonResponseMap.containsKey("content")) {
            String content = (String) pythonResponseMap.get("content");
            if (content != null && !content.trim().isEmpty()) {
                log.debug("从content字段中提取到内容: {}", content);
                return content;
            }
        }

        log.warn("无法从pythonResponseMap中提取到有效的医生消息");
        return null;
    }

    /**
     * 从cot_entries中提取倒数第二个observation
     * 作为"one_chief_complaint_and_five_histories"字段的值
     * 
     * @param pythonResponseMap Python返回的响应Map
     * @param id CaseItem的ID，用于日志记录
     * @return 提取的observation或null
     */
    private String extractOneChiefComplaintAndFiveHistories(Map<String, Object> pythonResponseMap, String id) {
        if (pythonResponseMap == null || !pythonResponseMap.containsKey("cot_entries")) {
            log.debug("[PROD Helper] Unable to extract one_chief_complaint_and_five_histories: null map or missing cot_entries for case ID: {}", id);
            return null;
        }
        
        try {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> cotEntries = (List<Map<String, Object>>) pythonResponseMap.get("cot_entries");
            
            if (cotEntries == null || cotEntries.size() < 2) {
                log.debug("[PROD Helper] Not enough cot_entries to extract one_chief_complaint_and_five_histories for case ID: {}", id);
                return null;
            }
            
            // 尝试获取倒数第二个observation
            Map<String, Object> secondLastEntry = cotEntries.get(cotEntries.size() - 2);
            if (secondLastEntry != null && secondLastEntry.containsKey("observation")) {
                Object observation = secondLastEntry.get("observation");
                if (observation instanceof String && !((String) observation).isEmpty()) {
                    log.debug("[PROD Helper] Successfully extracted one_chief_complaint_and_five_histories from second last entry for case ID: {}", id);
                    return (String) observation;
                }
            }
            
            // 如果倒数第二个没有，尝试其他位置的observation作为回退
            for (int i = cotEntries.size() - 3; i >= 0; i--) {
                Map<String, Object> entry = cotEntries.get(i);
                if (entry != null && entry.containsKey("observation")) {
                    Object observation = entry.get("observation");
                    if (observation instanceof String && !((String) observation).isEmpty()) {
                        log.info("[PROD Helper] Used fallback observation from entry {} for one_chief_complaint_and_five_histories for case ID: {}", i, id);
                        return (String) observation;
                    }
                }
            }
            
            log.warn("[PROD Helper] No suitable observation found for one_chief_complaint_and_five_histories for case ID: {}", id);
            return null;
        } catch (ClassCastException | NullPointerException e) {
            log.error("[PROD Helper] Error extracting one_chief_complaint_and_five_histories for case ID {}: {}", id, e.getMessage());
            return null;
        }
    }

    /**
     * 安全地发送 SSE 错误事件并尝试完成 Emitter。
     * 优化：确保始终有有意义的错误消息和 cot 内容，使用中文错误提示
     *
     * @param emitter      SseEmitter 实例。
     * @param errorMessage 错误消息字符串。
     * @param cot          可选的上下文信息（例如原始 Python 响应）。
     */
    private void sendSseError(SseEmitter emitter, String errorMessage, String cot) {
        if (emitter == null) return;
        
        String finalErrorMessage = errorMessage;
        String finalCot = cot;
        
        try {
            // 确保错误消息有效
            if (finalErrorMessage == null || finalErrorMessage.trim().isEmpty()) {
                finalErrorMessage = "未知错误";
                log.warn("[PROD SSE] 发送了空的错误消息，已替换为'未知错误'");
            }
            
            // 确保 COT 有内容
            if (finalCot == null || finalCot.trim().isEmpty()) {
                finalCot = "无可用的调试信息";
                log.warn("[PROD SSE] 发送了空的 COT 内容，已替换为默认消息");
            }
            
            Map<String, Object> errorData = new HashMap<>();
            
            // 尝试解析错误消息是否为 JSON
            try {
                @SuppressWarnings("unchecked")
                Map<String, Object> parsedError = objectMapper.readValue(finalErrorMessage, new TypeReference<Map<String, Object>>() {});
                errorData.putAll(parsedError);
                
                // 确保有 error 字段
                if (!errorData.containsKey("error") || errorData.get("error") == null || 
                    (errorData.get("error") instanceof String && ((String)errorData.get("error")).trim().isEmpty())) {
                    errorData.put("error", "未能从AI回复中提取到医生消息");
                }
            } catch (Exception e) {
                log.debug("[PROD SSE] 错误消息不是有效的 JSON，将作为纯文本发送: {}", e.getMessage());
                errorData.put("error", finalErrorMessage);
            }

            // 添加 COT 内容（确保不会太长）
            int maxCotLength = 500;
            if (finalCot.length() > maxCotLength) {
                finalCot = finalCot.substring(0, maxCotLength) + "...（已截断）";
            }
            errorData.put("cot", finalCot);

            // 记录实际发送的内容
            try {
                String errorJsonData = objectMapper.writeValueAsString(errorData);
                
                if (log.isInfoEnabled()) {
                    log.info("[PROD SSE] 发送错误消息: {}", 
                           errorJsonData.length() > 200 ? errorJsonData.substring(0, 200) + "..." : errorJsonData);
                }
                
                emitter.send(SseEmitter.event().name("error").data(errorJsonData));
            } catch (Exception e) {
                log.error("[PROD SSE] 序列化或发送错误数据失败: {}", e.getMessage());
                // 尝试发送简化的错误消息
                try {
                    String simpleError = "{\"error\":\"发送错误消息时出现问题\",\"cot\":\"" + e.getMessage() + "\"}";
                    emitter.send(SseEmitter.event().name("error").data(simpleError));
                } catch (Exception innerEx) {
                    log.error("[PROD SSE] 发送简化错误消息也失败: {}", innerEx.getMessage());
                }
            }
            
            // 发送DONE事件，确保客户端知道流已结束
            try {
                emitter.send(SseEmitter.event().data("[DONE]"));
                log.info("[PROD SSE] 发送DONE事件");
            } catch (Exception doneEx) {
                log.warn("[PROD SSE] 发送DONE事件失败: {}", doneEx.getMessage());
            }
        } catch (Exception e) {
            log.error("[PROD SSE] 处理或发送错误消息时发生意外错误: {}", e.getMessage(), e);
        } finally {
            try {
                if (emitter != null) {
                    emitter.complete();
                    log.debug("[PROD SSE] 在 sendSseError 的 finally 块中完成发射器");
                }
            } catch (Exception ignored) {
                log.debug("[PROD SSE] 在 sendSseError 的 finally 块中完成发射器时忽略异常");
            }
        }
    }

    /**
     * 对字符串进行基本的 JSON 转义。
     * @param str 输入字符串。
     * @return 转义后的字符串。
     */
    private String escapeJson(String str) {
        if (str == null) return null;
        return str.replace("\"", "\\\"").replace("\n", "\\n").replace("\r", "\\r").replace("\t", "\\t");
    }

    /**
     * 添加医生补充信息到指定案例的交互历史中。
     *
     * @param id CaseItem 的唯一标识符。
     * @param supplementaryInfo 要添加的补充信息字符串。
     * @return 如果成功更新则返回 true，否则返回 false。
     */
    public boolean addDoctorSupplementaryInfoProd(String id, String supplementaryInfo) {
        log.info("[PROD Add Info] Attempting to add supplementary info for case ID: {}", id);
        if (supplementaryInfo == null || supplementaryInfo.trim().isEmpty()) {
            log.warn("[PROD Add Info] Supplementary info is null or empty. No update performed for case ID: {}", id);
            return false;
        }

        try {
            CaseItem caseItem = caseItemMapper.findById(id);
            if (caseItem == null) {
                log.error("[PROD Add Info] CaseItem not found for ID: {}", id);
                return false;
            }

            Object historyDbObject = caseItem.getInteractionHistory();
            Map<String, Object> topLevelMap;

            if (historyDbObject instanceof Map) {
                topLevelMap = (Map<String, Object>) historyDbObject;
                log.debug("[PROD Add Info] Retrieved existing interaction history object (Map) for case ID: {}", id);
            } else {
                log.warn("[PROD Add Info] Interaction history is missing or not a Map for case ID: {}. Initializing a new structure.", id);
                topLevelMap = new HashMap<>(); // Initialize if missing
            }

            // Ensure the main 'interaction_history' key exists
            Object innerHistoryObj = topLevelMap.computeIfAbsent("interaction_history", k -> createInitialInnerInteractionHistoryProd());

            if (!(innerHistoryObj instanceof Map)) {
                 log.error("[PROD Add Info] Could not get or create a valid inner 'interaction_history' Map for case ID: {}. Type was: {}", id, innerHistoryObj != null ? innerHistoryObj.getClass().getName() : "null");
                 return false;
            }
            Map<String, Object> innerHistoryMap = (Map<String, Object>) innerHistoryObj;

            // Get or create the 'doctor_supplementary_info' list within the inner map
            Object existingInfoListObj = innerHistoryMap.computeIfAbsent("doctor_supplementary_info", k -> new ArrayList<String>());
            List<String> targetInfoList;

            if (existingInfoListObj instanceof List) {
                 // Attempt safe casting
                try {
                    targetInfoList = (List<String>) existingInfoListObj;
                } catch (ClassCastException e) {
                    log.warn("[PROD Add Info] Existing 'doctor_supplementary_info' is a List, but not of Strings for case ID: {}. Creating new list.", id);
                    targetInfoList = new ArrayList<>();
                    innerHistoryMap.put("doctor_supplementary_info", targetInfoList);
                }
            } else {
                log.warn("[PROD Add Info] 'doctor_supplementary_info' exists but is not a List for case ID: {}. Creating new list.", id);
                targetInfoList = new ArrayList<>();
                innerHistoryMap.put("doctor_supplementary_info", targetInfoList);
            }

            // Add the new info
            targetInfoList.add(supplementaryInfo);
            log.info("[PROD Add Info] Supplementary info added to the list for case ID: {}", id);

            // Update the database with the modified top-level map
            int updateCount = caseItemMapper.updateInteractionHistoryById(id, topLevelMap);
            if (updateCount > 0) {
                log.info("[PROD Add Info] Successfully updated database for case ID: {}", id);
                return true;
            } else {
                log.error("[PROD Add Info] Failed to update database for case ID: {}. Update count was 0.", id);
                return false;
            }
        } catch (Exception e) {
            log.error("[PROD Add Info] Unexpected error adding supplementary info for case ID {}: {}", id, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 调用 Python V2 诊断接口获取诊断、病情和治疗计划 (生产环境)。
     *
     * @param id CaseItem 的唯一标识符。
     * @return 包含诊断结果的 Map，或包含错误的 Map。
     */
    public Map<String, Object> diagnoseAiDoctorProd(String id) {
        log.info("[PROD Diagnose] Starting diagnosis process for case ID: {}", id);
        Map<String, Object> result = new HashMap<>();

        if (id == null || id.trim().isEmpty()) {
            log.error("[PROD Diagnose] Case ID is required.");
            result.put("error", "缺少案例 ID");
            return result;
        }

        try {
            // --- 1. 获取并验证CaseItem ---
            CaseItem caseItem = caseItemMapper.findById(id);
            if (caseItem == null) {
                log.error("[PROD Diagnose] CaseItem not found for ID: {}", id);
                result.put("error", "未找到案例记录");
                return result;
            }

            // --- 2. 获取并验证交互历史 ---
            Map<String, Object> topLevelMap = getAndValidateInteractionHistory(caseItem, id, result);
            if (topLevelMap == null) return result;
            
            Map<String, Object> requestHistoryMap = (Map<String, Object>) topLevelMap.get("interaction_history");
            logRequestHistoryDetails(id, requestHistoryMap);
            
            // --- 3. 准备并修复请求数据 ---
            Map<String, Object> requestPayload = prepareRequestPayload(requestHistoryMap, id);
            
            // --- 4. 调用Python诊断服务 ---
            ResponseEntity<String> responseEntity = callPythonDiagnoseService(id, requestPayload);
            String rawDiagnosisResponse = responseEntity.getBody();
            
            // --- 5. 处理Python响应 ---
            if (responseEntity.getStatusCode() != HttpStatus.OK || rawDiagnosisResponse == null) {
                return handleNonOkResponse(responseEntity, rawDiagnosisResponse, id, result);
            }
            
            // --- 6. 解析Python响应 ---
            Map<String, Object> diagnosisResultMap = parsePythonResponse(rawDiagnosisResponse, id, result);
            if (diagnosisResultMap == null) return result;
            
            // --- 7. 处理 guidelines_content - 添加到MongoDB ---
            if (diagnosisResultMap.containsKey("guidelines_content")) {
                try {
                    Object guidelinesApiContent = diagnosisResultMap.get("guidelines_content");
                    log.debug("[PROD Diagnose] 从Python诊断API接收到指南内容，案例ID: {}", id);

                    try {
                        // 使用Query查询已存在的指南内容
                        Query query = new Query();
                        query.addCriteria(Criteria.where("caseId").is(id));

                        GuidelinesContent existingContent = mongoTemplate.findOne(query, GuidelinesContent.class, "guidelines_content");

                        String savedContentId;
                        if (existingContent != null) {
                            log.info("[PROD Diagnose] 找到案例 {} 已存在的指南内容记录 ID: {}，准备更新", id, existingContent.getId());
                            existingContent.setContent(guidelinesApiContent);
                            existingContent.setCreatedAt(LocalDateTime.now());
                            mongoTemplate.save(existingContent, "guidelines_content");
                            savedContentId = existingContent.getId();
                            log.info("[PROD Diagnose] 已更新MongoDB中的诊断指南内容，案例ID: {}, 引用ID: {}", id, savedContentId);
                        } else {
                            log.info("[PROD Diagnose] 未找到案例 {} 的指南内容记录，创建新记录", id);
                            GuidelinesContent newContent = new GuidelinesContent();
                            newContent.setId(UUID.randomUUID().toString());
                            newContent.setCaseId(id);
                            newContent.setContent(guidelinesApiContent);
                            newContent.setCreatedAt(LocalDateTime.now());
                            mongoTemplate.insert(newContent, "guidelines_content");
                            savedContentId = newContent.getId();
                            log.info("[PROD Diagnose] 已在MongoDB中创建新的诊断指南内容，案例ID: {}, 引用ID: {}", id, savedContentId);
                        }

                        // 用引用ID替换原始内容
                        diagnosisResultMap.put("guidelines_content_ref", savedContentId);
                        diagnosisResultMap.remove("guidelines_content");
                        log.debug("[PROD Diagnose] 已将诊断中的 guidelines_content 替换为引用 ID: {}", savedContentId);
                    } catch (Exception mongoEx) {
                        log.error("[PROD Diagnose] MongoDB操作失败，无法存储诊断指南内容 (案例ID: {}): {}", id, mongoEx.getMessage(), mongoEx);
                        log.info("[PROD Diagnose] 由于MongoDB操作失败，诊断指南内容将保留在响应中");
                        // 保留guidelines_content，不从响应中删除
                    }
                } catch (Exception e) {
                    log.error("[PROD Diagnose] 处理诊断指南内容时发生异常 (案例ID: {}): {}", id, e.getMessage(), e);
                }
            }
            
            // --- 7.2 处理 treatment_guide - 添加到MongoDB ---
            if (diagnosisResultMap.containsKey("treatment_guide")) {
                try {
                    Object treatmentGuideContent = diagnosisResultMap.get("treatment_guide");
                    log.debug("[PROD Diagnose] 从Python诊断API接收到treatment_guide内容，案例ID: {}", id);

                    try {
                        // 使用Query查询已存在的指南内容
                        Query query = new Query();
                        query.addCriteria(Criteria.where("caseId").is(id));

                        GuidelinesContent existingContent = mongoTemplate.findOne(query, GuidelinesContent.class, "guidelines_content");

                        String savedContentId;
                        if (existingContent != null) {
                            log.info("[PROD Diagnose] 找到案例 {} 已存在的指南内容记录 ID: {}，准备更新treatment_guide内容", id, existingContent.getId());
                            existingContent.setContent(treatmentGuideContent);  // 使用treatment_guide内容更新
                            existingContent.setCreatedAt(LocalDateTime.now());
                            mongoTemplate.save(existingContent, "guidelines_content");
                            savedContentId = existingContent.getId();
                            log.info("[PROD Diagnose] 已更新MongoDB中的treatment_guide内容，案例ID: {}, 引用ID: {}", id, savedContentId);
                        } else {
                            log.info("[PROD Diagnose] 未找到案例 {} 的指南内容记录，创建新记录存储treatment_guide", id);
                            GuidelinesContent newContent = new GuidelinesContent();
                            newContent.setId(UUID.randomUUID().toString());
                            newContent.setCaseId(id);
                            newContent.setContent(treatmentGuideContent);  // 保存treatment_guide内容
                            newContent.setCreatedAt(LocalDateTime.now());
                            mongoTemplate.insert(newContent, "guidelines_content");
                            savedContentId = newContent.getId();
                            log.info("[PROD Diagnose] 已在MongoDB中创建新的treatment_guide内容，案例ID: {}, 引用ID: {}", id, savedContentId);
                        }

                        // 用引用ID替换原始内容
                        diagnosisResultMap.put("treatment_guide_ref", savedContentId);
                        diagnosisResultMap.remove("treatment_guide");
                        log.debug("[PROD Diagnose] 已将诊断中的 treatment_guide 替换为引用 ID: {}", savedContentId);
                    } catch (Exception mongoEx) {
                        log.error("[PROD Diagnose] MongoDB操作失败，无法存储treatment_guide内容 (案例ID: {}): {}", id, mongoEx.getMessage(), mongoEx);
                        log.info("[PROD Diagnose] 由于MongoDB操作失败，treatment_guide内容将保留在响应中");
                        // 保留treatment_guide，不从响应中删除
                    }
                } catch (Exception e) {
                    log.error("[PROD Diagnose] 处理treatment_guide内容时发生异常 (案例ID: {}): {}", id, e.getMessage(), e);
                }
            }
            
            // --- 8. 构建诊断结果 ---
            Map<String, Object> formattedDiagnosisMap = buildDiagnosisResultMap(diagnosisResultMap, requestHistoryMap, id);
            
            // --- 9. 更新数据库 ---
            updateDatabaseWithDiagnosisResults(id, topLevelMap, formattedDiagnosisMap, result);
            
            // --- 10. 分离治疗方案和推理过程 ---
            String rawTreatmentRecommendation = (String) diagnosisResultMap.getOrDefault("treatment_recommendation", "");
            Map<String, String> parsedTreatment = TreatmentUtils.parseTreatmentAndReasoning(rawTreatmentRecommendation);
            String cleanTreatmentRecommendation = parsedTreatment.get("treatment");
            String reasoningProcess = parsedTreatment.get("reasoning");

            diagnosisResultMap.put("treatment_recommendation", cleanTreatmentRecommendation);
            diagnosisResultMap.put("reasoning_process", reasoningProcess);
            log.info("[PROD Diagnose] Separated treatment recommendation and reasoning process for case ID: {}", id);

            // --- 11. 构建最终返回给前端的Map ---
            Map<String, Object> finalResultMap = buildDiagnosisResultMap(diagnosisResultMap, requestHistoryMap, id);

            // --- 12. 更新数据库 ---
            // 注意：finalResultMap已经包含了所有需要更新到topLevelMap中的信息
            updateDatabaseWithDiagnosisResults(id, topLevelMap, finalResultMap, result);
            
            // --- 13. 准备返回结果 ---
            // finalResultMap也包含了所有需要返回给前端的信息
            result.putAll(finalResultMap);

            log.info("[PROD Diagnose] Successfully processed diagnosis for case ID: {}", id);
            return result;

        } catch (Exception e) {
            log.error("[PROD Diagnose] Unexpected error during diagnosis process for case ID {}: {}", id, e.getMessage(), e);
            result.put("error", "诊断处理发生内部错误: " + e.getMessage());
                    return result;
                }
    }
    
    /**
     * 获取并验证交互历史数据
     */
    private Map<String, Object> getAndValidateInteractionHistory(CaseItem caseItem, String id, Map<String, Object> result) {
        Object historyDbObject = caseItem.getInteractionHistory();
        if (!(historyDbObject instanceof Map)) {
                log.error("[PROD Diagnose] Interaction history is missing or not a Map for case ID: {}", id);
                result.put("error", "交互历史不存在或格式无效");
            return null;
        }
        
        Map<String, Object> topLevelMap = (Map<String, Object>) historyDbObject;
        if (!topLevelMap.containsKey("interaction_history")) {
            log.error("[PROD Diagnose] Interaction history Map lacks the required 'interaction_history' top-level key for case ID: {}", id);
            result.put("error", "交互历史结构无效 (缺少顶层键)");
            return null;
        }
        
        log.debug("[PROD Diagnose] Retrieved interaction history object (Map) for case ID: {}", id);
        return topLevelMap;
    }
    
    /**
     * 记录requestHistoryMap的详细结构
     */
    private void logRequestHistoryDetails(String id, Map<String, Object> requestHistoryMap) {
        try {
            log.debug("[PROD Diagnose] Detailed requestHistoryMap (inner history) structure for case ID {}: {}", 
                     id, objectMapper.writeValueAsString(requestHistoryMap));
            
            // 特别记录test_recommendation字段的数据类型和内容
            Object testRecom = requestHistoryMap.get("test_recommendation");
            if (testRecom != null) {
                log.debug("[PROD Diagnose] test_recommendation type: {}, value: {}", 
                          testRecom.getClass().getName(), 
                          objectMapper.writeValueAsString(testRecom));
            } else {
                log.debug("[PROD Diagnose] test_recommendation is null in requestHistoryMap for case ID: {}", id);
            }
            } catch (Exception e) {
                log.warn("[PROD Diagnose] Could not serialize requestHistoryMap for detailed logging for case ID {}: {}", id, e.getMessage());
        }
    }
    
    /**
     * 准备请求数据，确保格式正确
     */
    private Map<String, Object> prepareRequestPayload(Map<String, Object> requestHistoryMap, String id) {
        // 修复test_recommendation结构
        fixTestRecommendationStructure(requestHistoryMap, id);
        
        // 创建符合Python服务期望的请求格式
            Map<String, Object> requestPayload = new HashMap<>();
            requestPayload.put("interaction_history", requestHistoryMap);
            
        // 记录最终请求体
        logFinalRequestPayload(requestPayload, requestHistoryMap, id);
        
        return requestPayload;
    }
    
    /**
     * 修复test_recommendation数据结构
     */
    private void fixTestRecommendationStructure(Map<String, Object> requestHistoryMap, String id) {
        if (requestHistoryMap.containsKey("test_recommendation")) {
            Object testRecom = requestHistoryMap.get("test_recommendation");
            // 如果是列表类型，则检查其中的每个元素
            if (testRecom instanceof List) {
                List<?> testList = (List<?>) testRecom;
                List<Map<String, Object>> fixedList = new ArrayList<>();
                
                for (Object test : testList) {
                    if (test instanceof Map) {
                        // 如果已经是Map则保留
                        fixedList.add((Map<String, Object>)test);
                    } else if (test instanceof String) {
                        // 如果是字符串，创建一个包含该字符串的Map
                        Map<String, Object> testMap = new HashMap<>();
                        testMap.put("项目名称", test);
                        testMap.put("结果", "");
                        fixedList.add(testMap);
                        log.info("[PROD Diagnose] Converted string test item to Map: {}", test);
                    }
                    // 忽略不是Map或字符串的其他类型
                }
                
                // 替换原始test_recommendation
                requestHistoryMap.put("test_recommendation", fixedList);
                log.info("[PROD Diagnose] Fixed test_recommendation structure with {} items", fixedList.size());
            } else if (testRecom instanceof String) {
                // 如果整个test_recommendation是字符串，创建一个只有一个元素的列表
                List<Map<String, Object>> fixedList = new ArrayList<>();
                Map<String, Object> testMap = new HashMap<>();
                testMap.put("项目名称", testRecom);
                testMap.put("结果", "");
                fixedList.add(testMap);
                requestHistoryMap.put("test_recommendation", fixedList);
                log.info("[PROD Diagnose] Converted string test_recommendation to List with one Map");
            }
        }
    }
    
    /**
     * 记录最终请求数据
     */
    private void logFinalRequestPayload(Map<String, Object> requestPayload, Map<String, Object> requestHistoryMap, String id) {
        try {
            String requestPayloadJson = objectMapper.writeValueAsString(requestPayload);
            log.info("[PROD Diagnose] FINAL REQUEST PAYLOAD: {}", 
                     LogUtils.truncate(requestPayloadJson, 1000));
            
            // 特别关注test_recommendation
            if (requestHistoryMap.containsKey("test_recommendation")) {
                Object finalTestRecom = requestHistoryMap.get("test_recommendation");
                if (finalTestRecom != null) {
                    log.info("[PROD Diagnose] FINAL test_recommendation type: {}", finalTestRecom.getClass().getName());
                    if (finalTestRecom instanceof List) {
                        List<?> finalList = (List<?>) finalTestRecom;
                        if (!finalList.isEmpty()) {
                            log.info("[PROD Diagnose] First item type in test_recommendation: {}", 
                                     finalList.get(0) != null ? finalList.get(0).getClass().getName() : "null");
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("[PROD Diagnose] Error serializing final request payload: {}", e.getMessage());
        }
    }
    
    /**
     * 调用Python诊断服务
     */
    private ResponseEntity<String> callPythonDiagnoseService(String id, Map<String, Object> requestPayload) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        // 添加Connection: close头，避免CLOSE_WAIT问题
        headers.add("Connection", "close");
        
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestPayload, headers);

        log.info("[PROD Diagnose] Calling Python Diagnose service URL: {} for case ID: {}", diagnoseUrl, id);
    
        try {
            ResponseEntity<String> response = restTemplate.exchange(
                diagnoseUrl, 
                HttpMethod.POST, 
                requestEntity, 
                String.class
            );
            
            // 记录响应状态，确保调试信息完整
            log.debug("[PROD Diagnose] Received response status: {} for case ID: {}", 
                     response.getStatusCode(), id);
            return response;
        } catch (RestClientException e) {
            log.error("[PROD Diagnose] Error calling Python Diagnose service for case ID {}: {}", 
                     id, e.getMessage(), e);
            throw e; // 向上抛出以便在主方法中处理
        }
    }
    
    /**
     * 处理非OK响应
     */
    private Map<String, Object> handleNonOkResponse(ResponseEntity<String> responseEntity, String rawDiagnosisResponse, String id, Map<String, Object> result) {
        log.error("[PROD Diagnose] Python Diagnose service returned non-OK status: {} or empty body for case ID {}. Raw: {}", 
                 responseEntity.getStatusCode(), id, rawDiagnosisResponse);
        result.put("error", "诊断服务调用失败");
        result.put("status", responseEntity.getStatusCodeValue());
        // result.put("cot", rawDiagnosisResponse);
                return result;
            }

    /**
     * 解析Python响应
     */
    private Map<String, Object> parsePythonResponse(String rawDiagnosisResponse, String id, Map<String, Object> result) {
                log.info("[PROD Diagnose] Received OK response from Python Diagnose service for case ID {}: {}", id, 
                        LogUtils.truncate(rawDiagnosisResponse, 500));

        try {
            Map<String, Object> diagnosisResultMap = objectMapper.readValue(rawDiagnosisResponse, new TypeReference<Map<String, Object>>() {});
            
            // 检查Python响应中的test_recommendation是否有问题
            fixResponseTestRecommendation(diagnosisResultMap, id);

                if (diagnosisResultMap.isEmpty()) {
                    log.error("[PROD Diagnose] Parsed diagnose response map is empty for case ID {}. Raw: {}", id, rawDiagnosisResponse);
                    result.put("error", "诊断服务响应解析为空");
                    // result.put("cot", rawDiagnosisResponse);
                return null;
                }

                if (diagnosisResultMap.containsKey("error")) {
                    log.error("[PROD Diagnose] Python Diagnose service returned an error for case ID {}: {}", id, diagnosisResultMap.get("error"));
                    result.put("error", "诊断服务内部错误: " + diagnosisResultMap.get("error"));
                    // result.put("cot", rawDiagnosisResponse);
                return null;
            }
            
            return diagnosisResultMap;
        } catch (IOException e) {
            log.error("[PROD Diagnose] Failed to parse Python Diagnose response for case ID {}: {}", id, e.getMessage(), e);
            result.put("error", "解析诊断服务响应失败");
            // result.put("cot", rawDiagnosisResponse); // Include raw response for debugging
            return null;
        }
    }
    
    /**
     * 修复响应中的test_recommendation
     */
    private void fixResponseTestRecommendation(Map<String, Object> diagnosisResultMap, String id) {
        if (diagnosisResultMap.containsKey("test_recommendation")) {
            Object testRecomResult = diagnosisResultMap.get("test_recommendation");
            log.debug("[PROD Diagnose] Python response test_recommendation type: {}", 
                      testRecomResult != null ? testRecomResult.getClass().getName() : "null");
            
            // 如果是字符串而不是预期的列表结构，尝试将其转换为正确的格式
            if (testRecomResult instanceof String) {
                log.warn("[PROD Diagnose] Python response contains test_recommendation as String, attempting to fix");
                try {
                    // 尝试解析为列表
                    List<Map<String, Object>> parsedList = objectMapper.readValue((String)testRecomResult, 
                                                  new TypeReference<List<Map<String, Object>>>() {});
                    diagnosisResultMap.put("test_recommendation", parsedList);
                    log.info("[PROD Diagnose] Successfully parsed test_recommendation string to list");
                } catch (Exception e) {
                    log.error("[PROD Diagnose] Failed to parse test_recommendation string: {}", e.getMessage());
                    // 如果解析失败，设置为空列表以避免错误
                    diagnosisResultMap.put("test_recommendation", new ArrayList<>());
                }
            }
        }
    }
    
    /**
     * 构建诊断结果Map
     */
    private Map<String, Object> buildDiagnosisResultMap(Map<String, Object> diagnosisResultMap, Map<String, Object> requestHistoryMap, String id) {
        Map<String, Object> formattedDiagnosisMap = new LinkedHashMap<>();

        // 关键诊断信息
        formattedDiagnosisMap.put("diagnosis", diagnosisResultMap.get("diagnosis"));
        formattedDiagnosisMap.put("condition", diagnosisResultMap.get("condition"));
        // 使用清理后的治疗建议
        formattedDiagnosisMap.put("treatment_recommendation", diagnosisResultMap.get("treatment_recommendation"));
        // 添加新的推理过程字段
        formattedDiagnosisMap.put("reasoning_process", diagnosisResultMap.get("reasoning_process"));

        // 指南和辅助检查处理
        if (diagnosisResultMap.containsKey("treatment_guide")) {
            formattedDiagnosisMap.put("treatment_guide", diagnosisResultMap.get("treatment_guide"));
        }
        if (diagnosisResultMap.containsKey("guidelines_content_ref")) {
            formattedDiagnosisMap.put("guidelines_content_ref", diagnosisResultMap.get("guidelines_content_ref"));
        }
        if (diagnosisResultMap.containsKey("test_recommendation")) {
            formattedDiagnosisMap.put("test_recommendation", diagnosisResultMap.get("test_recommendation"));
        }

        // 从请求历史中提取观察值
        String secondLastObservation = InteractionHistoryUtils.extractSecondLastObservation(requestHistoryMap);
        if (secondLastObservation != null && !secondLastObservation.isEmpty()) {
            formattedDiagnosisMap.put("observation", secondLastObservation);
        }
        
        log.info("[PROD Diagnose] Built final diagnosis map for case ID: {}", id);
        return formattedDiagnosisMap;
    }
    
    /**
     * 更新数据库
     */
    private void updateDatabaseWithDiagnosisResults(String id, Map<String, Object> topLevelMap, 
                                                   Map<String, Object> formattedDiagnosisMap, Map<String, Object> result) {
                try {
                    // 获取内部的 interaction_history map 以便更新
                    Object innerHistoryObj = topLevelMap.get("interaction_history");
                     if (!(innerHistoryObj instanceof Map)) {
                        log.error("[PROD Diagnose] Cannot update DB - inner 'interaction_history' is not a Map for case ID: {}", id);
                        // 返回成功获取的诊断结果，但不更新DB
                         result.put("warning", "数据库交互历史结构错误，诊断结果未保存");
                return;
                     }
                     Map<String, Object> innerHistoryMap = (Map<String, Object>) innerHistoryObj;

                    // 将诊断结果的 key-value 更新到 innerHistoryMap 中
                    // 保留important字段：treatment_guide_ref和guidelines_content_ref
                    // 这些是MongoDB中存储的内容的引用
                    innerHistoryMap.putAll(formattedDiagnosisMap);
                    log.debug("[PROD Diagnose] Updated inner history map with diagnosis results for case ID: {}", id);
                    log.info("[PROD Diagnose] 保留了treatment_guide_ref和guidelines_content_ref字段，这些引用了存储在MongoDB中的内容");

                    // 使用更新后的 topLevelMap (包含了修改后的 innerHistoryMap) 更新数据库
                    int updateCount = caseItemMapper.updateInteractionHistoryById(id, topLevelMap);
                    if (updateCount > 0) {
                        log.info("[PROD Diagnose] Successfully updated database with diagnosis results for case ID: {}", id);
                        log.info("[PROD Diagnose] 完整的treatment_guide和guidelines_content内容已存储在MongoDB中，引用ID保存在PostgreSQL");
                    } else {
                        log.error("[PROD Diagnose] Failed to update database with diagnosis results for case ID {}. Update count was 0.", id);
                         // 仍然返回成功获取的诊断结果
                         result.put("warning", "数据库更新失败，诊断结果未保存");
                    }
                } catch (Exception dbEx) {
                    log.error("[PROD Diagnose] Error updating database with diagnosis results for case ID {}: {}", id, dbEx.getMessage(), dbEx);
                    // 仍然返回成功获取的诊断结果
                    result.put("warning", "数据库更新时出错，诊断结果可能未保存: " + dbEx.getMessage());
        }
    }

    /**
     * 重置指定案例的交互历史。
     * 将 interaction_history 字段更新为空的 JSON 对象 {}。
     *
     * @param id CaseItem 的唯一标识符。
     * @return 如果成功重置则返回 true，否则返回 false。
     */
    public boolean resetInteractionHistory(String id) {
        log.info("[PROD Reset] Attempting to reset interaction history for case ID: {}", id);
        if (id == null || id.trim().isEmpty()) {
            log.error("[PROD Reset] Case ID is required for reset.");
            return false; // Or throw IllegalArgumentException
        }

        try {
            // 1. 验证 CaseItem 是否存在 (可选，update本身会返回0行如果ID不存在)
            CaseItem existingItem = caseItemMapper.findById(id);
            if (existingItem == null) {
                log.warn("[PROD Reset] CaseItem not found for ID: {}. Cannot reset.", id);
                return false; // Indicate item not found specifically if needed
            }

            // 2. 准备一个空的 Map，代表空的 JSON 对象 {}
            Map<String, Object> emptyHistory = Collections.emptyMap();

            // 3. 调用 Mapper 更新数据库
            int updateCount = caseItemMapper.updateInteractionHistoryById(id, emptyHistory);

            if (updateCount > 0) {
                log.info("[PROD Reset] Successfully reset interaction history for case ID: {}", id);
                return true;
            } else {
                // updateCount == 0 意味着 ID 可能不存在，或者没发生实际变化 (理论上总会变化)
                log.warn("[PROD Reset] Failed to reset interaction history for case ID: {}. Update count was 0 (Item might not exist or history was already empty).", id);
                // 根据业务逻辑，如果ID确定存在但更新为0行，可能表示有并发问题或意外情况
                return false; // 返回 false 表示未成功更新
            }
        } catch (Exception e) {
            log.error("[PROD Reset] Unexpected error resetting interaction history for case ID {}: {}", id, e.getMessage(), e);
            return false; // Or rethrow a custom service exception
        }
    }

    /**
     * 添加检查建议到指定案例的交互历史中的 test_recommendation 列表 (生产环境)。
     *
     * @param id CaseItem 的唯一标识符。
     * @param testRecommendation 要添加的检查建议列表，每个建议是一个 Map。
     * @return 如果成功更新则返回 true，否则返回 false。
     */
    @Transactional // Ensure atomicity
    public boolean addTestRecommendationToInteractionHistory(String id, List<Map<String, Object>> testRecommendation) {
        log.info("[PROD Add TestRecommendation] Attempting to add {} test recommendations for case ID: {}", testRecommendation != null ? testRecommendation.size() : 0, id);
        if (id == null || id.trim().isEmpty()) {
            log.warn("[PROD Add TestRecommendation] Case ID is null or empty. No update performed.");
            return false;
        }
        if (testRecommendation == null || testRecommendation.isEmpty()) {
            log.warn("[PROD Add TestRecommendation] Test recommendation list (testRecommendation) is null or empty. No update performed for case ID: {}", id);
            return false;
        }

        try {
            CaseItem caseItem = caseItemMapper.findById(id);
            if (caseItem == null) {
                log.error("[PROD Add TestRecommendation] CaseItem not found for ID: {}", id);
                return false;
            }

            Object historyDbObject = caseItem.getInteractionHistory();
            Map<String, Object> topLevelMap;

            if (historyDbObject instanceof Map) {
                topLevelMap = (Map<String, Object>) historyDbObject;
                log.debug("[PROD Add TestRecommendation] Retrieved existing interaction history object (Map) for case ID: {}", id);
            } else {
                log.warn("[PROD Add TestRecommendation] Interaction history is missing or not a Map for case ID: {}. Initializing a new structure.", id);
                topLevelMap = new HashMap<>(); // Initialize if missing
            }

            // Ensure the main 'interaction_history' key exists and is a Map
            Object innerHistoryObj = topLevelMap.computeIfAbsent("interaction_history", k -> createInitialInnerInteractionHistoryProd());
            if (!(innerHistoryObj instanceof Map)) {
                 log.error("[PROD Add TestRecommendation] Could not get or create a valid inner 'interaction_history' Map for case ID: {}. Type was: {}. Aborting.", id, innerHistoryObj != null ? innerHistoryObj.getClass().getName() : "null");
                 return false; // Critical failure if inner history cannot be established as a Map
            }
            Map<String, Object> innerHistoryMap = (Map<String, Object>) innerHistoryObj;

            // Get or create the 'test_recommendation' list within the INNER map
            Object existingRecommendationsObj = innerHistoryMap.get("test_recommendation");
            List<Map<String, Object>> targetRecommendationList;

            if (existingRecommendationsObj instanceof List) {
                try {
                    targetRecommendationList = (List<Map<String, Object>>) existingRecommendationsObj;
                    log.debug("[PROD Add TestRecommendation] Appending to existing 'test_recommendation' list (size: {}) for case ID: {}", targetRecommendationList.size(), id);
                } catch (ClassCastException e) {
                    log.warn("[PROD Add TestRecommendation] Existing 'test_recommendation' is a List, but not of Map<String, Object> for case ID: {}. Creating new list.", id, e);
                    targetRecommendationList = new ArrayList<>();
                    innerHistoryMap.put("test_recommendation", targetRecommendationList);
                }
            } else {
                if (existingRecommendationsObj != null) {
                    log.warn("[PROD Add TestRecommendation] 'test_recommendation' exists but is not a List (Type: {}) for case ID: {}. Creating new list.", existingRecommendationsObj.getClass().getName(), id);
                }
                targetRecommendationList = new ArrayList<>();
                innerHistoryMap.put("test_recommendation", targetRecommendationList);
                log.debug("[PROD Add TestRecommendation] Created new 'test_recommendation' list for case ID: {}", id);
            }

            // Add all new test recommendations to the list
            targetRecommendationList.addAll(testRecommendation);
            log.info("[PROD Add TestRecommendation] Added {} new test recommendations. New list size: {} for case ID: {}", testRecommendation.size(), targetRecommendationList.size(), id);

            // 确保添加的每个测试推荐都包含"检查"字段
            for (Map<String, Object> test : testRecommendation) {
                if (test.containsKey("项目名称") && !test.containsKey("检查")) {
                    test.put("检查", test.get("项目名称"));
                }
            }

            // Update the database with the modified top-level map
            int updateCount = caseItemMapper.updateInteractionHistoryById(id, topLevelMap);
            if (updateCount > 0) {
                log.info("[PROD Add TestRecommendation] Successfully updated database for case ID: {}", id);
                return true;
            } else {
                log.error("[PROD Add TestRecommendation] Failed to update database for case ID: {}. Update count was 0.", id);
                return false;
            }
        } catch (Exception e) {
            log.error("[PROD Add TestRecommendation] Unexpected error adding test recommendations for case ID {}: {}", id, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 调用Python拟诊接口，获取拟诊结果，并更新数据库（生产环境）
     *
     * @param id CaseItem的唯一标识符
     * @return 包含拟诊结果和观察内容的Map，或包含错误信息的Map
     */
    public Map<String, Object> getPreliminaryDiagnosisProd(String id) {
        // 统计总耗时
        long totalStart = System.currentTimeMillis();
        log.info("[PROD Preliminary Diagnosis] Starting preliminary diagnosis process for case ID: {}", id);
        Map<String, Object> result = new HashMap<>();

        if (id == null || id.trim().isEmpty()) {
            log.error("[PROD Preliminary Diagnosis] Case ID is required.");
            result.put("error", "缺少案例ID");
            return result;
        }

        try {
            // --- 1. 获取并验证CaseItem ---
            CaseItem caseItem = caseItemMapper.findById(id);
            if (caseItem == null) {
                log.error("[PROD Preliminary Diagnosis] CaseItem not found for ID: {}", id);
                result.put("error", "未找到案例记录");
                return result;
            }

            // --- 2. 获取交互历史 ---
            Object historyDbObject = caseItem.getInteractionHistory();
            if (!(historyDbObject instanceof Map)) {
                log.error("[PROD Preliminary Diagnosis] Interaction history is missing or not a Map for case ID: {}", id);
                result.put("error", "交互历史不存在或格式无效");
                return result;
            }
            
            Map<String, Object> topLevelMap = (Map<String, Object>) historyDbObject;
            if (!topLevelMap.containsKey("interaction_history")) {
                log.error("[PROD Preliminary Diagnosis] Interaction history Map lacks the required 'interaction_history' top-level key for case ID: {}", id);
                result.put("error", "交互历史结构无效（缺少顶层键）");
                return result;
            }

            Map<String, Object> requestHistoryMap = (Map<String, Object>) topLevelMap.get("interaction_history");
            
            // --- 3. 准备请求体 ---
            Map<String, Object> requestPayload = new HashMap<>();
            requestPayload.put("interaction_history", requestHistoryMap);
            
            // --- 4. 调用Python拟诊服务 ---
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            // 添加Connection: close头，避免CLOSE_WAIT问题
            headers.add("Connection", "close");
            
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestPayload, headers);

            log.info("[PROD Preliminary Diagnosis] Calling Python service URL: {} for case ID: {}", pythonProdPreliminaryDiagnosisUrl, id);
            
            ResponseEntity<String> response = null;
            try {
                long pythonStart = System.currentTimeMillis();
                response = restTemplate.exchange(
                    pythonProdPreliminaryDiagnosisUrl, HttpMethod.POST, requestEntity, String.class
                );
                long pythonDuration = System.currentTimeMillis() - pythonStart;
                log.info("[PROD Preliminary Diagnosis] Python service call duration: {} ms for case ID: {}", pythonDuration, id);
                
                // 确保完全消费响应体，避免连接泄漏
                String responseBody = response.getBody();
                if (responseBody != null) {
                    // 已经完全读取了响应体
                    log.debug("[PROD Preliminary Diagnosis] Response fully consumed for case ID: {}", id);
                }
            } catch (RestClientException e) {
                log.error("[PROD Preliminary Diagnosis] Error calling Python service for case ID {}: {}", id, e.getMessage(), e);
                result.put("error", "拟诊服务调用失败: " + e.getMessage());
                return result;
            }
            
            // --- 5. 处理Python响应 ---
            String rawResponse = response.getBody();
            if (response.getStatusCode() != HttpStatus.OK || rawResponse == null) {
                log.error("[PROD Preliminary Diagnosis] Python service returned non-OK status: {} or empty body for case ID {}", response.getStatusCode(), id);
                result.put("error", "拟诊服务返回错误状态码: " + response.getStatusCode());
                // result.put("raw_response", rawResponse);
                return result;
            }
            
            // --- 6. 解析Python响应 ---
            Map<String, Object> pythonResponseMap;
            try {
                pythonResponseMap = objectMapper.readValue(rawResponse, new TypeReference<Map<String, Object>>() {});
            } catch (IOException e) {
                log.error("[PROD Preliminary Diagnosis] Failed to parse Python response for case ID {}: {}", id, e.getMessage(), e);
                result.put("error", "解析拟诊服务响应失败: " + e.getMessage());
                // result.put("raw_response", rawResponse);
                return result;
            }
            
            // --- 7. 处理guidelines_content - 添加到MongoDB ---
            if (pythonResponseMap.containsKey("guidelines_content")) {
                try {
                    Object guidelinesApiContent = pythonResponseMap.get("guidelines_content");
                    log.debug("[PROD Preliminary Diagnosis] 从API接收到指南内容，案例ID: {}", id);

                    GuidelinesContent existingContent = null;
                    try {
                        // 创建查询对象
                        Query query = new Query();
                        query.addCriteria(Criteria.where("caseId").is(id));
                        existingContent = mongoTemplate.findOne(query, GuidelinesContent.class, "guidelines_content");
                    } catch (Exception findEx) {
                        log.error("[PROD Preliminary Diagnosis] 查询MongoDB失败，案例ID: {}, 错误: {}", id, findEx.getMessage(), findEx);
                    }

                    String savedContentId;
                    // 尝试更新或插入内容到MongoDB
                    try {
                        if (existingContent != null) {
                            log.info("[PROD Preliminary Diagnosis] 找到案例 {} 已存在的指南内容记录 ID: {}，准备更新", id, existingContent.getId());
                            existingContent.setContent(guidelinesApiContent);
                            existingContent.setCreatedAt(LocalDateTime.now());
                            mongoTemplate.save(existingContent, "guidelines_content");
                            savedContentId = existingContent.getId();
                            log.info("[PROD Preliminary Diagnosis] 已更新MongoDB中的指南内容，案例ID: {}, 引用ID: {}", id, savedContentId);
                        } else {
                            log.info("[PROD Preliminary Diagnosis] 未找到案例 {} 的指南内容记录，创建新记录", id);
                            GuidelinesContent newContent = new GuidelinesContent();
                            newContent.setId(UUID.randomUUID().toString());
                            newContent.setCaseId(id);
                            newContent.setContent(guidelinesApiContent);
                            newContent.setCreatedAt(LocalDateTime.now());
                            mongoTemplate.insert(newContent, "guidelines_content");
                            savedContentId = newContent.getId();
                            log.info("[PROD Preliminary Diagnosis] 已在MongoDB中创建新的指南内容，案例ID: {}, 引用ID: {}", id, savedContentId);
                        }
                        pythonResponseMap.put("guidelines_content_ref", savedContentId);
                        pythonResponseMap.remove("guidelines_content");
                        log.debug("[PROD Preliminary Diagnosis] 已将 guidelines_content 替换为引用 ID: {}", savedContentId);
                    } catch (Exception saveEx) {
                        log.error("[PROD Preliminary Diagnosis] 保存/更新指南内容到MongoDB失败，案例ID: {}，错误: {}", id, saveEx.getMessage(), saveEx);
                        log.info("[PROD Preliminary Diagnosis] MongoDB操作失败，guidelines_content将保留在响应中");
                        // 如果无法保存到MongoDB，不删除guidelines_content
                    }
                } catch (Exception e) {
                    log.error("[PROD Preliminary Diagnosis] 处理指南内容时发生异常，案例ID: {}，错误: {}", id, e.getMessage(), e);
                }
            }
            
            // --- 8. 提取所需字段 ---
            String observation = (String) pythonResponseMap.get("observation");
            String preliminaryDiagnosis = (String) pythonResponseMap.get("preliminary_diagnosis");
            String inspectionSuggestions = (String) pythonResponseMap.get("inspection_suggestions");
            
            if (preliminaryDiagnosis == null || preliminaryDiagnosis.trim().isEmpty()) {
                log.error("[PROD Preliminary Diagnosis] Python response does not contain valid preliminary_diagnosis for case ID: {}", id);
                result.put("error", "拟诊服务返回的拟诊结果为空");
                return result;
            }
            
            // --- 9. 更新数据库 ---
            Map<String, Object> innerHistoryMap = (Map<String, Object>) topLevelMap.get("interaction_history");
            innerHistoryMap.put("preliminary_diagnosis", preliminaryDiagnosis);
            
            // 同时更新检查建议字段
            if (inspectionSuggestions != null && !inspectionSuggestions.trim().isEmpty()) {
                innerHistoryMap.put("inspection_suggestions", inspectionSuggestions);
                log.info("[PROD Preliminary Diagnosis] Added inspection_suggestions to database for case ID: {}", id);
            } else {
                log.warn("[PROD Preliminary Diagnosis] inspectionSuggestions is empty for case ID: {}", id);
            }
            
            int updateCount = caseItemMapper.updateInteractionHistoryById(id, topLevelMap);
            if (updateCount <= 0) {
                log.error("[PROD Preliminary Diagnosis] Failed to update database for case ID: {}", id);
                result.put("warning", "数据库更新失败，拟诊结果未保存");
            } else {
                log.info("[PROD Preliminary Diagnosis] Database updated successfully with preliminary_diagnosis and inspection_suggestions for case ID: {}", id);
            }
            
            // --- 10. 跳过生成音频URL ---
            log.info("[PROD Preliminary Diagnosis] TTS generation skipped for case ID: {} (audio not required)", id);

            // --- 11. 构建返回结果 ---
            result.put("one_chief_complaint_and_five_histories", observation);
            result.put("preliminary_diagnosis", preliminaryDiagnosis);
            result.put("inspection_suggestions", inspectionSuggestions);
            result.put("content", "拟诊完成");
            
            long totalDuration = System.currentTimeMillis() - totalStart;
            log.info("[PROD Preliminary Diagnosis] Preliminary diagnosis successful for case ID: {} (Total duration: {} ms)", id, totalDuration);
            return result;
            
        } catch (Exception e) {
            log.error("[PROD Preliminary Diagnosis] Unexpected error during preliminary diagnosis for case ID {}: {}", id, e.getMessage(), e);
            result.put("error", "拟诊处理发生内部错误: " + e.getMessage());
            return result;
        }
    }

    /**
     * 创建一个新的 CaseItem 条目。
     *
     * @param id CaseItem 的唯一标识符。
     * @return 包含操作结果的 Map。
     */
    public Map<String, Object> createCaseItem(String id) {
        log.info("[PROD Create CaseItem] Attempting to create CaseItem with ID: {}", id);
        Map<String, Object> result = new HashMap<>();

        if (id == null || id.trim().isEmpty()) {
            log.error("[PROD Create CaseItem] Case ID is required.");
            result.put("error", "案例ID不能为空");
            result.put("success", false);
            return result;
        }

        try {
            // 1. 检查 ID 是否已存在
            CaseItem existingItem = caseItemMapper.findById(id);
            if (existingItem != null) {
                log.warn("[PROD Create CaseItem] CaseItem with ID: {} already exists.", id);
                result.put("error", "案例ID已存在");
                result.put("success", false);
                return result;
            }

            // 2. 使用 JDBC try-with-resources，显式将空 JSON 写入 JSONB 字段
            String sql = "INSERT INTO \"case_item\" (id, created_at, updated_at, profile, interaction_history) " +
                    "VALUES (?, NOW(), NOW(), '{}'::jsonb, '{}'::jsonb)";

            try (Connection conn = dataSource.getConnection();
                 PreparedStatement pstmt = conn.prepareStatement(sql)) {

                pstmt.setString(1, id);
                int insertCount = pstmt.executeUpdate();

                if (insertCount > 0) {
                    log.info("[PROD Create CaseItem] Successfully created CaseItem with ID: {} using direct SQL", id);
                    result.put("success", true);
                    result.put("message", "案例创建成功");
                    result.put("id", id);
                } else {
                    log.error("[PROD Create CaseItem] Failed to create CaseItem with ID: {} using direct SQL. Update count was 0.", id);
                    result.put("error", "案例创建失败，数据库插入操作未成功");
                    result.put("success", false);
                }
            } catch (SQLException e) {
                log.error("[PROD Create CaseItem] SQL error creating CaseItem with ID {}: {}", id, e.getMessage(), e);
                result.put("error", "创建案例时发生SQL错误: " + e.getMessage());
                result.put("success", false);
            }
        } catch (Exception e) {
            log.error("[PROD Create CaseItem] Unexpected error creating CaseItem with ID {}: {}", id, e.getMessage(), e);
            result.put("error", "创建案例时发生内部错误: " + e.getMessage());
            result.put("success", false);
        }
        return result;
    }

    /**
     * 获取指定案例的interaction_history字段。
     *
     * @param id CaseItem的唯一标识符。
     * @return 包含操作结果的Map，成功时包含interaction_history。
     */
    public Map<String, Object> getInteractionHistory(String id) {
        log.info("Service: Getting interaction history for case ID: {}", id);
        CaseItem caseItem = caseItemMapper.findById(id);

        if (caseItem == null) {
            log.warn("Service: CaseItem not found for ID: {}", id);
            return null; // 或者可以抛出自定义异常
        }

        // 确保 interactionHistory 是 Map 类型
        Object historyObj = caseItem.getInteractionHistory();
        if (historyObj instanceof Map) {
            return (Map<String, Object>) historyObj;
        } else if (historyObj != null) {
            // 如果不是 Map 但不为 null，可能需要转换或记录错误
            log.warn("Service: interaction_history for case ID {} is not of expected Map type. Type: {}", id, historyObj.getClass().getName());
            // 尝试进行转换
            try {
                return objectMapper.convertValue(historyObj, new TypeReference<Map<String, Object>>() {});
            } catch (IllegalArgumentException e) {
                log.error("Service: Failed to convert interaction_history to Map for case ID {}: {}", id, e.getMessage());
                return Map.of("error", "Invalid history format");
            }
        }

        return new HashMap<>(); // 返回一个空 Map
    }

    public PageInfo<Map<String, Object>> getAllInteractionHistories(ZonedDateTime startDate, ZonedDateTime endDate, int page, int size) {
        log.info("Fetching all interaction histories with pagination. Page: {}, Size: {}, StartDate: {}, EndDate: {}", page, size, startDate, endDate);

        long offset = (long) (page - 1) * size;
        Date start = (startDate != null) ? Date.from(startDate.toInstant()) : null;
        Date end = (endDate != null) ? Date.from(endDate.toInstant()) : null;

        List<CaseItem> items = caseItemMapper.findAllWithFiltersAndPagination(start, end, size, offset);
        long total = caseItemMapper.countAllWithFilters(start, end);

        List<Map<String, Object>> histories = items.stream()
                .map(this::convertCaseItemToHistoryMap)
                .collect(Collectors.toList());

        PageInfo<Map<String, Object>> pageInfo = new PageInfo<>();
        pageInfo.setList(histories);
        pageInfo.setTotal(total);
        pageInfo.setPageNum(page);
        pageInfo.setPageSize(size);

        return pageInfo;
    }

    public List<Map<String, Object>> getAllInteractionHistories(ZonedDateTime startDate, ZonedDateTime endDate) {
            List<CaseItem> caseItems = caseItemMapper.findAll(startDate, endDate);
            return caseItems.stream()
                .map(this::convertCaseItemToHistoryMap)
                .collect(Collectors.toList());
    }

    private Map<String, Object> convertCaseItemToHistoryMap(CaseItem item) {
                        Map<String, Object> historyMap = new HashMap<>();
                        historyMap.put("id", item.getId());
        historyMap.put("created_at", item.getCreatedAt());
        historyMap.put("updated_at", item.getUpdatedAt());

        // 安全地处理 interaction_history
        Object rawHistory = item.getInteractionHistory();
        if (rawHistory instanceof Map) {
            historyMap.put("interaction_history", rawHistory);
        } else if (rawHistory != null) {
            try {
                // 尝试将 PGobject 或 String 解析为 Map
                String jsonString = (rawHistory instanceof PGobject) ? ((PGobject) rawHistory).getValue() : rawHistory.toString();
                Map<String, Object> parsedHistory = objectMapper.readValue(jsonString, new TypeReference<>() {});
                historyMap.put("interaction_history", parsedHistory);
        } catch (Exception e) {
                log.warn("Error parsing interaction_history for case ID {}: {}", item.getId(), e.getMessage());
                historyMap.put("interaction_history", new HashMap<>()); // 出错时返回空对象
        }
        } else {
            historyMap.put("interaction_history", new HashMap<>()); // 为 null 时返回空对象
        }

        return historyMap;
    }

    @PreDestroy
    public void shutdownExecutorService() {
        log.info("Shutting down inquiryExecutorService...");
        inquiryExecutorService.shutdown();
        log.info("MessageService: Inquiry ExecutorService shut down.");
    }

    /**
     * 快速AI问诊服务 - 与Python服务进行快速对话
     * 优化版：增强JSONB处理能力、医生消息提取和错误处理
     * 
     * @param id 案例ID
     * @param userMessage 用户消息
     * @return SseEmitter 流式响应
     */
    public SseEmitter aiDoctorQuickInquiry(String id, String userMessage) {
        SseEmitter emitter = new SseEmitter(300000L); // 5分钟超时
        
        inquiryExecutorService.execute(() -> {
            String rawPythonResponseStr = null;
            Map<String, Object> pythonResponseMap = null;
            
            try {
                // 1. 获取案例并验证
                CaseItem caseItem = getCaseItemAndValidate(id, emitter);
                if (caseItem == null) {
                    return; // 已经发送了错误响应
                }
                
                log.info("[PROD Quick Inquiry] 开始为案例ID:{} 处理快速问诊请求", id);
                
                // 2. 获取或初始化交互历史 - 增强JSONB处理能力
                Map<String, Object> interactionHistory = null;
                Object historyData = caseItem.getInteractionHistory();
                
                // 适应多种可能的数据类型情况
                if (historyData == null) {
                    log.info("[PROD Quick Inquiry] 案例ID:{} 没有现有交互历史，创建新结构", id);
                    interactionHistory = createInitialInnerInteractionHistoryProd();
                } else if (historyData instanceof Map) {
                    log.debug("[PROD Quick Inquiry] 案例ID:{} 从Map对象获取交互历史", id);
                    Map<String, Object> historyMap = (Map<String, Object>) historyData;
                    
                    // 检查是否有嵌套的interaction_history
                    if (historyMap.containsKey("interaction_history") && historyMap.get("interaction_history") instanceof Map) {
                        interactionHistory = (Map<String, Object>) historyMap.get("interaction_history");
                        log.debug("[PROD Quick Inquiry] 案例ID:{} 从顶层Map中提取嵌套的交互历史", id);
                    } else if (historyMap.containsKey("cot_entries")) {
                        // 可能是扁平结构，直接使用
                        interactionHistory = historyMap;
                        log.debug("[PROD Quick Inquiry] 案例ID:{} 使用直接的扁平结构交互历史", id);
                    } else {
                        log.warn("[PROD Quick Inquiry] 案例ID:{} 交互历史Map缺少预期字段，创建新结构", id);
                        interactionHistory = createInitialInnerInteractionHistoryProd();
                    }
                } else if (historyData instanceof String) {
                    // JSONB可能被直接读取为字符串
                    log.debug("[PROD Quick Inquiry] 案例ID:{} 尝试解析字符串形式的交互历史", id);
                    String jsonStr = (String) historyData;
                    
                    try {
                        if (jsonStr.trim().isEmpty() || "null".equals(jsonStr) || "{}".equals(jsonStr)) {
                            interactionHistory = createInitialInnerInteractionHistoryProd();
                        } else {
                            // 尝试解析JSON字符串
                            Map<String, Object> parsedMap = objectMapper.readValue(jsonStr, 
                                                new TypeReference<Map<String, Object>>() {});
                            
                            // 检查嵌套结构同上
                            if (parsedMap.containsKey("interaction_history") && parsedMap.get("interaction_history") instanceof Map) {
                                interactionHistory = (Map<String, Object>) parsedMap.get("interaction_history");
                            } else if (parsedMap.containsKey("cot_entries")) {
                                interactionHistory = parsedMap;
                            } else {
                                interactionHistory = createInitialInnerInteractionHistoryProd();
                            }
                        }
                    } catch (Exception e) {
                        log.error("[PROD Quick Inquiry] 案例ID:{} 解析JSON字符串失败: {}", id, e.getMessage());
                        interactionHistory = createInitialInnerInteractionHistoryProd();
                    }
                } else {
                    log.warn("[PROD Quick Inquiry] 案例ID:{} 交互历史类型意外: {}, 创建新结构", 
                             id, historyData.getClass().getName());
                    interactionHistory = createInitialInnerInteractionHistoryProd();
                }
                
                // 确保cot_entries存在
                if (!interactionHistory.containsKey("cot_entries")) {
                    log.warn("[PROD Quick Inquiry] 案例ID:{} 交互历史缺少cot_entries字段，添加空列表", id);
                    interactionHistory.put("cot_entries", new ArrayList<>());
                }
                
                // 3. 将用户消息添加到交互历史
                addPatientMessageProd(interactionHistory, userMessage);
                log.debug("[PROD Quick Inquiry] 案例ID:{} 已添加用户消息到交互历史", id);
                
                // 4. 准备请求体
                Map<String, Object> requestPayload = new HashMap<>();
                requestPayload.put("interaction_history", interactionHistory);
                
                try {
                    // 记录请求细节（调试模式）
                    if (log.isDebugEnabled()) {
                        String requestJson = objectMapper.writeValueAsString(requestPayload);
                        int maxLogLen = 1000;
                        log.debug("[PROD Quick Inquiry] 案例ID:{} 发送请求到Python服务: {}", 
                                id, requestJson.length() > maxLogLen ? 
                                    requestJson.substring(0, maxLogLen) + "..." : requestJson);
                    }
                } catch (Exception e) {
                    log.warn("[PROD Quick Inquiry] 案例ID:{} 序列化请求时出错: {}", id, e.getMessage());
                }
                
                // 5. 调用Python服务 - 使用快速问诊URL
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                // 添加Connection: close头，避免CLOSE_WAIT问题
                headers.add("Connection", "close");
                
                HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestPayload, headers);
                
                ResponseEntity<String> responseEntity = null;
                try {
                    log.info("[PROD Quick Inquiry] 案例ID:{} 调用Python快速问诊服务: {}", id, pythonProdQuickInquiryUrl);
                    
                    responseEntity = restTemplate.exchange(
                        pythonProdQuickInquiryUrl,
                        HttpMethod.POST,
                        requestEntity,
                        String.class
                    );
                    
                    rawPythonResponseStr = responseEntity.getBody();
                    
                    // 确保完全消费响应体，避免连接泄漏
                    if (rawPythonResponseStr != null) {
                        // 已经完全读取了响应体
                        log.debug("[PROD Quick Inquiry] Response fully consumed for case ID: {}", id);
                    }
                    
                    // 检查HTTP状态和响应主体
                    if (responseEntity.getStatusCode() != HttpStatus.OK) {
                        log.error("[PROD Quick Inquiry] 案例ID:{} Python服务返回非成功状态码: {}", id, responseEntity.getStatusCode());
                        sendSseError(emitter, "AI服务返回错误状态: " + responseEntity.getStatusCode(), rawPythonResponseStr);
                        return;
                    }
                    
                    if (rawPythonResponseStr == null || rawPythonResponseStr.trim().isEmpty()) {
                        log.error("[PROD Quick Inquiry] 案例ID:{} Python服务返回空响应", id);
                        sendSseError(emitter, "AI服务返回空响应", "");
                        return;
                    }
                    
                    log.debug("[PROD Quick Inquiry] 案例ID:{} 收到Python响应: {}", 
                            id, LogUtils.truncate(rawPythonResponseStr, 200));
                    
                    // 6. 解析响应
                    try {
                        pythonResponseMap = objectMapper.readValue(rawPythonResponseStr, 
                                                 new TypeReference<Map<String, Object>>() {});
                        
                        if (pythonResponseMap == null || pythonResponseMap.isEmpty()) {
                            log.error("[PROD Quick Inquiry] 案例ID:{} Python响应解析为空Map", id);
                            sendSseError(emitter, "AI服务返回无效数据", rawPythonResponseStr);
                            return;
                        }
                        
                        // 检查是否包含错误信息
                        if (pythonResponseMap.containsKey("error")) {
                            String errorMsg = String.valueOf(pythonResponseMap.get("error"));
                            log.error("[PROD Quick Inquiry] 案例ID:{} Python服务返回错误: {}", id, errorMsg);
                            sendSseError(emitter, "AI服务错误: " + errorMsg, rawPythonResponseStr);
                            return;
                        }
                        
                    } catch (Exception e) {
                        log.error("[PROD Quick Inquiry] 案例ID:{} 解析Python响应时出错: {}", id, e.getMessage());
                        sendSseError(emitter, "无法解析AI服务响应: " + e.getMessage(), rawPythonResponseStr);
                        return;
                    }
                    
                    // 7. 更新数据库
                    updateDatabaseWithResponse(id, pythonResponseMap);
                    log.debug("[PROD Quick Inquiry] 案例ID:{} 已更新数据库", id);
                    
                    // 8. 提取医生消息 - 增强的提取和错误处理
                    String doctorMessage = extractLastDoctorMessageProd(pythonResponseMap);
                    
                    // 如果标准提取失败，尝试替代方法
                    if (doctorMessage == null || doctorMessage.isEmpty()) {
                        log.warn("[PROD Quick Inquiry] 案例ID:{} 使用标准方法无法提取医生消息，尝试替代方法", id);
                        
                        // 方法1: 检查是否有顶层"interaction_history"
                        if (pythonResponseMap.containsKey("interaction_history")) {
                            Map<String, Object> innerHistory = (Map<String, Object>) pythonResponseMap.get("interaction_history");
                            doctorMessage = extractLastDoctorMessageProd(innerHistory);
                            
                            if (doctorMessage != null) {
                                log.info("[PROD Quick Inquiry] 案例ID:{} 从内部interaction_history成功提取医生消息", id);
                            }
                        }
                        
                        // 方法2: 直接搜索任何包含"message"的字段
                        if (doctorMessage == null) {
                            for (String key : pythonResponseMap.keySet()) {
                                if (key.contains("message") && pythonResponseMap.get(key) instanceof String) {
                                    doctorMessage = (String) pythonResponseMap.get(key);
                                    log.info("[PROD Quick Inquiry] 案例ID:{} 从字段'{}'提取到消息", id, key);
                                    break;
                                }
                            }
                        }
                        
                        // 方法3: 最后的回退，使用完整响应作为医生消息
                        if (doctorMessage == null) {
                            doctorMessage = "拟诊完成";
                        }
                    }
                    
                    // 9. 确定目标类型
                    int target = determineTargetFromResponse(pythonResponseMap, id);
                    
                    // 9.1 如果是拟诊状态(target=3)，确保inspection_suggestions也被更新到数据库
                    if (target == 3) {
                        try {
                            // 提取preliminary_diagnosis和inspection_suggestions
                            String preliminaryDiagnosis = null;
                            String inspectionSuggestions = null;
                            
                            // 首先尝试从pythonResponseMap直接获取
                            if (pythonResponseMap.containsKey("preliminary_diagnosis")) {
                                preliminaryDiagnosis = (String) pythonResponseMap.get("preliminary_diagnosis");
                            }
                            
                            if (pythonResponseMap.containsKey("inspection_suggestions")) {
                                inspectionSuggestions = (String) pythonResponseMap.get("inspection_suggestions");
                            }
                            
                            // 如果在顶层没有找到，检查是否有interaction_history嵌套结构
                            if ((preliminaryDiagnosis == null || inspectionSuggestions == null) && 
                                pythonResponseMap.containsKey("interaction_history")) {
                                Map<String, Object> innerHistory = (Map<String, Object>) pythonResponseMap.get("interaction_history");
                                
                                if (preliminaryDiagnosis == null && innerHistory.containsKey("preliminary_diagnosis")) {
                                    preliminaryDiagnosis = (String) innerHistory.get("preliminary_diagnosis");
                                }
                                
                                if (inspectionSuggestions == null && innerHistory.containsKey("inspection_suggestions")) {
                                    inspectionSuggestions = (String) innerHistory.get("inspection_suggestions");
                                }
                            }
                            
                            // 再次更新数据库以确保包含inspection_suggestions
                            if (inspectionSuggestions != null && !inspectionSuggestions.trim().isEmpty()) {
                                // 获取最新的数据库对象
                                CaseItem updatedCaseItem = caseItemMapper.findById(id);
                                if (updatedCaseItem != null) {
                                    Object updatedHistoryObj = updatedCaseItem.getInteractionHistory();
                                    if (updatedHistoryObj instanceof Map) {
                                        Map<String, Object> updatedTopLevelMap = (Map<String, Object>) updatedHistoryObj;
                                        if (updatedTopLevelMap.containsKey("interaction_history")) {
                                            Map<String, Object> updatedInnerHistoryMap = (Map<String, Object>) updatedTopLevelMap.get("interaction_history");
                                            
                                            // 更新inspection_suggestions
                                            updatedInnerHistoryMap.put("inspection_suggestions", inspectionSuggestions);
                                            
                                            // 如果preliminary_diagnosis也存在且当前数据库中没有，也更新它
                                            if (preliminaryDiagnosis != null && !preliminaryDiagnosis.trim().isEmpty() && 
                                                !updatedInnerHistoryMap.containsKey("preliminary_diagnosis")) {
                                                updatedInnerHistoryMap.put("preliminary_diagnosis", preliminaryDiagnosis);
                                            }
                                            
                                            // 保存到数据库
                                            int updateCount = caseItemMapper.updateInteractionHistoryById(id, updatedTopLevelMap);
                                            if (updateCount > 0) {
                                                log.info("[PROD Quick Inquiry] 案例ID:{} 成功更新检查建议到数据库", id);
                                            } else {
                                                log.warn("[PROD Quick Inquiry] 案例ID:{} 更新检查建议到数据库失败", id);
                                            }
                                        }
                                    }
                                }
                            }
                        } catch (Exception e) {
                            log.error("[PROD Quick Inquiry] 案例ID:{} 更新检查建议时出错: {}", id, e.getMessage(), e);
                            // 继续处理，不中断响应流程
                        }
                    }
                    
                    // 10. 发送响应
                    try {
                        // 当target=2时，使用固定文本"请医生查体"作为TTS内容
                        String ttsContent = (target == 2) ? "请医生查体" : doctorMessage;
                        if (target == 2) {
                            log.info("[PROD Quick Inquiry] 案例ID:{} Target=2，使用固定TTS文本：请医生查体", id);
                        }
                        
                        String audioUrl = null;
                        if (target != 3) { // 诊断阶段(target=3)不生成语音
                            // 当target=2时，使用固定文本"请医生查体"作为TTS内容
                            ttsContent = (target == 2) ? "请医生查体" : doctorMessage;
                            if (target == 2) {
                                log.info("[PROD Quick Inquiry] 案例ID:{} Target=2，使用固定TTS文本：请医生查体", id);
                            }
                            
                            audioUrl = ttsService.generateTtsUrl(ttsContent);
                            log.info("[PROD Quick Inquiry] 案例ID:{} 生成TTS URL: {}", id, audioUrl);
                        } else {
                            log.info("[PROD Quick Inquiry] 案例ID:{}, Target=3, skipping TTS generation for diagnosis.", id);
                        }
                        
                        Map<String, Object> responseData = new HashMap<>();
                        responseData.put("content", doctorMessage);
                        responseData.put("target", target);
                        if (audioUrl != null) responseData.put("audioUrl", audioUrl);
                        
                        // 处理target=3的特殊响应字段
                        if (target == 3) {
                            addTarget3ResponseFields(responseData, pythonResponseMap, id);
                        }
                        
                        String jsonData = objectMapper.writeValueAsString(responseData);
                        emitter.send(SseEmitter.event().data(jsonData).id(String.valueOf(System.currentTimeMillis())).name("message"));
                        
                        // 发送DONE事件
                        emitter.send(SseEmitter.event().data("[DONE]"));
                        log.info("[PROD Quick Inquiry] 案例ID:{} 成功发送SSE响应", id);
                    } catch (Exception e) {
                        log.error("[PROD Quick Inquiry] 案例ID:{} 发送SSE响应时出错: {}", id, e.getMessage(), e);
                        sendSseError(emitter, "生成或发送响应时出错: " + e.getMessage(), "");
                    }
                    
                } catch (RestClientException e) {
                    log.error("[PROD Quick Inquiry] 案例ID:{} 连接Python服务失败: {}", id, e.getMessage(), e);
                    sendSseError(emitter, "无法连接到AI服务: " + e.getMessage(), "");
                }
            } catch (Exception e) {
                log.error("[PROD Quick Inquiry] 案例ID:{} 快速问诊过程中出现意外错误: {}", id, e.getMessage(), e);
                handleUnexpectedError(emitter, id, e, rawPythonResponseStr, pythonResponseMap);
            } finally {
                try {
                    // 直接尝试完成emitter，如果已经完成会抛出异常，会被catch捕获
                    if (emitter != null) {
                        emitter.complete();
                        log.debug("[PROD Quick Inquiry] 案例ID:{} 完成SSE发射器", id);
                    }
                } catch (Exception e) {
                    log.warn("[PROD Quick Inquiry] 案例ID:{} 完成SSE发射器时出错: {}", id, e.getMessage());
                }
            }
        });
        
        return emitter;
    }

    /**
     * 初始化交互历史，设置一个包含3条消息的交互历史：初始医生问候、病人回复和新的医生提问。
     *
     * @param id          CaseItem 的唯一标识符。
     * @param userMessage 用户发送的初始消息。
     * @return 用于流式传输响应的 SseEmitter。
     */
    public SseEmitter initialInteractionHistory(String id, String userMessage, Integer flag) {
        SseEmitter emitter = new SseEmitter(300000L);

        inquiryExecutorService.execute(() -> {
            try {
                log.info("[PROD Initial] Starting initialization for case ID: {}, flag: {}", id, flag);

                // --- 1. 获取并验证 CaseItem ---
                CaseItem caseItem = getCaseItemAndValidate(id, emitter);
                if (caseItem == null) return;

                // --- 1.1 (新增逻辑) 提取现有的 test_recommendation ---
                List<Map<String, Object>> existingTestRecommendations = Collections.emptyList();
                try {
                    Map<String, Object> existingHistory = getOrInitializeInteractionHistory(caseItem, id);
                    Object recommendationsObj = existingHistory.get("test_recommendation");
                    if (recommendationsObj instanceof List) {
                        existingTestRecommendations = (List<Map<String, Object>>) recommendationsObj;
                        if (!existingTestRecommendations.isEmpty()) {
                            log.info("[PROD Initial] Found {} existing test recommendations to preserve for case ID: {}", existingTestRecommendations.size(), id);
                        }
                    }
                } catch (Exception e) {
                    log.warn("[PROD Initial] Could not extract existing test recommendations for case ID: {}. Reason: {}", id, e.getMessage());
                }

                // --- 2. 创建初始化的交互历史结构 ---
                Map<String, Object> interactionHistory = createInitialInnerInteractionHistoryProd();
                log.debug("[PROD Initial] Created fresh interaction history structure for case ID: {}", id);

                // --- 2.1 (新增逻辑) 如果存在，则恢复 test_recommendation ---
                if (!existingTestRecommendations.isEmpty()) {
                    interactionHistory.put("test_recommendation", existingTestRecommendations);
                    log.info("[PROD Initial] Restored {} test recommendations into new history for case ID: {}", existingTestRecommendations.size(), id);
                }

                // --- 3. 获取第一个cot条目并添加病人消息 ---
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> cotEntries = (List<Map<String, Object>>) interactionHistory.get("cot_entries");
                if (cotEntries == null || cotEntries.isEmpty()) {
                    log.warn("[PROD Initial] No cot_entries in the initial history. Creating one.");
                    cotEntries = new ArrayList<>();
                    Map<String, Object> cotEntry = new LinkedHashMap<>();
                    cotEntry.put("dialogue_history", new ArrayList<>());
                    cotEntry.put("feedback", "");
                    cotEntry.put("observation", "");
                    cotEntry.put("reasoning", "");
                    cotEntry.put("strategy", "");
                    cotEntries.add(cotEntry);
                    interactionHistory.put("cot_entries", cotEntries);
                }

                Map<String, Object> firstCotEntry = cotEntries.get(0);
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> dialogueHistory = (List<Map<String, Object>>) firstCotEntry.get("dialogue_history");
                
                // 确保对话历史中有初始医生问候
                if (dialogueHistory == null || dialogueHistory.isEmpty()) {
                    dialogueHistory = new ArrayList<>();
                    Map<String, Object> initialDoctorMessage = new LinkedHashMap<>();
                    initialDoctorMessage.put("content", "您好，我是本次的医生，请问孩子的年龄，性别，主要的症状是什么？");
                    initialDoctorMessage.put("role", "doctor");
                    dialogueHistory.add(initialDoctorMessage);
                    firstCotEntry.put("dialogue_history", dialogueHistory);
                }

                // --- 4. 添加病人回复消息 ---
                Map<String, Object> patientMsg = new LinkedHashMap<>();
                patientMsg.put("content", userMessage);
                patientMsg.put("role", "patient");
                dialogueHistory.add(patientMsg);
                log.debug("[PROD Initial] Added patient message to dialogue history for case ID: {}", id);

                // --- 5. 根据 flag 添加新的医生提问消息 ---
                String doctorQuestion;
                if (flag != null && flag == 2) {
                    doctorQuestion = "孩子现在哪里不舒服呢？孩子或家长可以点击【开始说话】按钮后对着我回答，回答完毕请点击下面的\"说完了\"";
                } else { // 默认为 flag=1 的情况
                    doctorQuestion = "孩子现在哪里不舒服呢？孩子或家长可以直接对着我回答，回答完毕请点击下面的\"说完了\"";
                }
                
                Map<String, Object> newDoctorMsg = new LinkedHashMap<>();
                newDoctorMsg.put("content", doctorQuestion);
                newDoctorMsg.put("role", "doctor");
                dialogueHistory.add(newDoctorMsg);
                log.debug("[PROD Initial] Added new doctor question to dialogue history for case ID: {}", id);

                // --- 6. 准备顶层Map ---
                Map<String, Object> topLevelMap = new HashMap<>();
                topLevelMap.put("interaction_history", interactionHistory);

                // --- 7. 更新数据库 ---
                int updateCount = caseItemMapper.updateInteractionHistoryById(id, topLevelMap);
                if (updateCount > 0) {
                    log.info("[PROD Initial] Database updated successfully for case ID {}", id);
                } else {
                    log.warn("[PROD Initial] Failed to update database for case ID {}. Update count: {}", id, updateCount);
                    sendSseError(emitter, "{\"error\":\"Failed to update case in database\"}", null);
                    return;
                }

                // --- 8. 生成音频URL并发送响应 ---
                String audioUrl = ttsService.generateTtsUrl(doctorQuestion);
                log.info("[PROD Initial] Generated TTS URL for case ID {}: {}", id, audioUrl);

                Map<String, Object> responseData = new HashMap<>();
                responseData.put("content", doctorQuestion);
                responseData.put("target", 1); // 默认target为1，表示指向病人
                if (audioUrl != null) responseData.put("audioUrl", audioUrl);
                
                String jsonData = objectMapper.writeValueAsString(responseData);
                emitter.send(SseEmitter.event().data(jsonData).id(String.valueOf(System.currentTimeMillis())).name("message"));
                log.info("[PROD Initial] Sent message event via SSE for case ID {}", id);
                
                // 发送DONE事件，确保客户端知道流已结束
                try {
                    emitter.send(SseEmitter.event().data("[DONE]"));
                    log.info("[PROD Initial] 发送DONE事件");
                } catch (Exception doneEx) {
                    log.warn("[PROD Initial] 发送DONE事件失败: {}", doneEx.getMessage());
                }

                emitter.complete();
                log.info("[PROD Initial] Completed initialization for case ID {}", id);

            } catch (Exception e) {
                log.error("[PROD Initial] Error during initialization for case ID {}: {}", id, e.getMessage(), e);
                try {
                    sendSseError(emitter, "{\"error\":\"Initialization failed: " + escapeJson(e.getMessage()) + "\"}", null);
                } catch (Exception ignored) {}
            }
        });

        return emitter;
    }

    /**
     * 发送默认欢迎消息，并使用该消息初始化交互历史。
     *
     * @param id CaseItem 的唯一标识符。
     * @return 用于流式传输响应的 SseEmitter。
     */
    public SseEmitter sendDefaultWelcomeMessage(String id) {
        SseEmitter emitter = new SseEmitter(300000L);

        inquiryExecutorService.execute(() -> {
            try {
                log.info("[PROD Welcome] Starting default welcome message for case ID: {}", id);

                // --- 1. 获取并验证 CaseItem ---
                CaseItem caseItem = getCaseItemAndValidate(id, emitter);
                if (caseItem == null) return;

                // --- 2. 定义欢迎消息 ---
                String welcomeMessage = "我是北京儿童医院的AI儿科医生--贝医生，\n接下来我将问您几个问题，再由现场诊室医生做检查就可以帮您诊断啦。";

                // --- 3. 生成音频URL并发送响应 ---
                String audioUrl = ttsService.generateTtsUrl(welcomeMessage);
                log.info("[PROD Welcome] Generated TTS URL for case ID {}: {}", id, audioUrl);

                Map<String, Object> responseData = new HashMap<>();
                responseData.put("content", welcomeMessage);
                responseData.put("target", 1); // 默认target为1，表示指向病人
                if (audioUrl != null) responseData.put("audioUrl", audioUrl);

                String jsonData = objectMapper.writeValueAsString(responseData);
                emitter.send(SseEmitter.event().data(jsonData).id(String.valueOf(System.currentTimeMillis())).name("message"));
                log.info("[PROD Welcome] Sent message event via SSE for case ID {}", id);

                // --- 4. 发送DONE事件 ---
                try {
                    emitter.send(SseEmitter.event().data("[DONE]"));
                    log.info("[PROD Welcome] Sent [DONE] event");
                } catch (Exception doneEx) {
                    log.warn("[PROD Welcome] Failed to send [DONE] event: {}", doneEx.getMessage());
                }

                emitter.complete();
                log.info("[PROD Welcome] Completed request for case ID {}", id);

            } catch (Exception e) {
                log.error("[PROD Welcome] Error during welcome message for case ID {}: {}", id, e.getMessage(), e);
                try {
                    sendSseError(emitter, "{\"error\":\"Welcome message failed: " + escapeJson(e.getMessage()) + "\"}", null);
                } catch (Exception ignored) {}
            }
        });

        return emitter;
    }
}
