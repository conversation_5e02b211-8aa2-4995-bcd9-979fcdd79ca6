# AI Doctor Prod API 文档

## 简介

本文档描述了 AI Doctor 生产环境后端 (Java/Spring Boot) 提供的主要 API 接口。这些接口负责处理用户交互、调用后端 Python AI 服务进行问诊和诊断、以及处理语音和图像识别相关功能。

**基础 URL:** (请根据实际部署情况填写，例如 `http://<您的服务器IP>:9000`)

**强烈建议:** 优先查阅项目自动生成的 Swagger UI 文档以获取最准确、最实时和可交互的 API 信息： [`http://<您的服务器IP>:9000/swagger-ui/index.html`](http://<您的服务器IP>:9000/swagger-ui/index.html)

## 认证

(本文档未包含认证信息，请参考实际部署)

## 统一响应格式 (Result<T>)

所有接口（除获取音频文件外）通常遵循统一的 JSON 响应格式：

```json
{
  "success": true, // 或 false
  "message": "操作结果描述",
  "data": { ... } // 实际业务数据，类型取决于具体接口，可能为 null
}
```

## 核心概念

### InteractionHistory 对象 (通过接口传递和更新)

用于贯穿整个问诊流程，记录对话历史、AI 思考过程、测试建议等。在与 Python 服务交互时，Java 后端会处理这个对象的 JSON 表示，通常存储在数据库的 `case_item` 表中。其内部结构大致如下（基于 Python 端推断）：

```json
{
  "cot_entries": [
    {
      "dialogue_history": [
        {"role": "doctor", "content": "医生的问题或陈述"},
        {"role": "patient", "content": "患者的回答"}
        // ... 更多对话
      ],
      "strategy": "当前轮次的问诊策略",
      "reasoning": "生成该策略的原因",
      "observation": "基于本轮对话的观察总结",
      "feedback": "关于本轮问诊效果的反馈"
    }
    // ... 更多轮次
  ],
  "diagnosis": "最终诊断结果（如果有）",
  "test_recommendation": [
    {"检查": "检查项目名称", "结果": "检查结果或描述"}
    // ... 更多检查建议
  ],
  "treatment_recommendation": "治疗建议（如果有）",
  "preliminary_diagnosis": "初步诊断（可能在过程中生成）",
  "doctor_supplementary_info": [
    // ... 医生补充的信息
  ],
  "lastObservation": "最新的观察结果（可能用于诊断）"
}
```

## 主要 API 接口

### MessageController (AI 问诊与诊断)

#### 1. AI 问诊对话 (流式 SSE)

*   **功能:** 根据病例 ID 和用户消息，与 AI 模型进行流式问诊对话 (Server-Sent Events)。后端会更新交互历史并调用 Python `/ai_doctor_v2_inquiry` 服务。
*   **HTTP 方法:** `GET`
*   **URL 路径:** `/message/inquiry`
*   **请求参数 (Query Parameters):**
    *   `id` (String, **必须**): CaseItem 的唯一标识符 (例如: `case_uuid_123`)。
    *   `message` (String, **必须**): 用户发送给 AI 的消息内容 (例如: `孩子发烧三天了`)。
*   **成功响应 (SSE Stream):**
    *   状态码: `200 OK`
    *   `Content-Type`: `text/event-stream`
    *   **事件流格式:** 服务器会推送多个事件，每个事件包含 `data:` 字段，其内容为 JSON 字符串，结构可能类似 Python 返回值 (具体字段需参考 MessageService 实现和 Python 端)：
        ```json
        // 单个事件的 data 字段内容示例
        {
            "interaction_history": { ... }, // 部分或完整的更新后交互历史
            "content": "AI医生的回复片段",
            "audioUrl": "/audio/...", // 对应语音片段URL
            "cot": "...", // 思维链信息
            // ... 其他字段
        }
        ```
        流结束时会发送特定事件或关闭连接。
*   **失败响应:**
    *   状态码: `400 Bad Request` (参数无效), `404 Not Found` (ID 不存在), `500 Internal Server Error`

#### 2. 添加医生补充信息

*   **功能:** 向指定案例的交互历史中添加医生提供的补充信息。
*   **HTTP 方法:** `POST`
*   **URL 路径:** `/message/add_supplementary_info`
*   **请求参数 (Form Data / Query Parameters):**
    *   `id` (String, **必须**): CaseItem 的唯一标识符。
    *   `supplementary_info` (String, **必须**): 医生补充的信息。
*   **成功响应 (`ResponseEntity<String>`)**
    *   状态码: `200 OK`
    *   Body: `"补充信息添加成功"`
*   **失败响应 (`ResponseEntity<String>`)**
    *   状态码: `400 Bad Request` (参数为空), `404 Not Found` (ID 不存在，可能返回 500), `500 Internal Server Error`
    *   Body: 错误描述信息

#### 3. 生成诊断报告

*   **功能:** 根据案例 ID 获取完整的问诊历史，请求 AI 生成最终的诊断、病情描述和治疗建议，并将结果更新到数据库。此接口会调用后端的 Python `/ai_doctor_v2_diagnosis` 服务。
*   **HTTP 方法:** `POST`
*   **URL 路径:** `/message/diagnose`
*   **请求参数 (Query Parameter):**
    *   `id` (String, **必须**): CaseItem 的唯一标识符。
*   **成功响应 (`ResponseEntity<Map<String, Object>>`)**
    *   状态码: `200 OK`
    *   Body (JSON, 直接来自 Python `/ai_doctor_v2_diagnosis` 返回):
        ```json
        {
          "diagnosis": "最终诊断结果",
          "condition": "病情描述",
          "treatment_recommendation": "治疗建议"
          // 可能包含 cot 等其他字段
        }
        ```
*   **失败响应 (`ResponseEntity<Map<String, Object>>`)**
    *   状态码: `400 Bad Request` (ID 为空), `404 Not Found`, `500 Internal Server Error`, `503 Service Unavailable` (Python 服务连接失败)
    *   Body (JSON): `{"error": "错误描述信息"}`

#### 4. 重置案例交互历史

*   **功能:** 将指定案例的交互历史清空（设置为 `{}`）。
*   **HTTP 方法:** `POST`
*   **URL 路径:** `/message/reset`
*   **请求参数 (Query Parameter):**
    *   `id` (String, **必须**): 需要重置交互历史的 CaseItem 的唯一标识符。
*   **成功响应 (`ResponseEntity<Map<String, Object>>`)**
    *   状态码: `200 OK`
    *   Body (JSON):
        ```json
        {
          "success": true,
          "message": "交互历史已成功重置"
        }
        ```
*   **失败响应 (`ResponseEntity<Map<String, Object>>`)**
    *   状态码: `400 Bad Request` (ID 为空), `404 Not Found` (根据 Controller 逻辑，可能返回 500), `500 Internal Server Error`
    *   Body (JSON): `{\"success\": false, \"message\": \"错误描述信息\"}`

### TtsController (语音合成)

#### 5. 基础文本转语音

*   **功能:** 将文本转换为语音，使用默认参数进行转换。
*   **HTTP 方法:** `POST`
*   **URL 路径:** `/api/tts/convert`
*   **请求参数 (Query Parameter):**
    *   `text` (String, **必须**): 需要转换为语音的文本内容。
*   **成功响应 (`Map<String, Object>`)**
    *   状态码: `200 OK` (通过外层框架)
    *   Body (JSON):
        ```json
        {
          "success": true,
          "message": "转换成功",
          "data": "/audio/generated_audio_file.mp3" // 生成的音频文件相对路径 (String)
        }
        ```
*   **失败响应 (`Map<String, Object>`)**
    *   状态码: 通常是 `200 OK` (通过外层框架)，需要检查 `success` 字段。
    *   Body (JSON):
        ```json
        {
          "success": false,
          "message": "转换失败: <错误原因>"
        }
        ```

#### 6. 高级文本转语音

*   **功能:** 使用自定义参数（语速、音量、音高、发音人）将文本转换为语音。
*   **HTTP 方法:** `POST`
*   **URL 路径:** `/api/tts/convert/advanced`
*   **请求 Body (JSON):**
    ```json
    {
      "text": "需要转换为语音的文本内容", // 必须
      "speedRatio": 1.0, // 可选, 0.5-2.0
      "volumeRatio": 1.0, // 可选, 0.5-2.0
      "pitchRatio": 1.0, // 可选, 0.5-2.0
      "voiceType": "female" // 可选, 具体可用值参考 TTS 服务
    }
    ```
*   **成功响应 (`Map<String, Object>`)**
    *   状态码: `200 OK` (通过外层框架)
    *   Body (JSON):
        ```json
        {
          "success": true,
          "message": "转换成功",
          "data": "/audio/generated_audio_file.mp3" // 生成的音频文件相对路径 (String)
        }
        ```
*   **失败响应 (`Map<String, Object>`)**
    *   状态码: 通常是 `200 OK` (通过外层框架)，需要检查 `success` 字段。
    *   Body (JSON):
        ```json
        {
          "success": false,
          "message": "转换失败: <错误原因>"
        }
        ```

### ASRController (语音识别)

#### 7. 上传音频并开始识别

*   **功能:** 上传音频文件，服务器保存文件后立即开始后台识别，并返回用于后续建立 SSE 连接的 `audioId`。
*   **HTTP 方法:** `POST`
*   **URL 路径:** `/api/prod/asr/upload_and_recognize`
*   **请求 Body:** `multipart/form-data`
    *   `audio`: 包含音频数据的文件部分 (**必须**)。支持 `.wav`, `.mp3`, `.pcm`, `.m4a` 等（具体看服务端转换逻辑）。
*   **成功响应 (`ResponseEntity<String>`)**
    *   状态码: `200 OK`
    *   Body (JSON String): `"{"audioId": "generated_uuid_string"}"`
*   **失败响应 (`ResponseEntity<String>`)**
    *   状态码: `400 Bad Request` (文件为空), `500 Internal Server Error`
    *   Body (JSON String): `"{"error": "错误描述"}"`

#### 8. 获取语音识别结果 (流式 SSE)

*   **功能:** 使用上传时获取的 `audioId` 建立 Server-Sent Events 连接，实时接收语音识别结果。
*   **HTTP 方法:** `GET`
*   **URL 路径:** `/api/prod/asr/recognize/stream/{audioId}`
    *   `{audioId}` (String, **必须**): 从 `/upload_and_recognize` 接口获取的 ID。
*   **成功响应 (SSE Stream):**
    *   状态码: `200 OK`
    *   `Content-Type`: `text/event-stream`
    *   **事件流格式:** 服务器会推送多个事件，包括：
        *   `name: sse_connected`: 表示连接建立。
        *   `name: partial_result` / `name: full_result` / etc.: (具体事件名称取决于 ASRService 实现) 包含识别文本片段或完整结果。`data:` 字段通常是包含文本的 JSON 字符串或纯文本。
        *   `name: error`: 推送错误信息。
        *   流结束时会关闭连接或发送特定结束事件。
*   **失败响应 (SSE Stream):**
    *   如果 `audioId` 无效或任务已结束，会返回一个立即完成的 SSE 流，并可能包含一个 `name: error` 事件。

### OcrController (图像文字识别)

#### 9. 处理医疗报告图像识别

*   **功能:** 上传医疗报告图片进行 OCR 识别，并将提取的检查结果更新到指定案例的交互历史中。此接口会调用后端的 Python `/ocr_api` 服务。
*   **HTTP 方法:** `POST`
*   **URL 路径:** `/ocr/process`
*   **请求 Body:** `multipart/form-data`
    *   `image`: 包含医疗报告图像的文件部分 (**必须**)。
    *   `id` (String, **必须**): CaseItem 的唯一标识符。
    *   `name` (String, 可选): 用户名或其他标识符（可能用于 OCR 内部校验）。
*   **成功响应 (`ResponseEntity<Map<String, Object>>`)**
    *   状态码: `200 OK`
    *   Body (JSON):
        ```json
        {
          "content": { ... } // 直接来自 Python `/ocr_api` 返回的识别结果 JSON 对象
        }
        ```
*   **失败响应 (`ResponseEntity<Map<String, Object>>`)**
    *   状态码: `400 Bad Request` (参数缺失、OCR 识别失败如"识别失败"/"姓名错误"), `404 Not Found` (ID 不存在), `500 Internal Server Error`, `503 Service Unavailable` (Python 服务连接失败)
    *   Body (JSON): `{"error": "错误描述信息"}`

### 音频文件服务 (WebConfig)

#### 10. 获取音频文件

*   **功能:** 根据相对路径获取 TTS 生成的音频文件。
*   **HTTP 方法:** `GET`
*   **URL 路径:** `/audio/{filename}` (基于 `application.yml` 中的 `tts.audio.baseUrl` 和 `WebConfig` 配置)
    *   `{filename}`: 音频文件的名称，例如 `d8f472e7-6239-483c-a100-07669a2ac1d6.mp3`
*   **成功响应:**
    *   状态码: `200 OK`
    *   `Content-Type`: `audio/mpeg` (或其他对应的音频类型)
    *   Body: 音频文件的二进制数据
*   **失败响应:**
    *   状态码: `404 Not Found` (文件不存在)

## 错误处理

API 在发生错误时通常会返回相应的 HTTP 状态码。对于返回 `ResponseEntity` 的接口，响应体通常包含错误信息。对于返回 `Map` 的接口，错误信息可能包含在 Map 中（例如 `{"success": false, "message": "..."}` 或 `{"error": "..."}`）。对于 SSE 接口，错误通过特定的事件推送。常见的错误状态码包括 400 (客户端请求错误), 404 (资源未找到), 500 (服务器内部错误), 503 (服务不可用)。

---

**再次强调:** 这份文档是根据 Controller 代码进行的更新，但最准确、最全面的信息请参考项目自动生成的 Swagger UI 文档：[`http://<您的服务器IP>:9000/swagger-ui/index.html`](http://<您的服务器IP>:9000/swagger-ui/index.html)
