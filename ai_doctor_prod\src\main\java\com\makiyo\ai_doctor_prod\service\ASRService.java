package com.makiyo.ai_doctor_prod.service;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
// import com.makiyo.ai_doctor_prod.service.ASRService; // No longer needed
import com.makiyo.ai_doctor_prod.utils.AudioConverter;
import okhttp3.*;
import okhttp3.logging.HttpLoggingInterceptor;
import okio.ByteString;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service; // Import @Service
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.PreDestroy;
import javax.sound.sampled.AudioFormat;
import javax.sound.sampled.AudioInputStream;
import javax.sound.sampled.AudioSystem;
import javax.sound.sampled.UnsupportedAudioFileException;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;

/**
 * 语音识别服务类 (Prod) - 合并了接口与实现
 * 基于火山引擎的大模型流式语音识别API
 */
@Service // 添加 @Service 注解
public class ASRService { // 修改为 class
    private static final Logger logger = LoggerFactory.getLogger(ASRService.class);
    
    // --- 协议常量 --- 
    private static final byte PROTOCOL_VERSION = 0b0001;
    private static final byte DEFAULT_HEADER_SIZE = 0b0001;
    private static final byte FULL_CLIENT_REQUEST = 0b0001;
    private static final byte AUDIO_ONLY_REQUEST = 0b0010;
    private static final byte FULL_SERVER_RESPONSE = 0b1001;
    private static final byte SERVER_ACK = 0b1011;
    private static final byte SERVER_ERROR_RESPONSE = 0b1111;
    private static final byte NO_SEQUENCE = 0b0000;
    private static final byte POS_SEQUENCE = 0b0001;
    private static final byte NEG_SEQUENCE = 0b0010;
    private static final byte NEG_WITH_SEQUENCE = 0b0011;
    private static final byte NO_SERIALIZATION = 0b0000;
    private static final byte JSON = 0b0001;
    private static final byte NO_COMPRESSION = 0b0000;
    private static final byte GZIP = 0b0001;
    
    // --- 配置注入 --- 
    @Value("${asr.api.url:wss://openspeech.bytedance.com/api/v3/sauc/bigmodel}")
    private String apiUrl;
    
    @Value("${asr.api.appId}")
    private String appId;
    
    @Value("${asr.api.token}")
    private String token;
    
    @Value("${asr.temp.dir:${java.io.tmpdir}/asr_prod}")
    private String configuredTempDir;
    
    // --- 成员变量 --- 
    private final Gson gson = new Gson();
    private OkHttpClient okHttpClient;
    private final ExecutorService asrExecutor = Executors.newCachedThreadPool();

    @Autowired
    private AudioConverter audioConverter;

    // --- 初始化 --- 
    public ASRService() {
        HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor(message -> logger.debug("OkHttp: {}", message));
        loggingInterceptor.setLevel(HttpLoggingInterceptor.Level.HEADERS);
        
        this.okHttpClient = new OkHttpClient.Builder()
                .pingInterval(60, TimeUnit.SECONDS)
                .addInterceptor(loggingInterceptor)
                .readTimeout(180, TimeUnit.SECONDS)
                .writeTimeout(180, TimeUnit.SECONDS)
                .connectTimeout(30, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .build();
    }

    // --- 公开的服务方法 --- 
    /**
     * 异步处理音频文件并通过SSE发射器流式返回识别结果
     * 
     * @param initialAudioFile 初始上传的音频文件
     * @param emitter          SSE发射器
     */
    @Async // 使用 Spring 的 @Async 进行异步处理
    public void processAudioFile(File initialAudioFile, SseEmitter emitter) {
        logger.info("Async ASR process started for file: {}", initialAudioFile.getName());
        File tempDirFile = new File(configuredTempDir);
        if (!tempDirFile.exists()) {
            if (!tempDirFile.mkdirs()) {
                logger.error("无法创建配置的临时目录: {}", configuredTempDir);
                 sendErrorAndComplete(emitter, "无法创建服务临时目录");
                // 注意：此时 initialAudioFile 的删除职责在 Controller
                return;
            }
        }
        
        String convertedFileName = UUID.randomUUID() + "_16k.wav";
        File convertedFile = new File(tempDirFile, convertedFileName);
        AudioInputStream ins = null;

        try {
            if (initialAudioFile == null || !initialAudioFile.exists() || !initialAudioFile.canRead()) {
                 throw new IOException("传入的音频文件无效或不可读: " + (initialAudioFile != null ? initialAudioFile.getAbsolutePath() : "null"));
            }
            logger.info("开始处理音频文件: {}, 大小: {} 字节", initialAudioFile.getAbsolutePath(), initialAudioFile.length());
            
            logger.info("开始转换音频格式至: {}", convertedFile.getAbsolutePath());
            audioConverter.convertToStandardWav(initialAudioFile, convertedFile);
            logger.info("音频格式转换完成, 转换后文件大小: {} bytes", convertedFile.length());
            
            if (!convertedFile.exists() || convertedFile.length() == 0) {
                throw new IOException("音频转换失败，转换后的文件不存在或为空");
            }
            
            logger.info("准备连接 ASR WebSocket 服务...");
            ins = AudioSystem.getAudioInputStream(convertedFile);
            AudioFormat format = ins.getFormat();

            final Request request = new Request.Builder()
                    .url(apiUrl)
                    .header("X-Api-App-Key", appId)
                    .header("X-Api-Access-Key", token)
                    .header("X-Api-Resource-Id", "volc.bigasr.sauc.duration")
                    .header("X-Api-Connect-Id", UUID.randomUUID().toString())
                    .build();

            // 创建并启动 WebSocket 连接, ins 会被监听器接管并关闭
            okHttpClient.newWebSocket(request, new ASRWebSocketListener(ins, format, emitter, convertedFile)); // 传入 convertedFile 用于清理
            // ASRWebSocketListener 会在处理结束后调用 emitter.complete()

        } catch (UnsupportedAudioFileException e) {
             logger.error("不支持的音频文件格式: {}", initialAudioFile.getAbsolutePath(), e);
             sendErrorAndComplete(emitter, "不支持的音频文件格式: " + e.getMessage());
             deleteSafely(convertedFile); // 转换出错也要清理转换后的文件
             // 初始文件由 Controller 清理
        } catch (IOException e) {
            logger.error("处理音频文件 IO 异常", e);
            sendErrorAndComplete(emitter, "处理音频文件 IO 失败: " + e.getMessage());
            deleteSafely(convertedFile); 
        } catch (Exception e) {
            logger.error("处理音频文件时发生未知错误", e);
            sendErrorAndComplete(emitter, "语音识别处理失败: " + e.getMessage());
            deleteSafely(convertedFile);
        } finally {
            // 如果 ins 未能传递给 Listener (例如 getAudioInputStream 失败)，在此关闭
            if (ins != null && isAudioInputStreamOpen(ins)) { // 检查流是否仍然打开
                 try {
                     logger.debug("Closing AudioInputStream in finally block as it was not passed or closed by listener.");
                     ins.close();
                 } catch (IOException e) {
                     logger.warn("关闭 AudioInputStream (finally) 失败", e);
                 }
            } else if (ins == null) {
                 // 如果 ins 为 null (例如转换失败)，确保删除 convertedFile
                 logger.debug("AudioInputStream was null in finally block, ensuring converted file is deleted.");
                 deleteSafely(convertedFile);
            }
            // 初始文件 (initialAudioFile) 的删除由 Controller 负责
        }
    }

    // 检查 AudioInputStream 是否"打开"（这是一个简化的检查）
    private boolean isAudioInputStreamOpen(AudioInputStream stream) {
        try {
            // 尝试读取一个字节，如果流关闭了会抛出异常
            // 注意：这可能会消耗一个字节，但在流结束或关闭时通常是安全的
            // 更可靠的方法是跟踪状态，但 AudioInputStream 没有直接的 isOpen() 方法
             stream.mark(1);
             int byteRead = stream.read();
             stream.reset();
            return byteRead != -1; // 如果能读到字节（包括0），则认为它还开着
        } catch (IOException e) {
            // IOException 通常表示流已关闭
            logger.trace("isAudioInputStreamOpen check failed, likely closed: {}", e.getMessage());
            return false;
        }
    }


    // --- 内部辅助方法 --- 
    private void sendErrorAndComplete(SseEmitter emitter, String errorMessage) {
        try {
            if (emitter != null) { // Add null check for emitter
               emitter.send(SseEmitter.event().name("error").data(errorMessage));
               emitter.complete();
            } else {
                logger.warn("Attempted to send error on a null emitter: {}", errorMessage);
            }
        } catch (IOException | IllegalStateException e) {
            logger.warn("发送错误事件或完成 Emitter 失败: {}", errorMessage, e);
            // 尝试再次完成，以防万一
            try { 
                 if (emitter != null) emitter.complete(); 
            } catch (Exception ignored) {}
        }
    }

    private void deleteSafely(File file) {
        if (file != null && file.exists()) {
            try {
                 if (file.delete()) {
                    logger.info("已删除临时文件: {}", file.getAbsolutePath());
                 } else {
                    logger.warn("无法删除临时文件: {}", file.getAbsolutePath());
                 }
            } catch (SecurityException e) {
                 logger.error("删除文件时出现安全异常: {}", file.getAbsolutePath(), e);
            } catch (Exception e) { // Catch broader exceptions
                 logger.error("删除文件时发生未知错误: {}", file.getAbsolutePath(), e);
            }
        }
    }
    
    // --- WebSocket 监听器内部类 --- 
    private class ASRWebSocketListener extends WebSocketListener {
        private final AudioInputStream audioInputStream;
        private final AudioFormat audioFormat;
        private final SseEmitter emitter;
        private final File convertedAudioFile; // 持有对转换文件的引用，以便清理
        private byte[] buffer;
        private int bufferSize;
        private int seq = 0;
        private final AtomicInteger messageCount = new AtomicInteger(0);
        private volatile boolean finishedProcessing = false;

        public ASRWebSocketListener(AudioInputStream audioInputStream, AudioFormat audioFormat, SseEmitter emitter, File convertedAudioFile) {
            this.audioInputStream = audioInputStream;
            this.audioFormat = audioFormat;
            this.emitter = emitter;
            this.convertedAudioFile = convertedAudioFile; // 保存引用

            long frameLength = audioInputStream.getFrameLength();
            // Use a reasonable default chunk size if frameLength is unknown
            int framesPerChunk = (frameLength == AudioSystem.NOT_SPECIFIED || frameLength <= 0) ? 4096 : (int) Math.min(frameLength, 4096);
            this.bufferSize = (audioFormat.getSampleSizeInBits() / 8) * audioFormat.getChannels() * framesPerChunk;
             // Ensure bufferSize is at least some minimal value
             if (this.bufferSize <= 0) { 
                 logger.warn("Calculated buffer size is invalid ({}), using default 8192.", this.bufferSize);
                 this.bufferSize = 8192; // Default reasonable size
             }
            this.buffer = new byte[this.bufferSize];
            logger.debug("ASRWebSocketListener created with buffer size: {}", this.bufferSize);
        }
        
        @Override
        public void onOpen(WebSocket webSocket, Response response) {
            try {
                String logId = response.header("X-Tt-Logid");
                logger.info("ASR WebSocket 连接已打开, Log ID: {}", logId);
                sendFullClientRequest(webSocket);
                emitter.send(SseEmitter.event().name("connected").data("语音识别服务连接成功"));
            } catch (Exception e) {
                logger.error("WebSocket onOpen 阶段发生错误", e);
                closeResourcesAndCompleteEmitter("连接语音识别服务失败: " + e.getMessage(), webSocket);
            }
        }
        
        @Override
        public void onMessage(WebSocket webSocket, String text) {
            logger.warn("收到非预期的文本消息: {}", text);
        }
        
        @Override
        public void onMessage(WebSocket webSocket, ByteString bytes) {
             if (finishedProcessing) return;
            try {
                byte[] res = bytes.toByteArray();
                int parseResult = parseServerResponse(res);
                if (parseResult < 0) { // 结束
                    logger.info("收到服务器结束帧 (Seq: {}), 完成识别。", parseResult);
                    closeResourcesAndCompleteEmitter("语音识别完成", webSocket);
                } else if (parseResult > 0) { // 错误
                     logger.error("收到服务器错误帧: Code {}", parseResult);
                     closeResourcesAndCompleteEmitter("语音识别服务返回错误", webSocket);
                } else { // 继续
                    sendNextAudioChunk(webSocket);
                }
            } catch (IOException e) {
                 logger.error("处理 WebSocket 消息或发送音频时 IO 错误", e);
                 closeResourcesAndCompleteEmitter("处理识别结果 IO 失败: " + e.getMessage(), webSocket);
            } catch (Exception e) {
                logger.error("处理 WebSocket 消息时发生未知错误", e);
                closeResourcesAndCompleteEmitter("处理识别结果失败: " + e.getMessage(), webSocket);
            }
        }

        private void sendNextAudioChunk(WebSocket webSocket) throws IOException {
             if (finishedProcessing) return;
            int len = audioInputStream.read(buffer, 0, bufferSize);
            if (len <= 0) {
                logger.info("音频流读取完毕，发送最后一个空音频包。");
                if (seq >= 0) { sendAudioOnlyRequest(webSocket, new byte[0], 0, true); }
                return;
            }
            boolean isLast = false;
             try {
                 // available() is not always reliable for determining the end of a stream
                 // A more robust check might involve trying a non-blocking read or checking the frame position
                 isLast = audioInputStream.available() == 0;
                 /* // Temporarily comment out the getFramePosition check for diagnosis
                 if (!isLast && audioInputStream.getFrameLength() != AudioSystem.NOT_SPECIFIED) {
                     isLast = audioInputStream.getFrameLength() == audioInputStream.getFramePosition();
                 }
                 */
             } catch (IOException e) {
                 logger.warn("Error checking available bytes, assuming not last chunk: {}", e.getMessage());
             }

            // logger.debug("读取并发送音频块: length={}, isLast={}, available={}, frameLength={}, framePos={}", // Temporarily remove framePos from log
            //     len, isLast, audioInputStream.available(), audioInputStream.getFrameLength(), audioInputStream.getFramePosition());
             logger.debug("读取并发送音频块: length={}, isLast={}, available={}, frameLength={}", // Updated log without framePos
                 len, isLast, audioInputStream.available(), audioInputStream.getFrameLength());
            sendAudioOnlyRequest(webSocket, buffer, len, isLast);
        }
        
        @Override
        public void onClosing(WebSocket webSocket, int code, String reason) {
            logger.info("WebSocket 正在关闭: Code={}, Reason={}", code, reason);
             // 检查是否是服务器在收到最后一个包后发起的正常关闭
            if (code == 1000 && seq < 0) { 
                logger.info("识别流程正常结束 (服务器发起关闭 code=1000)。");
                // 将此视为正常完成
                closeResourcesAndCompleteEmitter("语音识别完成", webSocket); 
            } else if (!finishedProcessing) {
                 // 其他情况，如果处理未完成，则视为意外关闭
                logger.warn("WebSocket 在处理完成前关闭 (Closing), Code={}, Reason={}", code, reason);
                closeResourcesAndCompleteEmitter("语音识别服务连接意外关闭: " + reason + " (Code: " + code + ")", null); 
            }
        }
        
        @Override
        public void onClosed(WebSocket webSocket, int code, String reason) {
            logger.info("WebSocket 已关闭: Code={}, Reason={}", code, reason);
             // 作为备用检查，以防 onClosing 未完全处理
            if (code == 1000 && seq < 0 && !finishedProcessing) { 
                 logger.info("识别流程正常结束 (服务器已关闭连接 code=1000)。");
                 closeResourcesAndCompleteEmitter("语音识别完成", null); // WebSocket 在此可能已不可用
            } else if (!finishedProcessing) {
                logger.warn("WebSocket 在处理完成前关闭 (Closed), Code={}, Reason={}", code, reason);
                closeResourcesAndCompleteEmitter("语音识别服务连接已关闭: " + reason + " (Code: " + code + ")", null);
            }
        }
        
        @Override
        public void onFailure(WebSocket webSocket, Throwable t, Response response) {
            logger.error("WebSocket 连接失败: {}", t.getMessage(), t);
            String errorMsg = "语音识别服务连接失败: " + t.getMessage();
             if (response != null) { errorMsg += " (HTTP Status: " + response.code() + ")"; }
             if (!finishedProcessing) {
                closeResourcesAndCompleteEmitter(errorMsg, webSocket);
             }
        }
        
        private void sendFullClientRequest(WebSocket webSocket) {
            seq = 1;
            JsonObject user = new JsonObject();
            user.addProperty("uid", "prod_user_" + UUID.randomUUID().toString().substring(0, 8));
            JsonObject audio = new JsonObject();
            audio.addProperty("format", "pcm");
            audio.addProperty("sample_rate", (int) audioFormat.getSampleRate());
            audio.addProperty("bits", audioFormat.getSampleSizeInBits());
            audio.addProperty("channel", audioFormat.getChannels());
            audio.addProperty("codec", "raw");
            JsonObject requestJson = new JsonObject(); // Renamed from 'request' to avoid conflict
            requestJson.addProperty("model_name", "bigmodel");
            requestJson.addProperty("enable_punc", true);
            JsonObject payloadJson = new JsonObject();
            payloadJson.add("user", user);
            payloadJson.add("audio", audio);
            payloadJson.add("request", requestJson); // Use the renamed object
            String payloadStr = payloadJson.toString();
            logger.debug("发送 FullClientRequest, Payload: {}", payloadStr);
            byte[] payloadBytes = gzipCompress(payloadStr.getBytes(), payloadStr.getBytes().length);
            byte[] header = getHeader(FULL_CLIENT_REQUEST, POS_SEQUENCE, JSON, GZIP, (byte) 0);
            byte[] payloadSize = intToBytes(payloadBytes.length);
            byte[] seqBytes = intToBytes(seq);
            byte[] fullClientRequest = concatByteArrays(header, seqBytes, payloadSize, payloadBytes);
            if (!webSocket.send(ByteString.of(fullClientRequest))) {
                 logger.error("发送 FullClientRequest 失败!");
                 closeResourcesAndCompleteEmitter("发送初始化请求失败", webSocket);
            } else {
                 logger.info("FullClientRequest 已发送 (Seq: {})", seq);
            }
        }
        
        private boolean sendAudioOnlyRequest(WebSocket webSocket, byte[] audioData, int len, boolean isLast) {
             if (finishedProcessing) return false;
            seq++;
             if (isLast) { logger.info("准备发送最后一个音频包 (Seq: {})", -seq); seq = -seq; }
            byte messageTypeSpecificFlags = isLast ? NEG_WITH_SEQUENCE : POS_SEQUENCE;
            byte[] header = getHeader(AUDIO_ONLY_REQUEST, messageTypeSpecificFlags, NO_SERIALIZATION, GZIP, (byte) 0);
            byte[] sequenceBytes = intToBytes(seq);
            byte[] payloadBytes = (len > 0) ? gzipCompress(audioData, len) : new byte[0];
            byte[] payloadSize = intToBytes(payloadBytes.length);
            byte[] audioOnlyRequest = concatByteArrays(header, sequenceBytes, payloadSize, payloadBytes);
            boolean success = webSocket.send(ByteString.of(audioOnlyRequest));
             if (!success) {
                logger.error("发送 AudioOnlyRequest 失败 (Seq: {})", seq);
                closeResourcesAndCompleteEmitter("发送音频数据失败", webSocket);
             } else {
                 logger.debug("AudioOnlyRequest 已发送 (Seq: {}, Size: {}, Compressed: {})", seq, len, payloadBytes.length);
             }
             return success;
        }
        
        private int parseServerResponse(byte[] res) throws IOException {
            if (res == null || res.length < 12) { logger.warn("收到无效的服务器响应 (长度不足)"); return 999; }
            final byte num = 0b00001111;
            int messageType = (res[1] >> 4) & num;
            int messageCompression = res[2] & 0x0f;
            byte[] temp = new byte[4];
            System.arraycopy(res, 4, temp, 0, temp.length);
            int sequence = bytesToInt(temp);
            System.arraycopy(res, 8, temp, 0, temp.length);
            int payloadSize = bytesToInt(temp);
            // Basic check for payload size mismatch
            if (payloadSize < 0 || res.length < 12 + payloadSize) { 
                 logger.warn("收到无效的服务器响应 (负载大小不匹配或为负): Expected at least {}, Got {}", 12 + payloadSize, res.length);
                 // Depending on protocol, maybe allow processing if res.length >= 12
                 payloadSize = Math.max(0, res.length - 12); // Attempt to process available data
            }
            byte[] payload = new byte[payloadSize];
             if (payloadSize > 0) { System.arraycopy(res, 12, payload, 0, payloadSize); }
            String payloadStr = null;
            switch (messageType) {
                case FULL_SERVER_RESPONSE:
                    payloadStr = (messageCompression == GZIP) ? new String(gzipDecompress(payload)) : new String(payload);
                    logger.debug("收到 FullServerResponse (Seq: {}): {}", sequence, payloadStr);
                    try {
                        JsonObject jsonResponse = gson.fromJson(payloadStr, JsonObject.class);
                        if (jsonResponse != null && jsonResponse.has("result") && jsonResponse.getAsJsonObject("result").has("text")) {
                            String recognizedText = jsonResponse.getAsJsonObject("result").get("text").getAsString();
                            if (recognizedText != null && !recognizedText.isEmpty()) {
                                emitter.send(SseEmitter.event().id(String.valueOf(messageCount.incrementAndGet())).name("result").data(recognizedText));
                                 logger.info("已发送识别片段 (SSE ID: {}): {}", messageCount.get(), recognizedText);
                            } else { logger.debug("收到空识别结果，忽略。"); }
                        } else { logger.warn("收到的 FullServerResponse 缺少 result.text 字段: {}", payloadStr); }
                    } catch (Exception e) { logger.error("解析 FullServerResponse JSON 失败: {}", payloadStr, e); }
                    break;
                case SERVER_ACK:
                     payloadStr = (payloadSize > 0) ? new String(payload) : "[ACK]";
                    logger.debug("收到 Server ACK (Seq: {}): {}", sequence, payloadStr);
                    break;
                case SERVER_ERROR_RESPONSE:
                    payloadStr = new String(payload);
                    logger.error("收到 Server Error Response: Code={}, Message='{}'", sequence, payloadStr);
                     // Send error to client and mark processing as finished
                     closeResourcesAndCompleteEmitter("语音识别服务错误: " + payloadStr + " (Code: " + sequence + ")", null); // No websocket needed here
                     return sequence;
                default: logger.warn("收到未知的服务器消息类型: {}", messageType); break;
            }
            if (sequence < 0) { logger.info("响应序列号为负 ({})，表示识别流程结束。", sequence); return sequence; }
            else { return 0; }
        }

        private synchronized void closeResourcesAndCompleteEmitter(String completionMessage, WebSocket webSocket) {
            if (finishedProcessing) { logger.debug("资源清理和 Emitter 完成已被调用，跳过。"); return; }
            finishedProcessing = true;
            logger.info("开始清理资源并完成 Emitter, 原因: {}", completionMessage);
            // 1. 关闭 AudioInputStream
            if (audioInputStream != null) {
                try { audioInputStream.close(); logger.info("AudioInputStream 已关闭。"); } 
                catch (IOException e) { logger.warn("关闭 AudioInputStream 时发生错误", e); }
            } else { logger.warn("AudioInputStream 为 null，无法关闭。"); }
            // 2. 删除转换后的临时文件
             deleteSafely(convertedAudioFile); // 使用传入的引用删除
            // 3. 关闭 WebSocket (Best effort)
            if (webSocket != null) {
                try { webSocket.close(1000, "Client closing connection."); logger.info("尝试正常关闭 WebSocket 连接。"); }
                 catch (IllegalStateException e) { logger.warn("尝试关闭 WebSocket 时状态非法 (可能已关闭): {}", e.getMessage()); } // Catch specific state exception
                catch (Exception e) { logger.warn("尝试关闭 WebSocket 时发生错误: {}", e.getMessage()); }
            } else { logger.warn("WebSocket 实例为 null，无法关闭。"); }
            // 4. 完成 Emitter
             try {
                 if (emitter != null) { // Check if emitter is null before using
                    // 根据传入的消息判断事件类型
                     if ("语音识别完成".equals(completionMessage)) {
                         emitter.send(SseEmitter.event().name("complete").data(completionMessage));
                         logger.info("已发送最终完成事件到 SSE: {}", completionMessage);
                     } else if (completionMessage != null) { // 其他消息视为错误或状态更新
                         emitter.send(SseEmitter.event().name("error").data(completionMessage));
                         logger.info("已发送最终错误/状态事件到 SSE: {}", completionMessage);
                     } else { logger.warn("完成消息为空，仅完成 Emitter。"); }
                     emitter.complete();
                     logger.info("SSE Emitter 已完成。");
                 } else {
                      logger.warn("Emitter is null, cannot send final message or complete.");
                 }
             } catch (IOException | IllegalStateException e) {
                 logger.error("发送最终 SSE 事件或完成 Emitter 时失败", e);
                 try { 
                      if (emitter != null) emitter.complete(); 
                 } catch (Exception ignored) {}
             }
        }
    }
    
    // --- 静态工具方法 --- 
    private static byte[] getHeader(byte messageType, byte messageTypeSpecificFlags, byte serialMethod, byte compressionType,
                                   byte reservedData) {
        final byte[] header = new byte[4];
        header[0] = (PROTOCOL_VERSION << 4) | DEFAULT_HEADER_SIZE;
        header[1] = (byte) ((messageType << 4) | messageTypeSpecificFlags);
        header[2] = (byte) ((serialMethod << 4) | compressionType);
        header[3] = reservedData;
        return header;
    }
    
    private static byte[] intToBytes(int a) {
        return new byte[]{
                (byte) ((a >> 24) & 0xFF),
                (byte) ((a >> 16) & 0xFF),
                (byte) ((a >> 8) & 0xFF),
                (byte) (a & 0xFF)
        };
    }
    
    private static int bytesToInt(byte[] src) {
        if (src == null || (src.length != 4)) { throw new IllegalArgumentException("无效的字节数组，无法转为 int"); }
        return ((src[0] & 0xFF) << 24) | ((src[1] & 0xff) << 16) | ((src[2] & 0xff) << 8) | ((src[3] & 0xff));
    }
    
    private static byte[] gzipCompress(byte[] src) {
        return gzipCompress(src, src.length);
    }

    private static byte[] gzipCompress(byte[] src, int len) {
        if (src == null || len == 0) { return new byte[0]; }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try (GZIPOutputStream gzip = new GZIPOutputStream(out)) {
            gzip.write(src, 0, len);
        } catch (IOException e) { logger.error("Gzip 压缩失败", e); return new byte[0]; }
        return out.toByteArray();
    }
    
    private static byte[] gzipDecompress(byte[] src) throws IOException {
        if (src == null || src.length == 0) { return new byte[0]; }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ByteArrayInputStream ins = new ByteArrayInputStream(src);
        try (GZIPInputStream gzip = new GZIPInputStream(ins)) {
            byte[] buffer = new byte[1024];
            int n;
            while ((n = gzip.read(buffer)) >= 0) { out.write(buffer, 0, n); }
        } catch (IOException e) {
             logger.error("Gzip 解压失败", e);
             throw e; // Re-throw exception after logging
        }
        return out.toByteArray();
    }

    private static byte[] concatByteArrays(byte[]... arrays) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        for (byte[] array : arrays) {
            try { 
                 if (array != null) { // Add null check for arrays
                     out.write(array); 
                 }
            } catch (IOException e) { throw new RuntimeException("Failed to concatenate byte arrays", e); }
        }
        return out.toByteArray();
    }

    // --- 资源清理 --- 
    @PreDestroy
    public void cleanup() {
        logger.info("正在关闭 ASRService 资源...");
        if (okHttpClient != null) {
            okHttpClient.dispatcher().executorService().shutdown();
            okHttpClient.connectionPool().evictAll();
            try {
                 if (!okHttpClient.dispatcher().executorService().awaitTermination(5, TimeUnit.SECONDS)) {
                     logger.warn("OkHttp Dispatcher Executor 未能在5秒内关闭。");
                     okHttpClient.dispatcher().executorService().shutdownNow();
                 }
            } catch (InterruptedException e) {
                 logger.error("等待 OkHttp Dispatcher Executor 关闭时被中断", e);
                 Thread.currentThread().interrupt();
            }
            // Close WebSocket connections gracefully if possible (OkHttp might do this already)
            // okHttpClient.dispatcher().cancelAll(); // Force cancel ongoing calls/websockets
            logger.info("OkHttpClient 资源已清理。");
        }
         if (asrExecutor != null && !asrExecutor.isShutdown()) {
            asrExecutor.shutdown();
            try {
                if (!asrExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    asrExecutor.shutdownNow();
                     logger.warn("ASR Executor 未能在5秒内关闭，强制关闭。");
                } else { logger.info("ASR Executor 已关闭。"); }
            } catch (InterruptedException e) {
                asrExecutor.shutdownNow();
                Thread.currentThread().interrupt();
                 logger.error("关闭 ASR Executor 时被中断", e);
            }
        }
    }
} 