import pandas as pd
import re
import logging
from typing import List, Dict, Optional

# 配置日志


# 配置日志记录
logging.basicConfig(
    level=logging.INFO,  # 设置日志级别
    format="%(asctime)s - %(levelname)s - %(message)s",  # 日志格式
    handlers=[
        logging.StreamHandler(),  # 输出到控制台
        logging.FileHandler("doctor.log", mode="a", encoding="utf-8")  # 输出到文件
    ]
)

# 创建日志记录器
logger = logging.getLogger()

class Doctor:
    def __init__(self, age: int, gender: str, chief_complaint: str, history_of_present_illness: str,
                 past_history: str, contact_history_of_infectious_diseases: str,
                 personal_history: str, family_history: str, physical_examination: Optional[str] = None,
                 special_examination: Optional[str] = None, auxiliary_examination: Optional[str] = None, preliminary_exam: Optional[str] = None, treatment: Optional[str] = None, doctor_examination: Optional[str] = None, has_examination: Optional[str] = None, index: Optional[str] = None):
        self.age = age
        self.gender = gender
        self.chief_complaint = chief_complaint
        self.history_of_present_illness = history_of_present_illness
        self.past_history = past_history
        self.contact_history_of_infectious_diseases = contact_history_of_infectious_diseases
        self.personal_history = personal_history
        self.family_history = family_history
        self.physical_examination = physical_examination
        self.special_examination = special_examination
        self.auxiliary_examination = auxiliary_examination


        self.preliminary_exam = preliminary_exam
        self.treatment = treatment

        self.doctor_examination = doctor_examination
        self.has_examination = has_examination
        self.index = index

    def __repr__(self):
        return (f"Doctor(age={self.age}, gender={self.gender}, \n"
                f"index={self.index}, \n"
                f"chief_complaint={self.chief_complaint}, \n"
                f"history_of_present_illness={self.history_of_present_illness}, \n"
                f"past_history={self.past_history}, \n"
                f"contact_history_of_infectious_diseases={self.contact_history_of_infectious_diseases}, \n"
                f"personal_history={self.personal_history}, \n"
                f"family_history={self.family_history}, \n"
                f"physical_examination={self.physical_examination}, \n"
                f"special_examination={self.special_examination}, \n"
                f"auxiliary_examination={self.auxiliary_examination}), \n"
                f"preliminary_exam={self.preliminary_exam}, \n"
                f"treatment={self.treatment}, \n"
                f"doctor_examination={self.doctor_examination}, \n"
                f"has_examination={self.has_examination} \n"

                )

    @classmethod
    def load_from_csv(cls, file_path: str) -> List['Doctor']:
        """
        从 CSV 文件加载数据并创建 Doctor 实例列表。
        """
        try:
            df = pd.read_csv(file_path)
            logger.info(f"成功读取 CSV 文件: {file_path}")
        except Exception as e:
            logger.error(f"读取 CSV 文件失败: {e}")
            return []

        doctors = []
        for _, row in df.iterrows():
            try:
                doctor = cls(
                    age=row['就诊时年龄'],
                    gender=row['性别'],
                    chief_complaint=row['主诉'],
                    history_of_present_illness=row['现病史'],
                    past_history=row['既往史'],
                    contact_history_of_infectious_diseases=row['传染病接触史'],
                    personal_history=row['个人史'],
                    family_history=row['家族史'],
                    physical_examination=row.get('体格检查'),
                    special_examination=row.get('专科查体'),
                    auxiliary_examination=row.get('辅助检查')
                )
                doctors.append(doctor)
            except KeyError as e:
                logger.warning(f"CSV 文件缺少必要字段: {e}")
            except Exception as e:
                logger.error(f"创建 Doctor 实例失败: {e}")

        return doctors

    @classmethod
    def load_from_json_filepath(cls, file_path: str) -> List['Doctor']:
        """
        从 JSON 文件加载数据并创建 Doctor 实例列表。
        """
        try:
            df = pd.read_json(file_path)
            logger.info(f"成功读取 JSON 文件: {file_path}")
        except Exception as e:
            logger.error(f"读取 JSON 文件失败: {e}")
            return []

        doctors = []
        for _, row in df.iterrows():
            try:
                text = row["doctor_history"][-1]
                data = cls.extract_data(text)
                doctor = cls(**data)
                doctor.preliminary_exam = row.get("preliminary_exam")
                doctor.treatment = row.get("treatment")

                doctor.doctor_examination = row.get("doctor_examination")
                doctor.has_examination = row.get("has_examination")
                doctor.index = row.get("index")

                doctors.append(doctor)
            except (KeyError, IndexError) as e:
                logger.warning(f"JSON 文件缺少必要字段或格式错误: {e}")
            except Exception as e:
                logger.error(f"创建 Doctor 实例失败: {e}")

        return doctors
    

    @classmethod
    def load_from_json(cls, historys_json : dict) -> List['Doctor']:
        """
        从 JSON 格式的字符串 加载数据并创建 Doctor 实例列表。
        """
        try:
            df = pd.DataFrame(historys_json)
            logger.info(f"成功读取 JSON 文件:")
        except Exception as e:
            logger.error(f"读取 JSON 文件失败: {e}")
            return []

        doctors = []
        for _, row in df.iterrows():
            try:
                text = row["doctor_history"][-1]
                data = cls.extract_data(text)
                doctor = cls(**data)
                doctor.preliminary_exam = row.get("preliminary_exam")
                doctor.treatment = row.get("treatment")
                doctors.append(doctor)
            except (KeyError, IndexError) as e:
                logger.warning(f"JSON 文件缺少必要字段或格式错误: {e}")
            except Exception as e:
                logger.error(f"创建 Doctor 实例失败: {e}")

        return doctors
    
    @classmethod
    def load_from_solution(cls, solution: str) -> List['Doctor']:
        """
        从诊断方案加载数据并创建 Doctor 实例列表。
        """
        try:
            doctors = []
            data = cls.extract_data(solution)
            doctor = cls(**data)
            doctors.append(doctor)
        except Exception as e:
            logger.error(f"创建 Doctor 实例失败: {e}")

        return doctors
    

    @classmethod
    def extract_data(cls, text: str) -> Dict[str, str]:
        """
        使用正则表达式从文本中提取数据。
        """
        try:
            # 提取基本信息
            basic_info = re.search(r"【基本信息】(.*?)\n", text).group(1).strip()
            gender = "男" if "男" in basic_info else "女"
            age = int(re.search(r"\d+", basic_info).group())

            # 提取其他字段
            chief_complaint = re.search(r"【主诉】(.*?)\n", text).group(1).strip()
            history_of_present_illness = re.search(r"【现病史】(.*?)\n", text).group(1).strip()
            past_history = re.search(r"【既往史】(.*?)\n", text).group(1).strip()
            contact_history_of_infectious_diseases = re.search(r"【传染病接触史】(.*?)\n", text).group(1).strip()
            personal_history = re.search(r"【个人史】(.*?)\n", text).group(1).strip()
            family_history = re.search(r"【家族史】(.*?)\n", text).group(1).strip()

            

            return {
                "age": age,
                "gender": gender,
                "chief_complaint": chief_complaint,
                "history_of_present_illness": history_of_present_illness,
                "past_history": past_history,
                "contact_history_of_infectious_diseases": contact_history_of_infectious_diseases,
                "personal_history": personal_history,
                "family_history": family_history
            }
        except AttributeError as e:
            logger.error(f"正则表达式匹配失败: {e}")
            raise ValueError("文本格式不符合预期")
        except Exception as e:
            logger.error(f"提取数据失败: {e}")
            raise

if __name__ == '__main__':
    # 从 CSV 文件加载数据
    # doctors = Doctor.load_from_csv('../data/cases.csv')
    # for doctor in doctors:
    #     print(doctor)

    # 从 JSON 文件加载数据
    doctors = Doctor.load_from_json_filepath('cases_full.json')
    for doctor in doctors:
        print("index: ", doctors.index(doctor))
        print(doctor)
        print()
        print()

