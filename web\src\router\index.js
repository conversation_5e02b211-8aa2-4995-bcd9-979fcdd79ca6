import { createRouter, createWebHistory } from 'vue-router'
import HomePage from '../views/Home.vue'
import AboutPage from '../views/About.vue'
import { useUserStore } from '../stores/user' // 导入 user store

const routes = [
  {
    path: '/',
    name: 'home',
    component: HomePage
  },
  {
    path: '/about',
    name: 'about',
    // route level code-splitting
    // this generates a separate chunk (about.[hash].js) for this route
    // which is lazy-loaded when the route is visited.
    component: AboutPage
  },
  {
    path: '/chat/:userId',
    name: 'chat',
    component: () => import('../views/Chat.vue')  // 懒加载 Chat 组件
  },
  {
    path: '/chatv2/:userId',
    name: 'chatv2',
    component: () => import('../views/ChatV2.vue')  // 懒加载 ChatV2 组件
  },
  {
    path: '/quickchat/:userId',
    name: 'quickchat',
    component: () => import('../views/QuickChat.vue')  // 懒加载 QuickChat 组件
  },
  {
    path: '/admin-data',
    name: 'admin-data',
    component: () => import('../views/AdminData.vue'), // 懒加载 AdminData 组件
    meta: { requiresAuth: true } // 添加一个 meta 字段来标记需要认证的路由
  },
  {
    path: '/all-cases',
    name: 'all-cases',
    component: () => import('../views/AllCaseHistory.vue'), // 懒加载 AllCaseHistory 组件
    meta: { requiresAuth: true } // 需要认证
  },
  {
    path: '/ocr-viewer',
    name: 'ocr-viewer',
    component: () => import('../components/OcrImageViewer.vue'), // 懒加载 OcrImageViewer 组件
    meta: { requiresAuth: true } // 需要认证
  }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})

router.beforeEach((to, from, next) => {
  const userStore = useUserStore()
  const isLoggedIn = !!userStore.userInfo // 检查 userInfo 是否存在

  if (to.meta.requiresAuth && !isLoggedIn) {
    // 如果路由需要认证且用户未登录
    next({ name: 'home' }) // 重定向到首页，或者你可以改成登录页的路由name
  } else {
    next() // 否则，正常导航
  }
})

export default router
