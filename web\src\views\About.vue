<template>
  <div class="about">
    <div class="main-content">
      <h1 class="page-title">关于我们</h1>
      <div class="content">
        <div class="section">
          <h2>公司简介</h2>
          <p>Geoming AI 是一个创新的人工智能医疗咨询平台，致力于为用户提供便捷、准确的健康咨询服务。我们运用先进的人工智能技术，结合专业的医疗知识库，为您提供初步的健康评估和建议。</p>
        </div>
        
        <div class="section">
          <h2>我们的使命</h2>
          <p>通过人工智能技术，让每个人都能获得及时、专业的医疗咨询服务，提高医疗资源的可及性和效率。</p>
        </div>

        <div class="section">
          <h2>核心优势</h2>
          <div class="advantages">
            <div class="advantage-item">
              <h3>AI 技术</h3>
              <p>采用最新的人工智能技术，提供准确的初步诊断建议</p>
            </div>
            <div class="advantage-item">
              <h3>专业知识库</h3>
              <p>依托庞大的医疗数据库，覆盖广泛的疾病症状信息</p>
            </div>
            <div class="advantage-item">
              <h3>便捷服务</h3>
              <p>7*24小时在线服务，随时随地获取健康咨询</p>
            </div>
          </div>
        </div>

        <div class="section">
          <h2>联系我们</h2>
          <p>邮箱：<EMAIL></p>
          <p>地址：北京市海淀区全球创新社区</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { useRouter } from 'vue-router'

export default {
  name: 'AboutPage',
  setup() {
    const router = useRouter()
    return { router }
  }
}
</script>

<style scoped>
.about {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f5f5;
  padding-top: 0;
  overflow-y: auto;
}

.main-content {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-title {
  text-align: center;
  color: #4D6BFE;
  font-size: 2.5em;
  margin-bottom: 20px;
}

.content {
  background: #fff;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.section {
  margin-bottom: 40px;
}

.section:last-child {
  margin-bottom: 0;
}

.section h2 {
  color: #4D6BFE;
  margin-bottom: 20px;
  font-size: 1.8em;
}

.section p {
  color: #666;
  line-height: 1.8;
  font-size: 16px;
}

.advantages {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.advantage-item {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
}

.advantage-item h3 {
  color: #4D6BFE;
  margin-bottom: 10px;
}

.advantage-item p {
  color: #666;
  font-size: 14px;
}

.advantage-item:hover {
  background: rgba(77, 107, 254, 0.05);
  transition: all 0.3s ease;
}
</style> 