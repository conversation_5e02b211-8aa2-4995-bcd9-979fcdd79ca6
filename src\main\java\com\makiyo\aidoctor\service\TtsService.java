package com.makiyo.aidoctor.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.makiyo.aidoctor.utils.TextChunkUtils;
import com.makiyo.aidoctor.utils.TtsRequest;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.apache.commons.codec.binary.Base64;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Slf4j
@Data
@Service
public class TtsService {
    @Value("${tts.api.url}")
    private String apiUrl;

    @Value("${tts.api.appId}")
    private String appId;

    @Value("${tts.api.accessToken}")
    private String accessToken;

    @Value("${tts.audio.voiceType}")
    private String voiceType;

    @Value("${tts.audio.encoding}")
    private String encoding;

    @Value("${tts.audio.cluster}")
    private String cluster;

    @Value("${tts.audio.basePath}")
    private String audioBasePath;

    @Value("${tts.audio.baseUrl}")
    private String audioBaseUrl;

    @Value("${tts.audio.speedRatio}")
    private double speedRatio;

    @Value("${tts.audio.volumeRatio}")
    private double volumeRatio;

    @Value("${tts.audio.pitchRatio}")
    private double pitchRatio;

    /**
     * 生成语音文件并返回其访问URL
     *
     * @param text 要转换的文本
     * @return 生成的音频文件访问URL
     * @throws IOException 如果生成过程中发生错误
     */
    public String generateTtsUrl(String text) throws IOException {
        // 生成唯一的文件名
        String fileName = UUID.randomUUID().toString() + ".mp3";
        
        // 确保目录存在
        File directory = new File(audioBasePath);
        if (!directory.exists()) {
            directory.mkdirs();
        }

        // 构建完整的文件路径
        String filePath = audioBasePath + File.separator + fileName;
        
        // 生成语音文件
        generateLongTextAudio(text, filePath);
        
        // 返回可访问的URL
        return audioBaseUrl + "/" + fileName;
    }

    /**
     * 处理长文本生成语音
     *
     * @param text 要转换的文本
     * @param outputPath 输出文件路径
     * @throws IOException 如果生成过程中发生错误
     */
    private void generateLongTextAudio(String text, String outputPath) throws IOException {
        List<String> chunks = TextChunkUtils.splitTextIntoChunks(text);
        List<byte[]> audioChunks = new ArrayList<>();

        for (String chunk : chunks) {
            try {
                byte[] audioData = textToSpeechHttp(chunk);
                audioChunks.add(audioData);
            } catch (IOException e) {
                log.error("处理文本块时发生错误: {}", chunk, e);
                throw e;
            }
        }

        // 使用Java Sound API合并MP3文件
        try {
            // 创建临时文件目录
            File tempDir = new File(audioBasePath + File.separator + "temp");
            if (!tempDir.exists()) {
                tempDir.mkdirs();
            }
            
            // 将每个音频块写入临时文件
            List<File> tempFiles = new ArrayList<>();
            for (int i = 0; i < audioChunks.size(); i++) {
                File tempFile = new File(tempDir, "chunk_" + i + ".mp3");
                try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                    fos.write(audioChunks.get(i));
                }
                tempFiles.add(tempFile);
            }
            
            // 合并音频文件
            mergeAudioFiles(tempFiles, new File(outputPath));
            
            // 清理临时文件
            for (File tempFile : tempFiles) {
                tempFile.delete();
            }
            tempDir.delete();
            
        } catch (Exception e) {
            log.error("合并音频文件失败", e);
            // 如果合并失败，回退到简单拼接方式
            log.warn("回退到简单拼接方式");
            try (FileOutputStream fos = new FileOutputStream(outputPath)) {
                // 写入MP3头部
                if (!audioChunks.isEmpty()) {
                    byte[] firstChunk = audioChunks.get(0);
                    // 查找第一个MP3帧的开始位置
                    int frameStart = findFirstMp3Frame(firstChunk);
                    if (frameStart >= 0) {
                        // 写入第一个文件的头部和第一帧
                        fos.write(firstChunk, 0, firstChunk.length);
                        
                        // 写入其余文件的MP3帧（跳过它们的头部）
                        for (int i = 1; i < audioChunks.size(); i++) {
                            byte[] chunk = audioChunks.get(i);
                            int nextFrameStart = findFirstMp3Frame(chunk);
                            if (nextFrameStart >= 0) {
                                fos.write(chunk, nextFrameStart, chunk.length - nextFrameStart);
                            } else {
                                // 如果找不到帧开始，写入整个块
                                fos.write(chunk);
                            }
                        }
                    } else {
                        // 如果找不到帧，回退到完全拼接
                        for (byte[] audioChunk : audioChunks) {
                            fos.write(audioChunk);
                        }
                    }
                }
            }
        }
    }

    /**
     * 查找MP3文件中第一个帧的开始位置
     * MP3帧通常以0xFF开头，后跟0xE0-0xFF之间的字节
     */
    private int findFirstMp3Frame(byte[] data) {
        for (int i = 0; i < data.length - 1; i++) {
            if ((data[i] & 0xFF) == 0xFF && (data[i + 1] & 0xE0) == 0xE0) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 合并多个音频文件
     * 
     * @param inputFiles 输入文件列表
     * @param outputFile 输出文件
     * @throws Exception 如果合并过程中发生错误
     */
    private void mergeAudioFiles(List<File> inputFiles, File outputFile) throws Exception {
        if (inputFiles.isEmpty()) {
            throw new IllegalArgumentException("没有输入文件");
        }
        
        // 使用第一个文件的头部
        try (FileOutputStream fos = new FileOutputStream(outputFile)) {
            // 处理第一个文件 - 完整复制
            byte[] firstFileData = java.nio.file.Files.readAllBytes(inputFiles.get(0).toPath());
            fos.write(firstFileData);
            
            // 处理剩余文件 - 跳过头部，只复制音频帧
            for (int i = 1; i < inputFiles.size(); i++) {
                byte[] fileData = java.nio.file.Files.readAllBytes(inputFiles.get(i).toPath());
                
                // 查找第一个MP3帧的开始位置
                int frameStart = findFirstMp3Frame(fileData);
                if (frameStart >= 0) {
                    // 只写入MP3帧部分，跳过头部
                    fos.write(fileData, frameStart, fileData.length - frameStart);
                } else {
                    // 如果找不到帧开始，写入整个文件（不太可能发生）
                    fos.write(fileData);
                }
            }
        }
        
        log.debug("成功合并 {} 个音频文件", inputFiles.size());
    }

    /**
     * 使用JavaCV的FFmpeg工具合并多个MP3文件
     * 
     * @param inputFiles 输入文件路径列表
     * @param outputFile 输出文件路径
     * @throws Exception 如果合并过程中发生错误
     */
    private void mergeAudioFilesWithJavaCV(List<String> inputFiles, String outputFile) throws Exception {
        if (inputFiles.isEmpty()) {
            throw new IllegalArgumentException("没有输入文件");
        }
        
        try {
            // 使用第一个文件获取音频格式信息
            org.bytedeco.javacv.FFmpegFrameGrabber firstGrabber = new org.bytedeco.javacv.FFmpegFrameGrabber(inputFiles.get(0));
            firstGrabber.start();
            
            // 创建输出录制器
            org.bytedeco.javacv.FFmpegFrameRecorder recorder = new org.bytedeco.javacv.FFmpegFrameRecorder(
                outputFile,
                firstGrabber.getAudioChannels()
            );
            
            // 设置与输入相同的音频参数
            recorder.setAudioChannels(firstGrabber.getAudioChannels());
            recorder.setSampleRate(firstGrabber.getSampleRate());
            recorder.setAudioCodec(firstGrabber.getAudioCodec());
            recorder.setAudioBitrate(firstGrabber.getAudioBitrate());
            recorder.setFormat("mp3");
            
            recorder.start();
            
            // 处理第一个文件
            org.bytedeco.javacv.Frame frame;
            while ((frame = firstGrabber.grab()) != null) {
                if (frame.samples != null) {
                    recorder.record(frame);
                }
            }
            firstGrabber.stop();
            firstGrabber.release();
            
            // 处理剩余文件
            for (int i = 1; i < inputFiles.size(); i++) {
                org.bytedeco.javacv.FFmpegFrameGrabber grabber = new org.bytedeco.javacv.FFmpegFrameGrabber(inputFiles.get(i));
                grabber.start();
                
                while ((frame = grabber.grab()) != null) {
                    if (frame.samples != null) {
                        recorder.record(frame);
                    }
                }
                
                grabber.stop();
                grabber.release();
            }
            
            // 关闭录制器
            recorder.stop();
            recorder.release();
            
            log.debug("成功使用JavaCV合并 {} 个音频文件", inputFiles.size());
        } catch (Exception e) {
            log.error("JavaCV合并音频失败", e);
            throw e;
        }
    }

    /**
     * 使用FFmpeg合并多个MP3文件
     * 
     * @param inputFiles 输入文件路径列表
     * @param outputFile 输出文件路径
     * @throws Exception 如果合并过程中发生错误
     */
    private void mergeAudioFilesWithFFmpeg(List<String> inputFiles, String outputFile) throws Exception {
        if (inputFiles.isEmpty()) {
            throw new IllegalArgumentException("没有输入文件");
        }
        
        // 创建一个临时的文件列表文件
        String fileListPath = audioBasePath + File.separator + "filelist.txt";
        try (java.io.PrintWriter writer = new java.io.PrintWriter(fileListPath)) {
            for (String file : inputFiles) {
                writer.println("file '" + file.replace("\\", "/") + "'");
            }
        }
        
        // 使用ProcessBuilder执行FFmpeg命令
        ProcessBuilder pb = new ProcessBuilder(
            "ffmpeg", 
            "-f", "concat", 
            "-safe", "0", 
            "-i", fileListPath, 
            "-c", "copy", 
            outputFile
        );
        
        Process process = pb.start();
        int exitCode = process.waitFor();
        
        // 删除临时文件列表
        new File(fileListPath).delete();
        
        if (exitCode != 0) {
            throw new Exception("FFmpeg合并失败，退出代码: " + exitCode);
        }
        
        log.debug("成功合并 {} 个音频文件", inputFiles.size());
    }

    /**
     * Convert text to speech using HTTP method with enhanced error handling
     *
     * @param text Text to be converted to speech
     * @return Byte array of the generated audio
     * @throws IOException If API request fails
     */
    public byte[] textToSpeechHttp(String text) throws IOException {
        // 构建请求
        TtsRequest ttsRequest = buildTtsRequest(text);
        String requestJson = JSON.toJSONString(ttsRequest);
        log.debug("发送TTS请求");

        // 创建 HTTP 客户端
        OkHttpClient client = new OkHttpClient.Builder()
                .addInterceptor(chain -> {
                    Request originalRequest = chain.request();
                    Request authorizedRequest = originalRequest.newBuilder()
                            .addHeader("Authorization", "Bearer;" + accessToken)
                            .build();
                    return chain.proceed(authorizedRequest);
                })
                .build();

        // 准备请求体
        RequestBody body = RequestBody.create(
                requestJson,
                MediaType.get("application/json; charset=utf-8")
        );

        // 构建请求
        Request request = new Request.Builder()
                .url(apiUrl)
                .post(body)
                .build();

        // 执行请求并处理响应
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                String errorBody = response.body() != null ? response.body().string() : "No error details";
                log.error("API请求失败. 状态码: {}, 错误信息: {}", response.code(), errorBody);
                throw new IOException("API请求失败: " + response.code());
            }

            ResponseBody responseBody = response.body();
            if (responseBody == null) {
                throw new IOException("响应体为空");
            }

            String responseString = responseBody.string();
            
            JSONObject jsonResponse = JSON.parseObject(responseString);
            
            // 检查响应状态 - 注意：code=3000 是成功状态
            if (jsonResponse.getInteger("code") != 3000) {
                throw new IOException("TTS API Error: " + jsonResponse.getString("message"));
            }

            // 直接从data字段获取音频数据
            String audioBase64 = jsonResponse.getString("data");
            if (audioBase64 == null || audioBase64.isEmpty()) {
                throw new IOException("No audio data in response");
            }

            // 解码 Base64 音频数据
            byte[] audioBytes = Base64.decodeBase64(audioBase64);
            validateAudioBytes(audioBytes);

            log.debug("音频数据生成完成: {} 字节", audioBytes.length);

            return audioBytes;
        }
    }

    /**
     * 验证音频字节是否有效
     */
    private void validateAudioBytes(byte[] audioBytes) throws IOException {
        if (audioBytes == null || audioBytes.length == 0) {
            throw new IOException("生成的音频数据为空");
        }

        // 检查文件头标记是否为 MP3
        if (audioBytes.length >= 2) {
            boolean isMP3 = (audioBytes[0] == (byte)0xFF && 
                          (audioBytes[1] == (byte)0xFB || 
                           audioBytes[1] == (byte)0xF3 || 
                           audioBytes[1] == (byte)0xF2));
            if (!isMP3) {
                log.warn("可能不是有效的MP3文件");
            }
        }
    }

    /**
     * 构建 TTS 请求
     */
    private TtsRequest buildTtsRequest(String text) {
        return TtsRequest.builder()
                .app(TtsRequest.App.builder()
                        .appid(appId)
                        .cluster(cluster)
                        .build())
                .user(TtsRequest.User.builder()
                        .uid("uid")
                        .build())
                .audio(TtsRequest.Audio.builder()
                        .encoding(encoding)
                        .voiceType(voiceType)
                        .speedRatio(speedRatio)
                        .volumeRatio(volumeRatio)
                        .pitchRatio(pitchRatio)
                        .build())
                .request(TtsRequest.Request.builder()
                        .reqID(UUID.randomUUID().toString())
                        .operation("query")
                        .text(text)
                        .build())
                .build();
    }
}