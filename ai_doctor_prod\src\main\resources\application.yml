server:
  port: 9999
  max-http-header-size: 10000000

spring:
  application:
    name: ai-doctor-prod-dev
  data:
    mongodb:
      host: localhost
      port: 27017
      database: aiDoctor_prod
      username: admin
      password: abc123456
      authentication-database: admin
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: org.postgresql.Driver
      url: *********************************************************************************
      username: postgres
      password: lin171820...
      initial-size: 8
      max-active: 16
      min-idle: 8
      max-wait: 60000
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: makiyo
        login-password: lin171820...
        allow:
        deny:
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          enabled: true
          config:
            multi-statement-allow: false
        slf4j:
          enabled: true
          statement-executable-sql-log-enable: true
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.makiyo.ai_doctor_prod.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl

# TTS 语音合成服务配置
tts:
  api:
    url: https://openspeech.bytedance.com/api/v1/tts  # 使用标准接口
    appId: 4146985820
    accessToken: z_2JNCRIbNM7Mk-dI24H6M8VAOv1JFeu
  audio:
    voiceType: BV700_V2_streaming
    encoding: mp3
    cluster: volcano_tts
    basePath: audio
    baseUrl: /audio
    speedRatio: 1.0
    volumeRatio: 1.0
    pitchRatio: 1.0

# Python AI 服务配置
python:
  prod:
    diagnose:
      url: http://localhost:5555/ai_doctor_v2_diagnosis
    inquiry:
      url: http://localhost:5555/ai_doctor_v2_inquiry
    preliminary:
      diagnosis:
        url: http://localhost:5555/ai_doctor_preliminary_diagnosis
    quick:
      inquiry:
        url: http://localhost:5555/ai_doctor_v2_quick_inquiry
    # HTTP客户端超时配置（毫秒）
    http:
      connect-timeout: 300000  # 连接超时：5分钟
      socket-timeout: 300000   # 套接字超时：5分钟
      request-timeout: 300000  # 请求超时：5分钟

# Springdoc OpenAPI Configuration (Swagger UI)
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    display-request-duration: true
    doc-expansion: none
  info:
    title: AI Doctor Prod API
    version: 1.0.0
    description: AI Doctor 生产环境应用程序的 API 文档
    contact:
      name: Makiyo

# 语音识别服务配置
asr:
  api:
    url: wss://openspeech.bytedance.com/api/v3/sauc/bigmodel
    appId: 4146985820
    token: z_2JNCRIbNM7Mk-dI24H6M8VAOv1JFeu
  temp:
    dir: ${user.dir}/audio

# OCR specific configurations
ocr:
  python:
    prod:
      service:
        url: http://localhost:5555/ocr_api
  image:
    prod:
      save:
        path: ${user.dir}/images